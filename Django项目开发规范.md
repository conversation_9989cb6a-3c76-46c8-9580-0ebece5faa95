# Django项目开发规范

## 目录
1. [项目结构规范](#项目结构规范)
2. [编码风格规范](#编码风格规范)
3. [模型(Models)规范](#模型models规范)
4. [视图(Views)规范](#视图views规范)
5. [服务层(Services)规范](#服务层services规范)
6. [选择器(Selectors)规范](#选择器selectors规范)
7. [API设计规范](#api设计规范)
8. [测试规范](#测试规范)
9. [数据库规范](#数据库规范)
10. [安全规范](#安全规范)

## 项目结构规范

### 标准项目结构
```
project_name/
├── manage.py
├── requirements/
│   ├── base.txt
│   ├── development.txt
│   └── production.txt
├── config/
│   ├── __init__.py
│   ├── settings/
│   │   ├── __init__.py
│   │   ├── base.py
│   │   ├── development.py
│   │   └── production.py
│   ├── urls.py
│   ├── wsgi.py
│   └── asgi.py
├── apps/
│   ├── __init__.py
│   ├── common/
│   │   ├── __init__.py
│   │   ├── models.py
│   │   ├── utils.py
│   │   └── exceptions.py
│   └── your_app/
│       ├── __init__.py
│       ├── models.py
│       ├── views.py
│       ├── apis.py
│       ├── services.py
│       ├── selectors.py
│       ├── admin.py
│       ├── apps.py
│       ├── urls.py
│       ├── tests/
│       │   ├── __init__.py
│       │   ├── test_models.py
│       │   ├── test_services.py
│       │   └── test_apis.py
│       ├── migrations/
│       ├── templates/
│       │   └── your_app/
│       └── static/
│           └── your_app/
├── static/
├── media/
├── templates/
├── locale/
└── docs/
```

### 应用结构规范
每个Django应用应包含以下文件：
- `models.py` - 数据模型定义
- `services.py` - 业务逻辑层
- `selectors.py` - 数据查询层
- `apis.py` - API视图
- `admin.py` - 管理后台配置
- `urls.py` - URL路由配置
- `tests/` - 测试文件目录

## 编码风格规范

### Python导入规范
```python
# future imports
from __future__ import annotations

# standard library
import json
from itertools import chain
from typing import Dict, List, Optional

# third-party
import requests
from celery import shared_task

# Django
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.db import models, transaction
from django.http import HttpResponse

# local Django
from .models import YourModel
from .services import your_service

# try/except imports
try:
    import redis
except ImportError:
    redis = None
```

### 命名规范
- **变量和函数**: 使用snake_case
- **类名**: 使用PascalCase
- **常量**: 使用UPPER_SNAKE_CASE
- **私有方法**: 以单下划线开头 `_private_method`
- **模块级私有**: 以双下划线开头 `__private_module_var`

### 代码格式规范
```python
# 正确的函数定义
def user_create(
    *,
    email: str,
    name: str,
    is_active: bool = True
) -> User:
    """创建用户的服务函数"""
    pass

# 正确的类定义
class UserService:
    """用户相关业务逻辑服务类"""
    
    def __init__(self, user: User):
        self.user = user
    
    def update_profile(self, *, name: str) -> User:
        """更新用户资料"""
        pass
```

## 模型(Models)规范

### 基础模型定义
```python
from django.db import models
from django.core.exceptions import ValidationError
from django.utils import timezone


class BaseModel(models.Model):
    """基础模型类"""
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        abstract = True


class User(BaseModel):
    """用户模型"""
    email = models.EmailField(unique=True)
    name = models.CharField(max_length=255)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        db_table = 'users'
        verbose_name = '用户'
        verbose_name_plural = '用户'
    
    def clean(self):
        """模型验证"""
        if not self.email:
            raise ValidationError('邮箱不能为空')
    
    @property
    def display_name(self) -> str:
        """显示名称"""
        return self.name or self.email
    
    def __str__(self):
        return self.email
```

### 模型规范要点
1. **继承BaseModel**: 所有模型都应继承包含created_at和updated_at的基础模型
2. **使用clean()方法**: 在clean()方法中实现业务验证逻辑
3. **Meta类配置**: 正确配置db_table、verbose_name等元数据
4. **属性方法**: 使用@property装饰器定义计算属性
5. **字符串表示**: 实现有意义的__str__方法

## 视图(Views)规范

### 避免在视图中写业务逻辑
```python
# ❌ 错误示例 - 业务逻辑在视图中
class UserCreateView(APIView):
    def post(self, request):
        email = request.data.get('email')
        name = request.data.get('name')
        
        # 业务逻辑不应该在这里
        user = User(email=email, name=name)
        user.full_clean()
        user.save()
        
        # 发送邮件逻辑也不应该在这里
        send_welcome_email(user)
        
        return Response({'id': user.id})

# ✅ 正确示例 - 使用服务层
class UserCreateApi(APIView):
    class InputSerializer(serializers.Serializer):
        email = serializers.EmailField()
        name = serializers.CharField(max_length=255)
    
    def post(self, request):
        serializer = self.InputSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        user = user_create(**serializer.validated_data)
        
        return Response({'id': user.id}, status=status.HTTP_201_CREATED)
```

## 服务层(Services)规范

### 服务函数定义
```python
from django.db import transaction
from django.core.exceptions import ValidationError


@transaction.atomic
def user_create(
    *,
    email: str,
    name: str,
    is_active: bool = True
) -> User:
    """
    创建用户服务
    
    Args:
        email: 用户邮箱
        name: 用户姓名
        is_active: 是否激活
    
    Returns:
        User: 创建的用户实例
    
    Raises:
        ValidationError: 验证失败时抛出
    """
    # 检查邮箱是否已存在
    if User.objects.filter(email=email).exists():
        raise ValidationError(f'邮箱 {email} 已存在')
    
    # 创建用户
    user = User(
        email=email,
        name=name,
        is_active=is_active
    )
    user.full_clean()
    user.save()
    
    # 发送欢迎邮件
    send_welcome_email(user=user)
    
    return user


@transaction.atomic
def user_update(
    *,
    user: User,
    name: Optional[str] = None,
    is_active: Optional[bool] = None
) -> User:
    """更新用户信息"""
    if name is not None:
        user.name = name
    
    if is_active is not None:
        user.is_active = is_active
    
    user.full_clean()
    user.save()
    
    return user
```

### 服务类定义
```python
class UserService:
    """用户服务类 - 用于封装相关的业务逻辑"""
    
    def __init__(self, user: User):
        self.user = user
    
    @transaction.atomic
    def activate(self) -> User:
        """激活用户"""
        self.user.is_active = True
        self.user.full_clean()
        self.user.save()
        
        # 发送激活通知
        send_activation_email(user=self.user)
        
        return self.user
    
    @transaction.atomic
    def deactivate(self) -> User:
        """停用用户"""
        self.user.is_active = False
        self.user.full_clean()
        self.user.save()
        
        return self.user
```

### 服务层规范要点
1. **使用关键字参数**: 所有参数都应该是关键字参数 (*)
2. **事务装饰器**: 使用@transaction.atomic确保数据一致性
3. **调用full_clean()**: 保存前必须调用full_clean()进行验证
4. **类型注解**: 使用类型注解提高代码可读性
5. **文档字符串**: 提供清晰的函数说明
6. **异常处理**: 适当抛出ValidationError等异常

## 选择器(Selectors)规范

### 选择器函数定义
```python
from django.db.models import QuerySet
from typing import Optional, List


def user_get(*, user_id: int) -> Optional[User]:
    """根据ID获取用户"""
    try:
        return User.objects.get(id=user_id)
    except User.DoesNotExist:
        return None


def user_list(
    *,
    is_active: Optional[bool] = None,
    email_contains: Optional[str] = None
) -> QuerySet[User]:
    """获取用户列表"""
    queryset = User.objects.all()
    
    if is_active is not None:
        queryset = queryset.filter(is_active=is_active)
    
    if email_contains:
        queryset = queryset.filter(email__icontains=email_contains)
    
    return queryset.order_by('-created_at')


def user_get_active_count() -> int:
    """获取活跃用户数量"""
    return User.objects.filter(is_active=True).count()
```

### 选择器规范要点
1. **只负责查询**: 选择器只负责数据查询，不包含业务逻辑
2. **返回QuerySet或模型实例**: 根据需要返回适当的类型
3. **使用关键字参数**: 保持与服务层一致的参数风格
4. **优化查询**: 使用select_related、prefetch_related等优化查询

## API设计规范

### API视图结构
```python
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, serializers


class UserCreateApi(APIView):
    """用户创建API"""

    class InputSerializer(serializers.Serializer):
        """输入序列化器"""
        email = serializers.EmailField()
        name = serializers.CharField(max_length=255)
        is_active = serializers.BooleanField(default=True)

    class OutputSerializer(serializers.Serializer):
        """输出序列化器"""
        id = serializers.IntegerField()
        email = serializers.EmailField()
        name = serializers.CharField()
        is_active = serializers.BooleanField()
        created_at = serializers.DateTimeField()

    def post(self, request):
        """创建用户"""
        # 验证输入数据
        input_serializer = self.InputSerializer(data=request.data)
        input_serializer.is_valid(raise_exception=True)

        # 调用服务层
        user = user_create(**input_serializer.validated_data)

        # 序列化输出
        output_serializer = self.OutputSerializer(user)

        return Response(
            output_serializer.data,
            status=status.HTTP_201_CREATED
        )


class UserListApi(APIView):
    """用户列表API"""

    class FilterSerializer(serializers.Serializer):
        """过滤器序列化器"""
        is_active = serializers.BooleanField(required=False)
        email_contains = serializers.CharField(required=False)
        page = serializers.IntegerField(default=1, min_value=1)
        page_size = serializers.IntegerField(default=20, min_value=1, max_value=100)

    class OutputSerializer(serializers.Serializer):
        """输出序列化器"""
        id = serializers.IntegerField()
        email = serializers.EmailField()
        name = serializers.CharField()
        is_active = serializers.BooleanField()
        created_at = serializers.DateTimeField()

    def get(self, request):
        """获取用户列表"""
        # 验证过滤参数
        filter_serializer = self.FilterSerializer(data=request.query_params)
        filter_serializer.is_valid(raise_exception=True)

        filters = filter_serializer.validated_data
        page = filters.pop('page')
        page_size = filters.pop('page_size')

        # 获取数据
        users = user_list(**filters)

        # 分页处理
        start = (page - 1) * page_size
        end = start + page_size
        paginated_users = users[start:end]

        # 序列化输出
        output_serializer = self.OutputSerializer(paginated_users, many=True)

        return Response({
            'results': output_serializer.data,
            'count': users.count(),
            'page': page,
            'page_size': page_size
        })
```

### API规范要点
1. **分离输入输出序列化器**: 使用不同的序列化器处理输入和输出
2. **验证请求数据**: 使用序列化器验证所有输入数据
3. **调用服务层**: API层只负责数据验证和响应格式化
4. **统一响应格式**: 保持一致的API响应结构
5. **适当的HTTP状态码**: 使用正确的HTTP状态码

### 异常处理
```python
from django.core.exceptions import ValidationError as DjangoValidationError
from rest_framework.views import exception_handler
from rest_framework import exceptions
from rest_framework.serializers import as_serializer_error


def custom_exception_handler(exc, context):
    """自定义异常处理器"""
    # 处理Django ValidationError
    if isinstance(exc, DjangoValidationError):
        exc = exceptions.ValidationError(as_serializer_error(exc))

    # 调用默认异常处理器
    response = exception_handler(exc, context)

    if response is None:
        return response

    # 统一错误响应格式
    if isinstance(exc.detail, (list, dict)):
        response.data = {
            'error': True,
            'message': '请求数据验证失败',
            'details': response.data
        }
    else:
        response.data = {
            'error': True,
            'message': str(exc.detail),
            'details': {}
        }

    return response
```

## 测试规范

### 模型测试
```python
from django.test import TestCase
from django.core.exceptions import ValidationError
from django.utils import timezone
from datetime import timedelta

from apps.users.models import User


class UserModelTests(TestCase):
    """用户模型测试"""

    def test_user_creation(self):
        """测试用户创建"""
        user = User(
            email='<EMAIL>',
            name='Test User'
        )
        user.full_clean()  # 验证模型
        user.save()

        self.assertEqual(user.email, '<EMAIL>')
        self.assertEqual(user.name, 'Test User')
        self.assertTrue(user.is_active)

    def test_user_email_unique(self):
        """测试邮箱唯一性"""
        User.objects.create(
            email='<EMAIL>',
            name='User 1'
        )

        user2 = User(
            email='<EMAIL>',
            name='User 2'
        )

        with self.assertRaises(ValidationError):
            user2.full_clean()

    def test_display_name_property(self):
        """测试显示名称属性"""
        user = User(email='<EMAIL>', name='Test User')
        self.assertEqual(user.display_name, 'Test User')

        user_no_name = User(email='<EMAIL>', name='')
        self.assertEqual(user_no_name.display_name, '<EMAIL>')
```

### 服务层测试
```python
from unittest.mock import patch, Mock
from django.test import TestCase
from django.core.exceptions import ValidationError

from apps.users.services import user_create, user_update
from apps.users.models import User


class UserServiceTests(TestCase):
    """用户服务测试"""

    def test_user_create_success(self):
        """测试成功创建用户"""
        user = user_create(
            email='<EMAIL>',
            name='Test User'
        )

        self.assertEqual(user.email, '<EMAIL>')
        self.assertEqual(user.name, 'Test User')
        self.assertTrue(user.is_active)
        self.assertEqual(User.objects.count(), 1)

    def test_user_create_duplicate_email(self):
        """测试创建重复邮箱用户"""
        User.objects.create(
            email='<EMAIL>',
            name='Existing User'
        )

        with self.assertRaises(ValidationError):
            user_create(
                email='<EMAIL>',
                name='New User'
            )

    @patch('apps.users.services.send_welcome_email')
    def test_user_create_sends_welcome_email(self, mock_send_email):
        """测试创建用户时发送欢迎邮件"""
        user = user_create(
            email='<EMAIL>',
            name='Test User'
        )

        mock_send_email.assert_called_once_with(user=user)

    def test_user_update(self):
        """测试更新用户"""
        user = User.objects.create(
            email='<EMAIL>',
            name='Old Name'
        )

        updated_user = user_update(
            user=user,
            name='New Name',
            is_active=False
        )

        self.assertEqual(updated_user.name, 'New Name')
        self.assertFalse(updated_user.is_active)
```

### API测试
```python
from rest_framework.test import APITestCase
from rest_framework import status
from django.urls import reverse

from apps.users.models import User


class UserApiTests(APITestCase):
    """用户API测试"""

    def test_create_user_success(self):
        """测试成功创建用户"""
        url = reverse('users:create')
        data = {
            'email': '<EMAIL>',
            'name': 'Test User'
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(User.objects.count(), 1)

        user = User.objects.first()
        self.assertEqual(user.email, '<EMAIL>')
        self.assertEqual(user.name, 'Test User')

    def test_create_user_invalid_email(self):
        """测试创建用户时邮箱格式错误"""
        url = reverse('users:create')
        data = {
            'email': 'invalid-email',
            'name': 'Test User'
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(User.objects.count(), 0)

    def test_list_users(self):
        """测试获取用户列表"""
        # 创建测试数据
        User.objects.create(email='<EMAIL>', name='User 1')
        User.objects.create(email='<EMAIL>', name='User 2')

        url = reverse('users:list')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 2)
        self.assertEqual(response.data['count'], 2)
```

### 测试规范要点
1. **测试覆盖率**: 确保关键业务逻辑有足够的测试覆盖
2. **测试隔离**: 每个测试应该独立，不依赖其他测试
3. **使用Mock**: 对外部依赖使用Mock进行隔离测试
4. **测试命名**: 使用描述性的测试方法名
5. **测试数据**: 使用工厂模式或fixture创建测试数据

## 数据库规范

### 迁移文件管理
```python
# 创建迁移文件
python manage.py makemigrations

# 应用迁移
python manage.py migrate

# 查看迁移状态
python manage.py showmigrations

# 回滚迁移
python manage.py migrate app_name 0001
```

### 数据库查询优化
```python
# 使用select_related优化外键查询
users = User.objects.select_related('profile').all()

# 使用prefetch_related优化多对多和反向外键查询
users = User.objects.prefetch_related('orders').all()

# 使用only()限制查询字段
users = User.objects.only('id', 'email').all()

# 使用defer()排除不需要的字段
users = User.objects.defer('description').all()

# 使用exists()检查存在性
if User.objects.filter(email=email).exists():
    pass

# 使用count()获取数量
user_count = User.objects.filter(is_active=True).count()
```

### 索引优化
```python
class User(models.Model):
    email = models.EmailField(unique=True, db_index=True)
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)

    class Meta:
        indexes = [
            models.Index(fields=['email', 'is_active']),
            models.Index(fields=['-created_at']),
        ]
```

## 安全规范

### 输入验证
```python
from django.core.exceptions import ValidationError
from django.core.validators import validate_email


def validate_user_input(data):
    """验证用户输入"""
    # 邮箱验证
    try:
        validate_email(data.get('email', ''))
    except ValidationError:
        raise ValidationError('邮箱格式不正确')

    # 长度验证
    name = data.get('name', '')
    if len(name) > 255:
        raise ValidationError('姓名长度不能超过255个字符')

    # XSS防护 - 使用Django的escape
    from django.utils.html import escape
    safe_name = escape(name)

    return {
        'email': data['email'],
        'name': safe_name
    }
```

### 权限控制
```python
from django.contrib.auth.decorators import login_required, permission_required
from rest_framework.permissions import IsAuthenticated, IsAdminUser


class UserCreateApi(APIView):
    """需要管理员权限才能创建用户"""
    permission_classes = [IsAuthenticated, IsAdminUser]

    def post(self, request):
        # API逻辑
        pass


@login_required
@permission_required('users.add_user')
def create_user_view(request):
    """需要登录和特定权限的视图"""
    pass
```

### SQL注入防护
```python
# ✅ 正确 - 使用Django ORM
users = User.objects.filter(email=user_email)

# ✅ 正确 - 使用参数化查询
from django.db import connection
cursor = connection.cursor()
cursor.execute("SELECT * FROM users WHERE email = %s", [user_email])

# ❌ 错误 - 字符串拼接容易导致SQL注入
cursor.execute(f"SELECT * FROM users WHERE email = '{user_email}'")
```

### CSRF防护
```python
# 在settings.py中启用CSRF中间件
MIDDLEWARE = [
    'django.middleware.csrf.CsrfViewMiddleware',
    # ... 其他中间件
]

# 在模板中使用CSRF token
# {% csrf_token %}

# 在API中处理CSRF
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator

@method_decorator(csrf_exempt, name='dispatch')
class ApiView(APIView):
    pass
```

## 配置管理规范

### 环境配置分离
```python
# config/settings/base.py
import os
from pathlib import Path

BASE_DIR = Path(__file__).resolve().parent.parent.parent

# 从环境变量读取配置
SECRET_KEY = os.environ.get('SECRET_KEY')
DEBUG = os.environ.get('DEBUG', 'False').lower() == 'true'

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.environ.get('DB_NAME'),
        'USER': os.environ.get('DB_USER'),
        'PASSWORD': os.environ.get('DB_PASSWORD'),
        'HOST': os.environ.get('DB_HOST', 'localhost'),
        'PORT': os.environ.get('DB_PORT', '5432'),
    }
}

# config/settings/development.py
from .base import *

DEBUG = True
ALLOWED_HOSTS = ['localhost', '127.0.0.1']

# config/settings/production.py
from .base import *

DEBUG = False
ALLOWED_HOSTS = os.environ.get('ALLOWED_HOSTS', '').split(',')

# 生产环境安全设置
SECURE_SSL_REDIRECT = True
SECURE_HSTS_SECONDS = 31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
```

### 环境变量管理
```bash
# .env 文件示例
SECRET_KEY=your-secret-key-here
DEBUG=True
DB_NAME=your_database_name
DB_USER=your_database_user
DB_PASSWORD=your_database_password
DB_HOST=localhost
DB_PORT=5432
```

## 日志规范

### 日志配置
```python
# settings.py
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': 'logs/django.log',
            'formatter': 'verbose',
        },
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
    },
    'root': {
        'handlers': ['console', 'file'],
        'level': 'INFO',
    },
    'loggers': {
        'django': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': False,
        },
        'apps': {
            'handlers': ['console', 'file'],
            'level': 'DEBUG',
            'propagate': False,
        },
    },
}
```

### 日志使用
```python
import logging

logger = logging.getLogger(__name__)


def user_create(*, email: str, name: str) -> User:
    """创建用户服务"""
    logger.info(f'开始创建用户: {email}')

    try:
        user = User(email=email, name=name)
        user.full_clean()
        user.save()

        logger.info(f'用户创建成功: {user.id}')
        return user

    except ValidationError as e:
        logger.error(f'用户创建失败: {email}, 错误: {e}')
        raise
    except Exception as e:
        logger.exception(f'用户创建异常: {email}')
        raise
```

## 性能优化规范

### 缓存策略
```python
from django.core.cache import cache
from django.views.decorators.cache import cache_page
from django.utils.decorators import method_decorator


# 视图缓存
@cache_page(60 * 15)  # 缓存15分钟
def user_list_view(request):
    pass

# 模板片段缓存
# {% load cache %}
# {% cache 500 user_info user.id %}
#     {{ user.name }}
# {% endcache %}

# 手动缓存
def get_user_count():
    """获取用户数量（带缓存）"""
    cache_key = 'user_count'
    count = cache.get(cache_key)

    if count is None:
        count = User.objects.count()
        cache.set(cache_key, count, 300)  # 缓存5分钟

    return count

# 缓存失效
def user_create(*, email: str, name: str) -> User:
    user = User(email=email, name=name)
    user.save()

    # 清除相关缓存
    cache.delete('user_count')
    cache.delete_many(['user_list', 'active_users'])

    return user
```

### 数据库连接池
```python
# settings.py
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'your_db',
        'USER': 'your_user',
        'PASSWORD': 'your_password',
        'HOST': 'localhost',
        'PORT': '5432',
        'CONN_MAX_AGE': 600,  # 连接池配置
        'OPTIONS': {
            'MAX_CONNS': 20,
            'MIN_CONNS': 5,
        }
    }
}
```

## 部署规范

### Docker配置
```dockerfile
# Dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY requirements/ requirements/
RUN pip install -r requirements/production.txt

# 复制项目文件
COPY . .

# 收集静态文件
RUN python manage.py collectstatic --noinput

# 启动命令
CMD ["gunicorn", "--bind", "0.0.0.0:8000", "config.wsgi:application"]
```

### 环境变量配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  web:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DEBUG=False
      - SECRET_KEY=${SECRET_KEY}
      - DB_NAME=${DB_NAME}
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_HOST=db
    depends_on:
      - db
      - redis

  db:
    image: postgres:13
    environment:
      - POSTGRES_DB=${DB_NAME}
      - POSTGRES_USER=${DB_USER}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:6-alpine

volumes:
  postgres_data:
```

## 总结

本规范涵盖了Django项目开发的各个方面，从项目结构到部署配置。遵循这些规范可以：

1. **提高代码质量**: 统一的编码风格和架构模式
2. **增强可维护性**: 清晰的分层架构和职责分离
3. **提升开发效率**: 标准化的开发流程和最佳实践
4. **保证系统安全**: 完善的安全防护措施
5. **优化系统性能**: 有效的缓存和数据库优化策略

建议团队成员定期回顾和更新这些规范，确保与Django最新版本和最佳实践保持同步。
