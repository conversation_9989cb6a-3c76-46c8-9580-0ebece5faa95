/*
 * User Management Styles for Medical Device Reporting Platform
 * 医疗器械不良事件上报平台用户管理样式
 */

/* 用户管理容器 */
.user-management-container,
.user-form-container {
    padding: 1.5rem 0;
}

/* 页面标题 */
.page-header {
    margin-bottom: 2rem;
}

.page-title {
    font-size: 1.75rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.page-subtitle {
    font-size: 1rem;
    margin-bottom: 0;
}

/* 筛选面板 */
.filter-panel {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.75rem;
}

.filter-panel .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    border-radius: 0.75rem 0.75rem 0 0 !important;
}

.filter-panel .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

/* 用户列表面板 */
.user-list-panel {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.75rem;
}

.user-list-panel .card-header {
    background-color: #fff;
    border-bottom: 1px solid #dee2e6;
    border-radius: 0.75rem 0.75rem 0 0 !important;
}

/* 批量操作工具栏 */
.bulk-actions-toolbar {
    background-color: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
}

.selected-count {
    font-size: 0.875rem;
    color: #1976d2;
}

/* DataTables 样式定制 */
.dataTables_wrapper {
    font-size: 0.875rem;
}

.dataTables_filter {
    margin-bottom: 1rem;
}

.dataTables_filter input {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
    padding: 0.375rem 0.75rem;
}

.dataTables_length select {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
    padding: 0.375rem 0.75rem;
}

.dataTables_info {
    color: #6c757d;
    font-size: 0.875rem;
}

.dataTables_paginate .paginate_button {
    border-radius: 0.375rem !important;
    margin: 0 0.125rem;
}

.dataTables_paginate .paginate_button.current {
    background: #0d6efd !important;
    border-color: #0d6efd !important;
    color: white !important;
}

/* 表格样式 */
#userTable,
#departmentTable {
    border-radius: 0.5rem;
    overflow: hidden;
}

#userTable thead th,
#departmentTable thead th {
    background-color: #212529 !important;
    color: #ffffff !important;
    border: none !important;
    font-weight: 600;
    padding: 1rem 0.75rem;
    vertical-align: middle;
    text-align: center;
    font-size: 0.9rem;
    letter-spacing: 0.5px;
    box-shadow: inset 0 -1px 0 rgba(255, 255, 255, 0.1);
}

/* 确保Bootstrap的table-dark类不被覆盖 */
#departmentTable.table thead.table-dark th {
    background-color: #212529 !important;
    color: #ffffff !important;
    border-color: #32383e !important;
}

#userTable tbody td,
#departmentTable tbody td {
    padding: 0.75rem;
    vertical-align: middle;
    border-top: 1px solid #dee2e6;
}

#userTable tbody tr:hover,
#departmentTable tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* 状态徽章 */
.status-badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    font-weight: 500;
}

.status-active {
    background-color: #d1e7dd;
    color: #0f5132;
}

.status-inactive {
    background-color: #f8d7da;
    color: #842029;
}

/* 角色徽章 */
.role-badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    font-weight: 500;
}

.role-admin {
    background-color: #fff3cd;
    color: #664d03;
}

.role-staff {
    background-color: #d1ecf1;
    color: #055160;
}

/* 操作按钮 */
.action-buttons {
    display: flex;
    gap: 0.25rem;
}

.action-btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: 0.25rem;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-btn:hover {
    transform: translateY(-1px);
}

.btn-view {
    background-color: #0d6efd;
    color: white;
}

.btn-view:hover {
    background-color: #0b5ed7;
    color: white;
}

.btn-edit {
    background-color: #198754;
    color: white;
}

.btn-edit:hover {
    background-color: #157347;
    color: white;
}

.btn-toggle {
    background-color: #ffc107;
    color: #000;
}

.btn-toggle:hover {
    background-color: #ffca2c;
    color: #000;
}

/* 表单样式 */
.form-section-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
}

.form-label.required::after {
    content: " *";
    color: #dc3545;
}

.form-control:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-select:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-check-input:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-actions {
    background-color: #f8f9fa;
    margin: 0 -1.5rem -1.5rem;
    padding: 1rem 1.5rem;
    border-radius: 0 0 0.75rem 0.75rem;
}

/* 模态框样式 */
.modal-content {
    border-radius: 0.75rem;
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
    border-radius: 0.75rem 0.75rem 0 0;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
    border-radius: 0 0 0.75rem 0.75rem;
}

/* 用户详情样式 */
.user-detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f8f9fa;
}

.user-detail-item:last-child {
    border-bottom: none;
}

.user-detail-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0;
}

.user-detail-value {
    color: #6c757d;
    margin-bottom: 0;
}

/* 面包屑导航 */
.breadcrumb {
    background-color: transparent;
    padding: 0;
    margin-bottom: 1rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: ">";
    color: #6c757d;
}

.breadcrumb-item a {
    color: #0d6efd;
    text-decoration: none;
}

.breadcrumb-item a:hover {
    color: #0b5ed7;
    text-decoration: underline;
}

.breadcrumb-item.active {
    color: #6c757d;
}

/* 动画效果 */
.user-management-container,
.user-form-container {
    animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.filter-panel,
.user-list-panel {
    animation: slideInUp 0.6s ease-out;
    animation-fill-mode: both;
}

.filter-panel {
    animation-delay: 0.1s;
}

.user-list-panel {
    animation-delay: 0.2s;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .user-management-container,
    .user-form-container {
        padding: 1rem 0;
    }

    .page-title {
        font-size: 1.5rem;
    }

    .filter-panel .row.g-3 > .col-md-3 {
        margin-bottom: 1rem;
    }

    .bulk-actions-toolbar {
        text-align: center;
    }

    .bulk-actions-toolbar .row {
        flex-direction: column;
        gap: 0.5rem;
    }

    .action-buttons {
        flex-direction: column;
        gap: 0.125rem;
    }

    .dataTables_wrapper .row {
        flex-direction: column;
    }

    .dataTables_length,
    .dataTables_filter {
        text-align: center;
        margin-bottom: 1rem;
    }

    .dataTables_info,
    .dataTables_paginate {
        text-align: center;
        margin-top: 1rem;
    }
}

@media (max-width: 576px) {
    .page-header .row {
        flex-direction: column;
        gap: 1rem;
    }

    .page-header .col-auto {
        text-align: center;
    }

    .card {
        margin: 0 -0.5rem;
        border-radius: 0.5rem;
    }

    .form-actions {
        margin: 0 -1rem -1rem;
        padding: 1rem;
    }

    .form-actions .row {
        flex-direction: column;
        gap: 0.5rem;
    }

    .form-actions .col-auto {
        text-align: center;
    }
}

/* 加载状态 */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.btn-loading {
    opacity: 0.8;
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.75rem;
}

.tooltip-inner {
    background-color: #343a40;
    border-radius: 0.375rem;
}
