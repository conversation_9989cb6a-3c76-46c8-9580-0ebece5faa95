/*
 * Login Page Styles for Medical Device Reporting Platform
 * 医疗器械不良事件上报平台登录页面样式
 */

/* 登录容器 */
.login-container {
    min-height: calc(100vh - 200px);
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 2rem 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-attachment: fixed;
}

/* 登录卡片 */
.login-card {
    border: none;
    border-radius: 1rem;
    overflow: hidden;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.login-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

/* 登录头部 */
.login-header {
    padding: 1rem 0;
}

.login-icon {
    font-size: 3rem;
    margin-bottom: 0.5rem;
    display: block;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* 表单样式 */
.login-card .form-control {
    border-radius: 0.5rem;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.login-card .form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.15);
    transform: translateY(-2px);
}

.login-card .form-control.is-valid {
    border-color: #198754;
    background-image: none;
}

.login-card .form-control.is-invalid {
    border-color: #dc3545;
    background-image: none;
}

/* 输入组样式 */
.input-group-text {
    background-color: #f8f9fa;
    border: 2px solid #e9ecef;
    border-right: none;
    border-radius: 0.5rem 0 0 0.5rem;
    color: #6c757d;
    transition: all 0.3s ease;
}

.form-control:focus + .input-group-text,
.input-group-text:has(+ .form-control:focus) {
    border-color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.1);
    color: #0d6efd;
}

/* 登录按钮 */
.login-card .btn-primary {
    background: linear-gradient(45deg, #0d6efd, #6610f2);
    border: none;
    border-radius: 0.5rem;
    padding: 0.75rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.login-card .btn-primary:hover {
    background: linear-gradient(45deg, #0b5ed7, #5a0fc8);
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(13, 110, 253, 0.3);
}

.login-card .btn-primary:active {
    transform: translateY(0);
}

/* 按钮加载状态 */
.btn-loading {
    opacity: 0.8;
}

/* 表单验证反馈 */
.invalid-feedback {
    display: block;
    font-size: 0.875rem;
    color: #dc3545;
    margin-top: 0.25rem;
}

.valid-feedback {
    display: block;
    font-size: 0.875rem;
    color: #198754;
    margin-top: 0.25rem;
}

/* 帮助文本 */
.form-text {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.5rem;
}

/* 复选框样式 */
.form-check-input {
    border-radius: 0.25rem;
    border: 2px solid #dee2e6;
    transition: all 0.3s ease;
}

.form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.form-check-input:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* 功能特性展示 */
.feature-item {
    padding: 1rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.feature-item:hover {
    background-color: rgba(13, 110, 253, 0.05);
    transform: translateY(-2px);
}

.feature-item i {
    transition: transform 0.3s ease;
}

.feature-item:hover i {
    transform: scale(1.1);
}

/* 卡片底部 */
.card-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.125);
    background-color: rgba(248, 249, 250, 0.8) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .login-container {
        padding: 1rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    .login-card {
        margin: 0 0.5rem;
    }
    
    .login-icon {
        font-size: 2.5rem;
    }
    
    .login-card .form-control {
        font-size: 1rem;
    }
    
    .login-card .btn-primary {
        font-size: 1rem;
        padding: 0.75rem 1.5rem;
    }
}

@media (max-width: 576px) {
    .login-container {
        min-height: calc(100vh - 150px);
    }
    
    .login-header h4 {
        font-size: 1.25rem;
    }
    
    .login-header p {
        font-size: 0.9rem;
    }
}

/* 动画效果 */
.login-card {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 输入焦点动画 */
.form-control {
    position: relative;
}

.form-control:focus {
    animation: inputFocus 0.3s ease-out;
}

@keyframes inputFocus {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.02);
    }
    100% {
        transform: scale(1);
    }
}

/* 错误提示动画 */
.alert {
    animation: slideInDown 0.3s ease-out;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 加载状态 */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
