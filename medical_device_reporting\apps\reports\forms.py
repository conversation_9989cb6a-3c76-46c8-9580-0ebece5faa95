"""
不良事件上报管理表单
Adverse Event Reports Management Forms for Medical Device Reporting Platform
"""

from django import forms
from django.core.exceptions import ValidationError
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import date

from apps.common.utils import ValidationUtils
from apps.users.models import UserProfile, Department
from .models import AdverseEventReport


class ReporterInfoForm(forms.Form):
    """
    上报人信息表单
    """

    reporter = forms.ModelChoiceField(
        queryset=UserProfile.objects.filter(is_active=True, is_deleted=False),
        label='上报人',
        help_text='选择上报此事件的用户',
        widget=forms.Select(attrs={
            'class': 'form-select',
            'required': True
        })
    )

    department = forms.ModelChoiceField(
        queryset=Department.objects.filter(is_active=True, is_deleted=False),
        label='上报科室',
        help_text='上报人所属科室',
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )

    reporter_phone = forms.CharField(
        max_length=20,
        label='上报人联系电话',
        help_text='上报人的联系电话',
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '请输入手机号码',
            'required': True
        })
    )

    def clean_reporter_phone(self):
        """验证上报人联系电话"""
        phone = self.cleaned_data.get('reporter_phone')
        if phone and not ValidationUtils.validate_phone(phone):
            raise ValidationError('请输入正确的手机号码格式')
        return phone

    def clean(self):
        """整体验证"""
        cleaned_data = super().clean()
        reporter = cleaned_data.get('reporter')
        department = cleaned_data.get('department')

        if reporter and department:
            # 验证上报人和科室的关系
            if reporter.role == 'staff' and reporter.department != department:
                raise ValidationError('科室人员只能为本科室提交报告')
        elif reporter and not department:
            # 如果没有指定科室，使用上报人的科室
            if reporter.department:
                cleaned_data['department'] = reporter.department
            else:
                raise ValidationError('上报人未分配科室，请指定科室')

        return cleaned_data


class PatientInfoForm(forms.Form):
    """
    患者信息表单
    """

    patient_name = forms.CharField(
        max_length=100,
        label='患者姓名',
        help_text='发生不良事件的患者姓名',
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '请输入患者姓名',
            'required': True
        })
    )

    patient_age = forms.IntegerField(
        min_value=0,
        max_value=150,
        label='患者年龄',
        help_text='患者年龄（岁）',
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': '请输入年龄',
            'min': '0',
            'max': '150',
            'required': True
        })
    )

    patient_gender = forms.ChoiceField(
        choices=AdverseEventReport.GENDER_CHOICES,
        label='患者性别',
        help_text='患者的性别',
        widget=forms.Select(attrs={
            'class': 'form-select',
            'required': True
        })
    )

    patient_contact = forms.CharField(
        max_length=200,
        label='患者或其家属联系方式',
        help_text='患者或其家属的联系方式',
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '请输入联系方式（可选）'
        })
    )

    def clean_patient_name(self):
        """验证患者姓名"""
        name = self.cleaned_data.get('patient_name')
        if name:
            name = name.strip()
            if len(name) < 2:
                raise ValidationError('患者姓名至少需要2个字符')
        return name

    def clean_patient_age(self):
        """验证患者年龄"""
        age = self.cleaned_data.get('patient_age')
        if age is not None and (age < 0 or age > 150):
            raise ValidationError('患者年龄必须在0-150岁之间')
        return age


class EventInfoForm(forms.Form):
    """
    事件信息表单
    """

    device_malfunction = forms.CharField(
        label='器械故障表现',
        help_text='详细描述医疗器械的故障表现',
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 4,
            'placeholder': '请详细描述器械故障的具体表现...',
            'required': True
        })
    )

    event_date = forms.DateTimeField(
        label='事件发生日期时间',
        help_text='不良事件发生的具体日期和时间',
        widget=forms.DateTimeInput(attrs={
            'class': 'form-control',
            'type': 'datetime-local',
            'required': True
        })
    )

    injury_level = forms.ChoiceField(
        choices=AdverseEventReport.INJURY_LEVEL_CHOICES,
        label='伤害程度',
        help_text='选择不良事件造成的伤害程度',
        widget=forms.Select(attrs={
            'class': 'form-select',
            'required': True
        })
    )

    injury_description = forms.CharField(
        label='伤害表现',
        help_text='详细描述伤害的具体表现（死亡或严重伤害时必填）',
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 3,
            'placeholder': '请详细描述伤害的具体表现...'
        })
    )

    event_description = forms.CharField(
        label='事件陈述',
        help_text='详细描述不良事件的发生过程',
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 6,
            'placeholder': '请详细描述事件的发生过程、使用情况、发现过程等...',
            'required': True
        })
    )

    initial_cause_analysis = forms.CharField(
        label='初步原因分析',
        help_text='对事件发生原因的初步分析',
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 3,
            'placeholder': '请描述对事件原因的初步分析...'
        })
    )

    initial_treatment = forms.CharField(
        label='初步处理情况',
        help_text='事件发生后的初步处理措施',
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 3,
            'placeholder': '请描述事件发生后采取的处理措施...'
        })
    )

    def clean_event_date(self):
        """验证事件发生日期"""
        event_date = self.cleaned_data.get('event_date')
        if event_date and event_date > timezone.now():
            raise ValidationError('事件发生日期不能是未来时间')
        return event_date

    def clean_device_malfunction(self):
        """验证器械故障表现"""
        malfunction = self.cleaned_data.get('device_malfunction')
        if malfunction:
            malfunction = malfunction.strip()
            if len(malfunction) < 10:
                raise ValidationError('器械故障表现描述至少需要10个字符')
        return malfunction

    def clean_event_description(self):
        """验证事件陈述"""
        description = self.cleaned_data.get('event_description')
        if description:
            description = description.strip()
            if len(description) < 20:
                raise ValidationError('事件陈述至少需要20个字符')
        return description

    def clean(self):
        """整体验证"""
        cleaned_data = super().clean()
        injury_level = cleaned_data.get('injury_level')
        injury_description = cleaned_data.get('injury_description')

        # 如果伤害程度为死亡或严重伤害，必须填写伤害表现
        if injury_level in ['death', 'serious_injury'] and not injury_description:
            raise ValidationError('伤害程度为死亡或严重伤害时，必须填写伤害表现')

        return cleaned_data


class DeviceInfoForm(forms.Form):
    """
    医疗器械信息表单
    """

    device_name = forms.CharField(
        max_length=200,
        label='医疗器械名称',
        help_text='发生不良事件的医疗器械名称',
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '请输入医疗器械名称',
            'required': True
        })
    )

    registration_number = forms.CharField(
        max_length=100,
        label='注册证号',
        help_text='医疗器械注册证号',
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '请输入注册证号',
            'required': True
        })
    )

    manufacturer = forms.CharField(
        max_length=200,
        label='生产企业名称',
        help_text='医疗器械的生产企业名称',
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '请输入生产企业名称',
            'required': True
        })
    )

    specification = forms.CharField(
        max_length=100,
        label='规格',
        help_text='医疗器械的规格',
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '请输入规格（可选）'
        })
    )

    model = forms.CharField(
        max_length=100,
        label='型号',
        help_text='医疗器械的型号',
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '请输入型号（可选）'
        })
    )

    product_number = forms.CharField(
        max_length=100,
        label='产品编号',
        help_text='医疗器械的产品编号',
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '请输入产品编号（可选）'
        })
    )

    batch_number = forms.CharField(
        max_length=100,
        label='产品批号',
        help_text='医疗器械的产品批号',
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '请输入产品批号（可选）'
        })
    )

    production_date = forms.DateField(
        label='生产日期',
        help_text='医疗器械的生产日期',
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )

    expiry_date = forms.DateField(
        label='有效期至',
        help_text='医疗器械的有效期',
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )

    def clean_device_name(self):
        """验证医疗器械名称"""
        name = self.cleaned_data.get('device_name')
        if name:
            name = name.strip()
            if len(name) < 2:
                raise ValidationError('医疗器械名称至少需要2个字符')
        return name

    def clean_registration_number(self):
        """验证注册证号"""
        reg_number = self.cleaned_data.get('registration_number')
        if reg_number:
            reg_number = reg_number.strip()
            if len(reg_number) < 5:
                raise ValidationError('注册证号至少需要5个字符')
        return reg_number

    def clean_manufacturer(self):
        """验证生产企业名称"""
        manufacturer = self.cleaned_data.get('manufacturer')
        if manufacturer:
            manufacturer = manufacturer.strip()
            if len(manufacturer) < 2:
                raise ValidationError('生产企业名称至少需要2个字符')
        return manufacturer

    def clean(self):
        """整体验证"""
        cleaned_data = super().clean()
        production_date = cleaned_data.get('production_date')
        expiry_date = cleaned_data.get('expiry_date')

        # 验证日期逻辑
        if production_date and expiry_date:
            if production_date >= expiry_date:
                raise ValidationError('有效期必须晚于生产日期')

        return cleaned_data


class AdverseEventReportForm(forms.ModelForm):
    """
    完整的不良事件报告表单
    """

    class Meta:
        model = AdverseEventReport
        fields = [
            'reporter', 'department', 'reporter_phone',
            'patient_name', 'patient_age', 'patient_gender', 'patient_contact',
            'device_malfunction', 'event_date', 'injury_level', 'injury_description',
            'event_description', 'initial_cause_analysis', 'initial_treatment',
            'device_name', 'registration_number', 'manufacturer',
            'specification', 'model', 'product_number', 'batch_number',
            'production_date', 'expiry_date'
        ]

        widgets = {
            'reporter': forms.Select(attrs={'class': 'form-select'}),
            'department': forms.Select(attrs={'class': 'form-select'}),
            'reporter_phone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入联系电话'
            }),
            'patient_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入患者姓名'
            }),
            'patient_age': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0',
                'max': '150'
            }),
            'patient_gender': forms.Select(attrs={'class': 'form-select'}),
            'patient_contact': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入联系方式（可选）'
            }),
            'device_malfunction': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': '请详细描述器械故障表现...'
            }),
            'event_date': forms.DateTimeInput(attrs={
                'class': 'form-control',
                'type': 'datetime-local'
            }),
            'injury_level': forms.Select(attrs={'class': 'form-select'}),
            'injury_description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': '请详细描述伤害表现...'
            }),
            'event_description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 6,
                'placeholder': '请详细描述事件发生过程...'
            }),
            'initial_cause_analysis': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': '请描述初步原因分析...'
            }),
            'initial_treatment': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': '请描述初步处理情况...'
            }),
            'device_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入医疗器械名称'
            }),
            'registration_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入注册证号'
            }),
            'manufacturer': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入生产企业名称'
            }),
            'specification': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入规格（可选）'
            }),
            'model': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入型号（可选）'
            }),
            'product_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入产品编号（可选）'
            }),
            'batch_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入产品批号（可选）'
            }),
            'production_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'expiry_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
        }

        labels = {
            'reporter': '上报人',
            'department': '上报科室',
            'reporter_phone': '上报人联系电话',
            'patient_name': '患者姓名',
            'patient_age': '患者年龄',
            'patient_gender': '患者性别',
            'patient_contact': '患者或其家属联系方式',
            'device_malfunction': '器械故障表现',
            'event_date': '事件发生日期时间',
            'injury_level': '伤害程度',
            'injury_description': '伤害表现',
            'event_description': '事件陈述',
            'initial_cause_analysis': '初步原因分析',
            'initial_treatment': '初步处理情况',
            'device_name': '医疗器械名称',
            'registration_number': '注册证号',
            'manufacturer': '生产企业名称',
            'specification': '规格',
            'model': '型号',
            'product_number': '产品编号',
            'batch_number': '产品批号',
            'production_date': '生产日期',
            'expiry_date': '有效期至',
        }

    def __init__(self, *args, **kwargs):
        """初始化表单"""
        user_profile = kwargs.pop('user_profile', None)
        super().__init__(*args, **kwargs)

        # 根据用户权限过滤选项
        if user_profile:
            if user_profile.is_admin:
                # 管理员可以选择所有活跃用户和科室
                self.fields['reporter'].queryset = UserProfile.objects.filter(
                    is_active=True, is_deleted=False
                )
                self.fields['department'].queryset = Department.objects.filter(
                    is_active=True, is_deleted=False
                )
            else:
                # 科室人员只能选择自己和本科室
                self.fields['reporter'].queryset = UserProfile.objects.filter(
                    id=user_profile.id
                )
                if user_profile.department:
                    self.fields['department'].queryset = Department.objects.filter(
                        id=user_profile.department.id
                    )
                    # 设置默认值
                    self.fields['reporter'].initial = user_profile
                    self.fields['department'].initial = user_profile.department


class ReportSearchForm(forms.Form):
    """
    报告搜索表单
    """

    search = forms.CharField(
        max_length=200,
        label='搜索关键词',
        help_text='可搜索报告编号、患者姓名、器械名称、厂商等',
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '请输入搜索关键词...'
        })
    )

    status = forms.ChoiceField(
        choices=[('', '全部状态')] + AdverseEventReport.STATUS_CHOICES,
        label='报告状态',
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    department = forms.ModelChoiceField(
        queryset=Department.objects.filter(is_active=True, is_deleted=False),
        label='科室',
        required=False,
        empty_label='全部科室',
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    injury_level = forms.ChoiceField(
        choices=[('', '全部伤害程度')] + AdverseEventReport.INJURY_LEVEL_CHOICES,
        label='伤害程度',
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    start_date = forms.DateField(
        label='开始日期',
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )

    end_date = forms.DateField(
        label='结束日期',
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )

    ordering = forms.ChoiceField(
        choices=[
            ('-created_at', '创建时间（新到旧）'),
            ('created_at', '创建时间（旧到新）'),
            ('-event_date', '事件日期（新到旧）'),
            ('event_date', '事件日期（旧到新）'),
            ('report_number', '报告编号'),
            ('patient_name', '患者姓名'),
            ('device_name', '器械名称'),
        ],
        label='排序方式',
        initial='-created_at',
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    def clean(self):
        """整体验证"""
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        if start_date and end_date and start_date > end_date:
            raise ValidationError('开始日期不能晚于结束日期')

        return cleaned_data


class ReportReviewForm(forms.Form):
    """
    报告审核表单
    """

    action = forms.ChoiceField(
        choices=[
            ('start_review', '开始审核'),
            ('approve', '批准'),
            ('reject', '拒绝'),
        ],
        label='审核动作',
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    comments = forms.CharField(
        label='审核意见',
        help_text='审核意见（拒绝时必填）',
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 4,
            'placeholder': '请输入审核意见...'
        })
    )

    def clean(self):
        """整体验证"""
        cleaned_data = super().clean()
        action = cleaned_data.get('action')
        comments = cleaned_data.get('comments')

        if action == 'reject' and not comments:
            raise ValidationError('拒绝报告时必须填写审核意见')

        return cleaned_data


class ReportBulkActionForm(forms.Form):
    """
    报告批量操作表单
    """

    report_ids = forms.CharField(
        widget=forms.HiddenInput()
    )

    action = forms.ChoiceField(
        choices=[
            ('export', '导出报告'),
            ('bulk_review', '批量审核'),
        ],
        label='批量操作',
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    def clean_report_ids(self):
        """验证报告ID列表"""
        report_ids_str = self.cleaned_data.get('report_ids')
        if not report_ids_str:
            raise ValidationError('请选择要操作的报告')

        try:
            report_ids = [int(id_str.strip()) for id_str in report_ids_str.split(',') if id_str.strip()]
        except ValueError:
            raise ValidationError('报告ID格式不正确')

        if not report_ids:
            raise ValidationError('请选择要操作的报告')

        if len(report_ids) > 100:
            raise ValidationError('一次最多只能操作100个报告')

        # 验证报告是否存在
        existing_count = AdverseEventReport.objects.filter(
            id__in=report_ids,
            is_deleted=False
        ).count()

        if existing_count != len(report_ids):
            raise ValidationError('部分报告不存在或已删除')

        return report_ids
