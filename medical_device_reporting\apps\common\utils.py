"""
通用工具函数
Common utility functions for Medical Device Reporting Platform
"""

import re
import hashlib
import secrets
import string
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union
from decimal import Decimal, InvalidOperation

from django.core.exceptions import ValidationError
from django.core.validators import validate_email
from django.utils import timezone
from django.utils.text import slugify
from django.conf import settings


class ValidationUtils:
    """数据验证工具类"""
    
    @staticmethod
    def validate_phone_number(phone: str) -> bool:
        """
        验证手机号码格式
        
        Args:
            phone: 手机号码字符串
            
        Returns:
            bool: 验证结果
        """
        if not phone:
            return False
        
        # 中国大陆手机号码正则表达式
        pattern = r'^1[3-9]\d{9}$'
        return bool(re.match(pattern, phone.strip()))
    
    @staticmethod
    def validate_id_card(id_card: str) -> bool:
        """
        验证身份证号码格式
        
        Args:
            id_card: 身份证号码
            
        Returns:
            bool: 验证结果
        """
        if not id_card:
            return False
        
        id_card = id_card.strip().upper()
        
        # 18位身份证号码正则表达式
        pattern = r'^\d{17}[\dX]$'
        if not re.match(pattern, id_card):
            return False
        
        # 验证校验码
        weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
        check_codes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
        
        sum_value = sum(int(id_card[i]) * weights[i] for i in range(17))
        check_code = check_codes[sum_value % 11]
        
        return id_card[17] == check_code
    
    @staticmethod
    def validate_email_address(email: str) -> bool:
        """
        验证邮箱地址格式
        
        Args:
            email: 邮箱地址
            
        Returns:
            bool: 验证结果
        """
        if not email:
            return False
        
        try:
            validate_email(email.strip())
            return True
        except ValidationError:
            return False
    
    @staticmethod
    def validate_decimal(value: str, max_digits: int = 10, decimal_places: int = 2) -> bool:
        """
        验证小数格式

        Args:
            value: 待验证的值
            max_digits: 最大位数
            decimal_places: 小数位数

        Returns:
            bool: 验证结果
        """
        if not value:
            return False

        try:
            decimal_value = Decimal(str(value))

            # 检查总位数
            sign, digits, exponent = decimal_value.as_tuple()
            if len(digits) > max_digits:
                return False

            # 检查小数位数
            if exponent < -decimal_places:
                return False

            return True
        except (InvalidOperation, ValueError):
            return False

    @staticmethod
    def validate_account_number(account_number: str) -> bool:
        """
        验证4位数账号格式

        Args:
            account_number: 账号字符串

        Returns:
            bool: 验证结果
        """
        if not account_number:
            return False

        account_number = account_number.strip()

        # 检查是否为4位数字
        return bool(re.match(r'^\d{4}$', account_number))

    @staticmethod
    def validate_phone(phone: str) -> bool:
        """
        验证手机号码格式（别名方法，保持向后兼容）

        Args:
            phone: 手机号码字符串

        Returns:
            bool: 验证结果
        """
        return ValidationUtils.validate_phone_number(phone)

    @staticmethod
    def validate_email(email: str) -> bool:
        """
        验证邮箱地址格式（别名方法，保持向后兼容）

        Args:
            email: 邮箱地址

        Returns:
            bool: 验证结果
        """
        return ValidationUtils.validate_email_address(email)


class FormatUtils:
    """格式化工具类"""
    
    @staticmethod
    def format_phone_number(phone: str) -> str:
        """
        格式化手机号码
        
        Args:
            phone: 原始手机号码
            
        Returns:
            str: 格式化后的手机号码
        """
        if not phone:
            return ''
        
        phone = re.sub(r'\D', '', phone)  # 移除非数字字符
        
        if len(phone) == 11 and phone.startswith('1'):
            return f'{phone[:3]}-{phone[3:7]}-{phone[7:]}'
        
        return phone
    
    @staticmethod
    def mask_phone_number(phone: str) -> str:
        """
        手机号码脱敏
        
        Args:
            phone: 手机号码
            
        Returns:
            str: 脱敏后的手机号码
        """
        if not phone or len(phone) < 7:
            return phone
        
        return f'{phone[:3]}****{phone[-4:]}'
    
    @staticmethod
    def mask_id_card(id_card: str) -> str:
        """
        身份证号码脱敏
        
        Args:
            id_card: 身份证号码
            
        Returns:
            str: 脱敏后的身份证号码
        """
        if not id_card or len(id_card) < 8:
            return id_card
        
        return f'{id_card[:4]}**********{id_card[-4:]}'
    
    @staticmethod
    def format_file_size(size_bytes: int) -> str:
        """
        格式化文件大小
        
        Args:
            size_bytes: 文件大小（字节）
            
        Returns:
            str: 格式化后的文件大小
        """
        if size_bytes == 0:
            return '0 B'
        
        size_names = ['B', 'KB', 'MB', 'GB', 'TB']
        i = 0
        size = float(size_bytes)
        
        while size >= 1024.0 and i < len(size_names) - 1:
            size /= 1024.0
            i += 1
        
        return f'{size:.1f} {size_names[i]}'
    
    @staticmethod
    def format_duration(seconds: int) -> str:
        """
        格式化时间间隔
        
        Args:
            seconds: 秒数
            
        Returns:
            str: 格式化后的时间间隔
        """
        if seconds < 60:
            return f'{seconds}秒'
        elif seconds < 3600:
            minutes = seconds // 60
            return f'{minutes}分钟'
        elif seconds < 86400:
            hours = seconds // 3600
            minutes = (seconds % 3600) // 60
            return f'{hours}小时{minutes}分钟'
        else:
            days = seconds // 86400
            hours = (seconds % 86400) // 3600
            return f'{days}天{hours}小时'


class SecurityUtils:
    """安全工具类"""
    
    @staticmethod
    def generate_random_string(length: int = 32, include_symbols: bool = False) -> str:
        """
        生成随机字符串
        
        Args:
            length: 字符串长度
            include_symbols: 是否包含特殊符号
            
        Returns:
            str: 随机字符串
        """
        characters = string.ascii_letters + string.digits
        if include_symbols:
            characters += '!@#$%^&*'
        
        return ''.join(secrets.choice(characters) for _ in range(length))
    
    @staticmethod
    def hash_string(text: str, algorithm: str = 'sha256') -> str:
        """
        字符串哈希
        
        Args:
            text: 待哈希的字符串
            algorithm: 哈希算法
            
        Returns:
            str: 哈希值
        """
        if algorithm == 'md5':
            return hashlib.md5(text.encode()).hexdigest()
        elif algorithm == 'sha1':
            return hashlib.sha1(text.encode()).hexdigest()
        elif algorithm == 'sha256':
            return hashlib.sha256(text.encode()).hexdigest()
        else:
            raise ValueError(f'不支持的哈希算法: {algorithm}')
    
    @staticmethod
    def generate_token() -> str:
        """
        生成安全令牌
        
        Returns:
            str: 安全令牌
        """
        return secrets.token_urlsafe(32)


class DateTimeUtils:
    """日期时间工具类"""
    
    @staticmethod
    def get_current_timestamp() -> int:
        """
        获取当前时间戳
        
        Returns:
            int: 时间戳
        """
        return int(timezone.now().timestamp())
    
    @staticmethod
    def format_datetime(dt: datetime, format_str: str = '%Y-%m-%d %H:%M:%S') -> str:
        """
        格式化日期时间
        
        Args:
            dt: 日期时间对象
            format_str: 格式字符串
            
        Returns:
            str: 格式化后的日期时间
        """
        if not dt:
            return ''
        
        return dt.strftime(format_str)
    
    @staticmethod
    def parse_datetime(date_str: str, format_str: str = '%Y-%m-%d %H:%M:%S') -> Optional[datetime]:
        """
        解析日期时间字符串
        
        Args:
            date_str: 日期时间字符串
            format_str: 格式字符串
            
        Returns:
            datetime: 日期时间对象
        """
        try:
            return datetime.strptime(date_str, format_str)
        except ValueError:
            return None
    
    @staticmethod
    def get_date_range(start_date: datetime, end_date: datetime) -> List[datetime]:
        """
        获取日期范围内的所有日期
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            List[datetime]: 日期列表
        """
        dates = []
        current_date = start_date
        
        while current_date <= end_date:
            dates.append(current_date)
            current_date += timedelta(days=1)
        
        return dates


class SlugUtils:
    """URL友好字符串工具类"""
    
    @staticmethod
    def generate_slug(text: str, max_length: int = 50) -> str:
        """
        生成URL友好的字符串
        
        Args:
            text: 原始文本
            max_length: 最大长度
            
        Returns:
            str: URL友好的字符串
        """
        if not text:
            return ''
        
        # 使用Django的slugify函数
        slug = slugify(text, allow_unicode=True)
        
        # 限制长度
        if len(slug) > max_length:
            slug = slug[:max_length].rstrip('-')
        
        return slug
    
    @staticmethod
    def generate_unique_slug(text: str, model_class, field_name: str = 'slug', max_length: int = 50) -> str:
        """
        生成唯一的slug
        
        Args:
            text: 原始文本
            model_class: 模型类
            field_name: slug字段名
            max_length: 最大长度
            
        Returns:
            str: 唯一的slug
        """
        base_slug = SlugUtils.generate_slug(text, max_length - 10)  # 预留数字后缀空间
        slug = base_slug
        counter = 1
        
        while model_class.objects.filter(**{field_name: slug}).exists():
            slug = f'{base_slug}-{counter}'
            counter += 1
        
        return slug
