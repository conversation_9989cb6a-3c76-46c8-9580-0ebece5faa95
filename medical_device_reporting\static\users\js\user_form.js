/**
 * User Form JavaScript for Medical Device Reporting Platform
 * 医疗器械不良事件上报平台用户表单脚本
 */

document.addEventListener('DOMContentLoaded', function() {
    // 初始化用户表单
    initializeUserForm();
    
    /**
     * 初始化用户表单
     */
    function initializeUserForm() {
        // 绑定事件监听器
        bindEventListeners();
        
        // 初始化表单验证
        initializeValidation();
        
        // 设置表单联动
        setupFormInteractions();
        
        console.log('用户表单初始化完成');
    }
    
    /**
     * 绑定事件监听器
     */
    function bindEventListeners() {
        // 表单提交事件
        const userForm = document.getElementById('userForm');
        if (userForm) {
            userForm.addEventListener('submit', handleFormSubmit);
        }
        
        // 账号输入事件
        const accountNumberInput = document.getElementById('account_number');
        if (accountNumberInput) {
            accountNumberInput.addEventListener('input', handleAccountInput);
            accountNumberInput.addEventListener('blur', validateAccountNumber);
        }
        
        // 用户名输入事件
        const usernameInput = document.getElementById('username');
        if (usernameInput) {
            usernameInput.addEventListener('blur', validateUsername);
        }
        
        // 邮箱输入事件
        const emailInput = document.getElementById('email');
        if (emailInput) {
            emailInput.addEventListener('blur', validateEmail);
        }
        
        // 角色选择事件
        const roleSelect = document.getElementById('role');
        if (roleSelect) {
            roleSelect.addEventListener('change', handleRoleChange);
        }
    }
    
    /**
     * 处理表单提交
     */
    function handleFormSubmit(event) {
        event.preventDefault();
        
        // 验证表单
        if (!validateForm()) {
            return false;
        }
        
        // 显示加载状态
        showLoading();
        
        // 提交表单
        submitForm();
    }
    
    /**
     * 处理账号输入
     */
    function handleAccountInput(event) {
        const input = event.target;
        let value = input.value;
        
        // 只允许数字输入
        value = value.replace(/[^0-9]/g, '');
        
        // 限制长度为4位
        if (value.length > 4) {
            value = value.substring(0, 4);
        }
        
        input.value = value;
        
        // 实时验证
        if (value.length === 4) {
            validateAccountNumber();
        }
    }
    
    /**
     * 验证账号格式
     */
    function validateAccountNumber() {
        const input = document.getElementById('account_number');
        const value = input.value.trim();
        const isValid = /^[0-9]{4}$/.test(value);
        
        updateFieldValidation(input, isValid, '请输入4位数字账号');
        
        // 如果是新建用户且账号有效，检查账号是否已存在
        if (isValid && !isEditMode()) {
            checkAccountExists(value);
        }
        
        return isValid;
    }
    
    /**
     * 检查账号是否已存在
     */
    function checkAccountExists(accountNumber) {
        fetch(`/api/users/search/?account_number=${accountNumber}`)
            .then(response => {
                if (response.status === 404) {
                    // 账号不存在，可以使用
                    const input = document.getElementById('account_number');
                    updateFieldValidation(input, true, '账号可用');
                } else if (response.status === 200) {
                    // 账号已存在
                    const input = document.getElementById('account_number');
                    updateFieldValidation(input, false, '账号已存在');
                }
            })
            .catch(error => {
                console.error('检查账号失败:', error);
            });
    }
    
    /**
     * 验证用户名
     */
    function validateUsername() {
        const input = document.getElementById('username');
        const value = input.value.trim();
        const isValid = value.length >= 2;
        
        updateFieldValidation(input, isValid, '用户名至少2个字符');
        return isValid;
    }
    
    /**
     * 验证邮箱
     */
    function validateEmail() {
        const input = document.getElementById('email');
        const value = input.value.trim();
        
        if (!value) {
            // 邮箱是可选的
            updateFieldValidation(input, true);
            return true;
        }
        
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        const isValid = emailRegex.test(value);
        
        updateFieldValidation(input, isValid, '请输入有效的邮箱地址');
        return isValid;
    }
    
    /**
     * 处理角色变化
     */
    function handleRoleChange(event) {
        const role = event.target.value;
        const departmentSelect = document.getElementById('department_id');
        
        if (role === 'staff') {
            // 科室人员必须选择科室
            departmentSelect.required = true;
            departmentSelect.parentElement.querySelector('.form-text').innerHTML = 
                '<i class="bi bi-exclamation-triangle text-warning me-1"></i>科室人员必须选择所属科室';
        } else {
            // 管理员可以不选择科室
            departmentSelect.required = false;
            departmentSelect.parentElement.querySelector('.form-text').innerHTML = 
                '<i class="bi bi-info-circle me-1"></i>管理员可以不选择科室';
        }
    }
    
    /**
     * 验证表单
     */
    function validateForm() {
        let isValid = true;
        
        // 验证账号
        if (!validateAccountNumber()) {
            isValid = false;
        }
        
        // 验证用户名
        if (!validateUsername()) {
            isValid = false;
        }
        
        // 验证邮箱
        if (!validateEmail()) {
            isValid = false;
        }
        
        // 验证角色和科室的关联
        const role = document.getElementById('role').value;
        const departmentId = document.getElementById('department_id').value;
        
        if (role === 'staff' && !departmentId) {
            const departmentSelect = document.getElementById('department_id');
            updateFieldValidation(departmentSelect, false, '科室人员必须选择所属科室');
            isValid = false;
        }
        
        // 添加Bootstrap验证类
        const form = document.getElementById('userForm');
        form.classList.add('was-validated');
        
        return isValid;
    }
    
    /**
     * 更新字段验证状态
     */
    function updateFieldValidation(input, isValid, message = '') {
        if (isValid) {
            input.classList.remove('is-invalid');
            input.classList.add('is-valid');
            
            // 更新反馈消息
            const feedback = input.parentElement.querySelector('.invalid-feedback');
            if (feedback && message) {
                feedback.textContent = message;
                feedback.className = 'valid-feedback';
            }
        } else {
            input.classList.remove('is-valid');
            input.classList.add('is-invalid');
            
            // 更新反馈消息
            const feedback = input.parentElement.querySelector('.invalid-feedback, .valid-feedback');
            if (feedback && message) {
                feedback.textContent = message;
                feedback.className = 'invalid-feedback';
            }
        }
    }
    
    /**
     * 初始化表单验证
     */
    function initializeValidation() {
        // Bootstrap 5 表单验证
        const forms = document.querySelectorAll('.needs-validation');
        
        Array.from(forms).forEach(form => {
            form.addEventListener('submit', event => {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            });
        });
    }
    
    /**
     * 设置表单联动
     */
    function setupFormInteractions() {
        // 触发初始角色变化事件
        const roleSelect = document.getElementById('role');
        if (roleSelect) {
            handleRoleChange({ target: roleSelect });
        }
    }
    
    /**
     * 显示加载状态
     */
    function showLoading() {
        const submitBtn = document.getElementById('submitBtn');
        const btnText = submitBtn.querySelector('.btn-text');
        const btnLoading = submitBtn.querySelector('.btn-loading');
        
        if (btnText && btnLoading) {
            btnText.classList.add('d-none');
            btnLoading.classList.remove('d-none');
        }
        
        submitBtn.disabled = true;
    }
    
    /**
     * 隐藏加载状态
     */
    function hideLoading() {
        const submitBtn = document.getElementById('submitBtn');
        const btnText = submitBtn.querySelector('.btn-text');
        const btnLoading = submitBtn.querySelector('.btn-loading');
        
        if (btnText && btnLoading) {
            btnText.classList.remove('d-none');
            btnLoading.classList.add('d-none');
        }
        
        submitBtn.disabled = false;
    }
    
    /**
     * 提交表单
     */
    function submitForm() {
        const form = document.getElementById('userForm');
        const formData = new FormData(form);
        
        // 使用原生表单提交
        form.submit();
    }
    
    /**
     * 判断是否为编辑模式
     */
    function isEditMode() {
        return document.getElementById('account_number').hasAttribute('readonly');
    }
    
    /**
     * 显示错误消息
     */
    function showError(message) {
        const alertHtml = `
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        const cardBody = document.querySelector('.card-body');
        if (cardBody) {
            cardBody.insertAdjacentHTML('afterbegin', alertHtml);
        }
    }
});
