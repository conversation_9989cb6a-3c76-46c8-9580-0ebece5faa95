/**
 * 用户设置页面JavaScript
 * User Settings Page JavaScript
 */

$(document).ready(function() {
    // 初始化标签页功能
    initializeTabs();
    
    // 初始化偏好设置
    initializePreferences();
    
    // 初始化通知设置
    initializeNotifications();
    
    // 初始化修改密码功能
    initializeChangePassword();
    
    // 初始化事件监听器
    initializeEventListeners();
});

/**
 * 初始化标签页功能
 */
function initializeTabs() {
    // 记住最后访问的标签页
    const lastTab = localStorage.getItem('userSettingsLastTab');
    if (lastTab) {
        const tabButton = $(`button[data-bs-target="${lastTab}"]`);
        if (tabButton.length > 0) {
            const tab = new bootstrap.Tab(tabButton[0]);
            tab.show();
        }
    }
    
    // 监听标签页切换
    $('button[data-bs-toggle="tab"]').on('shown.bs.tab', function(e) {
        const target = $(e.target).attr('data-bs-target');
        localStorage.setItem('userSettingsLastTab', target);
    });
}

/**
 * 初始化偏好设置
 */
function initializePreferences() {
    const preferencesForm = $('#preferencesForm');
    
    if (!preferencesForm.length) return;
    
    // 加载保存的偏好设置
    loadPreferences();
    
    // 表单提交事件
    preferencesForm.on('submit', function(e) {
        e.preventDefault();
        savePreferences();
    });
    
    // 设置项变化时自动保存（防抖）
    let saveTimeout;
    preferencesForm.find('select, input').on('change', function() {
        clearTimeout(saveTimeout);
        saveTimeout = setTimeout(() => {
            autoSavePreferences();
        }, 1000);
    });
    
    // 主题变化时立即应用
    $('#theme').on('change', function() {
        applyTheme($(this).val());
    });
}

/**
 * 加载偏好设置
 */
function loadPreferences() {
    const preferences = getStoredPreferences();
    
    // 应用保存的设置
    Object.keys(preferences).forEach(key => {
        const element = $(`#${key}`);
        if (element.length > 0) {
            element.val(preferences[key]);
        }
    });
    
    // 应用主题
    applyTheme(preferences.theme || 'light');
}

/**
 * 保存偏好设置
 */
function savePreferences() {
    const formData = {
        theme: $('#theme').val(),
        language: $('#language').val(),
        timezone: $('#timezone').val(),
        page_size: $('#page_size').val()
    };
    
    // 显示加载状态
    const submitBtn = $('#preferencesForm button[type="submit"]');
    LoadingIndicator.show(submitBtn[0]);
    
    // 发送AJAX请求
    $.ajax({
        url: '/settings/',
        method: 'POST',
        headers: {
            'X-CSRFToken': getCsrfToken(),
            'Content-Type': 'application/json'
        },
        data: JSON.stringify({
            type: 'preferences',
            value: formData
        }),
        success: function(response) {
            if (response.success) {
                // 保存到本地存储
                storePreferences(formData);
                showSuccessMessage(response.message || '偏好设置已保存');
            } else {
                showErrorMessage(response.error || '保存设置失败');
            }
        },
        error: function(xhr, status, error) {
            console.error('保存偏好设置失败:', error);
            showErrorMessage('保存设置失败，请重试');
        },
        complete: function() {
            LoadingIndicator.hide(submitBtn[0]);
        }
    });
}

/**
 * 自动保存偏好设置
 */
function autoSavePreferences() {
    const formData = {
        theme: $('#theme').val(),
        language: $('#language').val(),
        timezone: $('#timezone').val(),
        page_size: $('#page_size').val()
    };
    
    // 静默保存到本地存储
    storePreferences(formData);
    
    // 发送AJAX请求（静默）
    $.ajax({
        url: '/settings/',
        method: 'POST',
        headers: {
            'X-CSRFToken': getCsrfToken(),
            'Content-Type': 'application/json'
        },
        data: JSON.stringify({
            type: 'preferences',
            value: formData
        }),
        success: function(response) {
            // 静默成功，不显示消息
            console.log('偏好设置自动保存成功');
        },
        error: function(xhr, status, error) {
            console.warn('偏好设置自动保存失败:', error);
        }
    });
}

/**
 * 应用主题
 */
function applyTheme(theme) {
    const body = $('body');
    
    // 移除现有主题类
    body.removeClass('theme-light theme-dark theme-auto');
    
    // 应用新主题
    if (theme === 'dark') {
        body.addClass('theme-dark');
        $('html').attr('data-bs-theme', 'dark');
    } else if (theme === 'auto') {
        body.addClass('theme-auto');
        // 根据系统偏好设置主题
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            $('html').attr('data-bs-theme', 'dark');
        } else {
            $('html').attr('data-bs-theme', 'light');
        }
    } else {
        body.addClass('theme-light');
        $('html').attr('data-bs-theme', 'light');
    }
}

/**
 * 初始化通知设置
 */
function initializeNotifications() {
    const notificationsForm = $('#notificationsForm');
    
    if (!notificationsForm.length) return;
    
    // 加载保存的通知设置
    loadNotificationSettings();
    
    // 表单提交事件
    notificationsForm.on('submit', function(e) {
        e.preventDefault();
        saveNotificationSettings();
    });
    
    // 开关变化时自动保存
    notificationsForm.find('input[type="checkbox"]').on('change', function() {
        autoSaveNotificationSettings();
    });
}

/**
 * 加载通知设置
 */
function loadNotificationSettings() {
    const settings = getStoredNotificationSettings();
    
    // 应用保存的设置
    Object.keys(settings).forEach(key => {
        const element = $(`#${key}`);
        if (element.length > 0 && element.is(':checkbox')) {
            element.prop('checked', settings[key]);
        }
    });
}

/**
 * 保存通知设置
 */
function saveNotificationSettings() {
    const formData = {};
    
    // 收集所有复选框状态
    $('#notificationsForm input[type="checkbox"]').each(function() {
        formData[this.id] = $(this).is(':checked');
    });
    
    // 显示加载状态
    const submitBtn = $('#notificationsForm button[type="submit"]');
    LoadingIndicator.show(submitBtn[0]);
    
    // 发送AJAX请求
    $.ajax({
        url: '/settings/',
        method: 'POST',
        headers: {
            'X-CSRFToken': getCsrfToken(),
            'Content-Type': 'application/json'
        },
        data: JSON.stringify({
            type: 'notifications',
            value: formData
        }),
        success: function(response) {
            if (response.success) {
                // 保存到本地存储
                storeNotificationSettings(formData);
                showSuccessMessage(response.message || '通知设置已保存');
            } else {
                showErrorMessage(response.error || '保存设置失败');
            }
        },
        error: function(xhr, status, error) {
            console.error('保存通知设置失败:', error);
            showErrorMessage('保存设置失败，请重试');
        },
        complete: function() {
            LoadingIndicator.hide(submitBtn[0]);
        }
    });
}

/**
 * 自动保存通知设置
 */
function autoSaveNotificationSettings() {
    const formData = {};
    
    // 收集所有复选框状态
    $('#notificationsForm input[type="checkbox"]').each(function() {
        formData[this.id] = $(this).is(':checked');
    });
    
    // 静默保存到本地存储
    storeNotificationSettings(formData);
    
    // 发送AJAX请求（静默）
    $.ajax({
        url: '/settings/',
        method: 'POST',
        headers: {
            'X-CSRFToken': getCsrfToken(),
            'Content-Type': 'application/json'
        },
        data: JSON.stringify({
            type: 'notifications',
            value: formData
        }),
        success: function(response) {
            console.log('通知设置自动保存成功');
        },
        error: function(xhr, status, error) {
            console.warn('通知设置自动保存失败:', error);
        }
    });
}

/**
 * 初始化修改密码功能
 */
function initializeChangePassword() {
    const changePasswordForm = $('#changePasswordForm');
    
    if (!changePasswordForm.length) return;
    
    // 表单提交事件
    changePasswordForm.on('submit', function(e) {
        e.preventDefault();
        handlePasswordChange();
    });
    
    // 密码确认验证
    $('#confirm_password').on('input', function() {
        validatePasswordConfirm();
    });
    
    // 新密码输入验证
    $('#new_password').on('input', function() {
        validateNewPassword();
        validatePasswordConfirm();
    });
    
    // 模态框显示时重置表单
    $('#changePasswordModal').on('show.bs.modal', function() {
        changePasswordForm[0].reset();
        changePasswordForm.removeClass('was-validated');
        $('.is-invalid').removeClass('is-invalid');
    });
}

/**
 * 处理密码修改
 */
function handlePasswordChange() {
    const form = $('#changePasswordForm')[0];
    
    if (!form.checkValidity()) {
        form.classList.add('was-validated');
        return;
    }
    
    if (!validatePasswordConfirm()) {
        return;
    }
    
    const formData = {
        current_password: $('#current_password').val(),
        new_password: $('#new_password').val(),
        confirm_password: $('#confirm_password').val()
    };
    
    const submitBtn = $('#changePasswordForm button[type="submit"]');
    LoadingIndicator.show(submitBtn[0]);
    
    $.ajax({
        url: '/change-password/',
        method: 'POST',
        headers: {
            'X-CSRFToken': getCsrfToken(),
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        data: formData,
        success: function(response) {
            if (response.success) {
                $('#changePasswordModal').modal('hide');
                showSuccessMessage(response.message || '密码修改成功，请重新登录');
                
                setTimeout(() => {
                    window.location.href = '/login/';
                }, 2000);
            } else {
                showErrorMessage(response.error || '密码修改失败');
            }
        },
        error: function(xhr, status, error) {
            console.error('密码修改失败:', error);
            
            if (xhr.responseJSON && xhr.responseJSON.error) {
                showErrorMessage(xhr.responseJSON.error);
            } else {
                showErrorMessage('密码修改失败，请重试');
            }
        },
        complete: function() {
            LoadingIndicator.hide(submitBtn[0]);
        }
    });
}

/**
 * 验证新密码
 */
function validateNewPassword() {
    const newPassword = $('#new_password');
    const value = newPassword.val();
    
    if (value.length > 0 && value.length < 6) {
        newPassword[0].setCustomValidity('密码长度至少6位');
        newPassword.addClass('is-invalid');
        return false;
    } else {
        newPassword[0].setCustomValidity('');
        newPassword.removeClass('is-invalid');
        return true;
    }
}

/**
 * 验证密码确认
 */
function validatePasswordConfirm() {
    const newPassword = $('#new_password').val();
    const confirmPassword = $('#confirm_password');
    const confirmValue = confirmPassword.val();
    
    if (confirmValue.length > 0 && confirmValue !== newPassword) {
        confirmPassword[0].setCustomValidity('两次输入的密码不一致');
        confirmPassword.addClass('is-invalid');
        return false;
    } else {
        confirmPassword[0].setCustomValidity('');
        confirmPassword.removeClass('is-invalid');
        return true;
    }
}

/**
 * 初始化事件监听器
 */
function initializeEventListeners() {
    // 设置卡片点击效果
    $('.settings-section').on('click', function() {
        $(this).addClass('shadow-lg').removeClass('shadow-sm');
        setTimeout(() => {
            $(this).removeClass('shadow-lg').addClass('shadow-sm');
        }, 200);
    });
}

// 本地存储工具函数
function getStoredPreferences() {
    const stored = localStorage.getItem('userPreferences');
    return stored ? JSON.parse(stored) : {};
}

function storePreferences(preferences) {
    localStorage.setItem('userPreferences', JSON.stringify(preferences));
}

function getStoredNotificationSettings() {
    const stored = localStorage.getItem('notificationSettings');
    return stored ? JSON.parse(stored) : {};
}

function storeNotificationSettings(settings) {
    localStorage.setItem('notificationSettings', JSON.stringify(settings));
}
