#!/usr/bin/env python
"""
简单API验证脚本
Simple API Validation Script
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

def test_imports():
    """测试导入"""
    
    print("=== API组件导入测试 ===\n")
    
    try:
        # 测试序列化器导入
        from apps.users.serializers import (
            UserSerializer, UserCreateSerializer, UserUpdateSerializer,
            DepartmentSerializer, DepartmentCreateSerializer
        )
        print("✅ 序列化器导入成功")
        
        # 测试API视图导入
        from apps.users.apis import (
            UserListApi, UserCreateApi, UserDetailApi,
            UserActivateApi, UserDeactivateApi,
            DepartmentListApi, DepartmentDetailApi
        )
        print("✅ API视图导入成功")
        
        # 测试序列化器实例化
        user_serializer = UserCreateSerializer(data={
            'account_number': '1111',
            'username': 'test_user',
            'role': 'staff'
        })
        print("✅ 序列化器实例化成功")
        
        # 测试数据验证
        if user_serializer.is_valid():
            print("✅ 数据验证通过")
        else:
            print(f"❌ 数据验证失败: {user_serializer.errors}")
        
        print("\n=== API组件测试完成 ===")
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_api_structure():
    """测试API结构"""
    
    print("\n=== API结构测试 ===\n")
    
    try:
        from apps.users.apis import UserListApi
        
        # 检查API类属性
        api_instance = UserListApi()
        
        if hasattr(api_instance, 'permission_classes'):
            print("✅ 权限类配置正确")
        else:
            print("❌ 缺少权限类配置")
        
        if hasattr(api_instance, 'get'):
            print("✅ GET方法存在")
        else:
            print("❌ 缺少GET方法")
        
        # 检查序列化器
        from apps.users.serializers import UserCreateSerializer
        
        serializer = UserCreateSerializer()
        if hasattr(serializer, 'validate_account_number'):
            print("✅ 账号验证方法存在")
        else:
            print("❌ 缺少账号验证方法")
        
        print("\n=== API结构测试完成 ===")
        return True
        
    except Exception as e:
        print(f"❌ API结构测试失败: {str(e)}")
        return False

def test_url_patterns():
    """测试URL模式"""
    
    print("\n=== URL模式测试 ===\n")
    
    try:
        from apps.users.urls import api_urlpatterns
        
        print(f"✅ API URL模式数量: {len(api_urlpatterns)}")
        
        # 检查关键URL
        url_names = [pattern.name for pattern in api_urlpatterns if hasattr(pattern, 'name')]
        
        required_urls = [
            'api_user_list',
            'api_user_create',
            'api_user_detail',
            'api_department_list'
        ]
        
        for url_name in required_urls:
            if url_name in url_names:
                print(f"✅ {url_name} URL存在")
            else:
                print(f"❌ {url_name} URL缺失")
        
        print("\n=== URL模式测试完成 ===")
        return True
        
    except Exception as e:
        print(f"❌ URL模式测试失败: {str(e)}")
        return False

if __name__ == '__main__':
    try:
        success1 = test_imports()
        success2 = test_api_structure()
        success3 = test_url_patterns()
        
        if success1 and success2 and success3:
            print("\n🎉 所有API组件测试通过！")
        else:
            print("\n❌ 部分测试失败")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n❌ 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
