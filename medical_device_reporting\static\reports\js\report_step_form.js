/**
 * Report Step Form JavaScript for Medical Device Reporting Platform
 * 医疗器械不良事件上报平台分步表单脚本
 */

document.addEventListener('DOMContentLoaded', function() {
    // 初始化分步表单
    initializeStepForm();
    
    /**
     * 初始化分步表单
     */
    function initializeStepForm() {
        // 绑定事件监听器
        bindEventListeners();
        
        // 初始化表单验证
        initializeValidation();
        
        // 设置表单联动
        setupFormInteractions();
        
        // 初始化进度动画
        initializeProgressAnimation();
        
        // 初始化键盘导航
        initializeKeyboardNavigation();
        
        console.log('分步表单初始化完成');
    }
    
    /**
     * 绑定事件监听器
     */
    function bindEventListeners() {
        // 表单提交事件
        const stepForm = document.getElementById('stepForm');
        if (stepForm) {
            stepForm.addEventListener('submit', handleStepSubmit);
        }
        
        // 患者年龄输入事件
        const patientAgeInput = document.getElementById('id_patient_age');
        if (patientAgeInput) {
            patientAgeInput.addEventListener('input', handleAgeInput);
            patientAgeInput.addEventListener('blur', validateAge);
        }
        
        // 联系电话输入事件
        const phoneInputs = document.querySelectorAll('#id_reporter_phone, #id_patient_contact');
        phoneInputs.forEach(input => {
            input.addEventListener('input', handlePhoneInput);
            input.addEventListener('blur', validatePhone);
        });
        
        // 事件日期输入事件
        const eventDateInput = document.getElementById('id_event_date');
        if (eventDateInput) {
            eventDateInput.addEventListener('change', validateEventDate);
        }
        
        // 伤害程度选择事件
        const injuryLevelSelect = document.getElementById('id_injury_level');
        if (injuryLevelSelect) {
            injuryLevelSelect.addEventListener('change', handleInjuryLevelChange);
        }
        
        // 器械故障复选框事件
        const deviceMalfunctionCheckbox = document.getElementById('id_device_malfunction');
        if (deviceMalfunctionCheckbox) {
            deviceMalfunctionCheckbox.addEventListener('change', handleDeviceMalfunctionChange);
        }
        
        // 生产日期和有效期联动
        const productionDateInput = document.getElementById('id_production_date');
        const expiryDateInput = document.getElementById('id_expiry_date');
        if (productionDateInput && expiryDateInput) {
            productionDateInput.addEventListener('change', function() {
                updateExpiryDateMin(this.value);
            });
        }
        
        // 步骤指示器点击事件
        const stepIndicators = document.querySelectorAll('.step-indicator');
        stepIndicators.forEach((indicator, index) => {
            if (indicator.classList.contains('completed')) {
                indicator.addEventListener('click', function() {
                    navigateToStep(index + 1);
                });
                indicator.style.cursor = 'pointer';
            }
        });
    }
    
    /**
     * 处理步骤提交
     */
    function handleStepSubmit(event) {
        event.preventDefault();
        
        // 验证当前步骤
        if (!validateCurrentStep()) {
            return false;
        }
        
        // 显示加载状态
        const submitButton = event.submitter;
        showLoading(submitButton);
        
        // 提交表单
        const form = event.target;
        form.submit();
    }
    
    /**
     * 验证当前步骤
     */
    function validateCurrentStep() {
        const form = document.getElementById('stepForm');
        const currentStepInputs = form.querySelectorAll('input:not([type="hidden"]), select, textarea');
        let isValid = true;
        
        currentStepInputs.forEach(input => {
            if (!validateField(input)) {
                isValid = false;
            }
        });
        
        // 添加Bootstrap验证类
        form.classList.add('was-validated');
        
        return isValid;
    }
    
    /**
     * 验证单个字段
     */
    function validateField(field) {
        const value = field.value.trim();
        const isRequired = field.hasAttribute('required');
        
        // 必填验证
        if (isRequired && !value) {
            updateFieldValidation(field, false, '此字段为必填项');
            return false;
        }
        
        // 特定字段验证
        switch (field.id) {
            case 'id_patient_age':
                return validateAge();
            case 'id_reporter_phone':
            case 'id_patient_contact':
                return validatePhone();
            case 'id_event_date':
                return validateEventDate();
            default:
                if (value || !isRequired) {
                    updateFieldValidation(field, true);
                    return true;
                }
        }
        
        return true;
    }
    
    /**
     * 处理年龄输入
     */
    function handleAgeInput(event) {
        const input = event.target;
        let value = input.value;
        
        // 只允许数字输入
        value = value.replace(/[^0-9]/g, '');
        
        // 限制最大值
        if (parseInt(value) > 150) {
            value = '150';
        }
        
        input.value = value;
    }
    
    /**
     * 处理电话输入
     */
    function handlePhoneInput(event) {
        const input = event.target;
        let value = input.value;
        
        // 只允许数字输入
        value = value.replace(/[^0-9]/g, '');
        
        // 限制长度为11位
        if (value.length > 11) {
            value = value.substring(0, 11);
        }
        
        input.value = value;
    }
    
    /**
     * 验证年龄
     */
    function validateAge() {
        const input = document.getElementById('id_patient_age');
        if (!input) return true;
        
        const value = parseInt(input.value);
        const isValid = !isNaN(value) && value >= 0 && value <= 150;
        
        updateFieldValidation(input, isValid, '请输入有效的年龄（0-150岁）');
        return isValid;
    }
    
    /**
     * 验证电话号码
     */
    function validatePhone() {
        const phoneInputs = document.querySelectorAll('#id_reporter_phone, #id_patient_contact');
        let allValid = true;
        
        phoneInputs.forEach(input => {
            const value = input.value.trim();
            
            if (!value) {
                // 电话号码是可选的
                updateFieldValidation(input, true);
                return;
            }
            
            const phoneRegex = /^1[3-9]\d{9}$/;
            const isValid = phoneRegex.test(value);
            
            updateFieldValidation(input, isValid, '请输入有效的手机号码');
            
            if (!isValid) {
                allValid = false;
            }
        });
        
        return allValid;
    }
    
    /**
     * 验证事件日期
     */
    function validateEventDate() {
        const input = document.getElementById('id_event_date');
        if (!input) return true;
        
        const value = input.value;
        
        if (!value) {
            updateFieldValidation(input, false, '请选择事件发生日期');
            return false;
        }
        
        const eventDate = new Date(value);
        const today = new Date();
        today.setHours(23, 59, 59, 999);
        
        const isValid = eventDate <= today;
        
        updateFieldValidation(input, isValid, '事件日期不能是未来日期');
        return isValid;
    }
    
    /**
     * 处理伤害程度变化
     */
    function handleInjuryLevelChange(event) {
        const level = event.target.value;
        const injuryDescInput = document.getElementById('id_injury_description');
        const injuryDescLabel = document.querySelector('label[for="id_injury_description"]');
        
        if (!injuryDescInput || !injuryDescLabel) return;
        
        if (level === 'severe' || level === 'death') {
            // 严重伤害或死亡需要填写描述
            injuryDescInput.required = true;
            injuryDescLabel.innerHTML = '伤害描述 <span class="text-danger">*</span>';
            
            if (level === 'death') {
                injuryDescInput.placeholder = '请详细描述死亡相关情况...';
            } else {
                injuryDescInput.placeholder = '请详细描述严重伤害情况...';
            }
        } else {
            // 其他情况描述为可选
            injuryDescInput.required = false;
            injuryDescLabel.textContent = '伤害描述';
            injuryDescInput.placeholder = '请描述伤害情况（可选）...';
        }
    }
    
    /**
     * 处理器械故障变化
     */
    function handleDeviceMalfunctionChange(event) {
        const isMalfunction = event.target.checked;
        const eventDescInput = document.getElementById('id_event_description');
        
        if (!eventDescInput) return;
        
        if (isMalfunction) {
            eventDescInput.placeholder = '请详细描述器械故障的具体情况和发生过程...';
        } else {
            eventDescInput.placeholder = '请详细描述不良事件的发生过程...';
        }
    }
    
    /**
     * 更新有效期最小值
     */
    function updateExpiryDateMin(productionDate) {
        const expiryDateInput = document.getElementById('id_expiry_date');
        if (expiryDateInput) {
            if (productionDate) {
                expiryDateInput.min = productionDate;
            } else {
                expiryDateInput.removeAttribute('min');
            }
        }
    }
    
    /**
     * 更新字段验证状态
     */
    function updateFieldValidation(input, isValid, message = '') {
        if (isValid) {
            input.classList.remove('is-invalid');
            input.classList.add('is-valid');
        } else {
            input.classList.remove('is-valid');
            input.classList.add('is-invalid');
            
            // 更新错误消息
            let feedback = input.parentElement.querySelector('.invalid-feedback');
            if (!feedback) {
                feedback = document.createElement('div');
                feedback.className = 'invalid-feedback';
                input.parentElement.appendChild(feedback);
            }
            feedback.textContent = message;
        }
    }
    
    /**
     * 初始化表单验证
     */
    function initializeValidation() {
        // 实时验证
        const inputs = document.querySelectorAll('#stepForm input, #stepForm select, #stepForm textarea');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                validateField(this);
            });
        });
    }
    
    /**
     * 设置表单联动
     */
    function setupFormInteractions() {
        // 设置日期字段的最大值为今天
        const today = new Date().toISOString().split('T')[0];
        const eventDateInput = document.getElementById('id_event_date');
        if (eventDateInput) {
            eventDateInput.max = today;
        }

        // 增强日期输入框的用户体验
        enhanceDateInputs();

        // 触发初始的伤害程度变化事件
        const injuryLevelSelect = document.getElementById('id_injury_level');
        if (injuryLevelSelect && injuryLevelSelect.value) {
            handleInjuryLevelChange({ target: injuryLevelSelect });
        }

        // 触发初始的器械故障变化事件
        const deviceMalfunctionCheckbox = document.getElementById('id_device_malfunction');
        if (deviceMalfunctionCheckbox) {
            handleDeviceMalfunctionChange({ target: deviceMalfunctionCheckbox });
        }
    }

    /**
     * 增强日期输入框的用户体验
     */
    function enhanceDateInputs() {
        console.log('分步表单：开始增强日期输入框');

        // 如果全局的日期增强函数存在，直接调用
        if (typeof initializeDateInputEnhancements === 'function') {
            initializeDateInputEnhancements();
            return;
        }

        // 否则使用本地实现
        const dateInputs = document.querySelectorAll('input[type="date"], input[type="datetime-local"], input[id*="date"], input[name*="date"]');
        console.log('分步表单：找到日期输入框数量:', dateInputs.length);

        dateInputs.forEach((input, index) => {
            console.log(`分步表单：处理日期输入框 ${index + 1}:`, input.id, input.name, input.type);

            // 确保输入框类型正确
            if (input.type !== 'date' && input.type !== 'datetime-local') {
                if (input.name && (input.name.includes('date') || input.id.includes('date'))) {
                    if (input.name.includes('event_date')) {
                        input.type = 'datetime-local';
                    } else {
                        input.type = 'date';
                    }
                    console.log(`分步表单：已将输入框类型设置为: ${input.type}`);
                }
            }

            // 添加点击事件，让整个输入框都可以点击打开日期选择器
            input.addEventListener('click', function(e) {
                console.log('分步表单：日期输入框被点击:', this.id, this.name);
                e.preventDefault();

                this.focus();

                setTimeout(() => {
                    if (this.showPicker && typeof this.showPicker === 'function') {
                        try {
                            console.log('分步表单：尝试调用 showPicker()');
                            this.showPicker();
                        } catch (e) {
                            console.log('分步表单：showPicker() 调用失败:', e);
                        }
                    }
                }, 50);
            });

            // 添加键盘事件支持
            input.addEventListener('keydown', function(e) {
                if (e.key === ' ' || e.key === 'Enter') {
                    e.preventDefault();
                    console.log('分步表单：键盘触发日期选择器:', e.key);

                    if (this.showPicker && typeof this.showPicker === 'function') {
                        try {
                            this.showPicker();
                        } catch (e) {
                            console.debug('分步表单：键盘触发showPicker失败:', e);
                        }
                    }
                }
            });

            // 改善视觉反馈
            input.style.cursor = 'pointer';
            input.style.transition = 'all 0.2s ease';

            // 添加hover效果
            input.addEventListener('mouseenter', function() {
                if (!this.disabled && !this.readOnly) {
                    this.style.backgroundColor = '#f8f9fa';
                    this.style.borderColor = '#86b7fe';
                }
            });

            input.addEventListener('mouseleave', function() {
                if (!this.disabled && !this.readOnly) {
                    this.style.backgroundColor = '';
                    this.style.borderColor = '';
                }
            });

            console.log(`分步表单：日期输入框 ${index + 1} 增强完成`);
        });
    }
    
    /**
     * 初始化进度动画
     */
    function initializeProgressAnimation() {
        const progressBar = document.querySelector('.progress-bar');
        const stepIndicators = document.querySelectorAll('.step-indicator');
        
        // 动画显示进度条
        if (progressBar) {
            const targetWidth = progressBar.style.width;
            progressBar.style.width = '0%';
            
            setTimeout(() => {
                progressBar.style.transition = 'width 1s ease-in-out';
                progressBar.style.width = targetWidth;
            }, 500);
        }
        
        // 动画显示步骤指示器
        stepIndicators.forEach((indicator, index) => {
            indicator.style.opacity = '0';
            indicator.style.transform = 'scale(0.8)';
            
            setTimeout(() => {
                indicator.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                indicator.style.opacity = '1';
                indicator.style.transform = 'scale(1)';
            }, 700 + index * 100);
        });
    }
    
    /**
     * 初始化键盘导航
     */
    function initializeKeyboardNavigation() {
        document.addEventListener('keydown', function(event) {
            // Ctrl + 左箭头：上一步
            if (event.ctrlKey && event.key === 'ArrowLeft') {
                event.preventDefault();
                const prevButton = document.querySelector('a[href*="step"]');
                if (prevButton) {
                    window.location.href = prevButton.href;
                }
            }
            
            // Ctrl + 右箭头：下一步
            if (event.ctrlKey && event.key === 'ArrowRight') {
                event.preventDefault();
                const nextButton = document.querySelector('button[type="submit"]');
                if (nextButton) {
                    nextButton.click();
                }
            }
        });
    }
    
    /**
     * 导航到指定步骤
     */
    function navigateToStep(step) {
        const currentUrl = new URL(window.location);
        const pathParts = currentUrl.pathname.split('/');
        
        // 替换步骤号
        for (let i = 0; i < pathParts.length; i++) {
            if (pathParts[i] === 'step' && i + 1 < pathParts.length) {
                pathParts[i + 1] = step.toString();
                break;
            }
        }
        
        const newPath = pathParts.join('/');
        window.location.href = newPath;
    }
    
    /**
     * 显示加载状态
     */
    function showLoading(button) {
        if (button) {
            button.disabled = true;
            const originalText = button.innerHTML;
            button.setAttribute('data-original-text', originalText);
            button.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status"></span>处理中...';
        }
    }
});
