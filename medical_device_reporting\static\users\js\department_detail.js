/**
 * 科室详情页面JavaScript
 * Department Detail Page JavaScript
 */

$(document).ready(function() {
    // 初始化用户列表DataTables
    initializeDepartmentUsersTable();
    
    // 初始化事件监听器
    initializeEventListeners();
    
    // 初始化用户详情模态框
    initializeUserDetailModal();
});

/**
 * 初始化科室用户列表DataTables
 */
function initializeDepartmentUsersTable() {
    if (!$('#departmentUsersTable').length) return;
    
    if ($.fn.DataTable.isDataTable('#departmentUsersTable')) {
        $('#departmentUsersTable').DataTable().destroy();
    }
    
    $('#departmentUsersTable').DataTable({
        processing: true,
        serverSide: false, // 使用客户端处理，因为数据量不大
        ajax: {
            url: window.location.pathname + 'users/',
            type: 'GET',
            error: function(xhr, error, thrown) {
                console.error('DataTables AJAX错误:', error);
                showErrorMessage('加载用户数据失败，请刷新页面重试');
            }
        },
        columns: [
            {
                data: 'account_number',
                title: '账号',
                render: function(data, type, row) {
                    return `<strong class="text-primary">${data}</strong>`;
                }
            },
            {
                data: 'full_name',
                title: '姓名',
                render: function(data, type, row) {
                    return data || row.username || '未设置';
                }
            },
            {
                data: 'email',
                title: '邮箱',
                render: function(data, type, row) {
                    if (data) {
                        return `<a href="mailto:${data}" class="text-decoration-none">${data}</a>`;
                    }
                    return '<span class="text-muted">未设置</span>';
                }
            },
            {
                data: 'role',
                title: '角色',
                render: function(data, type, row) {
                    const roleMap = {
                        'admin': '<span class="badge bg-warning">管理员</span>',
                        'staff': '<span class="badge bg-info">科室人员</span>'
                    };
                    return roleMap[data] || `<span class="badge bg-secondary">${data}</span>`;
                }
            },
            {
                data: 'is_active',
                title: '状态',
                render: function(data, type, row) {
                    if (data && row.user_is_active) {
                        return '<span class="badge bg-success"><i class="bi bi-check-circle me-1"></i>正常</span>';
                    }
                    return '<span class="badge bg-secondary"><i class="bi bi-x-circle me-1"></i>禁用</span>';
                }
            },
            {
                data: 'last_login',
                title: '最后登录',
                render: function(data, type, row) {
                    if (data) {
                        return `<small class="text-muted">${formatDate(new Date(data), 'MM-DD HH:mm')}</small>`;
                    }
                    return '<small class="text-muted">从未登录</small>';
                }
            },
            {
                data: null,
                title: '操作',
                orderable: false,
                render: function(data, type, row) {
                    const editUrl = `/users/${row.id}/edit/`;
                    
                    return `
                        <div class="btn-group btn-group-sm" role="group">
                            <a href="${editUrl}" class="btn btn-outline-primary" title="编辑用户">
                                <i class="bi bi-pencil"></i>
                            </a>
                            <button type="button" class="btn btn-outline-info user-detail-btn" 
                                    data-user-id="${row.id}" title="查看详情">
                                <i class="bi bi-eye"></i>
                            </button>
                        </div>
                    `;
                }
            }
        ],
        order: [[0, 'asc']],
        pageLength: 10,
        lengthMenu: [[5, 10, 25, 50], [5, 10, 25, 50]],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/zh.json'
        },
        responsive: true,
        dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>rtip',
        drawCallback: function(settings) {
            // 重新初始化工具提示
            initializeTooltips();
        }
    });
}

/**
 * 初始化事件监听器
 */
function initializeEventListeners() {
    // 刷新用户列表按钮
    $('#refreshUsersBtn').on('click', function() {
        $('#departmentUsersTable').DataTable().ajax.reload();
        showInfoMessage('用户列表已刷新');
    });
    
    // 用户详情按钮
    $(document).on('click', '.user-detail-btn', handleUserDetail);
    
    // 编辑用户按钮（从模态框中）
    $('#editUserFromModalBtn').on('click', function() {
        const userId = $(this).data('user-id');
        if (userId) {
            window.location.href = `/users/${userId}/edit/`;
        }
    });
    
    // 统计卡片点击效果
    $('.stat-card').on('click', function() {
        $(this).addClass('shadow-lg').removeClass('shadow-sm');
        setTimeout(() => {
            $(this).removeClass('shadow-lg').addClass('shadow-sm');
        }, 200);
    });
}

/**
 * 初始化用户详情模态框
 */
function initializeUserDetailModal() {
    // 模态框显示时清空内容
    $('#userDetailModal').on('show.bs.modal', function() {
        $('#userDetailContent').html(`
            <div class="text-center py-3">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
            </div>
        `);
    });
    
    // 模态框隐藏时清理数据
    $('#userDetailModal').on('hidden.bs.modal', function() {
        $('#editUserFromModalBtn').removeData('user-id');
    });
}

/**
 * 处理用户详情查看
 */
function handleUserDetail() {
    const userId = $(this).data('user-id');
    
    // 显示模态框
    $('#userDetailModal').modal('show');
    
    // 设置编辑按钮的用户ID
    $('#editUserFromModalBtn').data('user-id', userId);
    
    // 加载用户详情
    $.ajax({
        url: `/api/users/${userId}/`,
        method: 'GET',
        success: function(response) {
            renderUserDetail(response);
        },
        error: function(xhr, status, error) {
            console.error('加载用户详情失败:', error);
            $('#userDetailContent').html(`
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    加载用户详情失败，请重试
                </div>
            `);
        }
    });
}

/**
 * 渲染用户详情
 */
function renderUserDetail(user) {
    const statusBadge = user.is_active && user.user.is_active 
        ? '<span class="badge bg-success">正常</span>'
        : '<span class="badge bg-secondary">禁用</span>';
    
    const roleBadge = user.role === 'admin' 
        ? '<span class="badge bg-warning">管理员</span>'
        : '<span class="badge bg-info">科室人员</span>';
    
    const lastLogin = user.user.last_login 
        ? formatDate(new Date(user.user.last_login), 'YYYY-MM-DD HH:mm:ss')
        : '从未登录';
    
    const html = `
        <div class="row">
            <div class="col-md-6">
                <h6 class="mb-3">基本信息</h6>
                <dl class="row">
                    <dt class="col-sm-4">账号：</dt>
                    <dd class="col-sm-8"><span class="badge bg-primary">${user.account_number}</span></dd>
                    
                    <dt class="col-sm-4">用户名：</dt>
                    <dd class="col-sm-8">${user.user.username}</dd>
                    
                    <dt class="col-sm-4">姓名：</dt>
                    <dd class="col-sm-8">${user.user.first_name || user.user.last_name ? 
                        (user.user.first_name + ' ' + user.user.last_name).trim() : '未设置'}</dd>
                    
                    <dt class="col-sm-4">邮箱：</dt>
                    <dd class="col-sm-8">${user.user.email || '<span class="text-muted">未设置</span>'}</dd>
                </dl>
            </div>
            
            <div class="col-md-6">
                <h6 class="mb-3">账户信息</h6>
                <dl class="row">
                    <dt class="col-sm-4">角色：</dt>
                    <dd class="col-sm-8">${roleBadge}</dd>
                    
                    <dt class="col-sm-4">状态：</dt>
                    <dd class="col-sm-8">${statusBadge}</dd>
                    
                    <dt class="col-sm-4">注册时间：</dt>
                    <dd class="col-sm-8">
                        <small class="text-muted">${formatDate(new Date(user.user.date_joined), 'YYYY-MM-DD HH:mm')}</small>
                    </dd>
                    
                    <dt class="col-sm-4">最后登录：</dt>
                    <dd class="col-sm-8">
                        <small class="text-muted">${lastLogin}</small>
                    </dd>
                </dl>
            </div>
        </div>
        
        ${user.created_by || user.updated_by ? `
        <hr>
        <div class="row">
            <div class="col-12">
                <h6 class="mb-3">创建信息</h6>
                <div class="row">
                    ${user.created_by ? `
                    <div class="col-md-6">
                        <small class="text-muted">
                            创建人：${user.created_by.first_name || user.created_by.last_name ? 
                                (user.created_by.first_name + ' ' + user.created_by.last_name).trim() : 
                                user.created_by.username}<br>
                            创建时间：${formatDate(new Date(user.created_at), 'YYYY-MM-DD HH:mm')}
                        </small>
                    </div>
                    ` : ''}
                    ${user.updated_by ? `
                    <div class="col-md-6">
                        <small class="text-muted">
                            更新人：${user.updated_by.first_name || user.updated_by.last_name ? 
                                (user.updated_by.first_name + ' ' + user.updated_by.last_name).trim() : 
                                user.updated_by.username}<br>
                            更新时间：${formatDate(new Date(user.updated_at), 'YYYY-MM-DD HH:mm')}
                        </small>
                    </div>
                    ` : ''}
                </div>
            </div>
        </div>
        ` : ''}
    `;
    
    $('#userDetailContent').html(html);
}
