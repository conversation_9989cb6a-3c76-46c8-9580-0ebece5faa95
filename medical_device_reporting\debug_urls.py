#!/usr/bin/env python
"""
URL调试脚本
URL Debug Script
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

def debug_urls():
    """调试URL配置"""
    print("=== URL配置调试 ===")
    
    try:
        from django.urls import get_resolver
        from django.conf import settings
        
        # 获取根URL解析器
        resolver = get_resolver()
        
        print(f"ROOT_URLCONF: {settings.ROOT_URLCONF}")
        print("\n=== 主URL模式 ===")
        
        def print_url_patterns(patterns, prefix=""):
            for pattern in patterns:
                if hasattr(pattern, 'url_patterns'):
                    # 这是一个URLResolver（include）
                    print(f"{prefix}📁 {pattern.pattern} -> {pattern.urlconf_name}")
                    if hasattr(pattern, 'namespace') and pattern.namespace:
                        print(f"{prefix}   namespace: {pattern.namespace}")
                    
                    # 递归打印子模式
                    try:
                        sub_patterns = pattern.url_patterns
                        print_url_patterns(sub_patterns, prefix + "  ")
                    except Exception as e:
                        print(f"{prefix}  ❌ 无法加载子模式: {e}")
                else:
                    # 这是一个URLPattern
                    view_name = getattr(pattern.callback, '__name__', str(pattern.callback))
                    print(f"{prefix}📄 {pattern.pattern} -> {view_name}")
                    if hasattr(pattern, 'name') and pattern.name:
                        print(f"{prefix}   name: {pattern.name}")
        
        print_url_patterns(resolver.url_patterns)
        
        # 特别检查reports应用的URL
        print("\n=== Reports应用URL详情 ===")
        try:
            from apps.reports.urls import urlpatterns as reports_patterns
            print(f"Reports应用URL模式数量: {len(reports_patterns)}")
            
            for i, pattern in enumerate(reports_patterns):
                if hasattr(pattern, 'callback'):
                    view_name = getattr(pattern.callback, '__name__', str(pattern.callback))
                    print(f"{i+1}. {pattern.pattern} -> {view_name}")
                    if hasattr(pattern, 'name') and pattern.name:
                        print(f"   name: {pattern.name}")
                else:
                    print(f"{i+1}. {pattern.pattern} -> URLResolver")
        except Exception as e:
            print(f"❌ 无法加载reports URL: {e}")
        
        # 检查导出API是否存在
        print("\n=== 导出API检查 ===")
        try:
            from django.urls import reverse
            export_url = reverse('reports:api_report_export')
            print(f"✅ 导出API URL反向解析成功: {export_url}")
        except Exception as e:
            print(f"❌ 导出API URL反向解析失败: {e}")
        
        # 检查API视图是否可导入
        print("\n=== API视图检查 ===")
        try:
            from apps.reports.apis import ReportExportAPIView
            print(f"✅ ReportExportAPIView导入成功: {ReportExportAPIView}")
        except Exception as e:
            print(f"❌ ReportExportAPIView导入失败: {e}")
        
        # 检查URL模式匹配
        print("\n=== URL模式匹配测试 ===")
        test_urls = [
            '/reports/api/export/',
            '/reports/api/export/?format=excel',
            '/reports/api/reports/export/',
        ]
        
        for test_url in test_urls:
            try:
                match = resolver.resolve(test_url)
                print(f"✅ {test_url} -> {match.func} (args: {match.args}, kwargs: {match.kwargs})")
            except Exception as e:
                print(f"❌ {test_url} -> {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ URL调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 开始URL配置调试...")
    
    success = debug_urls()
    
    if success:
        print("\n🎉 URL调试完成！")
    else:
        print("\n❌ URL调试失败")
    
    return success

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
