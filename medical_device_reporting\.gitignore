# 医疗器械不良事件上报平台 Git 忽略文件
# Medical Device Reporting Platform .gitignore

# =============================================================================
# 环境变量和敏感信息
# =============================================================================

# 环境变量文件
.env
.env.local
.env.production
.env.staging

# 密钥文件
*.key
*.pem
*.p12
*.pfx

# =============================================================================
# Python 相关
# =============================================================================

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# =============================================================================
# Django 相关
# =============================================================================

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Static files (collected by Django)
/staticfiles/
/static_collected/

# Media files
/media/
!/media/.gitkeep

# Django migrations (可选，根据团队策略决定)
# */migrations/*.py
# !*/migrations/__init__.py

# =============================================================================
# 数据库相关
# =============================================================================

# MySQL
*.sql
*.dump

# PostgreSQL
*.backup

# SQLite
*.db
*.sqlite
*.sqlite3

# =============================================================================
# 日志文件
# =============================================================================

# 应用日志
logs/
*.log
*.log.*

# =============================================================================
# IDE 和编辑器
# =============================================================================

# PyCharm
.idea/

# Visual Studio Code
.vscode/
*.code-workspace

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# =============================================================================
# 操作系统相关
# =============================================================================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.tmp
*.temp
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# 部署和运维相关
# =============================================================================

# Docker
.dockerignore
Dockerfile.prod

# Kubernetes
*.yaml.local
*.yml.local

# Terraform
*.tfstate
*.tfstate.*
.terraform/

# =============================================================================
# 临时文件和缓存
# =============================================================================

# 临时文件
*.tmp
*.temp
*.bak
*.backup

# 缓存文件
.cache/
cache/

# =============================================================================
# 项目特定
# =============================================================================

# 测试数据
test_data/
fixtures/test_*.json

# 文档生成
docs/build/

# 配置文件备份
*.conf.bak
*.ini.bak

# 本地开发脚本
local_*.py
dev_*.py
