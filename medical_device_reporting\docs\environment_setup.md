# 环境变量配置指南

## 概述

医疗器械不良事件上报平台使用环境变量来管理配置，确保敏感信息的安全性和不同环境的配置分离。

## 文件说明

### .env.example
- **用途**: 环境变量模板文件
- **内容**: 包含所有必要的环境变量示例，不包含真实敏感信息
- **版本控制**: 应该提交到Git仓库
- **使用方法**: 复制为.env文件并填入实际值

### .env
- **用途**: 实际的环境变量配置文件
- **内容**: 包含真实的配置值，包括密码、密钥等敏感信息
- **版本控制**: 不应提交到Git仓库（已在.gitignore中忽略）
- **安全性**: 仅在本地开发环境使用

## 环境配置步骤

### 1. 复制模板文件
```bash
cp .env.example .env
```

### 2. 编辑配置文件
根据实际环境修改.env文件中的配置值：

#### Django核心配置
- `SECRET_KEY`: Django密钥，生产环境必须使用强密钥
- `DEBUG`: 调试模式，生产环境必须设置为False
- `ALLOWED_HOSTS`: 允许的主机列表

#### 数据库配置
- `DB_NAME`: 数据库名称
- `DB_USER`: 数据库用户名
- `DB_PASSWORD`: 数据库密码
- `DB_HOST`: 数据库主机
- `DB_PORT`: 数据库端口

#### 邮件配置
- `EMAIL_HOST`: SMTP服务器地址
- `EMAIL_PORT`: SMTP端口
- `EMAIL_USE_TLS`: 是否使用TLS
- `EMAIL_HOST_USER`: 邮箱用户名
- `EMAIL_HOST_PASSWORD`: 邮箱密码

## 环境特定配置

### 开发环境
```bash
# 基本配置
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0

# 数据库
DB_NAME=medical_device_reporting_dev
DB_PASSWORD=temper0201..

# 安全
SECURE_SSL_REDIRECT=False

# 日志
LOG_LEVEL=DEBUG
```

### 生产环境
```bash
# 基本配置
DEBUG=False
ALLOWED_HOSTS=your-domain.com,www.your-domain.com

# 数据库
DB_NAME=medical_device_reporting_prod
DB_PASSWORD=strong-production-password

# 安全
SECURE_SSL_REDIRECT=True

# 日志
LOG_LEVEL=INFO

# 错误跟踪
SENTRY_DSN=https://<EMAIL>/project-id
```

## 安全最佳实践

### 1. 密钥管理
- 使用强随机密钥
- 定期轮换密钥
- 不要在代码中硬编码密钥

### 2. 密码安全
- 使用复杂密码
- 不要重复使用密码
- 考虑使用密码管理器

### 3. 环境分离
- 开发、测试、生产环境使用不同的配置
- 不要在开发环境使用生产数据
- 限制生产环境的访问权限

### 4. 版本控制
- 永远不要提交.env文件到Git
- 定期检查是否意外提交了敏感信息
- 使用.gitignore确保敏感文件被忽略

## 故障排除

### 常见问题

#### 1. 环境变量未生效
- 检查.env文件是否存在
- 确认变量名拼写正确
- 重启Django开发服务器

#### 2. 数据库连接失败
- 检查数据库配置是否正确
- 确认MySQL服务是否运行
- 验证用户名和密码

#### 3. 静态文件加载失败
- 检查STATIC_URL和MEDIA_URL配置
- 运行collectstatic命令
- 确认文件权限正确

### 调试技巧

#### 1. 查看当前环境变量
```python
# 在Django shell中
from django.conf import settings
print(settings.DEBUG)
print(settings.DATABASES)
```

#### 2. 验证环境变量加载
```python
import environ
env = environ.Env()
print(env('DEBUG'))
print(env('DB_NAME'))
```

## 部署注意事项

### 1. 生产环境部署
- 使用环境变量或配置管理工具
- 不要在服务器上存储.env文件
- 使用安全的密钥管理服务

### 2. 容器化部署
- 使用Docker secrets或环境变量
- 不要在镜像中包含敏感信息
- 使用多阶段构建

### 3. 云平台部署
- 使用云平台的配置管理服务
- 启用加密存储
- 设置适当的访问控制

## 相关文档

- [Django Settings Documentation](https://docs.djangoproject.com/en/4.2/topics/settings/)
- [django-environ Documentation](https://django-environ.readthedocs.io/)
- [12-Factor App Configuration](https://12factor.net/config)
