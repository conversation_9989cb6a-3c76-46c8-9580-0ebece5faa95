"""
科室管理视图测试
Department Management Views Tests
"""

from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from django.core.files.uploadedfile import SimpleUploadedFile
import json

from apps.users.models import UserProfile, Department


class DepartmentViewTest(TestCase):
    """科室视图测试"""
    
    def setUp(self):
        """测试前准备"""
        self.client = Client()
        
        # 创建管理员用户
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>'
        )
        self.admin_profile = UserProfile.objects.create(
            user=self.admin_user,
            account_number='0001',
            role='admin',
            is_active=True
        )
        
        # 创建科室人员
        self.staff_user = User.objects.create_user(
            username='staff',
            email='<EMAIL>'
        )
        self.staff_profile = UserProfile.objects.create(
            user=self.staff_user,
            account_number='1001',
            role='staff',
            is_active=True
        )
        
        # 创建测试科室
        self.department = Department.objects.create(
            code='TEST',
            name='测试科室',
            is_active=True,
            created_by=self.admin_user
        )
        
        # 将科室人员分配到科室
        self.staff_profile.department = self.department
        self.staff_profile.save()
    
    def test_department_list_view_admin_access(self):
        """测试管理员访问科室列表"""
        self.client.force_login(self.admin_user)
        
        response = self.client.get(reverse('users:department_list'))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '科室管理')
        self.assertContains(response, self.department.name)
        self.assertTemplateUsed(response, 'users/department_list.html')
    
    def test_department_list_view_staff_denied(self):
        """测试科室人员访问科室列表被拒绝"""
        self.client.force_login(self.staff_user)
        
        response = self.client.get(reverse('users:department_list'))
        
        # 应该被重定向或返回403
        self.assertIn(response.status_code, [302, 403])
    
    def test_department_create_view_get(self):
        """测试科室创建页面GET请求"""
        self.client.force_login(self.admin_user)
        
        response = self.client.get(reverse('users:department_create'))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '新建科室')
        self.assertContains(response, 'name="code"')
        self.assertContains(response, 'name="name"')
        self.assertTemplateUsed(response, 'users/department_form.html')
    
    def test_department_create_view_post_success(self):
        """测试科室创建POST请求成功"""
        self.client.force_login(self.admin_user)
        
        data = {
            'code': 'ICU',
            'name': '重症监护科',
            'is_active': True
        }
        
        response = self.client.post(reverse('users:department_create'), data)
        
        # 应该重定向到科室列表
        self.assertEqual(response.status_code, 302)
        
        # 验证科室被创建
        self.assertTrue(Department.objects.filter(code='ICU').exists())
        created_dept = Department.objects.get(code='ICU')
        self.assertEqual(created_dept.name, '重症监护科')
        self.assertEqual(created_dept.created_by, self.admin_user)
    
    def test_department_create_view_post_duplicate_code(self):
        """测试科室创建POST请求重复代码"""
        self.client.force_login(self.admin_user)
        
        data = {
            'code': 'TEST',  # 已存在的代码
            'name': '另一个测试科室',
            'is_active': True
        }
        
        response = self.client.post(reverse('users:department_create'), data)
        
        # 应该返回表单页面并显示错误
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '科室代码已存在')
    
    def test_department_edit_view_get(self):
        """测试科室编辑页面GET请求"""
        self.client.force_login(self.admin_user)
        
        response = self.client.get(reverse('users:department_edit', args=[self.department.id]))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '编辑科室')
        self.assertContains(response, self.department.code)
        self.assertContains(response, self.department.name)
        self.assertTemplateUsed(response, 'users/department_form.html')
    
    def test_department_edit_view_post_success(self):
        """测试科室编辑POST请求成功"""
        self.client.force_login(self.admin_user)
        
        data = {
            'code': 'TEST',  # 代码不能修改
            'name': '更新的测试科室',
            'is_active': False
        }
        
        response = self.client.post(reverse('users:department_edit', args=[self.department.id]), data)
        
        # 应该重定向到科室列表
        self.assertEqual(response.status_code, 302)
        
        # 验证科室被更新
        self.department.refresh_from_db()
        self.assertEqual(self.department.name, '更新的测试科室')
        self.assertFalse(self.department.is_active)
    
    def test_department_detail_view(self):
        """测试科室详情页面"""
        self.client.force_login(self.admin_user)
        
        response = self.client.get(reverse('users:department_detail', args=[self.department.id]))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.department.name)
        self.assertContains(response, self.department.code)
        self.assertContains(response, self.staff_user.username)  # 应该显示科室用户
        self.assertTemplateUsed(response, 'users/department_detail.html')
    
    def test_department_delete_view(self):
        """测试科室删除"""
        self.client.force_login(self.admin_user)
        
        # 创建一个没有用户的科室用于删除测试
        empty_dept = Department.objects.create(
            code='EMPTY',
            name='空科室',
            created_by=self.admin_user
        )
        
        response = self.client.post(reverse('users:department_delete', args=[empty_dept.id]))
        
        # 应该重定向到科室列表
        self.assertEqual(response.status_code, 302)
        
        # 验证科室被删除
        self.assertFalse(Department.objects.filter(id=empty_dept.id).exists())
    
    def test_department_delete_view_with_users(self):
        """测试删除有用户的科室"""
        self.client.force_login(self.admin_user)
        
        response = self.client.post(reverse('users:department_delete', args=[self.department.id]))
        
        # 应该返回错误，不能删除有用户的科室
        self.assertEqual(response.status_code, 400)
        
        # 验证科室未被删除
        self.assertTrue(Department.objects.filter(id=self.department.id).exists())


class DepartmentImportViewTest(TestCase):
    """科室导入视图测试"""
    
    def setUp(self):
        """测试前准备"""
        self.client = Client()
        
        # 创建管理员用户
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>'
        )
        self.admin_profile = UserProfile.objects.create(
            user=self.admin_user,
            account_number='0001',
            role='admin',
            is_active=True
        )
        
        # 创建科室人员
        self.staff_user = User.objects.create_user(
            username='staff',
            email='<EMAIL>'
        )
        self.staff_profile = UserProfile.objects.create(
            user=self.staff_user,
            account_number='1001',
            role='staff',
            is_active=True
        )
    
    def test_department_import_view_get(self):
        """测试科室导入页面GET请求"""
        self.client.force_login(self.admin_user)
        
        response = self.client.get(reverse('users:department_import'))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '导入科室Excel')
        self.assertContains(response, '选择Excel文件')
        self.assertTemplateUsed(response, 'users/department_import.html')
    
    def test_department_import_view_staff_denied(self):
        """测试科室人员访问导入页面被拒绝"""
        self.client.force_login(self.staff_user)
        
        response = self.client.get(reverse('users:department_import'))
        
        # 应该被重定向或返回403
        self.assertIn(response.status_code, [302, 403])
    
    def test_department_import_view_post_no_file(self):
        """测试科室导入POST请求无文件"""
        self.client.force_login(self.admin_user)
        
        response = self.client.post(reverse('users:department_import'), {})
        
        self.assertEqual(response.status_code, 400)
        self.assertContains(response, '请选择要导入的Excel文件')
    
    def test_department_import_view_post_invalid_file(self):
        """测试科室导入POST请求无效文件"""
        self.client.force_login(self.admin_user)
        
        # 创建一个非Excel文件
        invalid_file = SimpleUploadedFile(
            "test.txt",
            b"invalid content",
            content_type="text/plain"
        )
        
        response = self.client.post(reverse('users:department_import'), {
            'excel_file': invalid_file
        })
        
        self.assertEqual(response.status_code, 400)
        self.assertContains(response, '请选择Excel文件')


class DepartmentPermissionTest(TestCase):
    """科室管理权限测试"""
    
    def setUp(self):
        """测试前准备"""
        self.client = Client()
        
        # 创建管理员用户
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>'
        )
        self.admin_profile = UserProfile.objects.create(
            user=self.admin_user,
            account_number='0001',
            role='admin',
            is_active=True
        )
        
        # 创建科室人员
        self.staff_user = User.objects.create_user(
            username='staff',
            email='<EMAIL>'
        )
        self.staff_profile = UserProfile.objects.create(
            user=self.staff_user,
            account_number='1001',
            role='staff',
            is_active=True
        )
        
        # 创建禁用用户
        self.inactive_user = User.objects.create_user(
            username='inactive',
            email='<EMAIL>'
        )
        self.inactive_profile = UserProfile.objects.create(
            user=self.inactive_user,
            account_number='1002',
            role='staff',
            is_active=False
        )
    
    def test_unauthenticated_access_redirects(self):
        """测试未认证用户访问科室管理页面重定向"""
        protected_urls = [
            reverse('users:department_list'),
            reverse('users:department_create'),
            reverse('users:department_import'),
        ]
        
        for url in protected_urls:
            response = self.client.get(url)
            self.assertEqual(response.status_code, 302)
            self.assertTrue(response.url.startswith('/login/'))
    
    def test_inactive_user_access_denied(self):
        """测试禁用用户访问被拒绝"""
        self.client.force_login(self.inactive_user)
        
        response = self.client.get(reverse('users:department_list'))
        
        # 应该被拒绝访问
        self.assertIn(response.status_code, [302, 403])
    
    def test_staff_access_restrictions(self):
        """测试科室人员访问限制"""
        self.client.force_login(self.staff_user)
        
        # 科室人员不能访问的页面
        restricted_urls = [
            reverse('users:department_list'),
            reverse('users:department_create'),
            reverse('users:department_import'),
        ]
        
        for url in restricted_urls:
            response = self.client.get(url)
            self.assertIn(response.status_code, [302, 403])
    
    def test_admin_full_access(self):
        """测试管理员完全访问权限"""
        self.client.force_login(self.admin_user)
        
        # 管理员可以访问的页面
        accessible_urls = [
            reverse('users:department_list'),
            reverse('users:department_create'),
            reverse('users:department_import'),
        ]
        
        for url in accessible_urls:
            response = self.client.get(url)
            self.assertEqual(response.status_code, 200)
