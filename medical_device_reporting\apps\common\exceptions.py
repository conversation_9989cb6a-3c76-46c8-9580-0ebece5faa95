"""
自定义异常类
Custom exceptions for Medical Device Reporting Platform
"""

from django.core.exceptions import ValidationError, PermissionDenied
from django.http import Http404
from rest_framework import status
from rest_framework.views import exception_handler
from rest_framework.response import Response
import logging

logger = logging.getLogger('apps')


class BaseCustomException(Exception):
    """
    自定义异常基类
    
    提供统一的异常处理接口
    """
    
    default_message = '发生了未知错误'
    default_code = 'unknown_error'
    default_status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    
    def __init__(self, message=None, code=None, status_code=None, details=None):
        self.message = message or self.default_message
        self.code = code or self.default_code
        self.status_code = status_code or self.default_status_code
        self.details = details or {}
        
        super().__init__(self.message)
    
    def to_dict(self):
        """
        转换为字典格式
        
        Returns:
            dict: 异常信息字典
        """
        result = {
            'error': True,
            'code': self.code,
            'message': self.message,
        }
        
        if self.details:
            result['details'] = self.details
        
        return result


class BusinessLogicError(BaseCustomException):
    """
    业务逻辑错误
    
    用于处理业务规则违反的情况
    """
    
    default_message = '业务逻辑错误'
    default_code = 'business_logic_error'
    default_status_code = status.HTTP_400_BAD_REQUEST


class DataValidationError(BaseCustomException):
    """
    数据验证错误
    
    用于处理数据格式或内容验证失败的情况
    """
    
    default_message = '数据验证失败'
    default_code = 'data_validation_error'
    default_status_code = status.HTTP_400_BAD_REQUEST


class ResourceNotFoundError(BaseCustomException):
    """
    资源未找到错误
    
    用于处理请求的资源不存在的情况
    """
    
    default_message = '请求的资源不存在'
    default_code = 'resource_not_found'
    default_status_code = status.HTTP_404_NOT_FOUND


class PermissionError(BaseCustomException):
    """
    权限错误
    
    用于处理用户权限不足的情况
    """
    
    default_message = '权限不足'
    default_code = 'permission_denied'
    default_status_code = status.HTTP_403_FORBIDDEN


class AuthenticationError(BaseCustomException):
    """
    认证错误
    
    用于处理用户认证失败的情况
    """
    
    default_message = '认证失败'
    default_code = 'authentication_failed'
    default_status_code = status.HTTP_401_UNAUTHORIZED


class ConfigurationError(BaseCustomException):
    """
    配置错误
    
    用于处理系统配置错误的情况
    """
    
    default_message = '系统配置错误'
    default_code = 'configuration_error'
    default_status_code = status.HTTP_500_INTERNAL_SERVER_ERROR


class ExternalServiceError(BaseCustomException):
    """
    外部服务错误
    
    用于处理外部服务调用失败的情况
    """
    
    default_message = '外部服务调用失败'
    default_code = 'external_service_error'
    default_status_code = status.HTTP_502_BAD_GATEWAY


class RateLimitError(BaseCustomException):
    """
    频率限制错误
    
    用于处理请求频率超限的情况
    """
    
    default_message = '请求频率超限'
    default_code = 'rate_limit_exceeded'
    default_status_code = status.HTTP_429_TOO_MANY_REQUESTS


class FileProcessingError(BaseCustomException):
    """
    文件处理错误
    
    用于处理文件上传、下载、处理失败的情况
    """
    
    default_message = '文件处理失败'
    default_code = 'file_processing_error'
    default_status_code = status.HTTP_400_BAD_REQUEST


class DatabaseError(BaseCustomException):
    """
    数据库错误
    
    用于处理数据库操作失败的情况
    """
    
    default_message = '数据库操作失败'
    default_code = 'database_error'
    default_status_code = status.HTTP_500_INTERNAL_SERVER_ERROR


# 医疗器械不良事件相关的特定异常

class AdverseEventError(BusinessLogicError):
    """
    不良事件相关错误
    """
    
    default_message = '不良事件处理错误'
    default_code = 'adverse_event_error'


class DeviceValidationError(DataValidationError):
    """
    医疗器械验证错误
    """
    
    default_message = '医疗器械信息验证失败'
    default_code = 'device_validation_error'


class ReportSubmissionError(BusinessLogicError):
    """
    报告提交错误
    """
    
    default_message = '报告提交失败'
    default_code = 'report_submission_error'


class PatientDataError(DataValidationError):
    """
    患者数据错误
    """
    
    default_message = '患者数据验证失败'
    default_code = 'patient_data_error'


class ReporterAuthorizationError(PermissionError):
    """
    报告者授权错误
    """
    
    default_message = '报告者权限验证失败'
    default_code = 'reporter_authorization_error'


def custom_exception_handler(exc, context):
    """
    自定义异常处理器
    
    Args:
        exc: 异常实例
        context: 异常上下文
        
    Returns:
        Response: HTTP响应
    """
    
    # 记录异常信息
    logger.error(f'异常发生: {exc}', exc_info=True, extra={
        'request': context.get('request'),
        'view': context.get('view'),
    })
    
    # 处理自定义异常
    if isinstance(exc, BaseCustomException):
        return Response(
            exc.to_dict(),
            status=exc.status_code
        )
    
    # 处理Django标准异常
    if isinstance(exc, ValidationError):
        return Response({
            'error': True,
            'code': 'validation_error',
            'message': '数据验证失败',
            'details': exc.message_dict if hasattr(exc, 'message_dict') else str(exc)
        }, status=status.HTTP_400_BAD_REQUEST)
    
    if isinstance(exc, PermissionDenied):
        return Response({
            'error': True,
            'code': 'permission_denied',
            'message': '权限不足'
        }, status=status.HTTP_403_FORBIDDEN)
    
    if isinstance(exc, Http404):
        return Response({
            'error': True,
            'code': 'not_found',
            'message': '请求的资源不存在'
        }, status=status.HTTP_404_NOT_FOUND)
    
    # 使用DRF默认异常处理器
    response = exception_handler(exc, context)
    
    if response is not None:
        # 统一异常响应格式
        custom_response_data = {
            'error': True,
            'code': 'api_error',
            'message': '请求处理失败',
            'details': response.data
        }
        response.data = custom_response_data
    
    return response


class ExceptionLogger:
    """
    异常日志记录器
    """
    
    @staticmethod
    def log_exception(exc, context=None, level='error'):
        """
        记录异常日志
        
        Args:
            exc: 异常实例
            context: 异常上下文
            level: 日志级别
        """
        
        extra_data = {}
        if context:
            extra_data.update(context)
        
        if isinstance(exc, BaseCustomException):
            extra_data.update({
                'error_code': exc.code,
                'error_details': exc.details
            })
        
        log_message = f'{exc.__class__.__name__}: {str(exc)}'
        
        if level == 'debug':
            logger.debug(log_message, extra=extra_data)
        elif level == 'info':
            logger.info(log_message, extra=extra_data)
        elif level == 'warning':
            logger.warning(log_message, extra=extra_data)
        elif level == 'error':
            logger.error(log_message, exc_info=True, extra=extra_data)
        elif level == 'critical':
            logger.critical(log_message, exc_info=True, extra=extra_data)


def handle_exception_with_logging(func):
    """
    异常处理装饰器
    
    自动记录异常日志并重新抛出
    """
    
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except BaseCustomException as e:
            ExceptionLogger.log_exception(e, {'function': func.__name__})
            raise
        except Exception as e:
            ExceptionLogger.log_exception(e, {'function': func.__name__})
            raise BusinessLogicError(
                message=f'函数 {func.__name__} 执行失败',
                details={'original_error': str(e)}
            )
    
    return wrapper
