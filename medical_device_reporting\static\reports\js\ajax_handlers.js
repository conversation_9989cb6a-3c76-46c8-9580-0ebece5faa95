/**
 * Ajax Handlers for Medical Device Reporting Platform
 * 医疗器械不良事件上报平台Ajax处理器
 */

// Ajax处理器类
class AjaxHandler {
    constructor() {
        this.defaultOptions = {
            timeout: 30000, // 30秒超时
            retryCount: 3,
            retryDelay: 1000
        };
    }
    
    /**
     * 发送Ajax请求
     * @param {string} url - 请求URL
     * @param {Object} options - 请求选项
     * @returns {Promise} Promise对象
     */
    async request(url, options = {}) {
        const config = {
            ...this.defaultOptions,
            ...options,
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': this.getCsrfToken(),
                ...options.headers
            }
        };
        
        // 如果是FormData，移除Content-Type
        if (config.body instanceof FormData) {
            delete config.headers['Content-Type'];
        }
        
        let lastError;
        
        // 重试机制
        for (let i = 0; i < config.retryCount; i++) {
            try {
                const response = await this.fetchWithTimeout(url, config, config.timeout);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('application/json')) {
                    return await response.json();
                } else {
                    return await response.text();
                }
                
            } catch (error) {
                lastError = error;
                
                // 如果不是网络错误或者是最后一次重试，直接抛出错误
                if (!this.isNetworkError(error) || i === config.retryCount - 1) {
                    throw error;
                }
                
                // 等待后重试
                await this.delay(config.retryDelay * (i + 1));
            }
        }
        
        throw lastError;
    }
    
    /**
     * 带超时的fetch
     * @param {string} url - 请求URL
     * @param {Object} options - 请求选项
     * @param {number} timeout - 超时时间
     * @returns {Promise} Promise对象
     */
    fetchWithTimeout(url, options, timeout) {
        return Promise.race([
            fetch(url, options),
            new Promise((_, reject) => 
                setTimeout(() => reject(new Error('请求超时')), timeout)
            )
        ]);
    }
    
    /**
     * 判断是否为网络错误
     * @param {Error} error - 错误对象
     * @returns {boolean} 是否为网络错误
     */
    isNetworkError(error) {
        return error.message.includes('网络') || 
               error.message.includes('timeout') || 
               error.message.includes('Failed to fetch');
    }
    
    /**
     * 延迟函数
     * @param {number} ms - 延迟毫秒数
     * @returns {Promise} Promise对象
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    /**
     * 获取CSRF Token
     * @returns {string} CSRF Token
     */
    getCsrfToken() {
        const token = document.querySelector('[name=csrfmiddlewaretoken]');
        return token ? token.value : '';
    }
    
    /**
     * GET请求
     * @param {string} url - 请求URL
     * @param {Object} params - 查询参数
     * @param {Object} options - 请求选项
     * @returns {Promise} Promise对象
     */
    get(url, params = {}, options = {}) {
        const searchParams = new URLSearchParams(params);
        const fullUrl = searchParams.toString() ? `${url}?${searchParams}` : url;
        
        return this.request(fullUrl, {
            method: 'GET',
            ...options
        });
    }
    
    /**
     * POST请求
     * @param {string} url - 请求URL
     * @param {Object|FormData} data - 请求数据
     * @param {Object} options - 请求选项
     * @returns {Promise} Promise对象
     */
    post(url, data = {}, options = {}) {
        const config = {
            method: 'POST',
            ...options
        };
        
        if (data instanceof FormData) {
            config.body = data;
        } else {
            config.body = JSON.stringify(data);
        }
        
        return this.request(url, config);
    }
    
    /**
     * PUT请求
     * @param {string} url - 请求URL
     * @param {Object|FormData} data - 请求数据
     * @param {Object} options - 请求选项
     * @returns {Promise} Promise对象
     */
    put(url, data = {}, options = {}) {
        const config = {
            method: 'PUT',
            ...options
        };
        
        if (data instanceof FormData) {
            config.body = data;
        } else {
            config.body = JSON.stringify(data);
        }
        
        return this.request(url, config);
    }
    
    /**
     * DELETE请求
     * @param {string} url - 请求URL
     * @param {Object} options - 请求选项
     * @returns {Promise} Promise对象
     */
    delete(url, options = {}) {
        return this.request(url, {
            method: 'DELETE',
            ...options
        });
    }
}

// 创建全局Ajax处理器实例
const ajaxHandler = new AjaxHandler();

// 报告相关的Ajax操作
const ReportAjax = {
    /**
     * 提交报告
     * @param {string} reportId - 报告ID
     * @returns {Promise} Promise对象
     */
    submit: function(reportId) {
        return ajaxHandler.post(`/reports/api/reports/${reportId}/submit/`);
    },
    
    /**
     * 审核报告
     * @param {string} reportId - 报告ID
     * @param {Object} reviewData - 审核数据
     * @returns {Promise} Promise对象
     */
    review: function(reportId, reviewData) {
        return ajaxHandler.post(`/reports/api/reports/${reportId}/review/`, reviewData);
    },
    
    /**
     * 获取报告详情
     * @param {string} reportId - 报告ID
     * @returns {Promise} Promise对象
     */
    getDetail: function(reportId) {
        return ajaxHandler.get(`/reports/api/reports/${reportId}/`);
    },
    
    /**
     * 获取报告列表
     * @param {Object} filters - 筛选条件
     * @returns {Promise} Promise对象
     */
    getList: function(filters = {}) {
        return ajaxHandler.get('/reports/api/reports/', filters);
    },
    
    /**
     * 创建报告
     * @param {FormData} formData - 表单数据
     * @returns {Promise} Promise对象
     */
    create: function(formData) {
        return ajaxHandler.post('/reports/api/reports/', formData);
    },
    
    /**
     * 更新报告
     * @param {string} reportId - 报告ID
     * @param {FormData} formData - 表单数据
     * @returns {Promise} Promise对象
     */
    update: function(reportId, formData) {
        return ajaxHandler.put(`/reports/api/reports/${reportId}/`, formData);
    },
    
    /**
     * 删除报告
     * @param {string} reportId - 报告ID
     * @returns {Promise} Promise对象
     */
    delete: function(reportId) {
        return ajaxHandler.delete(`/reports/api/reports/${reportId}/`);
    },
    
    /**
     * 导出报告
     * @param {Object} filters - 筛选条件
     * @returns {Promise} Promise对象
     */
    export: function(filters = {}) {
        return ajaxHandler.get('/reports/api/reports/export/', filters);
    },
    
    /**
     * 获取统计数据
     * @param {Object} params - 参数
     * @returns {Promise} Promise对象
     */
    getStatistics: function(params = {}) {
        return ajaxHandler.get('/reports/api/statistics/', params);
    },
    
    /**
     * 获取仪表板数据
     * @returns {Promise} Promise对象
     */
    getDashboard: function() {
        return ajaxHandler.get('/reports/api/dashboard/');
    }
};

// 错误处理器
const ErrorHandler = {
    /**
     * 处理Ajax错误
     * @param {Error} error - 错误对象
     * @param {string} context - 错误上下文
     */
    handle: function(error, context = '') {
        console.error(`Ajax错误 ${context}:`, error);
        
        let message = '操作失败，请稍后重试';
        
        if (error.message.includes('网络')) {
            message = '网络连接失败，请检查网络连接';
        } else if (error.message.includes('timeout') || error.message.includes('超时')) {
            message = '请求超时，请稍后重试';
        } else if (error.message.includes('HTTP 400')) {
            message = '请求参数错误';
        } else if (error.message.includes('HTTP 401')) {
            message = '未授权访问，请重新登录';
            // 可以在这里处理重新登录逻辑
        } else if (error.message.includes('HTTP 403')) {
            message = '权限不足';
        } else if (error.message.includes('HTTP 404')) {
            message = '请求的资源不存在';
        } else if (error.message.includes('HTTP 500')) {
            message = '服务器内部错误';
        }
        
        // 显示错误消息
        if (typeof showErrorMessage === 'function') {
            showErrorMessage(message);
        }
        
        return message;
    }
};

// 导出到全局
window.AjaxHandler = AjaxHandler;
window.ajaxHandler = ajaxHandler;
window.ReportAjax = ReportAjax;
window.ErrorHandler = ErrorHandler;
