# 更新日志

本文件记录了医疗器械不良事件上报平台的所有重要更改。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布]

### 重大更新 (2025-06-23)
- **不良事件报告系统全面优化**
  - **字段要求优化**: 患者信息改为可选，器械故障表现可选，事件描述取消字符限制
  - **产品信息验证**: 产品编号和产品批号至少填写其中一项的智能验证
  - **安全性增强**: 用户只能为自己提交报告，只能选择自己所属科室
  - **表单提交修复**: 解决disabled字段导致的提交问题，添加JavaScript防护机制
  - **中文化完善**: 用户显示名称优化，前端界面完全中文化，DataTables本地化
  - **系统优化**: 消除启动警告，设置自定义SECRET_KEY
  - **多层验证**: 前端限制 + 后端验证，确保数据安全性
  - **用户体验**: 清晰的视觉提示，直观的操作流程，友好的错误信息

### 修复 (2024-06-20)
- **用户状态切换功能修复**
  - 修复用户列表页面禁用/启用按钮HTTP 403错误
  - 解决JavaScript文件缓存导致的功能异常
  - 修正API调用路径从DRF API改为Django视图
  - 增强CSRF token获取机制，支持多种获取方式
  - 添加详细的调试日志和错误处理
  - 用户状态切换功能现已完全正常

- **科室管理Excel导入功能修复**
  - 修复科室管理页面"导入Excel"按钮无响应问题
  - 解决外部JavaScript文件加载和事件绑定失败
  - 改用内联JavaScript确保功能稳定性
  - 完善Excel文件格式验证和错误处理
  - 优化导入流程的用户体验和反馈机制
  - 确保模板下载功能正常工作
  - 科室Excel导入功能现已完全正常

### 计划中的功能
- 不良事件上报模块
- 医疗器械信息管理
- 报告生成和统计分析
- 通知提醒系统
- 数据导入导出功能

## [1.0.0] - 2024-01-XX

### 新增功能
- **用户管理系统**
  - 4位数账号登录机制
  - 用户创建、编辑、删除功能
  - 用户状态管理（激活/禁用）
  - 批量用户操作
  - 用户搜索和筛选

- **权限控制系统**
  - 基于角色的访问控制（RBAC）
  - 管理员和科室人员角色
  - 多层次权限架构（中间件+装饰器+DRF权限类）
  - 页面级和API级权限控制
  - 对象级权限管理

- **科室管理**
  - 科室信息管理
  - 用户科室分配
  - 科室状态控制

- **认证和会话管理**
  - 安全的登录/登出机制
  - 会话超时控制
  - 登录IP地址验证
  - 并发登录限制

- **RESTful API**
  - 完整的用户管理API
  - 科室管理API
  - 统计和搜索API
  - API权限控制
  - 标准化的错误响应

- **前端界面**
  - 响应式设计（Bootstrap 5）
  - 用户友好的管理界面
  - AJAX交互支持
  - 移动设备适配

- **安全特性**
  - CSRF保护
  - SQL注入防护
  - XSS防护
  - 输入验证和清理
  - 安全的密码处理

- **日志和审计**
  - 完整的操作日志记录
  - 用户行为追踪
  - 错误日志管理
  - 安全事件记录

- **系统配置**
  - 分环境配置（开发/生产）
  - 数据库配置优化
  - 静态文件管理
  - 国际化支持

### 技术实现
- **后端框架**: Django 5.1
- **数据库**: MySQL 8.0
- **前端框架**: Bootstrap 5.3
- **API框架**: Django REST Framework
- **认证系统**: Django内置认证 + 自定义扩展
- **权限系统**: Django权限 + 自定义权限类
- **中间件**: 自定义安全和权限中间件
- **日志系统**: Python logging + 自定义配置

### 测试覆盖
- 用户管理功能测试
- 权限控制系统测试
- API接口完整性测试
- 前端功能测试
- 认证系统测试
- 业务逻辑测试
- 数据库操作测试

### 文档
- 完整的README文档
- 权限控制实施总结
- URL路由和权限配置文档
- 环境搭建指南
- 日志系统指南
- 项目验证报告

### 部署支持
- 开发环境配置
- 生产环境部署指南
- Docker容器化支持
- Nginx配置示例
- 数据库迁移脚本

## [0.2.0] - 2024-01-XX

### 新增功能
- 基础的用户管理功能
- 简单的权限控制
- 数据库模型设计
- 基础的前端界面

### 修复
- 数据库连接问题
- 静态文件加载问题
- 基础的安全漏洞修复

## [0.1.0] - 2024-01-XX

### 新增功能
- 项目初始化
- Django项目结构搭建
- 基础配置文件
- 数据库连接配置
- 基础的用户模型

### 技术债务
- 需要完善权限系统
- 需要添加前端界面
- 需要完善API接口
- 需要添加测试用例

---

## 版本说明

### 版本号规则
- **主版本号**: 不兼容的API修改
- **次版本号**: 向下兼容的功能性新增
- **修订号**: 向下兼容的问题修正

### 更新类型
- **新增功能** (Added): 新功能
- **修改** (Changed): 对现有功能的变更
- **弃用** (Deprecated): 即将移除的功能
- **移除** (Removed): 已移除的功能
- **修复** (Fixed): 任何bug修复
- **安全** (Security): 安全相关的修复

### 发布周期
- **主版本**: 根据重大功能更新发布
- **次版本**: 每月发布一次（如有新功能）
- **修订版本**: 根据bug修复需要随时发布

### 支持政策
- 当前主版本: 完全支持
- 前一个主版本: 安全更新支持
- 更早版本: 不再支持

---

**注意**: 本项目目前处于活跃开发阶段，API可能会有变化。建议在生产环境使用前仔细测试所有功能。
