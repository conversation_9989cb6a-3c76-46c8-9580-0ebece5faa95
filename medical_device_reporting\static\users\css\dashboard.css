/*
 * Dashboard Styles for Medical Device Reporting Platform
 * 医疗器械不良事件上报平台用户中心样式
 */

/* 仪表板容器 */
.dashboard-container {
    padding: 1.5rem 0;
}

/* 欢迎横幅 */
.welcome-banner {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 1rem;
    padding: 2rem;
    color: white;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.welcome-title {
    font-size: 1.75rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.welcome-subtitle {
    font-size: 1rem;
    opacity: 0.9;
}

.welcome-actions .badge {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
}

/* 统计卡片 */
.stat-card {
    transition: all 0.3s ease;
    border-radius: 1rem;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.5rem;
    color: white;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.stat-label {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0;
    font-weight: 500;
}

/* 功能卡片 */
.feature-link {
    text-decoration: none;
    color: inherit;
    display: block;
}

.feature-card {
    padding: 1.5rem;
    border-radius: 0.75rem;
    background: white;
    border: 2px solid #f8f9fa;
    transition: all 0.3s ease;
    text-align: center;
    height: 100%;
}

.feature-card:hover {
    border-color: #0d6efd;
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(13, 110, 253, 0.15);
}

.feature-icon {
    width: 50px;
    height: 50px;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.25rem;
    color: white;
}

.feature-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.feature-desc {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0;
    line-height: 1.4;
}

/* 信息面板 */
.user-info .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f8f9fa;
}

.user-info .info-item:last-child {
    border-bottom: none;
}

.user-info .info-item label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0;
    font-size: 0.875rem;
}

.user-info .info-item span {
    font-size: 0.875rem;
    color: #6c757d;
}

/* 卡片样式增强 */
.card {
    border-radius: 1rem;
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.card-header {
    border-radius: 1rem 1rem 0 0 !important;
    padding: 1.25rem 1.5rem;
}

.card-title {
    font-weight: 600;
    color: #2c3e50;
}

.card-body {
    padding: 1.5rem;
}

/* 按钮样式 */
.btn {
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-outline-primary:hover {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: white;
}

.btn-outline-secondary:hover {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

.btn-outline-danger:hover {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
}

/* 徽章样式 */
.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
}

/* 响应式设计 */
@media (max-width: 992px) {
    .welcome-banner {
        padding: 1.5rem;
        text-align: center;
    }
    
    .welcome-title {
        font-size: 1.5rem;
    }
    
    .welcome-actions {
        margin-top: 1rem;
    }
}

@media (max-width: 768px) {
    .dashboard-container {
        padding: 1rem 0;
    }
    
    .welcome-banner {
        padding: 1rem;
        margin-bottom: 1.5rem;
    }
    
    .welcome-title {
        font-size: 1.25rem;
    }
    
    .welcome-subtitle {
        font-size: 0.875rem;
    }
    
    .stat-card {
        margin-bottom: 1rem;
    }
    
    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
    
    .stat-number {
        font-size: 1.5rem;
    }
    
    .feature-card {
        padding: 1rem;
    }
    
    .feature-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    .welcome-banner {
        margin: 0 -0.5rem 1.5rem;
        border-radius: 0.5rem;
    }
    
    .card {
        margin: 0 -0.5rem 1rem;
        border-radius: 0.5rem;
    }
    
    .card-header,
    .card-body {
        padding: 1rem;
    }
}

/* 动画效果 */
.dashboard-container {
    animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stat-card {
    animation: slideInUp 0.6s ease-out;
    animation-fill-mode: both;
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.feature-card {
    animation: fadeInScale 0.6s ease-out;
    animation-fill-mode: both;
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* 悬停效果增强 */
.stat-card:hover .stat-icon {
    transform: scale(1.1);
    transition: transform 0.3s ease;
}

.feature-card:hover .feature-icon {
    transform: scale(1.1) rotate(5deg);
    transition: transform 0.3s ease;
}

/* 加载状态 */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0d6efd;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
