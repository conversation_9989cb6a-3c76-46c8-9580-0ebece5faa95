{% extends 'reports/base.html' %}
{% load static %}

{% block page_title %}统计分析{% endblock %}
{% block page_heading %}统计分析仪表板{% endblock %}
{% block page_description %}多维度数据统计分析和可视化展示{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item active">统计分析</li>
{% endblock %}

{% block page_actions %}
<div class="btn-group" role="group">
    <a href="{% url 'reports:statistics_detail' %}?type=time_series" class="btn btn-outline-primary btn-sm">
        <i class="bi bi-graph-up me-1"></i>
        详细分析
    </a>
    <div class="btn-group" role="group">
        <button type="button" class="btn btn-outline-secondary btn-sm dropdown-toggle" data-bs-toggle="dropdown">
            <i class="bi bi-download me-1"></i>
            导出数据
        </button>
        <ul class="dropdown-menu">
            <li><a class="dropdown-item" href="#" onclick="exportStatistics('excel')">
                <i class="bi bi-file-earmark-excel me-2"></i>导出Excel
            </a></li>
            <li><a class="dropdown-item" href="#" onclick="exportStatistics('pdf')">
                <i class="bi bi-file-earmark-pdf me-2"></i>导出PDF
            </a></li>
            <li><hr class="dropdown-divider"></li>
            <li><a class="dropdown-item" href="#" onclick="exportBatch()">
                <i class="bi bi-files me-2"></i>批量导出
            </a></li>
        </ul>
    </div>
    <button type="button" class="btn btn-outline-info btn-sm" onclick="refreshCharts()">
        <i class="bi bi-arrow-clockwise me-1"></i>
        刷新
    </button>
</div>
{% endblock %}

{% block reports_content %}
<!-- 筛选控件 -->
<div class="filter-controls">
    <form method="get" id="filterForm">
        <div class="row">
            <div class="col-md-3 mb-3">
                <label for="chart_type" class="form-label">图表类型</label>
                <select class="form-select" id="chart_type" name="chart_type">
                    {% for value, label in filter_options.chart_types %}
                    <option value="{{ value }}" {% if current_filters.chart_type == value %}selected{% endif %}>
                        {{ label }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2 mb-3">
                <label for="granularity" class="form-label">时间粒度</label>
                <select class="form-select" id="granularity" name="granularity">
                    {% for value, label in filter_options.granularities %}
                    <option value="{{ value }}" {% if current_filters.granularity == value %}selected{% endif %}>
                        {{ label }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2 mb-3">
                <label for="start_date" class="form-label">开始日期</label>
                <input type="date" class="form-control" id="start_date" name="start_date" 
                       value="{{ current_filters.start_date|default:'' }}">
            </div>
            <div class="col-md-2 mb-3">
                <label for="end_date" class="form-label">结束日期</label>
                <input type="date" class="form-control" id="end_date" name="end_date" 
                       value="{{ current_filters.end_date|default:'' }}">
            </div>
            <div class="col-md-3 mb-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-funnel me-1"></i>
                        筛选
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="resetFilters()">
                        <i class="bi bi-arrow-counterclockwise me-1"></i>
                        重置
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- 基础统计卡片 -->
<div class="row mb-4">
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="stat-card card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="stat-icon bg-primary">
                    <i class="bi bi-file-text"></i>
                </div>
                <h4 class="stat-number">{{ stats.total_count|default:0 }}</h4>
                <p class="stat-label">总报告数</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="stat-card card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="stat-icon bg-success">
                    <i class="bi bi-check-circle"></i>
                </div>
                <h4 class="stat-number">{{ stats.approved_count|default:0 }}</h4>
                <p class="stat-label">已批准</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="stat-card card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="stat-icon bg-danger">
                    <i class="bi bi-exclamation-triangle"></i>
                </div>
                <h4 class="stat-number">{{ stats.serious_injury_count|default:0 }}</h4>
                <p class="stat-label">严重事件</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="stat-card card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="stat-icon bg-dark">
                    <i class="bi bi-x-circle"></i>
                </div>
                <h4 class="stat-number">{{ stats.death_count|default:0 }}</h4>
                <p class="stat-label">死亡事件</p>
            </div>
        </div>
    </div>
</div>

<!-- 图表展示区域 -->
<div class="statistics-grid">
    <!-- 时间趋势图表 -->
    <div class="statistics-card">
        <h5 class="card-title">
            <i class="bi bi-graph-up"></i>
            时间趋势分析
        </h5>
        <div class="chart-toolbar">
            <div class="btn-group btn-group-sm" role="group">
                <button type="button" class="btn btn-outline-primary active" data-chart="time_series" data-granularity="month">月度</button>
                <button type="button" class="btn btn-outline-primary" data-chart="time_series" data-granularity="week">周度</button>
                <button type="button" class="btn btn-outline-primary" data-chart="time_series" data-granularity="day">日度</button>
            </div>
            <small class="text-muted">点击切换时间粒度</small>
        </div>
        <div class="chart-container" id="timeSeriesChart">
            <div class="chart-loading">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2">正在加载图表数据...</p>
            </div>
        </div>
    </div>

    <!-- 交叉维度分析 -->
    <div class="statistics-card">
        <h5 class="card-title">
            <i class="bi bi-diagram-3"></i>
            交叉维度分析
        </h5>
        <div class="chart-toolbar">
            <div class="btn-group btn-group-sm" role="group">
                <select class="form-select form-select-sm" id="dimension1Select" style="width: auto;">
                    {% for value, label in filter_options.dimensions %}
                    <option value="{{ value }}" {% if current_filters.dimension1 == value %}selected{% endif %}>
                        {{ label }}
                    </option>
                    {% endfor %}
                </select>
                <span class="align-self-center mx-2">vs</span>
                <select class="form-select form-select-sm" id="dimension2Select" style="width: auto;">
                    {% for value, label in filter_options.dimensions %}
                    <option value="{{ value }}" {% if current_filters.dimension2 == value %}selected{% endif %}>
                        {{ label }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <button type="button" class="btn btn-outline-primary btn-sm" onclick="updateCrossDimension()">
                <i class="bi bi-arrow-clockwise"></i>
                更新
            </button>
        </div>
        <div class="chart-container" id="crossDimensionChart">
            <div class="chart-loading">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2">正在加载图表数据...</p>
            </div>
        </div>
    </div>
</div>

<!-- 器械和科室统计 -->
<div class="row">
    <!-- 器械统计 -->
    <div class="col-lg-6 mb-4">
        <div class="statistics-card">
            <h5 class="card-title">
                <i class="bi bi-gear"></i>
                热门器械统计
            </h5>
            <div class="chart-container small" id="deviceStatsChart">
                <div class="chart-loading">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在加载图表数据...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 科室统计（仅管理员） -->
    {% if is_admin %}
    <div class="col-lg-6 mb-4">
        <div class="statistics-card">
            <h5 class="card-title">
                <i class="bi bi-building"></i>
                科室统计分析
            </h5>
            <div class="chart-container small" id="departmentStatsChart">
                <div class="chart-loading">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在加载图表数据...</p>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- 趋势分析摘要 -->
{% if chart_data.trend_analysis %}
<div class="row">
    <div class="col-12">
        <div class="statistics-card">
            <h5 class="card-title">
                <i class="bi bi-trending-up"></i>
                趋势分析摘要
            </h5>
            <div class="row">
                <div class="col-md-3 text-center">
                    <div class="metric-value">{{ chart_data.trend_analysis.growth_rate|floatformat:1 }}%</div>
                    <div class="metric-label">增长率</div>
                    <div class="metric-change">
                        <span class="trend-indicator {{ chart_data.trend_analysis.trend_direction }}">
                            {% if chart_data.trend_analysis.trend_direction == 'increasing' %}
                                <i class="bi bi-arrow-up"></i> 上升趋势
                            {% elif chart_data.trend_analysis.trend_direction == 'decreasing' %}
                                <i class="bi bi-arrow-down"></i> 下降趋势
                            {% else %}
                                <i class="bi bi-arrow-right"></i> 稳定趋势
                            {% endif %}
                        </span>
                    </div>
                </div>
                <div class="col-md-9">
                    <p class="mb-0">{{ chart_data.trend_analysis.analysis }}</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block reports_extra_js %}
<!-- Chart.js CDN -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="{% static 'reports/js/statistics_charts.js' %}"></script>

<script>
// 页面加载完成后初始化图表
document.addEventListener('DOMContentLoaded', function() {
    // 传递图表数据到JavaScript
    window.chartData = {{ chart_data|safe }};
    window.isAdmin = {{ is_admin|yesno:"true,false" }};
    
    // 初始化所有图表
    if (typeof StatisticsCharts !== 'undefined') {
        StatisticsCharts.init();
    }
});

// 筛选表单自动提交
document.getElementById('chart_type').addEventListener('change', function() {
    document.getElementById('filterForm').submit();
});

document.getElementById('granularity').addEventListener('change', function() {
    document.getElementById('filterForm').submit();
});

// 重置筛选器
function resetFilters() {
    document.getElementById('start_date').value = '';
    document.getElementById('end_date').value = '';
    document.getElementById('chart_type').value = 'summary';
    document.getElementById('granularity').value = 'month';
    document.getElementById('filterForm').submit();
}

// 刷新图表
function refreshCharts() {
    location.reload();
}

// 导出统计数据
function exportStatistics() {
    // TODO: 实现数据导出功能
    alert('数据导出功能开发中...');
}

// 更新交叉维度分析
function updateCrossDimension() {
    const dimension1 = document.getElementById('dimension1Select').value;
    const dimension2 = document.getElementById('dimension2Select').value;
    
    const url = new URL(window.location);
    url.searchParams.set('dimension1', dimension1);
    url.searchParams.set('dimension2', dimension2);
    url.searchParams.set('chart_type', 'cross_dimension');
    
    window.location.href = url.toString();
}
</script>
{% endblock %}
