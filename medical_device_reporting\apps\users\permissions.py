"""
用户权限管理
User Permissions for Medical Device Reporting Platform
"""

from functools import wraps
from django.contrib.auth.decorators import login_required, permission_required
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.core.exceptions import PermissionDenied
from django.shortcuts import redirect
from django.contrib import messages
from django.http import JsonResponse
from rest_framework.permissions import BasePermission
import logging

logger = logging.getLogger(__name__)


class IsAdminUser(BasePermission):
    """
    DRF权限类：只允许管理员用户访问
    """
    
    def has_permission(self, request, view):
        return (
            request.user and
            request.user.is_authenticated and
            hasattr(request.user, 'profile') and
            request.user.profile.is_admin
        )


class IsOwnerOrAdmin(BasePermission):
    """
    DRF权限类：只允许对象所有者或管理员访问
    """
    
    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request, view, obj):
        # 管理员可以访问所有对象
        if hasattr(request.user, 'profile') and request.user.profile.is_admin:
            return True
        
        # 用户只能访问自己的对象
        if hasattr(obj, 'user'):
            return obj.user == request.user
        elif hasattr(obj, 'created_by'):
            return obj.created_by == request.user
        
        return False


class IsDepartmentMemberOrAdmin(BasePermission):
    """
    DRF权限类：只允许科室成员或管理员访问
    """

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False

        if not hasattr(request.user, 'profile'):
            return False

        profile = request.user.profile

        # 管理员可以访问
        if profile.is_admin:
            return True

        # 科室成员可以访问（必须有科室且状态正常）
        if (profile.role == 'staff' and
            profile.department and
            profile.department.is_active and
            profile.is_active):
            return True

        return False

    def has_object_permission(self, request, view, obj):
        # 管理员可以访问所有对象
        if hasattr(request.user, 'profile') and request.user.profile.is_admin:
            return True

        # 同科室成员可以访问
        if (hasattr(request.user, 'profile') and
            hasattr(obj, 'department') and
            request.user.profile.department == obj.department):
            return True

        return False


def admin_required(view_func=None, *, redirect_url=None, raise_exception=False):
    """
    装饰器：要求管理员权限
    
    Args:
        view_func: 视图函数
        redirect_url: 重定向URL（默认为登录页面）
        raise_exception: 是否抛出异常（默认为False，重定向）
    """
    def decorator(func):
        @wraps(func)
        @login_required
        def wrapper(request, *args, **kwargs):
            # 检查用户是否为管理员
            if not (hasattr(request.user, 'profile') and request.user.profile.is_admin):
                logger.warning(f'非管理员用户尝试访问管理功能: {request.user.username}')
                
                if raise_exception:
                    raise PermissionDenied('需要管理员权限')
                
                # 处理AJAX请求
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({'error': '需要管理员权限'}, status=403)
                
                # 添加错误消息
                messages.error(request, '您没有访问此页面的权限，需要管理员权限')
                
                # 重定向
                redirect_to = redirect_url or 'users:dashboard'
                return redirect(redirect_to)
            
            return func(request, *args, **kwargs)
        return wrapper
    
    if view_func is None:
        return decorator
    else:
        return decorator(view_func)


def department_member_or_admin_required(view_func=None, *, redirect_url=None, raise_exception=False):
    """
    装饰器：要求科室成员或管理员权限
    """
    def decorator(func):
        @wraps(func)
        @login_required
        def wrapper(request, *args, **kwargs):
            # 管理员可以访问
            if hasattr(request.user, 'profile') and request.user.profile.is_admin:
                return func(request, *args, **kwargs)
            
            # 检查是否为科室成员
            if not (hasattr(request.user, 'profile') and 
                    request.user.profile.department and 
                    request.user.profile.is_active):
                logger.warning(f'非科室成员用户尝试访问: {request.user.username}')
                
                if raise_exception:
                    raise PermissionDenied('需要科室成员权限')
                
                # 处理AJAX请求
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({'error': '需要科室成员权限'}, status=403)
                
                # 添加错误消息
                messages.error(request, '您没有访问此页面的权限，需要科室成员权限')
                
                # 重定向
                redirect_to = redirect_url or 'users:dashboard'
                return redirect(redirect_to)
            
            return func(request, *args, **kwargs)
        return wrapper
    
    if view_func is None:
        return decorator
    else:
        return decorator(view_func)


def owner_or_admin_required(view_func=None, *, get_object_func=None, redirect_url=None, raise_exception=False):
    """
    装饰器：要求对象所有者或管理员权限
    
    Args:
        get_object_func: 获取对象的函数，接收request和kwargs参数
    """
    def decorator(func):
        @wraps(func)
        @login_required
        def wrapper(request, *args, **kwargs):
            # 管理员可以访问
            if hasattr(request.user, 'profile') and request.user.profile.is_admin:
                return func(request, *args, **kwargs)
            
            # 获取对象并检查所有权
            if get_object_func:
                obj = get_object_func(request, kwargs)
                if obj:
                    # 检查是否为对象所有者
                    is_owner = False
                    if hasattr(obj, 'user') and obj.user == request.user:
                        is_owner = True
                    elif hasattr(obj, 'created_by') and obj.created_by == request.user:
                        is_owner = True
                    
                    if not is_owner:
                        logger.warning(f'用户尝试访问非自己的对象: {request.user.username}')
                        
                        if raise_exception:
                            raise PermissionDenied('只能访问自己的数据')
                        
                        # 处理AJAX请求
                        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                            return JsonResponse({'error': '只能访问自己的数据'}, status=403)
                        
                        # 添加错误消息
                        messages.error(request, '您只能访问自己的数据')
                        
                        # 重定向
                        redirect_to = redirect_url or 'users:dashboard'
                        return redirect(redirect_to)
            
            return func(request, *args, **kwargs)
        return wrapper
    
    if view_func is None:
        return decorator
    else:
        return decorator(view_func)


class AdminRequiredMixin(LoginRequiredMixin):
    """
    类视图Mixin：要求管理员权限
    """
    
    def dispatch(self, request, *args, **kwargs):
        if not (hasattr(request.user, 'profile') and request.user.profile.is_admin):
            logger.warning(f'非管理员用户尝试访问管理功能: {request.user.username}')
            raise PermissionDenied('需要管理员权限')
        return super().dispatch(request, *args, **kwargs)


class DepartmentMemberOrAdminMixin(LoginRequiredMixin):
    """
    类视图Mixin：要求科室成员或管理员权限
    """
    
    def dispatch(self, request, *args, **kwargs):
        # 管理员可以访问
        if hasattr(request.user, 'profile') and request.user.profile.is_admin:
            return super().dispatch(request, *args, **kwargs)
        
        # 检查是否为科室成员
        if not (hasattr(request.user, 'profile') and 
                request.user.profile.department and 
                request.user.profile.is_active):
            logger.warning(f'非科室成员用户尝试访问: {request.user.username}')
            raise PermissionDenied('需要科室成员权限')
        
        return super().dispatch(request, *args, **kwargs)


class OwnerOrAdminMixin(LoginRequiredMixin):
    """
    类视图Mixin：要求对象所有者或管理员权限
    """
    
    def get_object(self, queryset=None):
        obj = super().get_object(queryset)
        
        # 管理员可以访问
        if hasattr(self.request.user, 'profile') and self.request.user.profile.is_admin:
            return obj
        
        # 检查是否为对象所有者
        is_owner = False
        if hasattr(obj, 'user') and obj.user == self.request.user:
            is_owner = True
        elif hasattr(obj, 'created_by') and obj.created_by == self.request.user:
            is_owner = True
        
        if not is_owner:
            logger.warning(f'用户尝试访问非自己的对象: {self.request.user.username}')
            raise PermissionDenied('只能访问自己的数据')
        
        return obj


def check_user_permissions(user, required_permissions):
    """
    检查用户是否具有所需权限
    
    Args:
        user: 用户对象
        required_permissions: 所需权限列表
    
    Returns:
        bool: 是否具有权限
    """
    if not user or not user.is_authenticated:
        return False
    
    # 检查Django权限
    for perm in required_permissions:
        if not user.has_perm(perm):
            return False
    
    return True


def get_user_role_permissions(user):
    """
    获取用户角色权限
    
    Args:
        user: 用户对象
    
    Returns:
        dict: 权限字典
    """
    permissions = {
        'is_admin': False,
        'is_staff': False,
        'can_manage_users': False,
        'can_manage_departments': False,
        'can_view_reports': False,
        'can_create_reports': False,
    }
    
    if not user or not user.is_authenticated or not hasattr(user, 'profile'):
        return permissions
    
    profile = user.profile
    
    # 基础权限
    permissions['is_staff'] = profile.role == 'staff'
    permissions['is_admin'] = profile.role == 'admin'
    
    # 管理员权限
    if profile.is_admin:
        permissions.update({
            'can_manage_users': True,
            'can_manage_departments': True,
            'can_view_reports': True,
            'can_create_reports': True,
        })
    
    # 科室人员权限
    elif profile.role == 'staff' and profile.department:
        permissions.update({
            'can_view_reports': True,
            'can_create_reports': True,
        })
    
    return permissions
