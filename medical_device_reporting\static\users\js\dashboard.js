/**
 * Dashboard JavaScript for Medical Device Reporting Platform
 * 医疗器械不良事件上报平台用户中心脚本
 */

document.addEventListener('DOMContentLoaded', function() {
    // 初始化仪表板
    initializeDashboard();
    
    /**
     * 初始化仪表板
     */
    function initializeDashboard() {
        // 绑定事件监听器
        bindEventListeners();
        
        // 初始化工具提示
        initializeTooltips();
        
        // 加载统计数据
        loadStatistics();
        
        // 设置自动刷新
        setupAutoRefresh();
        
        console.log('用户中心初始化完成');
    }
    
    /**
     * 绑定事件监听器
     */
    function bindEventListeners() {
        // 功能卡片点击事件
        const featureCards = document.querySelectorAll('.feature-card');
        featureCards.forEach(card => {
            card.addEventListener('click', handleFeatureClick);
        });
        
        // 统计卡片点击事件
        const statCards = document.querySelectorAll('.stat-card');
        statCards.forEach(card => {
            card.addEventListener('click', handleStatClick);
        });
        
        // 快速操作按钮事件
        const quickActions = document.querySelectorAll('.btn[href="#"]');
        quickActions.forEach(btn => {
            btn.addEventListener('click', handleQuickAction);
        });
    }
    
    /**
     * 处理功能卡片点击
     */
    function handleFeatureClick(event) {
        const card = event.currentTarget;
        const link = card.closest('a');
        
        if (link && link.href && link.href !== '#') {
            // 添加点击效果
            card.style.transform = 'scale(0.95)';
            setTimeout(() => {
                card.style.transform = '';
            }, 150);
        } else {
            event.preventDefault();
            showComingSoon();
        }
    }
    
    /**
     * 处理统计卡片点击
     */
    function handleStatClick(event) {
        const card = event.currentTarget;
        
        // 添加点击效果
        card.style.transform = 'scale(0.98)';
        setTimeout(() => {
            card.style.transform = '';
        }, 150);
        
        // 显示详细信息（可以扩展）
        console.log('统计卡片被点击');
    }
    
    /**
     * 处理快速操作
     */
    function handleQuickAction(event) {
        const btn = event.currentTarget;
        const text = btn.textContent.trim();
        
        if (btn.href === '#') {
            event.preventDefault();
            
            if (text.includes('新建报告')) {
                showComingSoon('新建报告功能');
            } else if (text.includes('查询报告')) {
                showComingSoon('查询报告功能');
            }
        }
    }
    
    /**
     * 初始化工具提示
     */
    function initializeTooltips() {
        // 如果使用Bootstrap 5的工具提示
        if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }
    }
    
    /**
     * 加载统计数据
     */
    function loadStatistics() {
        // 这里可以通过AJAX加载最新的统计数据
        // 目前使用模拟数据
        
        const statNumbers = document.querySelectorAll('.stat-number');
        
        statNumbers.forEach((element, index) => {
            const finalValue = parseInt(element.textContent) || 0;
            animateNumber(element, 0, finalValue, 1000 + (index * 200));
        });
    }
    
    /**
     * 数字动画效果
     */
    function animateNumber(element, start, end, duration) {
        const startTime = performance.now();
        
        function updateNumber(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // 使用缓动函数
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const current = Math.floor(start + (end - start) * easeOutQuart);
            
            element.textContent = current;
            
            if (progress < 1) {
                requestAnimationFrame(updateNumber);
            } else {
                element.textContent = end;
            }
        }
        
        requestAnimationFrame(updateNumber);
    }
    
    /**
     * 设置自动刷新
     */
    function setupAutoRefresh() {
        // 每5分钟刷新一次统计数据
        setInterval(() => {
            refreshStatistics();
        }, 5 * 60 * 1000);
    }
    
    /**
     * 刷新统计数据
     */
    function refreshStatistics() {
        // 这里可以通过AJAX获取最新数据
        console.log('刷新统计数据');
        
        // 模拟数据更新
        const statCards = document.querySelectorAll('.stat-card');
        statCards.forEach(card => {
            card.style.opacity = '0.7';
            setTimeout(() => {
                card.style.opacity = '1';
            }, 500);
        });
    }
    
    /**
     * 显示即将推出提示
     */
    function showComingSoon(feature = '该功能') {
        // 创建提示模态框
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header border-0">
                        <h5 class="modal-title">
                            <i class="bi bi-info-circle text-info me-2"></i>
                            功能提示
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body text-center py-4">
                        <i class="bi bi-tools text-warning" style="font-size: 3rem;"></i>
                        <h6 class="mt-3 mb-2">${feature}正在开发中</h6>
                        <p class="text-muted mb-0">敬请期待后续版本更新</p>
                    </div>
                    <div class="modal-footer border-0 justify-content-center">
                        <button type="button" class="btn btn-primary" data-bs-dismiss="modal">
                            <i class="bi bi-check me-2"></i>
                            知道了
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // 显示模态框
        if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();
            
            // 模态框关闭后移除元素
            modal.addEventListener('hidden.bs.modal', () => {
                document.body.removeChild(modal);
            });
        } else {
            // 如果没有Bootstrap，使用简单的alert
            alert(`${feature}正在开发中，敬请期待！`);
            document.body.removeChild(modal);
        }
    }
    
    /**
     * 显示加载状态
     */
    function showLoading(element) {
        if (element) {
            element.classList.add('loading');
        }
    }
    
    /**
     * 隐藏加载状态
     */
    function hideLoading(element) {
        if (element) {
            element.classList.remove('loading');
        }
    }
    
    /**
     * 显示成功消息
     */
    function showSuccess(message) {
        showToast(message, 'success');
    }
    
    /**
     * 显示错误消息
     */
    function showError(message) {
        showToast(message, 'danger');
    }
    
    /**
     * 显示Toast消息
     */
    function showToast(message, type = 'info') {
        const toastContainer = getOrCreateToastContainer();
        
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type} border-0`;
        toast.setAttribute('role', 'alert');
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <i class="bi bi-check-circle me-2"></i>
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        
        toastContainer.appendChild(toast);
        
        // 显示Toast
        if (typeof bootstrap !== 'undefined' && bootstrap.Toast) {
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
            
            // Toast隐藏后移除元素
            toast.addEventListener('hidden.bs.toast', () => {
                toastContainer.removeChild(toast);
            });
        } else {
            // 如果没有Bootstrap，3秒后自动移除
            setTimeout(() => {
                if (toastContainer.contains(toast)) {
                    toastContainer.removeChild(toast);
                }
            }, 3000);
        }
    }
    
    /**
     * 获取或创建Toast容器
     */
    function getOrCreateToastContainer() {
        let container = document.querySelector('.toast-container');
        
        if (!container) {
            container = document.createElement('div');
            container.className = 'toast-container position-fixed top-0 end-0 p-3';
            container.style.zIndex = '1055';
            document.body.appendChild(container);
        }
        
        return container;
    }
    
    /**
     * 工具函数：防抖
     */
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    /**
     * 工具函数：节流
     */
    function throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
    
    // 导出函数供外部使用
    window.Dashboard = {
        showSuccess,
        showError,
        showComingSoon,
        refreshStatistics,
        showLoading,
        hideLoading
    };
});
