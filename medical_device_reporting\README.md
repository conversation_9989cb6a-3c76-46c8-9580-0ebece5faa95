# 医疗器械不良事件上报平台

[![Python](https://img.shields.io/badge/Python-3.11+-blue.svg)](https://www.python.org/)
[![Django](https://img.shields.io/badge/Django-5.1+-green.svg)](https://www.djangoproject.com/)
[![MySQL](https://img.shields.io/badge/MySQL-8.0+-orange.svg)](https://www.mysql.com/)
[![Bootstrap](https://img.shields.io/badge/Bootstrap-5.3+-purple.svg)](https://getbootstrap.com/)

一个基于Django的医疗器械不良事件上报和管理平台，提供完整的用户管理、权限控制和事件上报功能。

## 🚀 项目特性

### 核心功能
- **用户管理系统** - 4位数账号登录，支持管理员和科室人员角色
- **权限控制** - 多层次权限架构，精细化访问控制
- **科室管理** - 完整的医院科室信息管理，支持Excel导入导出
- **不良事件报告** - 完整的医疗器械不良事件上报和管理系统
  - 分步创建报告，支持灵活的数据录入
  - 患者信息可选，保护隐私
  - 器械故障表现详细描述，无字符限制
  - 产品编号/批号智能验证
  - 安全的用户权限控制，防止跨科室数据泄露
- **个人设置** - 用户个人信息管理、偏好设置、密码修改
- **会话安全** - 登录验证、IP检查、会话超时控制
- **审计日志** - 完整的操作记录和数据变更追踪

### 技术特性
- **现代化架构** - Django 5.1 + MySQL 8.0 + Bootstrap 5
- **RESTful API** - 完整的API接口，支持前后端分离
- **响应式设计** - 支持桌面和移动设备访问，DataTables集成
- **文件处理** - Excel导入导出，拖拽上传，进度反馈
- **前端交互** - AJAX操作，表单验证，实时反馈
- **国际化支持** - 中文界面，支持多语言扩展
- **安全防护** - CSRF保护、SQL注入防护、XSS防护

## 📋 系统要求

### 环境要求
- Python 3.11+
- MySQL 8.0+
- Node.js 16+ (可选，用于前端构建)

### 推荐配置
- 内存：4GB+
- 存储：20GB+
- 操作系统：Windows 10/11, Ubuntu 20.04+, CentOS 8+

## 🛠️ 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd medical_device_reporting
```

### 2. 创建虚拟环境
```bash
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate
```

### 3. 安装依赖
```bash
pip install -r requirements/development.txt
```

### 4. 配置数据库
```bash
# 创建MySQL数据库
mysql -u root -p
CREATE DATABASE medical_device_reporting CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 运行数据库迁移
python manage.py migrate --settings=config.settings.development
```

### 5. 初始化系统用户
```bash
# 方式一：完整初始化（推荐）
python create_superuser.py

# 方式二：一键快速设置
python quick_setup.py
```

### 6. 启动开发服务器
```bash
python manage.py runserver --settings=config.settings.development
```

访问 http://127.0.0.1:8000 开始使用系统。

## 🔑 默认登录信息

### 业务系统登录 (http://127.0.0.1:8000/login/)
- **管理员账号**: `0001` (无需密码)
- **测试人员账号**: `1001` (无需密码)

### Django管理后台 (http://127.0.0.1:8000/admin/)
- **用户名**: `admin`
- **密码**: `admin123456`

## 📁 项目结构

```
medical_device_reporting/
├── apps/                          # 应用模块
│   ├── common/                    # 公共模块
│   │   ├── exceptions.py          # 自定义异常
│   │   ├── middleware.py          # 公共中间件
│   │   └── utils.py              # 工具函数
│   └── users/                     # 用户管理模块
│       ├── models.py             # 数据模型
│       ├── views.py              # 视图函数
│       ├── apis.py               # API接口
│       ├── services.py           # 业务逻辑
│       ├── selectors.py          # 数据查询
│       ├── permissions.py        # 权限控制
│       ├── middleware.py         # 用户中间件
│       └── serializers.py       # 序列化器
├── config/                        # 配置文件
│   ├── settings/                 # 分环境配置
│   │   ├── base.py              # 基础配置
│   │   ├── development.py       # 开发环境
│   │   └── production.py        # 生产环境
│   ├── urls.py                  # URL路由
│   └── wsgi.py                  # WSGI配置
├── docs/                         # 项目文档
├── static/                       # 静态文件
├── templates/                    # 模板文件
├── requirements/                 # 依赖文件
└── manage.py                     # Django管理脚本
```

## 🔐 用户角色和权限

### 角色定义
- **管理员 (admin)** - 拥有所有权限，可以管理用户、科室和系统设置
- **科室人员 (staff)** - 只能访问基础功能，必须隶属于某个科室

### 权限控制
- **页面权限** - 基于角色的页面访问控制
- **API权限** - RESTful API的精细化权限管理
- **对象权限** - 数据级别的访问控制
- **会话安全** - 登录状态和IP地址验证

详细权限配置请参考：[权限控制文档](docs/PERMISSION_CONTROL_SUMMARY.md)

## 🔧 配置说明

### 环境变量
```bash
# 数据库配置
DB_NAME=medical_device_reporting
DB_USER=root
DB_PASSWORD=your_password
DB_HOST=localhost
DB_PORT=3306

# 安全配置
SECRET_KEY=your_secret_key
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/django.log
```

### 数据库配置
```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'medical_device_reporting',
        'USER': 'root',
        'PASSWORD': 'temper0201..',
        'HOST': 'localhost',
        'PORT': '3306',
        'OPTIONS': {
            'charset': 'utf8mb4',
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
        },
    }
}
```

## 🧪 测试

### 运行测试
```bash
# 运行所有测试
python manage.py test --settings=config.settings.development

# 运行特定测试模块
python test_user_management.py      # 用户管理功能测试
python test_permissions.py          # 权限控制测试
python test_apis.py                 # API接口测试
python test_frontend.py             # 前端功能测试
python test_authentication.py       # 认证系统测试
python test_services.py             # 业务逻辑测试
python test_selectors.py            # 数据查询测试

# 检查Django配置
python manage.py check --settings=config.settings.development
```

### 测试覆盖
- ✅ **用户管理功能** - 用户创建、编辑、删除、状态管理
- ✅ **科室管理功能** - 科室CRUD操作、Excel导入导出、用户分配
- ✅ **不良事件报告** - 报告创建、编辑、提交、审核流程
  - 字段要求验证（患者信息可选、产品编号/批号验证）
  - 安全性测试（用户权限、科室限制）
  - 表单提交测试（分步创建、数据完整性）
  - 中文化测试（界面显示、用户体验）
- ✅ **个人设置功能** - 个人信息编辑、偏好设置、密码修改
- ✅ **权限控制系统** - 角色权限、访问控制、API权限
- ✅ **API接口测试** - RESTful API的完整性和安全性
- ✅ **前端功能测试** - 页面渲染、表单提交、AJAX交互
- ✅ **模板渲染测试** - 模板内容、上下文变量、权限显示
- ✅ **集成测试** - 跨模块功能、完整工作流程
- ✅ **认证系统测试** - 登录、登出、会话管理
- ✅ **业务逻辑测试** - 服务层和选择器的功能验证
- ✅ **数据库操作** - 模型关系、数据完整性、事务处理

### 测试结果示例
```
=== 用户管理功能测试 ===
✅ 用户创建功能正常
✅ 用户编辑功能正常
✅ 用户删除功能正常
✅ 用户状态切换正常
✅ 批量操作功能正常

=== 权限控制测试 ===
✅ 未登录用户访问控制
✅ 管理员权限验证
✅ 科室人员权限限制
✅ API权限控制正常
```

## 📚 API文档

### 用户管理API
```
GET    /api/users/                 # 用户列表
POST   /api/users/create/          # 创建用户
GET    /api/users/{id}/            # 用户详情
PUT    /api/users/{id}/            # 更新用户
DELETE /api/users/{id}/            # 删除用户
POST   /api/users/{id}/activate/   # 激活用户
POST   /api/users/{id}/deactivate/ # 禁用用户
```

### 科室管理API
```
GET    /api/departments/           # 科室列表
POST   /api/departments/           # 创建科室
GET    /api/departments/{id}/      # 科室详情
PUT    /api/departments/{id}/      # 更新科室
DELETE /api/departments/{id}/      # 删除科室
POST   /api/departments/import/    # Excel导入
GET    /api/departments/export/    # Excel导出
GET    /api/departments/template/  # 下载模板
GET    /api/departments/check-code/ # 检查代码可用性
```

### 个人设置API
```
GET    /profile/                   # 个人信息查看
POST   /profile/edit/              # 个人信息编辑
GET    /settings/                  # 用户设置页面
POST   /settings/                  # 保存用户设置
POST   /change-password/           # 修改密码
```

详细API文档请参考：[API接口文档](docs/api_documentation.md)

## 🚀 部署

### 生产环境部署
1. **环境准备**
   ```bash
   pip install -r requirements/production.txt
   ```

2. **数据库配置**
   ```bash
   python manage.py migrate --settings=config.settings.production
   python manage.py collectstatic --settings=config.settings.production
   ```

3. **启动服务**
   ```bash
   gunicorn config.wsgi:application --settings=config.settings.production
   ```

### Docker部署
```bash
# 构建镜像
docker build -t medical-device-reporting .

# 运行容器
docker run -d -p 8000:8000 \
  -e DB_HOST=your_db_host \
  -e DB_PASSWORD=your_db_password \
  medical-device-reporting
```

### Nginx配置示例
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location /static/ {
        alias /path/to/staticfiles/;
        expires 30d;
    }

    location /media/ {
        alias /path/to/media/;
        expires 30d;
    }

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 🔍 故障排除

### 最新修复问题 (2025-06-21)

1. **URL路由错误**
   ```
   错误: django.urls.exceptions.NoReverseMatch: Reverse for 'step_create' not found
   解决: 添加分步创建入口URL和视图函数
   ```

2. **模板语法错误**
   ```
   错误: TemplateSyntaxError: 'block' tag with name 'page_heading' appears more than once
   解决: 删除templates/reports/base.html中重复的block定义
   ```

### 常见问题

3. **数据库连接失败**
   ```bash
   # 检查MySQL服务状态
   systemctl status mysql

   # 检查数据库配置
   python manage.py dbshell --settings=config.settings.development
   ```

4. **权限错误**
   ```bash
   # 重新初始化用户组和权限
   python manage.py shell --settings=config.settings.development
   >>> from apps.users.services import initialize_user_groups
   >>> initialize_user_groups()
   ```

5. **静态文件问题**
   ```bash
   # 重新收集静态文件
   python manage.py collectstatic --clear --settings=config.settings.development
   ```

6. **日志查看**
   ```bash
   # 查看应用日志
   tail -f logs/django.log

   # 查看错误日志
   grep ERROR logs/django.log
   ```

### 性能优化

1. **数据库优化**
   - 使用数据库连接池
   - 添加适当的索引
   - 优化查询语句

2. **缓存配置**
   ```python
   CACHES = {
       'default': {
           'BACKEND': 'django.core.cache.backends.redis.RedisCache',
           'LOCATION': 'redis://127.0.0.1:6379/1',
       }
   }
   ```

3. **静态文件优化**
   - 启用Gzip压缩
   - 使用CDN加速
   - 设置适当的缓存头

## 📖 文档

- [环境搭建指南](docs/environment_setup.md)
- [权限控制总结](docs/PERMISSION_CONTROL_SUMMARY.md)
- [URL路由配置](docs/URL_ROUTING_PERMISSIONS.md)
- [日志系统指南](docs/logging_guide.md)
- [项目验证报告](docs/project_verification_report.md)
- [故障排除指南](docs/TROUBLESHOOTING.md) - **已更新 (2025-06-21)**
- [用户状态切换修复报告](docs/USER_STATUS_TOGGLE_FIX.md)
- [科室Excel导入修复报告](docs/DEPARTMENT_EXCEL_IMPORT_FIX.md)
- [API接口文档](docs/API.md)
- [部署指南](docs/DEPLOYMENT.md)
- [测试文档](docs/TESTING.md)
- [功能特性文档](docs/FEATURES.md) - **已更新 (2025-06-21)**
- [最新修复报告 (2025-06-21)](docs/LATEST_FIXES_2025_06_21.md) - **新增**
- [最新修复报告 (2025-06-23)](docs/LATEST_FIXES_2025_06_23.md) - **新增**

## 🤝 贡献

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

- 项目维护者：[您的姓名]
- 邮箱：[您的邮箱]
- 项目链接：[项目仓库地址]

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户。

---

**注意**：这是一个医疗相关的系统，请确保在生产环境中遵循相关的医疗数据安全和隐私保护法规。
