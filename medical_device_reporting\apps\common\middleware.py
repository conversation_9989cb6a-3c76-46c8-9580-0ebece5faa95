"""
通用中间件
Common Middleware for Medical Device Reporting Platform
"""

import logging
from django.shortcuts import redirect
from django.contrib import messages
from django.urls import reverse
from django.http import JsonResponse
from django.utils.deprecation import MiddlewareMixin

logger = logging.getLogger(__name__)


class PermissionCheckMiddleware(MiddlewareMixin):
    """
    权限检查中间件
    自动检查用户权限，对于需要特定权限的页面进行访问控制
    """
    
    # 需要管理员权限的URL模式
    ADMIN_REQUIRED_PATTERNS = [
        'users:user_list',
        'users:user_create',
        'users:user_edit',
        'users:department_list',
        'users:department_create',
        'users:department_edit',
        'reports:pending_review',
        'reports:serious_events',
        'reports:report_review',
    ]
    
    # 需要登录的URL模式（排除登录页面本身）
    LOGIN_REQUIRED_PATTERNS = [
        'users:dashboard',
        'users:profile',
        'reports:dashboard',
        'reports:report_list',
        'reports:report_create',
        'reports:report_detail',
    ]
    
    # 公开访问的URL模式
    PUBLIC_PATTERNS = [
        'users:login',
        'users:logout',
        'admin:',
    ]
    
    def process_view(self, request, view_func, view_args, view_kwargs):
        """
        在视图执行前检查权限
        """
        # 获取当前URL名称
        url_name = request.resolver_match.url_name if request.resolver_match else None
        namespace = request.resolver_match.namespace if request.resolver_match else None
        
        if namespace and url_name:
            full_url_name = f"{namespace}:{url_name}"
        else:
            full_url_name = url_name or ''
        
        # 检查是否为公开页面
        if self._is_public_url(full_url_name):
            return None
        
        # 检查用户是否已登录
        if not request.user.is_authenticated:
            if self._requires_login(full_url_name):
                if request.is_ajax() or request.content_type == 'application/json':
                    return JsonResponse({'error': '请先登录'}, status=401)
                else:
                    messages.warning(request, '请先登录后访问')
                    return redirect('users:login')
            return None
        
        # 检查是否需要管理员权限
        if self._requires_admin(full_url_name):
            try:
                if not request.user.profile.is_admin:
                    if request.is_ajax() or request.content_type == 'application/json':
                        return JsonResponse({'error': '权限不足'}, status=403)
                    else:
                        messages.error(request, '您没有权限访问此页面')
                        return redirect('users:dashboard')
            except AttributeError:
                # 用户没有profile，重定向到登录页面
                if request.is_ajax() or request.content_type == 'application/json':
                    return JsonResponse({'error': '用户信息不完整'}, status=403)
                else:
                    messages.error(request, '用户信息不完整，请重新登录')
                    return redirect('users:login')
        
        return None
    
    def _is_public_url(self, url_name):
        """检查是否为公开URL"""
        for pattern in self.PUBLIC_PATTERNS:
            if url_name.startswith(pattern):
                return True
        return False
    
    def _requires_login(self, url_name):
        """检查是否需要登录"""
        for pattern in self.LOGIN_REQUIRED_PATTERNS:
            if url_name == pattern:
                return True
        return False
    
    def _requires_admin(self, url_name):
        """检查是否需要管理员权限"""
        for pattern in self.ADMIN_REQUIRED_PATTERNS:
            if url_name == pattern:
                return True
        return False


class UserActivityMiddleware(MiddlewareMixin):
    """
    用户活动记录中间件
    记录用户的访问活动，用于统计和审计
    """
    
    def process_request(self, request):
        """
        记录用户访问活动
        """
        if request.user.is_authenticated:
            try:
                # 更新用户最后活动时间
                from django.utils import timezone
                request.user.profile.last_activity = timezone.now()
                request.user.profile.save(update_fields=['last_activity'])
            except AttributeError:
                # 用户没有profile，跳过
                pass
            except Exception as e:
                # 记录错误但不影响正常流程
                logger.warning(f'更新用户活动时间失败: {str(e)}')
        
        return None


class SecurityHeadersMiddleware(MiddlewareMixin):
    """
    安全头部中间件
    添加安全相关的HTTP头部
    """
    
    def process_response(self, request, response):
        """
        添加安全头部
        """
        # 防止点击劫持
        response['X-Frame-Options'] = 'DENY'
        
        # 防止MIME类型嗅探
        response['X-Content-Type-Options'] = 'nosniff'
        
        # XSS保护
        response['X-XSS-Protection'] = '1; mode=block'
        
        # 引用策略
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        
        # 内容安全策略（开发环境相对宽松）
        if not hasattr(request, 'is_secure') or not request.is_secure():
            response['Content-Security-Policy'] = (
                "default-src 'self' 'unsafe-inline' 'unsafe-eval' "
                "https://cdn.jsdelivr.net https://cdnjs.cloudflare.com "
                "https://fonts.googleapis.com https://fonts.gstatic.com; "
                "img-src 'self' data: https:; "
                "font-src 'self' https://fonts.gstatic.com;"
            )
        
        return response


class ErrorHandlingMiddleware(MiddlewareMixin):
    """
    错误处理中间件
    统一处理应用程序错误
    """
    
    def process_exception(self, request, exception):
        """
        处理异常
        """
        # 记录错误
        logger.error(f'应用程序错误: {str(exception)}', exc_info=True)
        
        # 对于Ajax请求，返回JSON错误响应
        if request.is_ajax() or request.content_type == 'application/json':
            return JsonResponse({
                'error': '服务器内部错误',
                'message': str(exception) if hasattr(exception, 'message') else '未知错误'
            }, status=500)
        
        # 对于普通请求，让Django的默认错误处理器处理
        return None
