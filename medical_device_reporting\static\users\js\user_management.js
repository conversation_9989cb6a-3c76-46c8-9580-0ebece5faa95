/**
 * User Management JavaScript for Medical Device Reporting Platform
 * 医疗器械不良事件上报平台用户管理脚本
 */

document.addEventListener('DOMContentLoaded', function() {
    // 版本标识 - 确保文件已更新
    console.log('🔧 用户管理JS已加载 - 版本: 2024-06-20-18:05 - 使用Django视图');

    // 全局变量
    let userTable;
    let selectedUsers = [];

    // 初始化用户管理
    initializeUserManagement();
    
    /**
     * 初始化用户管理
     */
    function initializeUserManagement() {
        // 初始化DataTables
        initializeDataTable();
        
        // 绑定事件监听器
        bindEventListeners();
        
        // 初始化工具提示
        initializeTooltips();
        
        console.log('用户管理初始化完成');
    }
    
    /**
     * 初始化DataTables
     */
    function initializeDataTable() {
        console.log('开始初始化DataTables...');

        // 检查表格元素是否存在
        const tableElement = $('#userTable');
        if (tableElement.length === 0) {
            console.error('找不到#userTable元素');
            return;
        }
        console.log('找到表格元素:', tableElement);

        userTable = $('#userTable').DataTable({
            processing: true,
            serverSide: true,
            ajax: {
                url: '/api/users/',
                type: 'GET',
                data: function(d) {
                    // console.log('DataTables发送请求，原始参数:', d);

                    try {
                        // 添加筛选参数
                        d.department = $('#departmentFilter').val();
                        d.role = $('#roleFilter').val();
                        d.is_active = $('#statusFilter').val();
                        d.search = $('#searchInput').val();

                        // 转换DataTables参数为Django REST framework参数
                        const params = {
                            page: Math.floor(d.start / d.length) + 1,
                            page_size: d.length,
                            search: d.search.value || d.search || '',
                            ordering: getOrderingParam(d.order, d.columns)
                        };

                        // 只添加非空的筛选参数
                        if (d.department && d.department !== '') {
                            params.department_id = d.department;
                        }
                        if (d.role && d.role !== '') {
                            params.role = d.role;
                        }
                        if (d.is_active && d.is_active !== '') {
                            params.is_active = d.is_active;
                        }

                        // console.log('转换后的API参数:', params);
                        return params;

                    } catch (error) {
                        console.error('处理DataTables参数时出错:', error);
                        return {
                            page: 1,
                            page_size: 25
                        };
                    }
                },
                dataSrc: function(json) {
                    // console.log('API响应数据:', json);

                    // 检查响应格式
                    if (!json || typeof json !== 'object') {
                        console.error('API响应格式错误:', json);
                        return [];
                    }

                    // 转换Django REST framework响应为DataTables格式
                    const recordsTotal = json.count || 0;
                    const results = json.results || [];

                    // 设置DataTables需要的属性
                    json.recordsTotal = recordsTotal;
                    json.recordsFiltered = recordsTotal;

                    console.log(`DataTables加载了 ${results.length} 条记录，总共 ${recordsTotal} 条`);

                    return results;
                },
                error: function(xhr, error, thrown) {
                    console.error('AJAX请求失败:', error, thrown);
                    console.error('响应状态:', xhr.status);
                    console.error('响应内容:', xhr.responseText);
                }
            },
            columns: [
                {
                    data: null,
                    orderable: false,
                    searchable: false,
                    render: function(data, type, row) {
                        return `<div class="form-check">
                            <input class="form-check-input user-checkbox" type="checkbox" value="${row.id}">
                        </div>`;
                    }
                },
                {
                    data: 'account_number',
                    render: function(data, type, row) {
                        return `<strong>${data}</strong>`;
                    }
                },
                {
                    data: 'display_name',
                    render: function(data, type, row) {
                        return data || '-';
                    }
                },
                {
                    data: 'email',
                    render: function(data, type, row) {
                        return data || '-';
                    }
                },
                {
                    data: 'department_name',
                    render: function(data, type, row) {
                        return data || '<span class="text-muted">未分配</span>';
                    }
                },
                {
                    data: 'role_display',
                    render: function(data, type, row) {
                        const roleClass = row.role === 'admin' ? 'role-admin' : 'role-staff';
                        return `<span class="role-badge ${roleClass}">${data}</span>`;
                    }
                },
                {
                    data: 'is_active',
                    render: function(data, type, row) {
                        const statusClass = data ? 'status-active' : 'status-inactive';
                        const statusText = data ? '正常' : '禁用';
                        return `<span class="status-badge ${statusClass}">${statusText}</span>`;
                    }
                },
                {
                    data: 'last_login',
                    render: function(data, type, row) {
                        if (data) {
                            const date = new Date(data);
                            return date.toLocaleString('zh-CN');
                        }
                        return '<span class="text-muted">从未登录</span>';
                    }
                },
                {
                    data: null,
                    orderable: false,
                    searchable: false,
                    render: function(data, type, row) {
                        return `
                            <div class="action-buttons">
                                <button class="action-btn btn-view" onclick="viewUser(${row.id})" title="查看详情">
                                    <i class="bi bi-eye"></i>
                                </button>
                                <button class="action-btn btn-edit" onclick="editUser(${row.id})" title="编辑用户">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button class="action-btn btn-toggle" onclick="toggleUserStatus(${row.id}, ${!row.is_active})" title="${row.is_active ? '禁用' : '启用'}用户">
                                    <i class="bi bi-${row.is_active ? 'x-circle' : 'check-circle'}"></i>
                                </button>
                            </div>
                        `;
                    }
                }
            ],
            order: [[1, 'asc']], // 按账号列排序
            pageLength: 25,
            lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
            language: {
                "sProcessing": "处理中...",
                "sLengthMenu": "显示 _MENU_ 项结果",
                "sZeroRecords": "没有匹配结果",
                "sInfo": "显示第 _START_ 至 _END_ 项结果，共 _TOTAL_ 项",
                "sInfoEmpty": "显示第 0 至 0 项结果，共 0 项",
                "sInfoFiltered": "(由 _MAX_ 项结果过滤)",
                "sInfoPostFix": "",
                "sSearch": "搜索:",
                "sUrl": "",
                "sEmptyTable": "表中数据为空",
                "sLoadingRecords": "载入中...",
                "sInfoThousands": ",",
                "oPaginate": {
                    "sFirst": "首页",
                    "sPrevious": "上页",
                    "sNext": "下页",
                    "sLast": "末页"
                },
                "oAria": {
                    "sSortAscending": ": 以升序排列此列",
                    "sSortDescending": ": 以降序排列此列"
                }
            },
            responsive: true,
            dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>rtip',
            drawCallback: function() {
                console.log('DataTables drawCallback 被调用');
                // 重新初始化工具提示
                initializeTooltips();

                // 更新选中状态
                updateSelectedUsers();
            },
            initComplete: function() {
                console.log('DataTables 初始化完成');
                console.log('表格行数:', this.api().rows().count());
            }
        });
    }
    
    /**
     * 获取排序参数
     */
    function getOrderingParam(order, columns) {
        if (order && order.length > 0) {
            const orderCol = order[0];
            const column = columns[orderCol.column];

            // 检查列是否存在且有data属性
            if (!column || !column.data) {
                return '';
            }

            const columnName = column.data;
            const direction = orderCol.dir === 'desc' ? '-' : '';

            // 映射列名到API字段名
            const fieldMapping = {
                'account_number': 'account_number',
                'display_name': 'user__first_name',
                'email': 'user__email',
                'department_name': 'department__name',
                'role_display': 'role',
                'is_active': 'is_active',
                'last_login': 'user__last_login'
            };

            const field = fieldMapping[columnName] || columnName;
            return direction + field;
        }
        return '';
    }
    
    /**
     * 绑定事件监听器
     */
    function bindEventListeners() {
        // 筛选表单变化事件
        $('#departmentFilter, #roleFilter, #statusFilter').on('change', function() {
            userTable.ajax.reload();
        });
        
        // 搜索输入事件
        $('#searchInput').on('input', debounce(function() {
            userTable.ajax.reload();
        }, 500));
        
        // 清除搜索按钮
        $('#clearSearch').on('click', function() {
            $('#searchInput').val('');
            userTable.ajax.reload();
        });
        
        // 全选复选框
        $('#selectAll').on('change', function() {
            const isChecked = this.checked;
            $('.user-checkbox').prop('checked', isChecked);
            updateSelectedUsers();
        });
        
        // 用户复选框变化
        $(document).on('change', '.user-checkbox', function() {
            updateSelectedUsers();
        });
        
        // 刷新按钮
        $('#refreshBtn').on('click', function() {
            userTable.ajax.reload();
            showSuccess('数据已刷新');
        });
        
        // 导出按钮
        $('#exportBtn').on('click', function() {
            exportUsers();
        });
        
        // 批量操作按钮
        $('#bulkActivateBtn').on('click', function() {
            bulkAction('activate');
        });

        $('#bulkDeactivateBtn').on('click', function() {
            bulkAction('deactivate');
        });

        // 用户详情弹窗中的编辑按钮
        $('#editUserBtn').on('click', function() {
            const userId = $(this).data('user-id');
            if (userId) {
                editUser(userId);
            } else {
                showError('无法获取用户ID');
            }
        });
    }
    
    /**
     * 更新选中用户
     */
    function updateSelectedUsers() {
        selectedUsers = [];
        $('.user-checkbox:checked').each(function() {
            selectedUsers.push(parseInt($(this).val()));
        });
        
        // 更新批量操作工具栏
        if (selectedUsers.length > 0) {
            $('.bulk-actions-toolbar').show();
            $('.selected-count strong').text(selectedUsers.length);
        } else {
            $('.bulk-actions-toolbar').hide();
        }
        
        // 更新全选复选框状态
        const totalCheckboxes = $('.user-checkbox').length;
        const checkedCheckboxes = $('.user-checkbox:checked').length;
        
        if (checkedCheckboxes === 0) {
            $('#selectAll').prop('indeterminate', false).prop('checked', false);
        } else if (checkedCheckboxes === totalCheckboxes) {
            $('#selectAll').prop('indeterminate', false).prop('checked', true);
        } else {
            $('#selectAll').prop('indeterminate', true);
        }
    }
    
    /**
     * 初始化工具提示
     */
    function initializeTooltips() {
        if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }
    }
    
    /**
     * 查看用户详情
     */
    function viewUser(userId) {
        fetch(`/api/users/${userId}/`)
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showError(data.error);
                    return;
                }

                // 构建用户详情HTML
                const detailHtml = `
                    <div class="user-detail-item">
                        <span class="user-detail-label">账号:</span>
                        <span class="user-detail-value">${data.account_number}</span>
                    </div>
                    <div class="user-detail-item">
                        <span class="user-detail-label">用户名:</span>
                        <span class="user-detail-value">${data.username}</span>
                    </div>
                    <div class="user-detail-item">
                        <span class="user-detail-label">姓名:</span>
                        <span class="user-detail-value">${data.display_name || '-'}</span>
                    </div>
                    <div class="user-detail-item">
                        <span class="user-detail-label">邮箱:</span>
                        <span class="user-detail-value">${data.email || '-'}</span>
                    </div>
                    <div class="user-detail-item">
                        <span class="user-detail-label">科室:</span>
                        <span class="user-detail-value">${data.department_name || '未分配'}</span>
                    </div>
                    <div class="user-detail-item">
                        <span class="user-detail-label">角色:</span>
                        <span class="user-detail-value">${data.role_display}</span>
                    </div>
                    <div class="user-detail-item">
                        <span class="user-detail-label">状态:</span>
                        <span class="user-detail-value">
                            <span class="status-badge ${data.is_active ? 'status-active' : 'status-inactive'}">
                                ${data.is_active ? '正常' : '禁用'}
                            </span>
                        </span>
                    </div>
                    <div class="user-detail-item">
                        <span class="user-detail-label">最后登录:</span>
                        <span class="user-detail-value">${data.last_login ? new Date(data.last_login).toLocaleString('zh-CN') : '从未登录'}</span>
                    </div>
                    <div class="user-detail-item">
                        <span class="user-detail-label">创建时间:</span>
                        <span class="user-detail-value">${new Date(data.created_at).toLocaleString('zh-CN')}</span>
                    </div>
                `;

                $('#userDetailContent').html(detailHtml);
                $('#editUserBtn').data('user-id', userId);

                const modal = new bootstrap.Modal(document.getElementById('userDetailModal'));
                modal.show();
            })
            .catch(error => {
                console.error('获取用户详情失败:', error);
                showError('获取用户详情失败');
            });
    }

    /**
     * 编辑用户
     */
    function editUser(userId) {
        window.location.href = `/users/${userId}/edit/`;
    }

    /**
     * 切换用户状态
     */
    function toggleUserStatus(userId, activate) {
        const action = activate ? '启用' : '禁用';
        // 使用传统的Django视图而不是DRF API
        const url = `/users/${userId}/toggle-status/`;

        console.log(`准备${action}用户 ${userId}，URL: ${url}`);

        showConfirm(`确定要${action}该用户吗？`, function() {
            const csrfToken = getCsrfToken();
            console.log('CSRF Token:', csrfToken ? `已获取 (长度: ${csrfToken.length})` : '未获取');
            console.log('CSRF Token值:', csrfToken);

            fetch(url, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': csrfToken,
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                // 也可以通过body发送CSRF token
                body: `csrfmiddlewaretoken=${encodeURIComponent(csrfToken)}`
            })
            .then(response => {
                console.log(`API响应状态: ${response.status}`);
                if (!response.ok) {
                    // 处理HTTP错误状态
                    return response.json().then(errorData => {
                        console.error('API错误响应:', errorData);
                        throw new Error(errorData.error || `HTTP ${response.status}`);
                    }).catch(() => {
                        // 如果不是JSON响应，抛出通用错误
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    });
                }
                return response.json();
            })
            .then(data => {
                console.log('API成功响应:', data);

                // 检查是否有错误字段
                if (data.error) {
                    showError(data.error);
                    return;
                }

                // 检查是否成功
                if (data.success) {
                    showSuccess(data.message || `用户${action}成功`);
                    userTable.ajax.reload(null, false);
                } else {
                    showError(`用户状态更新失败`);
                    userTable.ajax.reload(null, false);
                }
            })
            .catch(error => {
                console.error(`${action}用户失败:`, error);
                showError(error.message || `${action}用户失败`);
            });
        });
    }

    /**
     * 批量操作
     */
    function bulkAction(action) {
        if (selectedUsers.length === 0) {
            showError('请先选择要操作的用户');
            return;
        }

        const actionText = action === 'activate' ? '启用' : '禁用';

        showConfirm(`确定要批量${actionText} ${selectedUsers.length} 个用户吗？`, function() {
            fetch('/api/users/bulk-action/', {
                method: 'POST',
                headers: {
                    'X-CSRFToken': getCsrfToken(),
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    user_ids: selectedUsers,
                    action: action
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showError(data.error);
                    return;
                }

                showSuccess(`批量${actionText}完成: 成功 ${data.success_count} 个，失败 ${data.failed_count} 个`);
                userTable.ajax.reload(null, false);

                // 清除选择
                selectedUsers = [];
                $('.user-checkbox').prop('checked', false);
                $('#selectAll').prop('checked', false);
                $('.bulk-actions-toolbar').hide();
            })
            .catch(error => {
                console.error(`批量${actionText}失败:`, error);
                showError(`批量${actionText}失败`);
            });
        });
    }

    /**
     * 导出用户数据
     */
    function exportUsers() {
        // 构建导出参数
        const params = new URLSearchParams({
            department: $('#departmentFilter').val(),
            role: $('#roleFilter').val(),
            is_active: $('#statusFilter').val(),
            search: $('#searchInput').val(),
            export: 'csv'
        });

        // 创建下载链接
        const url = `/api/users/?${params.toString()}`;
        const link = document.createElement('a');
        link.href = url;
        link.download = `users_${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        showSuccess('导出任务已开始');
    }

    /**
     * 显示成功消息
     */
    function showSuccess(message) {
        showToast(message, 'success');
    }

    /**
     * 显示错误消息
     */
    function showError(message) {
        showToast(message, 'danger');
    }

    /**
     * 显示确认对话框
     */
    function showConfirm(message, callback) {
        $('#confirmMessage').text(message);

        const modal = new bootstrap.Modal(document.getElementById('confirmModal'));
        modal.show();

        // 绑定确认按钮事件
        $('#confirmBtn').off('click').on('click', function() {
            modal.hide();
            if (callback) callback();
        });
    }

    /**
     * 显示Toast消息
     */
    function showToast(message, type = 'info') {
        const toastContainer = getOrCreateToastContainer();

        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type} border-0`;
        toast.setAttribute('role', 'alert');
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <i class="bi bi-check-circle me-2"></i>
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;

        toastContainer.appendChild(toast);

        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();

        toast.addEventListener('hidden.bs.toast', () => {
            toastContainer.removeChild(toast);
        });
    }

    /**
     * 获取或创建Toast容器
     */
    function getOrCreateToastContainer() {
        let container = document.querySelector('.toast-container');

        if (!container) {
            container = document.createElement('div');
            container.className = 'toast-container position-fixed top-0 end-0 p-3';
            container.style.zIndex = '1055';
            document.body.appendChild(container);
        }

        return container;
    }

    /**
     * 获取CSRF Token
     */
    function getCsrfToken() {
        // 尝试多种方式获取CSRF token
        let token = '';

        // 方式1: 从meta标签获取
        const metaToken = document.querySelector('meta[name=csrf-token]')?.getAttribute('content');
        if (metaToken) {
            token = metaToken;
            console.log('从meta标签获取CSRF token:', token.substring(0, 10) + '...');
        }

        // 方式2: 从隐藏表单字段获取
        if (!token) {
            const inputToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value;
            if (inputToken) {
                token = inputToken;
                console.log('从表单字段获取CSRF token:', token.substring(0, 10) + '...');
            }
        }

        // 方式3: 从cookie获取
        if (!token) {
            const cookieToken = getCookieValue('csrftoken');
            if (cookieToken) {
                token = cookieToken;
                console.log('从cookie获取CSRF token:', token.substring(0, 10) + '...');
            }
        }

        if (!token) {
            console.error('无法获取CSRF token！');
        }

        return token;
    }

    /**
     * 从cookie获取值
     */
    function getCookieValue(name) {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) return parts.pop().split(';').shift();
        return null;
    }

    /**
     * 防抖函数
     */
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // 导出全局函数供HTML调用
    window.viewUser = viewUser;
    window.editUser = editUser;
    window.toggleUserStatus = toggleUserStatus;
    window.bulkAction = bulkAction;
    window.exportUsers = exportUsers;
    window.showSuccess = showSuccess;
    window.showError = showError;
    window.showConfirm = showConfirm;
    window.debounce = debounce;
});
