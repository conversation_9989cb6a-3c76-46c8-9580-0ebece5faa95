{% extends 'reports/base.html' %}
{% load static %}

{% block page_title %}报告详情{% endblock %}
{% block page_heading %}报告详情{% endblock %}
{% block page_description %}{{ report.report_number }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'reports:report_list' %}">报告列表</a></li>
<li class="breadcrumb-item active">{{ report.report_number }}</li>
{% endblock %}

{% block page_actions %}
<div class="btn-group" role="group">
    {% if can_edit %}
    <a href="{% url 'reports:report_edit' report_id=report.id %}" class="btn btn-outline-primary">
        <i class="bi bi-pencil me-2"></i>
        编辑
    </a>
    {% endif %}
    {% if can_submit %}
    <button type="button" class="btn btn-success" id="submitBtn" data-report-id="{{ report.id }}">
        <i class="bi bi-send me-2"></i>
        提交
    </button>
    {% endif %}
    {% if can_review %}
    <a href="{% url 'reports:report_review' report_id=report.id %}" class="btn btn-warning">
        <i class="bi bi-eye-fill me-2"></i>
        审核
    </a>
    {% endif %}
    <button type="button" class="btn btn-outline-secondary" onclick="window.print()">
        <i class="bi bi-printer me-2"></i>
        打印
    </button>
</div>
{% endblock %}

{% block reports_content %}
<div class="row">
    <!-- 主要内容 -->
    <div class="col-lg-8">
        <!-- 基本信息 -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle me-2"></i>
                    基本信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="info-item">
                            <label>报告编号：</label>
                            <span class="fw-bold">{{ report.report_number }}</span>
                        </div>
                        <div class="info-item">
                            <label>上报人：</label>
                            <span>{{ report.reporter.display_name }}</span>
                        </div>
                        <div class="info-item">
                            <label>科室：</label>
                            <span>{{ report.department.name|default:"未指定" }}</span>
                        </div>
                        <div class="info-item">
                            <label>联系电话：</label>
                            <span>{{ report.reporter_phone|default:"未填写" }}</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-item">
                            <label>状态：</label>
                            <span class="badge bg-{% if report.status == 'approved' %}success{% elif report.status == 'rejected' %}danger{% elif report.status == 'under_review' %}warning{% elif report.status == 'submitted' %}info{% else %}secondary{% endif %} fs-6">
                                {{ report.get_status_display }}
                            </span>
                        </div>
                        <div class="info-item">
                            <label>创建时间：</label>
                            <span>{{ report.created_at|date:"Y-m-d H:i:s" }}</span>
                        </div>
                        <div class="info-item">
                            <label>更新时间：</label>
                            <span>{{ report.updated_at|date:"Y-m-d H:i:s" }}</span>
                        </div>
                        {% if report.submitted_at %}
                        <div class="info-item">
                            <label>提交时间：</label>
                            <span>{{ report.submitted_at|date:"Y-m-d H:i:s" }}</span>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- 患者信息 -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="bi bi-person me-2"></i>
                    患者信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="info-item">
                            <label>患者姓名：</label>
                            <span>{{ report.patient_name }}</span>
                        </div>
                        <div class="info-item">
                            <label>年龄：</label>
                            <span>{{ report.patient_age }}岁</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-item">
                            <label>性别：</label>
                            <span>{{ report.get_patient_gender_display }}</span>
                        </div>
                        <div class="info-item">
                            <label>联系方式：</label>
                            <span>{{ report.patient_contact|default:"未填写" }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 事件信息 -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-warning text-dark">
                <h5 class="card-title mb-0">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    事件信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="info-item">
                            <label>器械故障：</label>
                            <span class="badge bg-{% if report.device_malfunction %}danger{% else %}success{% endif %}">
                                {% if report.device_malfunction %}是{% else %}否{% endif %}
                            </span>
                        </div>
                        <div class="info-item">
                            <label>事件日期：</label>
                            <span>{{ report.event_date|date:"Y-m-d" }}</span>
                        </div>
                        <div class="info-item">
                            <label>伤害程度：</label>
                            <span class="badge bg-{% if report.injury_level == 'death' %}danger{% elif report.injury_level == 'serious_injury' %}warning{% else %}secondary{% endif %} fs-6">
                                {{ report.get_injury_level_display }}
                            </span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        {% if report.injury_description %}
                        <div class="info-item">
                            <label>伤害描述：</label>
                            <div class="mt-1">
                                <p class="text-muted">{{ report.injury_description }}</p>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="info-item">
                            <label>事件描述：</label>
                            <div class="mt-2">
                                <div class="border rounded p-3 bg-light">
                                    {{ report.event_description|linebreaks }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {% if report.initial_cause_analysis %}
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="info-item">
                            <label>初步原因分析：</label>
                            <div class="mt-2">
                                <div class="border rounded p-3 bg-light">
                                    {{ report.initial_cause_analysis|linebreaks }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}

                {% if report.initial_treatment %}
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="info-item">
                            <label>初步处理措施：</label>
                            <div class="mt-2">
                                <div class="border rounded p-3 bg-light">
                                    {{ report.initial_treatment|linebreaks }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- 器械信息 -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="bi bi-gear me-2"></i>
                    医疗器械信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="info-item">
                            <label>器械名称：</label>
                            <span class="fw-bold">{{ report.device_name }}</span>
                        </div>
                        <div class="info-item">
                            <label>注册证号：</label>
                            <span>{{ report.registration_number }}</span>
                        </div>
                        <div class="info-item">
                            <label>生产企业：</label>
                            <span>{{ report.manufacturer }}</span>
                        </div>
                        <div class="info-item">
                            <label>规格型号：</label>
                            <span>{{ report.specification|default:"未填写" }}</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-item">
                            <label>产品型号：</label>
                            <span>{{ report.model|default:"未填写" }}</span>
                        </div>
                        <div class="info-item">
                            <label>产品编号：</label>
                            <span>{{ report.product_number|default:"未填写" }}</span>
                        </div>
                        <div class="info-item">
                            <label>批号：</label>
                            <span>{{ report.batch_number|default:"未填写" }}</span>
                        </div>
                        <div class="info-item">
                            <label>生产日期：</label>
                            <span>{{ report.production_date|date:"Y-m-d"|default:"未填写" }}</span>
                        </div>
                        <div class="info-item">
                            <label>有效期至：</label>
                            <span>{{ report.expiry_date|date:"Y-m-d"|default:"未填写" }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 侧边栏 -->
    <div class="col-lg-4">
        <!-- 操作历史 -->
        {% if report.status != 'draft' %}
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-secondary text-white">
                <h6 class="card-title mb-0">
                    <i class="bi bi-clock-history me-2"></i>
                    操作历史
                </h6>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-primary"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">创建报告</h6>
                            <p class="timeline-text">{{ report.created_by.userprofile.display_name|default:report.created_by.username }}</p>
                            <small class="timeline-time">{{ report.created_at|date:"Y-m-d H:i" }}</small>
                        </div>
                    </div>
                    
                    {% if report.submitted_at %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-info"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">提交报告</h6>
                            <p class="timeline-text">{{ report.submitted_by.userprofile.display_name|default:report.submitted_by.username|default:report.created_by.userprofile.display_name|default:report.created_by.username }}</p>
                            <small class="timeline-time">{{ report.submitted_at|date:"Y-m-d H:i" }}</small>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if report.reviewed_at %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-{% if report.status == 'approved' %}success{% else %}danger{% endif %}"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">审核完成</h6>
                            <p class="timeline-text">{{ report.reviewed_by.userprofile.display_name|default:report.reviewed_by.username }}</p>
                            <small class="timeline-time">{{ report.reviewed_at|date:"Y-m-d H:i" }}</small>
                            {% if report.review_comments %}
                            <div class="mt-2">
                                <small class="text-muted">{{ report.review_comments }}</small>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}

        <!-- 快速操作 -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-light">
                <h6 class="card-title mb-0">
                    <i class="bi bi-lightning me-2"></i>
                    快速操作
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'reports:report_list' %}" class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-list me-2"></i>
                        返回列表
                    </a>
                    {% if can_edit %}
                    <a href="{% url 'reports:report_edit' report_id=report.id %}" class="btn btn-outline-secondary btn-sm">
                        <i class="bi bi-pencil me-2"></i>
                        编辑报告
                    </a>
                    {% endif %}
                    <button type="button" class="btn btn-outline-info btn-sm" onclick="window.print()">
                        <i class="bi bi-printer me-2"></i>
                        打印报告
                    </button>
                    <a href="{% url 'reports:report_create' %}" class="btn btn-outline-success btn-sm">
                        <i class="bi bi-plus-circle me-2"></i>
                        新建报告
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 提交确认模态框 -->
<div class="modal fade" id="submitModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-send me-2"></i>
                    确认提交
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要提交此报告吗？提交后将无法修改。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmSubmitBtn">确认提交</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

<!-- 隐藏的CSRF token表单 -->
<form style="display: none;">
    {% csrf_token %}
</form>

{% block reports_extra_css %}
<style>
.info-item {
    display: flex;
    margin-bottom: 12px;
    align-items: flex-start;
}
.info-item label {
    min-width: 120px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 0;
}
.info-item span, .info-item div {
    flex: 1;
}

.timeline {
    position: relative;
    padding-left: 30px;
}
.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}
.timeline-item {
    position: relative;
    margin-bottom: 20px;
}
.timeline-marker {
    position: absolute;
    left: -37px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #dee2e6;
}
.timeline-title {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 4px;
}
.timeline-text {
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 2px;
}
.timeline-time {
    font-size: 0.8rem;
    color: #adb5bd;
}

@media print {
    .btn, .card-header, .timeline, .page-header, nav {
        display: none !important;
    }
    .card {
        border: none !important;
        box-shadow: none !important;
    }
}
</style>
{% endblock %}

{% block reports_extra_js %}
<script>
$(document).ready(function() {
    // 获取CSRF token的函数
    function getCSRFToken() {
        // 方法1: 从隐藏表单获取
        let token = $('[name=csrfmiddlewaretoken]').val();
        if (token) return token;

        // 方法2: 从cookie获取
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, 10) === 'csrftoken=') {
                    cookieValue = decodeURIComponent(cookie.substring(10));
                    break;
                }
            }
        }
        return cookieValue;
    }

    // 提交报告
    $('#submitBtn').on('click', function() {
        $('#submitModal').modal('show');
    });

    $('#confirmSubmitBtn').on('click', function() {
        const reportId = $('#submitBtn').data('report-id');
        const csrfToken = getCSRFToken();

        if (!csrfToken) {
            alert('安全验证失败，请刷新页面重试');
            return;
        }

        $.ajax({
            url: `/reports/${reportId}/submit/`,
            type: 'POST',
            headers: {
                'X-CSRFToken': csrfToken
            },
            success: function(response) {
                $('#submitModal').modal('hide');
                location.reload();
            },
            error: function(xhr) {
                let errorMsg = '未知错误';
                if (xhr.responseJSON && xhr.responseJSON.error) {
                    errorMsg = xhr.responseJSON.error;
                } else if (xhr.status === 403) {
                    errorMsg = '权限不足或安全验证失败';
                } else if (xhr.status === 404) {
                    errorMsg = '报告不存在';
                } else if (xhr.status === 500) {
                    errorMsg = '服务器内部错误';
                }
                alert('提交失败：' + errorMsg);
            }
        });
    });
});
</script>
{% endblock %}
