"""
用户管理服务层
User Management Services for Medical Device Reporting Platform
"""

from typing import Optional, Dict, Any
from django.db import transaction
from django.contrib.auth.models import User, Group
from django.core.exceptions import ValidationError
from django.utils import timezone
import logging

from apps.common.exceptions import (
    BusinessLogicError,
    DataValidationError,
    ResourceNotFoundError
)
from apps.common.utils import ValidationUtils
from .models import UserProfile, Department

logger = logging.getLogger('apps.users')


@transaction.atomic
def user_create(
    *,
    account_number: str,
    username: str,
    first_name: str = '',
    last_name: str = '',
    email: str = '',
    department_id: Optional[int] = None,
    role: str = 'staff',

    is_active: bool = True,
    created_by: Optional[User] = None
) -> UserProfile:
    """
    创建用户
    
    Args:
        account_number: 4位数账号
        username: 用户名
        first_name: 名
        last_name: 姓
        email: 邮箱
        department_id: 科室ID
        role: 用户角色

        is_active: 是否启用
        created_by: 创建者
        
    Returns:
        UserProfile: 创建的用户配置文件
        
    Raises:
        DataValidationError: 数据验证失败
        BusinessLogicError: 业务逻辑错误
    """
    
    # 验证4位数账号格式
    if not ValidationUtils.validate_account_number(account_number):
        raise DataValidationError('账号必须是4位数字')
    
    # 检查账号唯一性
    if UserProfile.objects.filter(account_number=account_number).exists():
        raise BusinessLogicError(f'账号 {account_number} 已存在')
    
    # 检查用户名唯一性
    if User.objects.filter(username=username).exists():
        raise BusinessLogicError(f'用户名 {username} 已存在')
    
    # 验证科室
    department = None
    if department_id:
        try:
            department = Department.objects.get(id=department_id, is_active=True)
        except Department.DoesNotExist:
            raise ResourceNotFoundError(f'科室 ID {department_id} 不存在或已禁用')
    
    # 验证角色和科室的关系
    if role == 'staff' and not department:
        raise DataValidationError('科室人员必须指定所属科室')
    
    # 验证邮箱格式
    if email and not ValidationUtils.validate_email(email):
        raise DataValidationError('邮箱格式不正确')
    

    
    try:
        # 创建Django用户
        user = User.objects.create_user(
            username=username,
            email=email,
            first_name=first_name,
            last_name=last_name,
            is_active=is_active
        )
        
        # 创建用户配置文件
        user_profile = UserProfile.objects.create(
            user=user,
            account_number=account_number,
            department=department,
            role=role,

            is_active=is_active,
            created_by=created_by
        )
        
        # 分配用户组
        _assign_user_group(user, role)
        
        logger.info(
            f'用户创建成功: {account_number} - {username}',
            extra={
                'account_number': account_number,
                'username': username,
                'role': role,
                'department_id': department_id,
                'created_by': created_by.username if created_by else None
            }
        )
        
        return user_profile
        
    except Exception as e:
        logger.error(f'用户创建失败: {str(e)}')
        raise BusinessLogicError(f'用户创建失败: {str(e)}')


@transaction.atomic
def user_update(
    *,
    user_profile_id: int,
    username: Optional[str] = None,
    first_name: Optional[str] = None,
    last_name: Optional[str] = None,
    email: Optional[str] = None,
    department_id: Optional[int] = None,
    role: Optional[str] = None,

    is_active: Optional[bool] = None,
    updated_by: Optional[User] = None
) -> UserProfile:
    """
    更新用户信息
    
    Args:
        user_profile_id: 用户配置文件ID
        username: 用户名
        first_name: 名
        last_name: 姓
        email: 邮箱
        department_id: 科室ID
        role: 用户角色

        is_active: 是否启用
        updated_by: 更新者
        
    Returns:
        UserProfile: 更新后的用户配置文件
        
    Raises:
        ResourceNotFoundError: 用户不存在
        DataValidationError: 数据验证失败
        BusinessLogicError: 业务逻辑错误
    """
    
    try:
        user_profile = UserProfile.objects.select_related('user', 'department').get(
            id=user_profile_id
        )
    except UserProfile.DoesNotExist:
        raise ResourceNotFoundError(f'用户配置文件 ID {user_profile_id} 不存在')
    
    user = user_profile.user
    
    # 更新Django用户信息
    if username is not None:
        if User.objects.filter(username=username).exclude(id=user.id).exists():
            raise BusinessLogicError(f'用户名 {username} 已存在')
        user.username = username
    
    if first_name is not None:
        user.first_name = first_name
    
    if last_name is not None:
        user.last_name = last_name
    
    if email is not None:
        if email and not ValidationUtils.validate_email(email):
            raise DataValidationError('邮箱格式不正确')
        user.email = email
    
    if is_active is not None:
        user.is_active = is_active
    
    # 更新用户配置文件
    if department_id is not None:
        if department_id:
            try:
                department = Department.objects.get(id=department_id, is_active=True)
                user_profile.department = department
            except Department.DoesNotExist:
                raise ResourceNotFoundError(f'科室 ID {department_id} 不存在或已禁用')
        else:
            user_profile.department = None
    
    if role is not None:
        # 验证角色和科室的关系
        department = user_profile.department if department_id is None else (
            Department.objects.get(id=department_id) if department_id else None
        )
        if role == 'staff' and not department:
            raise DataValidationError('科室人员必须指定所属科室')
        
        user_profile.role = role
        # 重新分配用户组
        _assign_user_group(user, role)
    

    
    if is_active is not None:
        user_profile.is_active = is_active
    
    if updated_by is not None:
        user_profile.updated_by = updated_by
    
    # 保存更改
    user.save()
    user_profile.save()
    
    logger.info(
        f'用户更新成功: {user_profile.account_number} - {user.username}',
        extra={
            'user_profile_id': user_profile_id,
            'account_number': user_profile.account_number,
            'updated_by': updated_by.username if updated_by else None
        }
    )
    
    return user_profile


@transaction.atomic
def user_delete(
    *,
    user_profile_id: int,
    deleted_by: Optional[User] = None
) -> bool:
    """
    删除用户（软删除）

    Args:
        user_profile_id: 用户配置文件ID
        deleted_by: 删除者

    Returns:
        bool: 删除是否成功

    Raises:
        ResourceNotFoundError: 用户不存在
        BusinessLogicError: 业务逻辑错误
    """

    try:
        user_profile = UserProfile.objects.select_related('user').get(
            id=user_profile_id,
            is_deleted=False
        )
    except UserProfile.DoesNotExist:
        raise ResourceNotFoundError(f'用户配置文件 ID {user_profile_id} 不存在')

    # 检查是否可以删除
    if user_profile.role == 'admin':
        # 检查是否是最后一个管理员
        admin_count = UserProfile.objects.filter(
            role='admin',
            is_active=True,
            is_deleted=False
        ).exclude(id=user_profile_id).count()

        if admin_count == 0:
            raise BusinessLogicError('不能删除最后一个管理员账号')

    # 执行软删除
    user_profile.is_deleted = True
    user_profile.deleted_at = timezone.now()
    user_profile.is_active = False

    # 同时禁用Django用户
    user = user_profile.user
    user.is_active = False
    user.save()

    user_profile.save()

    logger.info(
        f'用户删除成功: {user_profile.account_number} - {user.username}',
        extra={
            'user_profile_id': user_profile_id,
            'account_number': user_profile.account_number,
            'deleted_by': deleted_by.username if deleted_by else None
        }
    )

    return True


@transaction.atomic
def user_activate(
    *,
    user_profile_id: int,
    updated_by: Optional[User] = None
) -> UserProfile:
    """
    激活用户

    Args:
        user_profile_id: 用户配置文件ID
        updated_by: 更新者

    Returns:
        UserProfile: 更新后的用户配置文件

    Raises:
        ResourceNotFoundError: 用户不存在
    """

    try:
        user_profile = UserProfile.objects.select_related('user').get(
            id=user_profile_id,
            is_deleted=False
        )
    except UserProfile.DoesNotExist:
        raise ResourceNotFoundError(f'用户配置文件 ID {user_profile_id} 不存在')

    # 激活用户
    user_profile.is_active = True
    user_profile.updated_by = updated_by

    # 同时激活Django用户
    user = user_profile.user
    user.is_active = True
    user.save()

    user_profile.save()

    logger.info(
        f'用户激活成功: {user_profile.account_number} - {user.username}',
        extra={
            'user_profile_id': user_profile_id,
            'account_number': user_profile.account_number,
            'updated_by': updated_by.username if updated_by else None
        }
    )

    return user_profile


@transaction.atomic
def user_deactivate(
    *,
    user_profile_id: int,
    updated_by: Optional[User] = None
) -> UserProfile:
    """
    禁用用户

    Args:
        user_profile_id: 用户配置文件ID
        updated_by: 更新者

    Returns:
        UserProfile: 更新后的用户配置文件

    Raises:
        ResourceNotFoundError: 用户不存在
        BusinessLogicError: 业务逻辑错误
    """

    try:
        user_profile = UserProfile.objects.select_related('user').get(
            id=user_profile_id,
            is_deleted=False
        )
    except UserProfile.DoesNotExist:
        raise ResourceNotFoundError(f'用户配置文件 ID {user_profile_id} 不存在')

    # 检查是否可以禁用
    if user_profile.role == 'admin':
        # 检查是否是最后一个活跃管理员
        active_admin_count = UserProfile.objects.filter(
            role='admin',
            is_active=True,
            is_deleted=False
        ).exclude(id=user_profile_id).count()

        if active_admin_count == 0:
            raise BusinessLogicError('不能禁用最后一个管理员账号')

    # 禁用用户
    user_profile.is_active = False
    user_profile.updated_by = updated_by

    # 同时禁用Django用户
    user = user_profile.user
    user.is_active = False
    user.save()

    user_profile.save()

    logger.info(
        f'用户禁用成功: {user_profile.account_number} - {user.username}',
        extra={
            'user_profile_id': user_profile_id,
            'account_number': user_profile.account_number,
            'updated_by': updated_by.username if updated_by else None
        }
    )

    return user_profile


def _assign_user_group(user: User, role: str) -> None:
    """
    分配用户组

    Args:
        user: Django用户对象
        role: 用户角色
    """
    # 清除现有组
    user.groups.clear()

    # 根据角色分配组
    if role == 'admin':
        try:
            admin_group = Group.objects.get(name='管理员')
            user.groups.add(admin_group)
            logger.info(f'用户 {user.username} 已分配到管理员组')
        except Group.DoesNotExist:
            logger.warning('管理员用户组不存在')

    elif role == 'staff':
        try:
            staff_group = Group.objects.get(name='科室人员')
            user.groups.add(staff_group)
            logger.info(f'用户 {user.username} 已分配到科室人员组')
        except Group.DoesNotExist:
            logger.warning('科室人员用户组不存在')


@transaction.atomic
def user_change_role(
    *,
    user_profile_id: int,
    new_role: str,
    updated_by: Optional[User] = None
) -> UserProfile:
    """
    更改用户角色

    Args:
        user_profile_id: 用户配置文件ID
        new_role: 新角色
        updated_by: 更新者

    Returns:
        UserProfile: 更新后的用户配置文件

    Raises:
        ResourceNotFoundError: 用户不存在
        DataValidationError: 数据验证失败
        BusinessLogicError: 业务逻辑错误
    """

    try:
        user_profile = UserProfile.objects.select_related('user', 'department').get(
            id=user_profile_id,
            is_deleted=False
        )
    except UserProfile.DoesNotExist:
        raise ResourceNotFoundError(f'用户配置文件 ID {user_profile_id} 不存在')

    # 验证新角色
    valid_roles = dict(UserProfile.ROLE_CHOICES).keys()
    if new_role not in valid_roles:
        raise DataValidationError(f'无效的角色: {new_role}')

    old_role = user_profile.role

    # 检查角色变更的业务逻辑
    if old_role == 'admin' and new_role != 'admin':
        # 检查是否是最后一个管理员
        admin_count = UserProfile.objects.filter(
            role='admin',
            is_active=True,
            is_deleted=False
        ).exclude(id=user_profile_id).count()

        if admin_count == 0:
            raise BusinessLogicError('不能将最后一个管理员改为其他角色')

    # 验证角色和科室的关系
    if new_role == 'staff' and not user_profile.department:
        raise DataValidationError('科室人员必须指定所属科室')

    # 更新角色
    user_profile.role = new_role
    user_profile.updated_by = updated_by
    user_profile.save()

    # 重新分配用户组
    _assign_user_group(user_profile.user, new_role)

    logger.info(
        f'用户角色变更成功: {user_profile.account_number} 从 {old_role} 变更为 {new_role}',
        extra={
            'user_profile_id': user_profile_id,
            'account_number': user_profile.account_number,
            'old_role': old_role,
            'new_role': new_role,
            'updated_by': updated_by.username if updated_by else None
        }
    )

    return user_profile


@transaction.atomic
def user_change_department(
    *,
    user_profile_id: int,
    department_id: Optional[int],
    updated_by: Optional[User] = None
) -> UserProfile:
    """
    更改用户科室

    Args:
        user_profile_id: 用户配置文件ID
        department_id: 新科室ID，None表示移除科室
        updated_by: 更新者

    Returns:
        UserProfile: 更新后的用户配置文件

    Raises:
        ResourceNotFoundError: 用户或科室不存在
        DataValidationError: 数据验证失败
    """

    try:
        user_profile = UserProfile.objects.select_related('user', 'department').get(
            id=user_profile_id,
            is_deleted=False
        )
    except UserProfile.DoesNotExist:
        raise ResourceNotFoundError(f'用户配置文件 ID {user_profile_id} 不存在')

    # 验证新科室
    new_department = None
    if department_id:
        try:
            new_department = Department.objects.get(id=department_id, is_active=True)
        except Department.DoesNotExist:
            raise ResourceNotFoundError(f'科室 ID {department_id} 不存在或已禁用')

    # 验证角色和科室的关系
    if user_profile.role == 'staff' and not new_department:
        raise DataValidationError('科室人员必须指定所属科室')

    old_department = user_profile.department

    # 更新科室
    user_profile.department = new_department
    user_profile.updated_by = updated_by
    user_profile.save()

    logger.info(
        f'用户科室变更成功: {user_profile.account_number} 从 {old_department.name if old_department else "无"} 变更为 {new_department.name if new_department else "无"}',
        extra={
            'user_profile_id': user_profile_id,
            'account_number': user_profile.account_number,
            'old_department_id': old_department.id if old_department else None,
            'new_department_id': department_id,
            'updated_by': updated_by.username if updated_by else None
        }
    )

    return user_profile


@transaction.atomic
def users_bulk_activate(
    *,
    user_profile_ids: list[int],
    updated_by: Optional[User] = None
) -> Dict[str, Any]:
    """
    批量激活用户

    Args:
        user_profile_ids: 用户配置文件ID列表
        updated_by: 更新者

    Returns:
        Dict: 操作结果统计
    """

    success_count = 0
    failed_count = 0
    errors = []

    for user_profile_id in user_profile_ids:
        try:
            user_activate(
                user_profile_id=user_profile_id,
                updated_by=updated_by
            )
            success_count += 1
        except Exception as e:
            failed_count += 1
            errors.append(f'用户 ID {user_profile_id}: {str(e)}')

    result = {
        'success_count': success_count,
        'failed_count': failed_count,
        'errors': errors
    }

    logger.info(
        f'批量激活用户完成: 成功 {success_count} 个，失败 {failed_count} 个',
        extra={
            'success_count': success_count,
            'failed_count': failed_count,
            'updated_by': updated_by.username if updated_by else None
        }
    )

    return result


@transaction.atomic
def users_bulk_deactivate(
    *,
    user_profile_ids: list[int],
    updated_by: Optional[User] = None
) -> Dict[str, Any]:
    """
    批量禁用用户

    Args:
        user_profile_ids: 用户配置文件ID列表
        updated_by: 更新者

    Returns:
        Dict: 操作结果统计
    """

    success_count = 0
    failed_count = 0
    errors = []

    for user_profile_id in user_profile_ids:
        try:
            user_deactivate(
                user_profile_id=user_profile_id,
                updated_by=updated_by
            )
            success_count += 1
        except Exception as e:
            failed_count += 1
            errors.append(f'用户 ID {user_profile_id}: {str(e)}')

    result = {
        'success_count': success_count,
        'failed_count': failed_count,
        'errors': errors
    }

    logger.info(
        f'批量禁用用户完成: 成功 {success_count} 个，失败 {failed_count} 个',
        extra={
            'success_count': success_count,
            'failed_count': failed_count,
            'updated_by': updated_by.username if updated_by else None
        }
    )

    return result


# ==================== 科室管理服务 ====================

@transaction.atomic
def department_create(
    *,
    name: str,
    code: str,
    is_active: bool = True,
    created_by: Optional[User] = None
) -> Department:
    """
    创建科室

    Args:
        name: 科室名称
        code: 科室代码
        is_active: 是否启用
        created_by: 创建者

    Returns:
        Department: 创建的科室

    Raises:
        DataValidationError: 数据验证失败
        BusinessLogicError: 业务逻辑错误
    """

    # 验证科室名称
    if not name or len(name.strip()) < 2:
        raise DataValidationError('科室名称至少需要2个字符')

    # 验证科室代码
    if not code or len(code.strip()) < 2:
        raise DataValidationError('科室代码至少需要2个字符')

    if not code.replace('_', '').isalnum():
        raise DataValidationError('科室代码只能包含字母、数字和下划线')

    # 检查名称唯一性
    if Department.objects.filter(name=name.strip()).exists():
        raise BusinessLogicError(f'科室名称 {name} 已存在')

    # 检查代码唯一性
    if Department.objects.filter(code=code.strip().upper()).exists():
        raise BusinessLogicError(f'科室代码 {code} 已存在')

    try:
        department = Department.objects.create(
            name=name.strip(),
            code=code.strip().upper(),
            is_active=is_active,
            created_by=created_by
        )

        logger.info(
            f'科室创建成功: {department.code} - {department.name}',
            extra={
                'department_id': department.id,
                'department_name': department.name,
                'department_code': department.code,
                'created_by': created_by.username if created_by else None
            }
        )

        return department

    except Exception as e:
        logger.error(f'科室创建失败: {str(e)}')
        raise BusinessLogicError(f'科室创建失败: {str(e)}')


@transaction.atomic
def department_update(
    *,
    department_id: int,
    name: Optional[str] = None,
    code: Optional[str] = None,
    is_active: Optional[bool] = None,
    updated_by: Optional[User] = None
) -> Department:
    """
    更新科室信息

    Args:
        department_id: 科室ID
        name: 科室名称
        code: 科室代码
        is_active: 是否启用
        updated_by: 更新者

    Returns:
        Department: 更新后的科室

    Raises:
        ResourceNotFoundError: 科室不存在
        DataValidationError: 数据验证失败
        BusinessLogicError: 业务逻辑错误
    """

    try:
        department = Department.objects.get(id=department_id, is_deleted=False)
    except Department.DoesNotExist:
        raise ResourceNotFoundError(f'科室 ID {department_id} 不存在')

    # 更新科室名称
    if name is not None:
        name = name.strip()
        if len(name) < 2:
            raise DataValidationError('科室名称至少需要2个字符')

        if Department.objects.filter(name=name).exclude(id=department_id).exists():
            raise BusinessLogicError(f'科室名称 {name} 已存在')

        department.name = name

    # 更新科室代码
    if code is not None:
        code = code.strip().upper()
        if len(code) < 2:
            raise DataValidationError('科室代码至少需要2个字符')

        if not code.replace('_', '').isalnum():
            raise DataValidationError('科室代码只能包含字母、数字和下划线')

        if Department.objects.filter(code=code).exclude(id=department_id).exists():
            raise BusinessLogicError(f'科室代码 {code} 已存在')

        department.code = code

    # 更新状态
    if is_active is not None:
        if not is_active:
            # 检查是否有用户在该科室
            user_count = UserProfile.objects.filter(
                department=department,
                is_active=True,
                is_deleted=False
            ).count()

            if user_count > 0:
                raise BusinessLogicError(f'科室下还有 {user_count} 个活跃用户，不能禁用')

        department.is_active = is_active

    if updated_by is not None:
        department.updated_by = updated_by

    department.save()

    logger.info(
        f'科室更新成功: {department.code} - {department.name}',
        extra={
            'department_id': department_id,
            'department_name': department.name,
            'department_code': department.code,
            'updated_by': updated_by.username if updated_by else None
        }
    )

    return department


@transaction.atomic
def department_delete(
    *,
    department_id: int,
    deleted_by: Optional[User] = None
) -> bool:
    """
    删除科室（软删除）

    Args:
        department_id: 科室ID
        deleted_by: 删除者

    Returns:
        bool: 删除是否成功

    Raises:
        ResourceNotFoundError: 科室不存在
        BusinessLogicError: 业务逻辑错误
    """

    try:
        department = Department.objects.get(id=department_id, is_deleted=False)
    except Department.DoesNotExist:
        raise ResourceNotFoundError(f'科室 ID {department_id} 不存在')

    # 检查是否有用户在该科室
    user_count = UserProfile.objects.filter(
        department=department,
        is_deleted=False
    ).count()

    if user_count > 0:
        raise BusinessLogicError(f'科室下还有 {user_count} 个用户，不能删除')

    # 执行软删除
    department.is_deleted = True
    department.deleted_at = timezone.now()
    department.is_active = False
    department.save()

    logger.info(
        f'科室删除成功: {department.code} - {department.name}',
        extra={
            'department_id': department_id,
            'department_name': department.name,
            'department_code': department.code,
            'deleted_by': deleted_by.username if deleted_by else None
        }
    )

    return True


# ==================== 权限管理服务 ====================

def user_has_permission(
    *,
    user_profile_id: int,
    permission_codename: str
) -> bool:
    """
    检查用户是否有特定权限

    Args:
        user_profile_id: 用户配置文件ID
        permission_codename: 权限代码名

    Returns:
        bool: 是否有权限
    """

    try:
        user_profile = UserProfile.objects.select_related('user').get(
            id=user_profile_id,
            is_active=True,
            is_deleted=False
        )

        return user_profile.user.has_perm(f'users.{permission_codename}')

    except UserProfile.DoesNotExist:
        return False


def user_can_manage_users(*, user_profile_id: int) -> bool:
    """
    检查用户是否可以管理其他用户

    Args:
        user_profile_id: 用户配置文件ID

    Returns:
        bool: 是否可以管理用户
    """

    try:
        user_profile = UserProfile.objects.select_related('user').get(
            id=user_profile_id,
            is_active=True,
            is_deleted=False
        )

        return (user_profile.role == 'admin' and
                user_profile.user.has_perm('users.can_manage_users'))

    except UserProfile.DoesNotExist:
        return False


def user_can_view_all_users(*, user_profile_id: int) -> bool:
    """
    检查用户是否可以查看所有用户

    Args:
        user_profile_id: 用户配置文件ID

    Returns:
        bool: 是否可以查看所有用户
    """

    try:
        user_profile = UserProfile.objects.select_related('user').get(
            id=user_profile_id,
            is_active=True,
            is_deleted=False
        )

        return (user_profile.role == 'admin' and
                user_profile.user.has_perm('users.can_view_all_users'))

    except UserProfile.DoesNotExist:
        return False


# ==================== 工具函数 ====================

def generate_unique_account_number() -> str:
    """
    生成唯一的4位数账号

    Returns:
        str: 4位数账号

    Raises:
        BusinessLogicError: 无法生成唯一账号
    """

    import random

    # 尝试生成唯一账号，最多尝试100次
    for _ in range(100):
        account_number = f"{random.randint(1000, 9999)}"

        if not UserProfile.objects.filter(account_number=account_number).exists():
            return account_number

    raise BusinessLogicError('无法生成唯一的4位数账号')


def get_user_statistics() -> Dict[str, Any]:
    """
    获取用户统计信息

    Returns:
        Dict: 统计信息
    """

    total_users = UserProfile.objects.filter(is_deleted=False).count()
    active_users = UserProfile.objects.filter(is_active=True, is_deleted=False).count()
    inactive_users = total_users - active_users

    admin_users = UserProfile.objects.filter(
        role='admin',
        is_active=True,
        is_deleted=False
    ).count()

    staff_users = UserProfile.objects.filter(
        role='staff',
        is_active=True,
        is_deleted=False
    ).count()

    departments_with_users = Department.objects.filter(
        users__is_active=True,
        users__is_deleted=False,
        is_active=True,
        is_deleted=False
    ).distinct().count()

    return {
        'total_users': total_users,
        'active_users': active_users,
        'inactive_users': inactive_users,
        'admin_users': admin_users,
        'staff_users': staff_users,
        'departments_with_users': departments_with_users
    }


def get_department_statistics() -> Dict[str, Any]:
    """
    获取科室统计信息

    Returns:
        Dict: 统计信息
    """

    total_departments = Department.objects.filter(is_deleted=False).count()
    active_departments = Department.objects.filter(is_active=True, is_deleted=False).count()
    inactive_departments = total_departments - active_departments

    departments_with_users = Department.objects.filter(
        users__is_active=True,
        users__is_deleted=False,
        is_active=True,
        is_deleted=False
    ).distinct().count()

    empty_departments = active_departments - departments_with_users

    return {
        'total_departments': total_departments,
        'active_departments': active_departments,
        'inactive_departments': inactive_departments,
        'departments_with_users': departments_with_users,
        'empty_departments': empty_departments
    }


@transaction.atomic
def initialize_user_groups() -> Dict[str, Any]:
    """
    初始化用户组和权限

    Returns:
        Dict: 初始化结果
    """

    from django.contrib.auth.models import Permission
    from django.contrib.contenttypes.models import ContentType

    result = {
        'groups_created': [],
        'permissions_assigned': []
    }

    # 创建管理员组
    admin_group, created = Group.objects.get_or_create(name='管理员')
    if created:
        result['groups_created'].append('管理员')

    # 创建科室人员组
    staff_group, created = Group.objects.get_or_create(name='科室人员')
    if created:
        result['groups_created'].append('科室人员')

    # 获取用户相关权限
    user_content_type = ContentType.objects.get_for_model(UserProfile)
    dept_content_type = ContentType.objects.get_for_model(Department)

    # 管理员权限
    admin_permissions = [
        'can_manage_users',
        'can_view_all_users',
        'can_assign_roles',
        'add_userprofile',
        'change_userprofile',
        'delete_userprofile',
        'view_userprofile',
        'add_department',
        'change_department',
        'delete_department',
        'view_department',
    ]

    for perm_codename in admin_permissions:
        try:
            if perm_codename.startswith('can_'):
                permission = Permission.objects.get(
                    codename=perm_codename,
                    content_type=user_content_type
                )
            elif 'department' in perm_codename:
                permission = Permission.objects.get(
                    codename=perm_codename,
                    content_type=dept_content_type
                )
            else:
                permission = Permission.objects.get(
                    codename=perm_codename,
                    content_type=user_content_type
                )

            admin_group.permissions.add(permission)
            result['permissions_assigned'].append(f'管理员: {perm_codename}')
        except Permission.DoesNotExist:
            logger.warning(f'权限不存在: {perm_codename}')

    # 科室人员权限（只能查看科室信息，不能管理用户）
    staff_permissions = [
        'view_department',
    ]

    for perm_codename in staff_permissions:
        try:
            if 'department' in perm_codename:
                permission = Permission.objects.get(
                    codename=perm_codename,
                    content_type=dept_content_type
                )
            else:
                permission = Permission.objects.get(
                    codename=perm_codename,
                    content_type=user_content_type
                )

            staff_group.permissions.add(permission)
            result['permissions_assigned'].append(f'科室人员: {perm_codename}')
        except Permission.DoesNotExist:
            logger.warning(f'权限不存在: {perm_codename}')

    logger.info(
        f'用户组初始化完成: 创建组 {len(result["groups_created"])} 个，分配权限 {len(result["permissions_assigned"])} 个'
    )

    return result
