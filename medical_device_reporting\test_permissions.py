#!/usr/bin/env python
"""
权限控制测试脚本
Permission Control Test Script for Medical Device Reporting Platform
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from apps.users.models import UserProfile, Department
from apps.users.services import initialize_user_groups

def setup_test_users():
    """设置测试用户"""
    
    print("设置测试用户...")
    
    # 初始化用户组和权限
    initialize_user_groups()
    
    # 创建测试科室
    department, created = Department.objects.get_or_create(
        code='PERMTEST',
        defaults={
            'name': '权限测试科室',
            'is_active': True
        }
    )
    
    # 创建管理员用户
    admin_user, created = User.objects.get_or_create(
        username='perm_admin',
        defaults={
            'first_name': '权限测试',
            'last_name': '管理员',
            'email': '<EMAIL>',
            'is_active': True
        }
    )
    
    admin_profile, created = UserProfile.objects.get_or_create(
        account_number='9001',
        defaults={
            'user': admin_user,
            'role': 'admin',
            'is_active': True
        }
    )
    
    # 创建科室人员用户
    staff_user, created = User.objects.get_or_create(
        username='perm_staff',
        defaults={
            'first_name': '权限测试',
            'last_name': '科室人员',
            'email': '<EMAIL>',
            'is_active': True
        }
    )
    
    staff_profile, created = UserProfile.objects.get_or_create(
        account_number='9002',
        defaults={
            'user': staff_user,
            'department': department,
            'role': 'staff',
            'is_active': True
        }
    )
    
    # 创建禁用的用户（用于测试禁用用户访问）
    disabled_user, created = User.objects.get_or_create(
        username='perm_disabled',
        defaults={
            'first_name': '权限测试',
            'last_name': '禁用用户',
            'email': '<EMAIL>',
            'is_active': True
        }
    )

    disabled_profile, created = UserProfile.objects.get_or_create(
        account_number='9003',
        defaults={
            'user': disabled_user,
            'department': department,
            'role': 'staff',
            'is_active': False  # 禁用状态
        }
    )
    
    return admin_user, staff_user, disabled_user, department

def test_permissions():
    """测试权限控制"""
    
    print("=== 权限控制测试 ===\n")
    
    # 设置测试用户
    admin_user, staff_user, disabled_user, department = setup_test_users()
    
    # 创建测试客户端
    client = Client()
    
    # 测试URL列表
    test_urls = [
        # 页面URL
        ('用户中心', '/dashboard/', 'GET'),
        ('用户列表', '/users/', 'GET'),
        ('用户创建', '/users/create/', 'GET'),
        ('用户编辑', f'/users/{staff_user.profile.id}/edit/', 'GET'),
        
        # API URL
        ('用户列表API', '/api/users/', 'GET'),
        ('用户创建API', '/api/users/create/', 'POST'),
        ('用户激活API', f'/api/users/{staff_user.profile.id}/activate/', 'POST'),
        ('用户禁用API', f'/api/users/{staff_user.profile.id}/deactivate/', 'POST'),
        ('批量操作API', '/api/users/bulk-action/', 'POST'),
        ('统计API', '/api/users/statistics/', 'GET'),
        ('搜索API', '/api/users/search/', 'GET'),
        ('科室列表API', '/api/departments/', 'GET'),
    ]
    
    # 1. 测试未登录用户
    print("1. 测试未登录用户访问...")
    for name, url, method in test_urls[:4]:  # 只测试页面URL
        try:
            if method == 'GET':
                response = client.get(url)
            else:
                response = client.post(url)
            
            if response.status_code == 302:  # 重定向到登录页面
                print(f"   ✅ {name}: 正确重定向到登录页面")
            else:
                print(f"   ❌ {name}: 未正确重定向 (状态码: {response.status_code})")
        except Exception as e:
            print(f"   ❌ {name}: 测试异常 - {str(e)}")
    
    # 2. 测试管理员用户
    print("\n2. 测试管理员用户访问...")
    client.force_login(admin_user)
    
    for name, url, method in test_urls:
        try:
            if method == 'GET':
                response = client.get(url)
            else:
                # 为POST请求提供必要的数据
                data = {}
                if 'create' in url:
                    data = {
                        'account_number': '9999',
                        'username': 'test_create',
                        'role': 'staff'
                    }
                elif 'bulk-action' in url:
                    data = {
                        'user_ids': [staff_user.profile.id],
                        'action': 'activate'
                    }
                
                response = client.post(url, data, content_type='application/json')
            
            if response.status_code in [200, 201]:
                print(f"   ✅ {name}: 管理员可以访问")
            elif response.status_code == 403:
                print(f"   ❌ {name}: 管理员被拒绝访问")
            else:
                print(f"   ⚠️ {name}: 其他状态码 {response.status_code}")
        except Exception as e:
            print(f"   ❌ {name}: 测试异常 - {str(e)}")
    
    # 3. 测试科室人员用户
    print("\n3. 测试科室人员用户访问...")
    client.force_login(staff_user)
    
    # 科室人员应该能访问的URL
    allowed_urls = [
        ('用户中心', '/dashboard/', 'GET'),
        ('统计API', '/api/users/statistics/', 'GET'),
        ('搜索API', '/api/users/search/', 'GET'),
        ('科室列表API', '/api/departments/', 'GET'),
    ]
    
    # 科室人员不应该能访问的URL
    forbidden_urls = [
        ('用户列表', '/users/', 'GET'),
        ('用户创建', '/users/create/', 'GET'),
        ('用户编辑', f'/users/{staff_user.profile.id}/edit/', 'GET'),
        ('用户列表API', '/api/users/', 'GET'),
        ('用户创建API', '/api/users/create/', 'POST'),
        ('用户激活API', f'/api/users/{staff_user.profile.id}/activate/', 'POST'),
        ('批量操作API', '/api/users/bulk-action/', 'POST'),
    ]
    
    print("   应该允许访问的URL:")
    for name, url, method in allowed_urls:
        try:
            if method == 'GET':
                response = client.get(url)
            else:
                response = client.post(url)
            
            if response.status_code in [200, 201]:
                print(f"     ✅ {name}: 科室人员可以访问")
            elif response.status_code == 403:
                print(f"     ❌ {name}: 科室人员被拒绝访问")
            else:
                print(f"     ⚠️ {name}: 其他状态码 {response.status_code}")
        except Exception as e:
            print(f"     ❌ {name}: 测试异常 - {str(e)}")
    
    print("   应该拒绝访问的URL:")
    for name, url, method in forbidden_urls:
        try:
            if method == 'GET':
                response = client.get(url)
            else:
                data = {}
                if 'create' in url:
                    data = {
                        'account_number': '9998',
                        'username': 'test_forbidden',
                        'role': 'staff'
                    }
                elif 'bulk-action' in url:
                    data = {
                        'user_ids': [admin_user.profile.id],
                        'action': 'activate'
                    }
                
                response = client.post(url, data, content_type='application/json')
            
            if response.status_code == 403:
                print(f"     ✅ {name}: 正确拒绝科室人员访问")
            elif response.status_code == 302:
                print(f"     ✅ {name}: 重定向（权限拒绝）")
            elif response.status_code in [200, 201]:
                print(f"     ❌ {name}: 科室人员不应该能访问")
            else:
                print(f"     ⚠️ {name}: 其他状态码 {response.status_code}")
        except Exception as e:
            print(f"     ❌ {name}: 测试异常 - {str(e)}")
    
    # 4. 测试禁用用户
    print("\n4. 测试禁用用户访问...")
    client.force_login(disabled_user)

    # 禁用用户应该被拒绝访问大部分功能
    for name, url, method in [('用户中心', '/dashboard/', 'GET')]:
        try:
            if method == 'GET':
                response = client.get(url)
            else:
                response = client.post(url)

            if response.status_code == 403:
                print(f"   ✅ {name}: 正确拒绝禁用用户访问")
            elif response.status_code == 302:
                print(f"   ✅ {name}: 重定向（权限拒绝）")
            elif response.status_code in [200, 201]:
                print(f"   ❌ {name}: 禁用用户不应该能访问")
            else:
                print(f"   ⚠️ {name}: 其他状态码 {response.status_code}")
        except Exception as e:
            print(f"   ❌ {name}: 测试异常 - {str(e)}")
    
    print("\n=== 权限控制测试完成 ===")
    
    return True

if __name__ == '__main__':
    try:
        test_permissions()
        print("\n🎉 权限控制测试完成！")
    except Exception as e:
        print(f"\n❌ 权限控制测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
