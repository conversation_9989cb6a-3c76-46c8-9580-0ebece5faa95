"""
通用信号处理器
Common signal handlers for Medical Device Reporting Platform
"""

import logging
from django.db.models.signals import post_save, post_delete, pre_save
from django.dispatch import receiver
from django.contrib.auth.models import User
from django.utils import timezone

logger = logging.getLogger('apps.common')


@receiver(post_save, sender=User)
def user_post_save_handler(sender, instance, created, **kwargs):
    """
    用户保存后的信号处理器
    
    Args:
        sender: 发送信号的模型类
        instance: 保存的实例
        created: 是否为新创建的实例
        **kwargs: 其他参数
    """
    if created:
        logger.info(f'新用户创建: {instance.username} (ID: {instance.id})')
        
        # 可以在这里添加新用户创建后的逻辑
        # 例如：创建用户配置文件、发送欢迎邮件等
        
    else:
        logger.info(f'用户信息更新: {instance.username} (ID: {instance.id})')


@receiver(post_delete, sender=User)
def user_post_delete_handler(sender, instance, **kwargs):
    """
    用户删除后的信号处理器
    
    Args:
        sender: 发送信号的模型类
        instance: 删除的实例
        **kwargs: 其他参数
    """
    logger.info(f'用户删除: {instance.username} (ID: {instance.id})')
    
    # 可以在这里添加用户删除后的清理逻辑
    # 例如：清理相关文件、发送通知等


def log_model_changes(sender, instance, **kwargs):
    """
    通用模型变更日志处理器
    
    记录模型实例的创建、更新、删除操作
    
    Args:
        sender: 发送信号的模型类
        instance: 模型实例
        **kwargs: 其他参数
    """
    
    # 检查是否为BaseModel的子类
    from .models import BaseModel
    
    if not issubclass(sender, BaseModel):
        return
    
    action = 'unknown'
    if 'created' in kwargs:
        action = 'created' if kwargs['created'] else 'updated'
    elif 'signal' in kwargs:
        if kwargs['signal'] == post_delete:
            action = 'deleted'
    
    logger.info(
        f'模型变更: {sender.__name__} {action}',
        extra={
            'model': sender.__name__,
            'instance_id': getattr(instance, 'id', None),
            'action': action,
            'timestamp': timezone.now().isoformat()
        }
    )


def setup_model_logging():
    """
    为所有BaseModel子类设置日志记录
    """
    from django.apps import apps
    from .models import BaseModel
    
    # 获取所有已注册的模型
    for model in apps.get_models():
        if issubclass(model, BaseModel):
            # 连接信号
            post_save.connect(log_model_changes, sender=model)
            post_delete.connect(log_model_changes, sender=model)


# 在模块加载时设置模型日志记录
# setup_model_logging()  # 暂时注释，避免在开发阶段产生过多日志
