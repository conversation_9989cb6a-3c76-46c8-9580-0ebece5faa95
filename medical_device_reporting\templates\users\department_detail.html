{% extends 'base.html' %}
{% load static %}

{% block title %}{{ department.name }} - 科室详情 - 医疗器械不良事件上报平台{% endblock %}

{% block extra_css %}
<link href="{% static 'users/css/user_management.css' %}" rel="stylesheet">
<!-- DataTables CSS -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
{% endblock %}

{% block nav_items %}
<li class="nav-item">
    <a class="nav-link" href="{% url 'users:dashboard' %}">
        <i class="bi bi-house"></i>
        用户中心
    </a>
</li>
{% if user.profile.is_admin %}
<li class="nav-item">
    <a class="nav-link" href="{% url 'users:user_list' %}">
        <i class="bi bi-people"></i>
        用户管理
    </a>
</li>
<li class="nav-item">
    <a class="nav-link" href="{% url 'users:department_list' %}">
        <i class="bi bi-building"></i>
        科室管理
    </a>
</li>
{% endif %}
<li class="nav-item dropdown">
    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
        <i class="bi bi-person-circle"></i>
        {{ user.get_full_name|default:user.username }}
    </a>
    <ul class="dropdown-menu">
        <li><a class="dropdown-item" href="#"><i class="bi bi-gear me-2"></i>个人设置</a></li>
        <li><hr class="dropdown-divider"></li>
        <li><a class="dropdown-item" href="{% url 'users:logout' %}"><i class="bi bi-box-arrow-right me-2"></i>退出登录</a></li>
    </ul>
</li>
{% endblock %}

{% block content %}
<div class="department-detail-container">
    <!-- 页面标题 -->
    <div class="page-header mb-4">
        <div class="row align-items-center">
            <div class="col">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'users:dashboard' %}">用户中心</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'users:department_list' %}">科室管理</a></li>
                        <li class="breadcrumb-item active">{{ department.name }}</li>
                    </ol>
                </nav>
                <h2 class="page-title">
                    <i class="bi bi-building me-2"></i>
                    {{ department.name }}
                    {% if department.is_active %}
                        <span class="badge bg-success ms-2">启用</span>
                    {% else %}
                        <span class="badge bg-secondary ms-2">禁用</span>
                    {% endif %}
                </h2>
                <p class="page-subtitle text-muted">科室代码：{{ department.code }}</p>
            </div>
            <div class="col-auto">
                <div class="btn-group" role="group">
                    <a href="{% url 'users:department_list' %}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left me-2"></i>
                        返回列表
                    </a>
                    <a href="{% url 'users:department_edit' department.id %}" class="btn btn-primary">
                        <i class="bi bi-pencil me-2"></i>
                        编辑科室
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 科室信息卡片 -->
        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-info-circle me-2"></i>
                        科室信息
                    </h6>
                </div>
                <div class="card-body">
                    <dl class="row">
                        <dt class="col-sm-4">科室代码：</dt>
                        <dd class="col-sm-8">
                            <span class="badge bg-primary">{{ department.code }}</span>
                        </dd>
                        
                        <dt class="col-sm-4">科室名称：</dt>
                        <dd class="col-sm-8">{{ department.name }}</dd>
                        
                        <dt class="col-sm-4">状态：</dt>
                        <dd class="col-sm-8">
                            {% if department.is_active %}
                                <span class="badge bg-success">
                                    <i class="bi bi-check-circle me-1"></i>启用
                                </span>
                            {% else %}
                                <span class="badge bg-secondary">
                                    <i class="bi bi-x-circle me-1"></i>禁用
                                </span>
                            {% endif %}
                        </dd>
                        
                        <dt class="col-sm-4">创建时间：</dt>
                        <dd class="col-sm-8">
                            <small class="text-muted">{{ department.created_at|date:"Y-m-d H:i:s" }}</small>
                        </dd>
                        
                        {% if department.created_by %}
                        <dt class="col-sm-4">创建人：</dt>
                        <dd class="col-sm-8">
                            <small class="text-muted">{{ department.created_by.get_full_name|default:department.created_by.username }}</small>
                        </dd>
                        {% endif %}
                        
                        {% if department.updated_at %}
                        <dt class="col-sm-4">更新时间：</dt>
                        <dd class="col-sm-8">
                            <small class="text-muted">{{ department.updated_at|date:"Y-m-d H:i:s" }}</small>
                        </dd>
                        {% endif %}
                        
                        {% if department.updated_by %}
                        <dt class="col-sm-4">更新人：</dt>
                        <dd class="col-sm-8">
                            <small class="text-muted">{{ department.updated_by.get_full_name|default:department.updated_by.username }}</small>
                        </dd>
                        {% endif %}
                    </dl>
                </div>
            </div>

            <!-- 统计信息卡片 -->
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-bar-chart me-2"></i>
                        用户统计
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-6">
                            <div class="text-center">
                                <h3 class="text-primary mb-1">{{ total_users }}</h3>
                                <small class="text-muted">总用户数</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h3 class="text-success mb-1">
                                    {% for user in users %}
                                        {% if user.is_active %}{{ forloop.counter }}{% endif %}
                                    {% empty %}
                                        0
                                    {% endfor %}
                                </h3>
                                <small class="text-muted">活跃用户</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h3 class="text-warning mb-1">
                                    {% for user in users %}
                                        {% if user.role == 'admin' %}{{ forloop.counter }}{% endif %}
                                    {% empty %}
                                        0
                                    {% endfor %}
                                </h3>
                                <small class="text-muted">管理员</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h3 class="text-info mb-1">
                                    {% for user in users %}
                                        {% if user.role == 'staff' %}{{ forloop.counter }}{% endif %}
                                    {% empty %}
                                        0
                                    {% endfor %}
                                </h3>
                                <small class="text-muted">科室人员</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 用户列表 -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h6 class="card-title mb-0">
                                <i class="bi bi-people me-2"></i>
                                科室用户列表
                            </h6>
                        </div>
                        <div class="col-auto">
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-primary" id="refreshUsersBtn">
                                    <i class="bi bi-arrow-clockwise me-1"></i>
                                    刷新
                                </button>
                                <a href="{% url 'users:user_create' %}?department={{ department.id }}" class="btn btn-primary">
                                    <i class="bi bi-plus-circle me-1"></i>
                                    添加用户
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    {% if users %}
                    <div class="table-responsive">
                        <table id="departmentUsersTable" class="table table-striped table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>账号</th>
                                    <th>姓名</th>
                                    <th>邮箱</th>
                                    <th>角色</th>
                                    <th>状态</th>
                                    <th>最后登录</th>
                                    <th width="100">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user_profile in users %}
                                <tr>
                                    <td>
                                        <strong class="text-primary">{{ user_profile.account_number }}</strong>
                                    </td>
                                    <td>
                                        {{ user_profile.user.get_full_name|default:user_profile.user.username }}
                                    </td>
                                    <td>
                                        {% if user_profile.user.email %}
                                            <a href="mailto:{{ user_profile.user.email }}" class="text-decoration-none">
                                                {{ user_profile.user.email }}
                                            </a>
                                        {% else %}
                                            <span class="text-muted">未设置</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if user_profile.role == 'admin' %}
                                            <span class="badge bg-warning">管理员</span>
                                        {% elif user_profile.role == 'staff' %}
                                            <span class="badge bg-info">科室人员</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ user_profile.get_role_display }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if user_profile.is_active and user_profile.user.is_active %}
                                            <span class="badge bg-success">
                                                <i class="bi bi-check-circle me-1"></i>正常
                                            </span>
                                        {% else %}
                                            <span class="badge bg-secondary">
                                                <i class="bi bi-x-circle me-1"></i>禁用
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if user_profile.user.last_login %}
                                            <small class="text-muted">
                                                {{ user_profile.user.last_login|date:"m-d H:i" }}
                                            </small>
                                        {% else %}
                                            <small class="text-muted">从未登录</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="{% url 'users:user_edit' user_profile.id %}" 
                                               class="btn btn-outline-primary" title="编辑用户">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-info user-detail-btn" 
                                                    data-user-id="{{ user_profile.id }}" title="查看详情">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    {% if users.has_other_pages %}
                    <nav aria-label="用户列表分页" class="mt-3">
                        <ul class="pagination justify-content-center">
                            {% if users.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ users.previous_page_number }}">
                                        <i class="bi bi-chevron-left"></i>
                                    </a>
                                </li>
                            {% endif %}
                            
                            {% for num in users.paginator.page_range %}
                                {% if users.number == num %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ num }}</span>
                                    </li>
                                {% elif num > users.number|add:'-3' and num < users.number|add:'3' %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if users.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ users.next_page_number }}">
                                        <i class="bi bi-chevron-right"></i>
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    {% else %}
                    <!-- 空状态 -->
                    <div class="text-center py-5">
                        <i class="bi bi-people fs-1 text-muted d-block mb-3"></i>
                        <h5 class="text-muted">该科室暂无用户</h5>
                        <p class="text-muted">点击上方"添加用户"按钮为该科室分配用户</p>
                        <a href="{% url 'users:user_create' %}?department={{ department.id }}" class="btn btn-primary">
                            <i class="bi bi-plus-circle me-2"></i>
                            添加第一个用户
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 用户详情模态框 -->
<div class="modal fade" id="userDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-person-badge me-2"></i>
                    用户详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="userDetailContent">
                    <!-- 用户详情内容将在这里动态加载 -->
                    <div class="text-center py-3">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="editUserFromModalBtn">编辑用户</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- DataTables JavaScript -->
<script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>

<!-- 科室详情JavaScript -->
<script src="{% static 'users/js/department_detail.js' %}"></script>
{% endblock %}
