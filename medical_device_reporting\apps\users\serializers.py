"""
用户管理序列化器
User Management Serializers for Medical Device Reporting Platform
"""

from rest_framework import serializers
from django.contrib.auth.models import User
from apps.common.utils import ValidationUtils
from .models import UserProfile, Department


class DepartmentSerializer(serializers.ModelSerializer):
    """
    科室序列化器
    """
    
    user_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Department
        fields = [
            'id', 'name', 'code', 'is_active',
            'user_count', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'user_count']
    
    def get_user_count(self, obj):
        """获取科室用户数量"""
        return obj.users.filter(is_deleted=False, is_active=True).count()
    
    def validate_code(self, value):
        """验证科室代码"""
        if not value:
            raise serializers.ValidationError('科室代码不能为空')
        
        value = value.strip().upper()
        
        if len(value) < 2 or len(value) > 20:
            raise serializers.ValidationError('科室代码长度必须在2-20个字符之间')
        
        if not value.replace('_', '').isalnum():
            raise serializers.ValidationError('科室代码只能包含字母、数字和下划线')
        
        # 检查唯一性（排除当前对象）
        queryset = Department.objects.filter(code=value, is_deleted=False)
        if self.instance:
            queryset = queryset.exclude(id=self.instance.id)
        
        if queryset.exists():
            raise serializers.ValidationError('科室代码已存在')
        
        return value
    
    def validate_name(self, value):
        """验证科室名称"""
        if not value:
            raise serializers.ValidationError('科室名称不能为空')
        
        value = value.strip()
        
        if len(value) < 2:
            raise serializers.ValidationError('科室名称至少需要2个字符')
        
        # 检查唯一性（排除当前对象）
        queryset = Department.objects.filter(name=value, is_deleted=False)
        if self.instance:
            queryset = queryset.exclude(id=self.instance.id)
        
        if queryset.exists():
            raise serializers.ValidationError('科室名称已存在')
        
        return value


class UserSerializer(serializers.ModelSerializer):
    """
    用户序列化器（用于列表和详情显示）
    """
    
    username = serializers.CharField(source='user.username', read_only=True)
    first_name = serializers.CharField(source='user.first_name', read_only=True)
    last_name = serializers.CharField(source='user.last_name', read_only=True)
    email = serializers.EmailField(source='user.email', read_only=True)
    last_login = serializers.DateTimeField(source='user.last_login', read_only=True)
    display_name = serializers.CharField(read_only=True)
    role_display = serializers.CharField(source='get_role_display', read_only=True)
    department = DepartmentSerializer(read_only=True)
    department_name = serializers.CharField(source='department.name', read_only=True)
    created_by_username = serializers.CharField(source='created_by.username', read_only=True)
    updated_by_username = serializers.CharField(source='updated_by.username', read_only=True)
    
    class Meta:
        model = UserProfile
        fields = [
            'id', 'account_number', 'username', 'first_name', 'last_name',
            'email', 'last_login', 'display_name', 'department', 'department_name',
            'role', 'role_display', 'is_active', 'last_login_ip', 'created_at',
            'updated_at', 'created_by_username', 'updated_by_username'
        ]
        read_only_fields = [
            'id', 'username', 'first_name', 'last_name', 'email', 'last_login',
            'display_name', 'department', 'department_name', 'role_display',
            'last_login_ip', 'created_at', 'updated_at', 'created_by_username',
            'updated_by_username'
        ]


class UserCreateSerializer(serializers.Serializer):
    """
    用户创建序列化器
    """
    
    account_number = serializers.CharField(max_length=4, min_length=4)
    username = serializers.CharField(max_length=150)
    first_name = serializers.CharField(max_length=150, required=False, allow_blank=True)
    last_name = serializers.CharField(max_length=150, required=False, allow_blank=True)
    email = serializers.EmailField(required=False, allow_blank=True)
    department_id = serializers.IntegerField(required=False, allow_null=True)
    role = serializers.ChoiceField(choices=UserProfile.ROLE_CHOICES, default='staff')
    is_active = serializers.BooleanField(default=True)
    
    def validate_account_number(self, value):
        """验证账号"""
        if not ValidationUtils.validate_account_number(value):
            raise serializers.ValidationError('账号必须是4位数字')
        
        if UserProfile.objects.filter(account_number=value, is_deleted=False).exists():
            raise serializers.ValidationError('账号已存在')
        
        return value
    
    def validate_username(self, value):
        """验证用户名"""
        if not value:
            raise serializers.ValidationError('用户名不能为空')
        
        value = value.strip()
        
        if User.objects.filter(username=value).exists():
            raise serializers.ValidationError('用户名已存在')
        
        return value
    
    def validate_email(self, value):
        """验证邮箱"""
        if value and not ValidationUtils.validate_email(value):
            raise serializers.ValidationError('邮箱格式不正确')
        
        if value and User.objects.filter(email=value).exists():
            raise serializers.ValidationError('邮箱已存在')
        
        return value
    
    def validate_department_id(self, value):
        """验证科室"""
        if value is not None:
            try:
                department = Department.objects.get(id=value, is_active=True, is_deleted=False)
            except Department.DoesNotExist:
                raise serializers.ValidationError('科室不存在或已禁用')
        
        return value
    
    def validate(self, attrs):
        """整体验证"""
        role = attrs.get('role')
        department_id = attrs.get('department_id')
        
        # 科室人员必须指定科室
        if role == 'staff' and not department_id:
            raise serializers.ValidationError('科室人员必须指定所属科室')
        
        return attrs


class UserUpdateSerializer(serializers.Serializer):
    """
    用户更新序列化器
    """
    
    first_name = serializers.CharField(max_length=150, required=False, allow_blank=True)
    last_name = serializers.CharField(max_length=150, required=False, allow_blank=True)
    email = serializers.EmailField(required=False, allow_blank=True)
    is_active = serializers.BooleanField(required=False)
    
    def validate_email(self, value):
        """验证邮箱"""
        if value and not ValidationUtils.validate_email(value):
            raise serializers.ValidationError('邮箱格式不正确')
        
        # 检查邮箱唯一性（排除当前用户）
        if value and hasattr(self, 'instance') and self.instance:
            if User.objects.filter(email=value).exclude(id=self.instance.user.id).exists():
                raise serializers.ValidationError('邮箱已存在')
        
        return value


class UserRoleChangeSerializer(serializers.Serializer):
    """
    用户角色变更序列化器
    """
    
    role = serializers.ChoiceField(choices=UserProfile.ROLE_CHOICES)
    
    def validate_role(self, value):
        """验证角色"""
        if not value:
            raise serializers.ValidationError('角色不能为空')
        
        return value


class UserDepartmentChangeSerializer(serializers.Serializer):
    """
    用户科室变更序列化器
    """
    
    department_id = serializers.IntegerField(required=False, allow_null=True)
    
    def validate_department_id(self, value):
        """验证科室"""
        if value is not None:
            try:
                Department.objects.get(id=value, is_active=True, is_deleted=False)
            except Department.DoesNotExist:
                raise serializers.ValidationError('科室不存在或已禁用')
        
        return value


class UserBulkActionSerializer(serializers.Serializer):
    """
    用户批量操作序列化器
    """
    
    user_ids = serializers.ListField(
        child=serializers.IntegerField(),
        min_length=1,
        max_length=100
    )
    action = serializers.ChoiceField(choices=['activate', 'deactivate'])
    
    def validate_user_ids(self, value):
        """验证用户ID列表"""
        if not value:
            raise serializers.ValidationError('用户ID列表不能为空')
        
        # 检查用户是否存在
        existing_ids = set(
            UserProfile.objects.filter(
                id__in=value,
                is_deleted=False
            ).values_list('id', flat=True)
        )
        
        invalid_ids = set(value) - existing_ids
        if invalid_ids:
            raise serializers.ValidationError(f'以下用户ID不存在: {list(invalid_ids)}')
        
        return value


class DepartmentCreateSerializer(serializers.Serializer):
    """
    科室创建序列化器
    """
    
    name = serializers.CharField(max_length=100)
    code = serializers.CharField(max_length=20)
    is_active = serializers.BooleanField(default=True)
    
    def validate_code(self, value):
        """验证科室代码"""
        if not value:
            raise serializers.ValidationError('科室代码不能为空')
        
        value = value.strip().upper()
        
        if len(value) < 2 or len(value) > 20:
            raise serializers.ValidationError('科室代码长度必须在2-20个字符之间')
        
        if not value.replace('_', '').isalnum():
            raise serializers.ValidationError('科室代码只能包含字母、数字和下划线')
        
        if Department.objects.filter(code=value, is_deleted=False).exists():
            raise serializers.ValidationError('科室代码已存在')
        
        return value
    
    def validate_name(self, value):
        """验证科室名称"""
        if not value:
            raise serializers.ValidationError('科室名称不能为空')
        
        value = value.strip()
        
        if len(value) < 2:
            raise serializers.ValidationError('科室名称至少需要2个字符')
        
        if Department.objects.filter(name=value, is_deleted=False).exists():
            raise serializers.ValidationError('科室名称已存在')
        
        return value
