{% extends 'reports/base.html' %}
{% load static %}

{% block page_title %}待审核报告{% endblock %}
{% block page_heading %}待审核报告{% endblock %}
{% block page_description %}需要审核的不良事件报告列表{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item active">待审核报告</li>
{% endblock %}

{% block page_actions %}
<div class="btn-group" role="group">
    <a href="{% url 'reports:report_list' %}" class="btn btn-outline-primary">
        <i class="bi bi-list me-2"></i>
        全部报告
    </a>
    <a href="{% url 'reports:serious_events' %}" class="btn btn-outline-danger">
        <i class="bi bi-exclamation-triangle me-2"></i>
        严重事件
    </a>
</div>
{% endblock %}

{% block reports_content %}
<!-- 统计信息 -->
<div class="row mb-4">
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="stat-card card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="stat-icon bg-warning">
                    <i class="bi bi-clock"></i>
                </div>
                <h4 class="stat-number">{{ total_count|default:0 }}</h4>
                <p class="stat-label">待审核</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="stat-card card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="stat-icon bg-danger">
                    <i class="bi bi-exclamation-triangle"></i>
                </div>
                <h4 class="stat-number">{{ reports|length|default:0 }}</h4>
                <p class="stat-label">本页显示</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="stat-card card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="stat-icon bg-info">
                    <i class="bi bi-calendar-day"></i>
                </div>
                <h4 class="stat-number">{{ today_count|default:0 }}</h4>
                <p class="stat-label">今日新增</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="stat-card card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="stat-icon bg-success">
                    <i class="bi bi-speedometer2"></i>
                </div>
                <h4 class="stat-number">{{ urgent_count|default:0 }}</h4>
                <p class="stat-label">紧急处理</p>
            </div>
        </div>
    </div>
</div>

<!-- 报告列表 -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-warning text-dark">
        <div class="row align-items-center">
            <div class="col">
                <h5 class="card-title mb-0">
                    <i class="bi bi-eye me-2"></i>
                    待审核报告列表
                </h5>
            </div>
            <div class="col-auto">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-dark btn-sm" id="refreshBtn">
                        <i class="bi bi-arrow-clockwise me-1"></i>
                        刷新
                    </button>
                    <button type="button" class="btn btn-outline-dark btn-sm" id="batchReviewBtn">
                        <i class="bi bi-check-all me-1"></i>
                        批量审核
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        {% if reports %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-dark" style="background-color: #000000 !important;">
                    <tr>
                        <th width="40" style="background-color: #000000 !important; color: #ffffff !important; font-weight: bold; text-align: center; padding: 12px 8px;">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="selectAll">
                            </div>
                        </th>
                        <th style="background-color: #000000 !important; color: #ffffff !important; font-weight: bold; text-align: center; padding: 12px 8px;">报告编号</th>
                        <th style="background-color: #000000 !important; color: #ffffff !important; font-weight: bold; text-align: center; padding: 12px 8px;">器械名称</th>
                        <th style="background-color: #000000 !important; color: #ffffff !important; font-weight: bold; text-align: center; padding: 12px 8px;">上报人</th>
                        <th style="background-color: #000000 !important; color: #ffffff !important; font-weight: bold; text-align: center; padding: 12px 8px;">科室</th>
                        <th style="background-color: #000000 !important; color: #ffffff !important; font-weight: bold; text-align: center; padding: 12px 8px;">事件日期</th>
                        <th style="background-color: #000000 !important; color: #ffffff !important; font-weight: bold; text-align: center; padding: 12px 8px;">伤害程度</th>
                        <th style="background-color: #000000 !important; color: #ffffff !important; font-weight: bold; text-align: center; padding: 12px 8px;">提交时间</th>
                        <th style="background-color: #000000 !important; color: #ffffff !important; font-weight: bold; text-align: center; padding: 12px 8px;">等待时间</th>
                        <th width="150" style="background-color: #000000 !important; color: #ffffff !important; font-weight: bold; text-align: center; padding: 12px 8px;">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for report in reports %}
                    <tr class="{% if report.injury_level == 'death' or report.injury_level == 'severe' %}table-danger{% endif %}">
                        <td>
                            <div class="form-check">
                                <input class="form-check-input row-checkbox" type="checkbox" value="{{ report.id }}">
                            </div>
                        </td>
                        <td>
                            <a href="{% url 'reports:report_detail' report_id=report.id %}" class="text-decoration-none fw-bold">
                                {{ report.report_number }}
                            </a>
                            {% if report.injury_level == 'death' or report.injury_level == 'severe' %}
                            <span class="badge bg-danger ms-1">紧急</span>
                            {% endif %}
                        </td>
                        <td>{{ report.device_name|truncatechars:25 }}</td>
                        <td>{{ report.reporter.username }}</td>
                        <td>{{ report.department.name|default:"未指定" }}</td>
                        <td>{{ report.event_date|date:"m-d" }}</td>
                        <td>
                            <span class="badge bg-{% if report.injury_level == 'death' %}danger{% elif report.injury_level == 'severe' %}warning{% elif report.injury_level == 'moderate' %}info{% else %}secondary{% endif %}">
                                {{ report.get_injury_level_display }}
                            </span>
                        </td>
                        <td>{{ report.submitted_at|date:"m-d H:i" }}</td>
                        <td>
                            <span class="text-muted small">
                                {{ report.submitted_at|timesince }}前
                            </span>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{% url 'reports:report_detail' report_id=report.id %}" class="btn btn-outline-primary btn-sm" title="查看详情">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <a href="{% url 'reports:report_review' report_id=report.id %}" class="btn btn-warning btn-sm" title="审核">
                                    <i class="bi bi-eye-fill"></i>
                                </a>
                                <button type="button" class="btn btn-outline-success btn-sm quick-approve-btn" data-report-id="{{ report.id }}" title="快速批准">
                                    <i class="bi bi-check"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- 分页 -->
        {% if reports.has_other_pages %}
        <nav aria-label="待审核报告分页" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if reports.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page=1">首页</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ reports.previous_page_number }}">上一页</a>
                </li>
                {% endif %}

                {% for num in reports.paginator.page_range %}
                {% if reports.number == num %}
                <li class="page-item active">
                    <span class="page-link">{{ num }}</span>
                </li>
                {% elif num > reports.number|add:'-3' and num < reports.number|add:'3' %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                </li>
                {% endif %}
                {% endfor %}

                {% if reports.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ reports.next_page_number }}">下一页</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ reports.paginator.num_pages }}">末页</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
        
        {% else %}
        <!-- 空状态 -->
        <div class="text-center py-5">
            <i class="bi bi-check-circle display-1 text-success"></i>
            <h4 class="mt-3 text-muted">暂无待审核报告</h4>
            <p class="text-muted">所有报告都已审核完成</p>
            <a href="{% url 'reports:report_list' %}" class="btn btn-primary">
                <i class="bi bi-list me-2"></i>
                查看全部报告
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- 快速批准模态框 -->
<div class="modal fade" id="quickApproveModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-check-circle me-2"></i>
                    快速批准
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要批准此报告吗？</p>
                <div class="mb-3">
                    <label for="quickApproveComments" class="form-label">审核意见（可选）</label>
                    <textarea class="form-control" id="quickApproveComments" rows="3" placeholder="请填写审核意见..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-success" id="confirmQuickApproveBtn">确认批准</button>
            </div>
        </div>
    </div>
</div>

<!-- 批量审核模态框 -->
<div class="modal fade" id="batchReviewModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-check-all me-2"></i>
                    批量审核
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>已选择 <span id="selectedCount">0</span> 个报告</p>
                <div class="mb-3">
                    <label for="batchAction" class="form-label">审核决定</label>
                    <select class="form-select" id="batchAction">
                        <option value="">请选择</option>
                        <option value="approve">批准</option>
                        <option value="start_review">开始审核</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label for="batchComments" class="form-label">审核意见</label>
                    <textarea class="form-control" id="batchComments" rows="3" placeholder="请填写审核意见..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmBatchReviewBtn">确认审核</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block reports_extra_css %}
<style>
.stat-card {
    transition: transform 0.2s;
}
.stat-card:hover {
    transform: translateY(-2px);
}
.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    color: white;
    font-size: 24px;
}
.stat-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}
.stat-label {
    color: #6c757d;
    margin-bottom: 0;
}

.table-danger {
    --bs-table-bg: rgba(220, 53, 69, 0.1);
}

.btn-group .btn {
    border-radius: 0.25rem;
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn-group .btn {
        padding: 0.25rem 0.5rem;
    }
    
    .stat-number {
        font-size: 1.5rem;
    }
}
</style>
{% endblock %}

{% block reports_extra_js %}
<script>
$(document).ready(function() {
    let currentReportId = null;
    
    // 全选/取消全选
    $('#selectAll').on('change', function() {
        $('.row-checkbox').prop('checked', this.checked);
        updateSelectedCount();
    });
    
    $('.row-checkbox').on('change', function() {
        updateSelectedCount();
    });
    
    function updateSelectedCount() {
        const selectedCount = $('.row-checkbox:checked').length;
        $('#selectedCount').text(selectedCount);
        
        // 更新全选状态
        const totalCount = $('.row-checkbox').length;
        $('#selectAll').prop('indeterminate', selectedCount > 0 && selectedCount < totalCount);
        $('#selectAll').prop('checked', selectedCount === totalCount && totalCount > 0);
    }

    // 快速批准
    $('.quick-approve-btn').on('click', function() {
        currentReportId = $(this).data('report-id');
        $('#quickApproveModal').modal('show');
    });

    $('#confirmQuickApproveBtn').on('click', function() {
        if (currentReportId) {
            const comments = $('#quickApproveComments').val();
            
            $.ajax({
                url: `/reports/api/reports/${currentReportId}/review/`,
                type: 'POST',
                headers: {
                    'X-CSRFToken': $('[name=csrfmiddlewaretoken]').val()
                },
                data: {
                    action: 'approve',
                    comments: comments
                },
                success: function(response) {
                    $('#quickApproveModal').modal('hide');
                    location.reload();
                },
                error: function(xhr) {
                    alert('审核失败：' + (xhr.responseJSON?.error || '未知错误'));
                }
            });
        }
    });

    // 批量审核
    $('#batchReviewBtn').on('click', function() {
        const selectedCount = $('.row-checkbox:checked').length;
        if (selectedCount === 0) {
            alert('请先选择要审核的报告');
            return;
        }
        $('#batchReviewModal').modal('show');
    });

    $('#confirmBatchReviewBtn').on('click', function() {
        const action = $('#batchAction').val();
        const comments = $('#batchComments').val();
        
        if (!action) {
            alert('请选择审核决定');
            return;
        }
        
        const selectedIds = $('.row-checkbox:checked').map(function() {
            return $(this).val();
        }).get();
        
        // TODO: 实现批量审核API调用
        alert('批量审核功能开发中...');
    });

    // 刷新按钮
    $('#refreshBtn').on('click', function() {
        location.reload();
    });

    // 初始化选择计数
    updateSelectedCount();
});
</script>
{% endblock %}
