"""
用户管理视图测试
User Management Views Tests
"""

from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse

from apps.users.models import UserProfile, Department


class UserViewTest(TestCase):
    """用户视图测试"""
    
    def setUp(self):
        """测试前准备"""
        self.client = Client()
        
        # 创建管理员用户
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>'
        )
        self.admin_profile = UserProfile.objects.create(
            user=self.admin_user,
            account_number='0001',
            role='admin',
            is_active=True
        )
        
        # 创建科室
        self.department = Department.objects.create(
            code='TEST',
            name='测试科室',
            created_by=self.admin_user
        )
        
        # 创建科室人员
        self.staff_user = User.objects.create_user(
            username='staff',
            email='<EMAIL>'
        )
        self.staff_profile = UserProfile.objects.create(
            user=self.staff_user,
            account_number='1001',
            department=self.department,
            role='staff',
            is_active=True
        )
    
    def test_login_view_get(self):
        """测试登录页面GET请求"""
        response = self.client.get('/login/')
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '用户登录')
        self.assertContains(response, 'account_number')
    
    def test_login_view_post_success(self):
        """测试登录POST请求成功"""
        response = self.client.post('/login/', {
            'account_number': '1001'
        })
        
        # 应该重定向到用户中心
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, '/dashboard/')
    
    def test_login_view_post_invalid_account(self):
        """测试登录POST请求无效账号"""
        response = self.client.post('/login/', {
            'account_number': '9999'
        })
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '账号不存在或已被禁用')
    
    def test_dashboard_view_admin(self):
        """测试管理员访问用户中心"""
        self.client.force_login(self.admin_user)
        
        response = self.client.get('/dashboard/')
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '用户中心')
        self.assertContains(response, '用户管理')  # 管理员应该看到用户管理
    
    def test_dashboard_view_staff(self):
        """测试科室人员访问用户中心"""
        self.client.force_login(self.staff_user)
        
        response = self.client.get('/dashboard/')
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '用户中心')
        # 科室人员不应该看到用户管理链接
        self.assertNotContains(response, '用户管理')
    
    def test_user_list_view_admin_access(self):
        """测试管理员访问用户列表"""
        self.client.force_login(self.admin_user)
        
        response = self.client.get('/users/')
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '用户管理')
        self.assertContains(response, self.staff_user.username)
    
    def test_user_list_view_staff_denied(self):
        """测试科室人员访问用户列表被拒绝"""
        self.client.force_login(self.staff_user)
        
        response = self.client.get('/users/')
        
        # 应该被重定向或返回403
        self.assertIn(response.status_code, [302, 403])
    
    def test_user_create_view_admin_access(self):
        """测试管理员访问用户创建页面"""
        self.client.force_login(self.admin_user)
        
        response = self.client.get('/users/create/')
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '创建用户')
        self.assertContains(response, 'account_number')
        self.assertContains(response, 'username')
    
    def test_user_create_view_post_success(self):
        """测试用户创建POST请求成功"""
        self.client.force_login(self.admin_user)
        
        data = {
            'account_number': '1002',
            'username': '新用户',
            'first_name': '新',
            'last_name': '用户',
            'email': '<EMAIL>',
            'role': 'staff',
            'department': self.department.id,
            'phone': '***********'
        }
        
        response = self.client.post('/users/create/', data)
        
        # 应该重定向到用户列表
        self.assertEqual(response.status_code, 302)
        
        # 验证用户被创建
        self.assertTrue(UserProfile.objects.filter(account_number='1002').exists())
    
    def test_user_edit_view_admin_access(self):
        """测试管理员访问用户编辑页面"""
        self.client.force_login(self.admin_user)
        
        response = self.client.get(f'/users/{self.staff_profile.id}/edit/')
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '编辑用户')
        self.assertContains(response, self.staff_user.username)
    
    def test_user_edit_view_post_success(self):
        """测试用户编辑POST请求成功"""
        self.client.force_login(self.admin_user)
        
        data = {
            'username': '更新的用户名',
            'first_name': '更新',
            'last_name': '用户',
            'email': '<EMAIL>',
            'role': 'staff',
            'department': self.department.id,
            'phone': '***********'
        }
        
        response = self.client.post(f'/users/{self.staff_profile.id}/edit/', data)
        
        # 应该重定向到用户列表
        self.assertEqual(response.status_code, 302)
        
        # 验证用户被更新
        self.staff_profile.refresh_from_db()
        self.assertEqual(self.staff_profile.user.username, '更新的用户名')
        self.assertEqual(self.staff_profile.phone, '***********')
    
    def test_user_toggle_status_view(self):
        """测试用户状态切换"""
        self.client.force_login(self.admin_user)
        
        # 禁用用户
        response = self.client.post(f'/users/{self.staff_profile.id}/toggle-status/')
        
        self.assertEqual(response.status_code, 302)
        
        # 验证用户被禁用
        self.staff_profile.refresh_from_db()
        self.assertFalse(self.staff_profile.is_active)
        
        # 再次切换，应该激活用户
        response = self.client.post(f'/users/{self.staff_profile.id}/toggle-status/')
        
        self.assertEqual(response.status_code, 302)
        
        # 验证用户被激活
        self.staff_profile.refresh_from_db()
        self.assertTrue(self.staff_profile.is_active)
    
    def test_logout_view(self):
        """测试登出视图"""
        self.client.force_login(self.admin_user)
        
        response = self.client.post('/logout/')
        
        # 应该重定向到登录页面
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, '/login/')
        
        # 验证用户已登出
        response = self.client.get('/dashboard/')
        self.assertEqual(response.status_code, 302)  # 重定向到登录页面


class PermissionViewTest(TestCase):
    """权限视图测试"""
    
    def setUp(self):
        """测试前准备"""
        self.client = Client()
        
        # 创建管理员用户
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>'
        )
        self.admin_profile = UserProfile.objects.create(
            user=self.admin_user,
            account_number='0001',
            role='admin',
            is_active=True
        )
        
        # 创建科室
        self.department = Department.objects.create(
            code='TEST',
            name='测试科室',
            created_by=self.admin_user
        )
        
        # 创建科室人员
        self.staff_user = User.objects.create_user(
            username='staff',
            email='<EMAIL>'
        )
        self.staff_profile = UserProfile.objects.create(
            user=self.staff_user,
            account_number='1001',
            department=self.department,
            role='staff',
            is_active=True
        )
        
        # 创建禁用用户
        self.inactive_user = User.objects.create_user(
            username='inactive',
            email='<EMAIL>'
        )
        self.inactive_profile = UserProfile.objects.create(
            user=self.inactive_user,
            account_number='1002',
            department=self.department,
            role='staff',
            is_active=False
        )
    
    def test_unauthenticated_access_redirects(self):
        """测试未认证用户访问受保护页面重定向"""
        protected_urls = [
            '/dashboard/',
            '/users/',
            '/users/create/',
            f'/users/{self.staff_profile.id}/edit/',
        ]
        
        for url in protected_urls:
            response = self.client.get(url)
            self.assertEqual(response.status_code, 302)
            self.assertTrue(response.url.startswith('/login/'))
    
    def test_inactive_user_access_denied(self):
        """测试禁用用户访问被拒绝"""
        self.client.force_login(self.inactive_user)
        
        response = self.client.get('/dashboard/')
        
        # 应该被拒绝访问
        self.assertIn(response.status_code, [302, 403])
    
    def test_staff_access_restrictions(self):
        """测试科室人员访问限制"""
        self.client.force_login(self.staff_user)
        
        # 科室人员不能访问的页面
        restricted_urls = [
            '/users/',
            '/users/create/',
            f'/users/{self.admin_profile.id}/edit/',
        ]
        
        for url in restricted_urls:
            response = self.client.get(url)
            self.assertIn(response.status_code, [302, 403])
    
    def test_admin_full_access(self):
        """测试管理员完全访问权限"""
        self.client.force_login(self.admin_user)
        
        # 管理员可以访问的页面
        accessible_urls = [
            '/dashboard/',
            '/users/',
            '/users/create/',
            f'/users/{self.staff_profile.id}/edit/',
        ]
        
        for url in accessible_urls:
            response = self.client.get(url)
            self.assertEqual(response.status_code, 200)


class TemplateRenderingTest(TestCase):
    """模板渲染测试"""
    
    def setUp(self):
        """测试前准备"""
        self.client = Client()
        
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>'
        )
        self.admin_profile = UserProfile.objects.create(
            user=self.admin_user,
            account_number='0001',
            role='admin',
            is_active=True
        )
    
    def test_login_template_rendering(self):
        """测试登录模板渲染"""
        response = self.client.get('/login/')
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'users/login.html')
        self.assertContains(response, '医疗器械不良事件上报平台')
    
    def test_dashboard_template_rendering(self):
        """测试用户中心模板渲染"""
        self.client.force_login(self.admin_user)
        
        response = self.client.get('/dashboard/')
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'users/dashboard.html')
        self.assertContains(response, '用户中心')
    
    def test_user_list_template_rendering(self):
        """测试用户列表模板渲染"""
        self.client.force_login(self.admin_user)
        
        response = self.client.get('/users/')
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'users/user_list.html')
        self.assertContains(response, '用户管理')
    
    def test_user_create_template_rendering(self):
        """测试用户创建模板渲染"""
        self.client.force_login(self.admin_user)
        
        response = self.client.get('/users/create/')
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'users/user_create.html')
        self.assertContains(response, '创建用户')
