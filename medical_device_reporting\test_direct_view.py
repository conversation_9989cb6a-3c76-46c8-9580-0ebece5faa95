#!/usr/bin/env python
"""
直接测试视图函数
Direct View Function Test
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from django.test import RequestFactory
from django.contrib.auth.models import User
from apps.users.models import UserProfile, Department
from apps.reports.apis import ReportExportAPIView

def test_direct_view():
    """直接测试视图函数"""
    print("=== 直接视图函数测试 ===")
    
    try:
        # 创建测试用户
        import uuid
        unique_id = str(uuid.uuid4())[:8]
        
        # 创建科室
        department = Department.objects.create(
            code=f'DEPT_{unique_id[:4]}',
            name=f'测试科室_{unique_id[:4]}',
            is_active=True,
            created_by_id=1
        )
        
        # 创建管理员用户
        admin_user = User.objects.create_user(
            username=f'admin_{unique_id}', 
            email='<EMAIL>'
        )
        admin_profile = UserProfile.objects.create(
            user=admin_user,
            account_number=f'{hash(unique_id) % 9000 + 1000:04d}',
            department=department,
            role='admin',
            created_by=admin_user
        )
        
        # 创建请求工厂
        factory = RequestFactory()
        
        # 创建GET请求
        request = factory.get('/reports/api/export/', {
            'format': 'excel',
            'analysis_type': 'summary'
        })
        request.user = admin_user
        
        # 直接调用视图
        view = ReportExportAPIView()
        view.setup(request)
        
        print("调用视图函数...")
        response = view.get(request)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应类型: {type(response).__name__}")
        
        if hasattr(response, 'content'):
            print(f"响应内容大小: {len(response.content)} bytes")
        
        if hasattr(response, 'get'):
            content_type = response.get('Content-Type', 'N/A')
            content_disposition = response.get('Content-Disposition', 'N/A')
            print(f"Content-Type: {content_type}")
            print(f"Content-Disposition: {content_disposition}")
        
        if response.status_code == 200:
            print("✅ 视图函数调用成功！")
            return True
        else:
            print(f"❌ 视图函数返回错误状态码: {response.status_code}")
            if hasattr(response, 'data'):
                print(f"错误信息: {response.data}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理测试数据
        try:
            UserProfile.objects.filter(created_by=admin_user).delete()
            Department.objects.filter(code__contains=unique_id[:4]).delete()
            User.objects.filter(username__contains=unique_id).delete()
        except:
            pass

def main():
    """主测试函数"""
    print("🔧 开始直接视图函数测试...")
    
    success = test_direct_view()
    
    if success:
        print("🎉 直接视图函数测试成功！")
    else:
        print("❌ 直接视图函数测试失败")
    
    return success

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
