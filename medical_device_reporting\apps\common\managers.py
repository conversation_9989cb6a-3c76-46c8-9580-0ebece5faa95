"""
通用管理器和查询集
Common managers and querysets for Medical Device Reporting Platform
"""

from django.db import models
from django.utils import timezone


class SoftDeleteQuerySet(models.QuerySet):
    """
    软删除查询集
    
    提供软删除相关的查询方法
    """
    
    def active(self):
        """
        获取未删除的记录
        
        Returns:
            QuerySet: 未删除的记录查询集
        """
        return self.filter(is_deleted=False)
    
    def deleted(self):
        """
        获取已删除的记录
        
        Returns:
            QuerySet: 已删除的记录查询集
        """
        return self.filter(is_deleted=True)
    
    def soft_delete(self):
        """
        批量软删除
        
        Returns:
            int: 受影响的记录数
        """
        return self.update(
            is_deleted=True,
            deleted_at=timezone.now()
        )
    
    def restore(self):
        """
        批量恢复软删除的记录
        
        Returns:
            int: 受影响的记录数
        """
        return self.update(
            is_deleted=False,
            deleted_at=None
        )
    
    def hard_delete(self):
        """
        批量硬删除（真正删除）
        
        Returns:
            tuple: (删除的记录数, 删除详情字典)
        """
        return self.delete()


class SoftDeleteManager(models.Manager):
    """
    软删除管理器
    
    默认只返回未删除的记录
    """
    
    def get_queryset(self):
        """
        获取查询集，默认过滤掉已删除的记录
        
        Returns:
            SoftDeleteQuerySet: 查询集
        """
        return SoftDeleteQuerySet(self.model, using=self._db).active()
    
    def all_with_deleted(self):
        """
        获取所有记录（包括已删除的）
        
        Returns:
            SoftDeleteQuerySet: 包含所有记录的查询集
        """
        return SoftDeleteQuerySet(self.model, using=self._db)
    
    def deleted_only(self):
        """
        只获取已删除的记录
        
        Returns:
            SoftDeleteQuerySet: 已删除记录的查询集
        """
        return SoftDeleteQuerySet(self.model, using=self._db).deleted()


class StatusQuerySet(models.QuerySet):
    """
    状态查询集
    
    提供状态相关的查询方法
    """
    
    def active(self):
        """
        获取活跃状态的记录
        
        Returns:
            QuerySet: 活跃状态的记录查询集
        """
        return self.filter(status='active')
    
    def inactive(self):
        """
        获取非活跃状态的记录
        
        Returns:
            QuerySet: 非活跃状态的记录查询集
        """
        return self.filter(status='inactive')
    
    def draft(self):
        """
        获取草稿状态的记录
        
        Returns:
            QuerySet: 草稿状态的记录查询集
        """
        return self.filter(status='draft')
    
    def archived(self):
        """
        获取已归档状态的记录
        
        Returns:
            QuerySet: 已归档状态的记录查询集
        """
        return self.filter(status='archived')
    
    def by_status(self, status):
        """
        根据状态获取记录
        
        Args:
            status: 状态值
            
        Returns:
            QuerySet: 指定状态的记录查询集
        """
        return self.filter(status=status)


class StatusManager(models.Manager):
    """
    状态管理器
    """
    
    def get_queryset(self):
        """
        获取查询集
        
        Returns:
            StatusQuerySet: 状态查询集
        """
        return StatusQuerySet(self.model, using=self._db)
    
    def active(self):
        """
        获取活跃状态的记录
        
        Returns:
            QuerySet: 活跃状态的记录查询集
        """
        return self.get_queryset().active()
    
    def inactive(self):
        """
        获取非活跃状态的记录
        
        Returns:
            QuerySet: 非活跃状态的记录查询集
        """
        return self.get_queryset().inactive()


class AuditableQuerySet(models.QuerySet):
    """
    可审计查询集
    
    提供审计相关的查询方法
    """
    
    def created_by(self, user):
        """
        根据创建者获取记录
        
        Args:
            user: 用户对象
            
        Returns:
            QuerySet: 指定创建者的记录查询集
        """
        return self.filter(created_by=user)
    
    def updated_by(self, user):
        """
        根据最后修改者获取记录
        
        Args:
            user: 用户对象
            
        Returns:
            QuerySet: 指定修改者的记录查询集
        """
        return self.filter(updated_by=user)
    
    def created_between(self, start_date, end_date):
        """
        根据创建时间范围获取记录
        
        Args:
            start_date: 开始时间
            end_date: 结束时间
            
        Returns:
            QuerySet: 指定时间范围内创建的记录查询集
        """
        return self.filter(
            created_at__gte=start_date,
            created_at__lte=end_date
        )
    
    def updated_between(self, start_date, end_date):
        """
        根据更新时间范围获取记录
        
        Args:
            start_date: 开始时间
            end_date: 结束时间
            
        Returns:
            QuerySet: 指定时间范围内更新的记录查询集
        """
        return self.filter(
            updated_at__gte=start_date,
            updated_at__lte=end_date
        )


class AuditableManager(models.Manager):
    """
    可审计管理器
    """
    
    def get_queryset(self):
        """
        获取查询集
        
        Returns:
            AuditableQuerySet: 可审计查询集
        """
        return AuditableQuerySet(self.model, using=self._db)
    
    def created_by(self, user):
        """
        根据创建者获取记录
        
        Args:
            user: 用户对象
            
        Returns:
            QuerySet: 指定创建者的记录查询集
        """
        return self.get_queryset().created_by(user)


class CombinedQuerySet(SoftDeleteQuerySet, StatusQuerySet, AuditableQuerySet):
    """
    组合查询集
    
    结合软删除、状态和审计功能
    """
    pass


class CombinedManager(models.Manager):
    """
    组合管理器
    
    结合软删除、状态和审计功能
    """
    
    def get_queryset(self):
        """
        获取查询集
        
        Returns:
            CombinedQuerySet: 组合查询集
        """
        return CombinedQuerySet(self.model, using=self._db).active()
    
    def all_with_deleted(self):
        """
        获取所有记录（包括已删除的）
        
        Returns:
            CombinedQuerySet: 包含所有记录的查询集
        """
        return CombinedQuerySet(self.model, using=self._db)
