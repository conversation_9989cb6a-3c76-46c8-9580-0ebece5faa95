"""
报告管理服务测试
Reports Management Services Tests
"""

from django.test import TestCase
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError, PermissionDenied
from datetime import date, timedelta

from apps.users.models import UserProfile, Department
from apps.reports.models import AdverseEventReport
from apps.reports.services import (
    report_create,
    report_update,
    report_submit,
    report_review,
    report_delete
)


class ReportServicesTest(TestCase):
    """报告服务测试"""
    
    def setUp(self):
        """测试前准备"""
        # 创建管理员用户
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>'
        )
        
        # 创建普通用户
        self.staff_user = User.objects.create_user(
            username='staff',
            first_name='测试',
            last_name='用户',
            email='<EMAIL>'
        )
        
        # 创建科室
        self.department = Department.objects.create(
            code='TEST',
            name='测试科室',
            created_by=self.admin_user
        )
        
        # 创建用户配置文件
        self.admin_profile = UserProfile.objects.create(
            user=self.admin_user,
            account_number='0001',
            role='admin',
            created_by=self.admin_user
        )
        
        self.staff_profile = UserProfile.objects.create(
            user=self.staff_user,
            account_number='1001',
            department=self.department,
            role='staff',
            created_by=self.admin_user
        )
    
    def test_report_create_service(self):
        """测试报告创建服务"""
        data = {
            'reporter_name': '测试用户',
            'reporter_phone': '***********',
            'patient_name': '张三',
            'patient_age': 45,
            'patient_gender': 'male',
            'event_date': date.today(),
            'injury_level': 'moderate',
            'event_description': '测试事件描述',
            'device_name': '测试器械',
            'registration_number': 'TEST123456',
            'manufacturer': '测试厂商'
        }
        
        report = report_create(user=self.staff_user, data=data)
        
        self.assertIsInstance(report, AdverseEventReport)
        self.assertEqual(report.reporter, self.staff_user)
        self.assertEqual(report.patient_name, '张三')
        self.assertEqual(report.device_name, '测试器械')
        self.assertEqual(report.status, 'draft')
        self.assertEqual(report.department, self.department)
    
    def test_report_create_validation_error(self):
        """测试报告创建验证错误"""
        # 缺少必填字段
        data = {
            'reporter_name': '测试用户',
            # 缺少 patient_name
            'device_name': '测试器械',
            'event_date': date.today(),
            'injury_level': 'moderate',
            'event_description': '测试事件描述'
        }
        
        with self.assertRaises(ValidationError):
            report_create(user=self.staff_user, data=data)
    
    def test_report_update_service(self):
        """测试报告更新服务"""
        # 创建报告
        report = AdverseEventReport.objects.create(
            reporter=self.staff_user,
            reporter_name='测试用户',
            patient_name='张三',
            event_date=date.today(),
            injury_level='moderate',
            event_description='测试事件描述',
            device_name='测试器械'
        )
        
        # 更新数据
        update_data = {
            'patient_name': '李四',
            'device_name': '更新器械',
            'injury_level': 'severe'
        }
        
        updated_report = report_update(
            report_id=report.id,
            user=self.staff_user,
            data=update_data
        )
        
        self.assertEqual(updated_report.patient_name, '李四')
        self.assertEqual(updated_report.device_name, '更新器械')
        self.assertEqual(updated_report.injury_level, 'severe')
    
    def test_report_update_permission_denied(self):
        """测试报告更新权限拒绝"""
        # 创建其他用户的报告
        other_user = User.objects.create_user(username='other')
        other_profile = UserProfile.objects.create(
            user=other_user,
            account_number='2001',
            department=self.department,
            role='staff',
            created_by=self.admin_user
        )
        
        report = AdverseEventReport.objects.create(
            reporter=other_user,
            reporter_name='其他用户',
            patient_name='王五',
            event_date=date.today(),
            injury_level='moderate',
            event_description='其他事件描述',
            device_name='其他器械'
        )
        
        # 普通用户不能更新其他人的报告
        update_data = {'patient_name': '修改名称'}
        
        with self.assertRaises(PermissionDenied):
            report_update(
                report_id=report.id,
                user=self.staff_user,
                data=update_data
            )
    
    def test_admin_can_update_any_report(self):
        """测试管理员可以更新任何报告"""
        # 创建其他用户的报告
        report = AdverseEventReport.objects.create(
            reporter=self.staff_user,
            reporter_name='测试用户',
            patient_name='张三',
            event_date=date.today(),
            injury_level='moderate',
            event_description='测试事件描述',
            device_name='测试器械'
        )
        
        # 管理员可以更新任何报告
        update_data = {'patient_name': '管理员修改'}
        
        updated_report = report_update(
            report_id=report.id,
            user=self.admin_user,
            data=update_data
        )
        
        self.assertEqual(updated_report.patient_name, '管理员修改')
    
    def test_report_submit_service(self):
        """测试报告提交服务"""
        report = AdverseEventReport.objects.create(
            reporter=self.staff_user,
            reporter_name='测试用户',
            patient_name='张三',
            event_date=date.today(),
            injury_level='moderate',
            event_description='测试事件描述',
            device_name='测试器械'
        )
        
        submitted_report = report_submit(
            report_id=report.id,
            user=self.staff_user
        )
        
        self.assertEqual(submitted_report.status, 'submitted')
        self.assertIsNotNone(submitted_report.submitted_at)
    
    def test_report_submit_invalid_status(self):
        """测试提交无效状态的报告"""
        report = AdverseEventReport.objects.create(
            reporter=self.staff_user,
            reporter_name='测试用户',
            patient_name='张三',
            event_date=date.today(),
            injury_level='moderate',
            event_description='测试事件描述',
            device_name='测试器械',
            status='approved'  # 已批准状态不能再提交
        )
        
        with self.assertRaises(ValidationError):
            report_submit(report_id=report.id, user=self.staff_user)
    
    def test_report_review_service(self):
        """测试报告审核服务"""
        report = AdverseEventReport.objects.create(
            reporter=self.staff_user,
            reporter_name='测试用户',
            patient_name='张三',
            event_date=date.today(),
            injury_level='moderate',
            event_description='测试事件描述',
            device_name='测试器械',
            status='submitted'
        )
        
        # 管理员审核批准
        reviewed_report = report_review(
            report_id=report.id,
            user=self.admin_user,
            action='approve',
            comments='审核通过'
        )
        
        self.assertEqual(reviewed_report.status, 'approved')
        self.assertEqual(reviewed_report.review_comments, '审核通过')
        self.assertEqual(reviewed_report.reviewed_by, self.admin_user)
        self.assertIsNotNone(reviewed_report.reviewed_at)
    
    def test_report_review_reject(self):
        """测试报告审核拒绝"""
        report = AdverseEventReport.objects.create(
            reporter=self.staff_user,
            reporter_name='测试用户',
            patient_name='张三',
            event_date=date.today(),
            injury_level='moderate',
            event_description='测试事件描述',
            device_name='测试器械',
            status='submitted'
        )
        
        # 管理员审核拒绝
        reviewed_report = report_review(
            report_id=report.id,
            user=self.admin_user,
            action='reject',
            comments='信息不完整，请补充'
        )
        
        self.assertEqual(reviewed_report.status, 'rejected')
        self.assertEqual(reviewed_report.review_comments, '信息不完整，请补充')
    
    def test_report_review_permission_denied(self):
        """测试报告审核权限拒绝"""
        report = AdverseEventReport.objects.create(
            reporter=self.staff_user,
            reporter_name='测试用户',
            patient_name='张三',
            event_date=date.today(),
            injury_level='moderate',
            event_description='测试事件描述',
            device_name='测试器械',
            status='submitted'
        )
        
        # 普通用户不能审核报告
        with self.assertRaises(PermissionDenied):
            report_review(
                report_id=report.id,
                user=self.staff_user,
                action='approve',
                comments='普通用户审核'
            )
    
    def test_report_delete_service(self):
        """测试报告删除服务"""
        report = AdverseEventReport.objects.create(
            reporter=self.staff_user,
            reporter_name='测试用户',
            patient_name='张三',
            event_date=date.today(),
            injury_level='moderate',
            event_description='测试事件描述',
            device_name='测试器械'
        )
        
        # 软删除报告
        deleted_report = report_delete(
            report_id=report.id,
            user=self.staff_user
        )
        
        self.assertTrue(deleted_report.is_deleted)
        self.assertIsNotNone(deleted_report.deleted_at)
        self.assertEqual(deleted_report.deleted_by, self.staff_user)
    
    def test_report_delete_submitted_report(self):
        """测试删除已提交的报告"""
        report = AdverseEventReport.objects.create(
            reporter=self.staff_user,
            reporter_name='测试用户',
            patient_name='张三',
            event_date=date.today(),
            injury_level='moderate',
            event_description='测试事件描述',
            device_name='测试器械',
            status='submitted'
        )
        
        # 已提交的报告不能删除
        with self.assertRaises(ValidationError):
            report_delete(report_id=report.id, user=self.staff_user)
    
    def test_admin_can_delete_any_report(self):
        """测试管理员可以删除任何报告"""
        report = AdverseEventReport.objects.create(
            reporter=self.staff_user,
            reporter_name='测试用户',
            patient_name='张三',
            event_date=date.today(),
            injury_level='moderate',
            event_description='测试事件描述',
            device_name='测试器械',
            status='submitted'
        )
        
        # 管理员可以删除任何状态的报告
        deleted_report = report_delete(
            report_id=report.id,
            user=self.admin_user
        )
        
        self.assertTrue(deleted_report.is_deleted)
