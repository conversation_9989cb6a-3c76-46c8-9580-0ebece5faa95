"""
权限系统测试
Permission System Tests
"""

from django.test import TestCase, RequestFactory
from django.contrib.auth.models import User, AnonymousUser
from django.core.exceptions import PermissionDenied
from rest_framework.test import APIRequestFactory

from apps.users.models import UserProfile, Department
from apps.users.permissions import (
    IsAdminUser, IsDepartmentMemberOrAdmin, IsOwnerOrAdmin,
    admin_required, department_member_or_admin_required,
    owner_or_admin_required, get_user_role_permissions
)


class PermissionClassTest(TestCase):
    """权限类测试"""
    
    def setUp(self):
        """测试前准备"""
        # 创建管理员用户
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>'
        )
        self.admin_profile = UserProfile.objects.create(
            user=self.admin_user,
            account_number='0001',
            role='admin',
            is_active=True
        )
        
        # 创建科室
        self.department = Department.objects.create(
            code='TEST',
            name='测试科室',
            created_by=self.admin_user
        )
        
        # 创建科室人员
        self.staff_user = User.objects.create_user(
            username='staff',
            email='<EMAIL>'
        )
        self.staff_profile = UserProfile.objects.create(
            user=self.staff_user,
            account_number='1001',
            department=self.department,
            role='staff',
            is_active=True
        )
        
        # 创建禁用用户
        self.inactive_user = User.objects.create_user(
            username='inactive',
            email='<EMAIL>'
        )
        self.inactive_profile = UserProfile.objects.create(
            user=self.inactive_user,
            account_number='1002',
            department=self.department,
            role='staff',
            is_active=False
        )
        
        self.factory = APIRequestFactory()
    
    def test_is_admin_user_permission(self):
        """测试管理员权限类"""
        permission = IsAdminUser()
        
        # 测试管理员用户
        request = self.factory.get('/')
        request.user = self.admin_user
        self.assertTrue(permission.has_permission(request, None))
        
        # 测试科室人员
        request.user = self.staff_user
        self.assertFalse(permission.has_permission(request, None))
        
        # 测试禁用用户
        request.user = self.inactive_user
        self.assertFalse(permission.has_permission(request, None))
        
        # 测试匿名用户
        request.user = AnonymousUser()
        self.assertFalse(permission.has_permission(request, None))
    
    def test_is_department_member_or_admin_permission(self):
        """测试科室成员或管理员权限类"""
        permission = IsDepartmentMemberOrAdmin()
        
        # 测试管理员用户
        request = self.factory.get('/')
        request.user = self.admin_user
        self.assertTrue(permission.has_permission(request, None))
        
        # 测试科室人员
        request.user = self.staff_user
        self.assertTrue(permission.has_permission(request, None))
        
        # 测试禁用用户
        request.user = self.inactive_user
        self.assertFalse(permission.has_permission(request, None))
        
        # 测试匿名用户
        request.user = AnonymousUser()
        self.assertFalse(permission.has_permission(request, None))
    
    def test_is_owner_or_admin_permission(self):
        """测试对象所有者或管理员权限类"""
        permission = IsOwnerOrAdmin()
        
        request = self.factory.get('/')
        
        # 测试管理员访问任何对象
        request.user = self.admin_user
        self.assertTrue(permission.has_object_permission(request, None, self.staff_profile))
        
        # 测试用户访问自己的对象
        request.user = self.staff_user
        self.assertTrue(permission.has_object_permission(request, None, self.staff_profile))
        
        # 测试用户访问他人的对象
        request.user = self.staff_user
        self.assertFalse(permission.has_object_permission(request, None, self.admin_profile))


class PermissionDecoratorTest(TestCase):
    """权限装饰器测试"""
    
    def setUp(self):
        """测试前准备"""
        # 创建管理员用户
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>'
        )
        self.admin_profile = UserProfile.objects.create(
            user=self.admin_user,
            account_number='0001',
            role='admin',
            is_active=True
        )
        
        # 创建科室
        self.department = Department.objects.create(
            code='TEST',
            name='测试科室',
            created_by=self.admin_user
        )
        
        # 创建科室人员
        self.staff_user = User.objects.create_user(
            username='staff',
            email='<EMAIL>'
        )
        self.staff_profile = UserProfile.objects.create(
            user=self.staff_user,
            account_number='1001',
            department=self.department,
            role='staff',
            is_active=True
        )
        
        self.factory = RequestFactory()
    
    def test_admin_required_decorator(self):
        """测试管理员权限装饰器"""
        @admin_required
        def test_view(request):
            return "success"
        
        # 测试管理员访问
        request = self.factory.get('/')
        request.user = self.admin_user
        result = test_view(request)
        self.assertEqual(result, "success")
        
        # 测试科室人员访问
        request.user = self.staff_user
        with self.assertRaises(PermissionDenied):
            test_view(request)
        
        # 测试匿名用户访问
        request.user = AnonymousUser()
        with self.assertRaises(PermissionDenied):
            test_view(request)
    
    def test_department_member_or_admin_required_decorator(self):
        """测试科室成员或管理员权限装饰器"""
        @department_member_or_admin_required
        def test_view(request):
            return "success"
        
        # 测试管理员访问
        request = self.factory.get('/')
        request.user = self.admin_user
        result = test_view(request)
        self.assertEqual(result, "success")
        
        # 测试科室人员访问
        request.user = self.staff_user
        result = test_view(request)
        self.assertEqual(result, "success")
        
        # 测试匿名用户访问
        request.user = AnonymousUser()
        with self.assertRaises(PermissionDenied):
            test_view(request)
    
    def test_owner_or_admin_required_decorator(self):
        """测试对象所有者或管理员权限装饰器"""
        def get_user_object(request, user_id):
            return UserProfile.objects.get(id=user_id)
        
        @owner_or_admin_required(get_object_func=get_user_object)
        def test_view(request, user_id):
            return "success"
        
        # 测试管理员访问任何用户
        request = self.factory.get('/')
        request.user = self.admin_user
        result = test_view(request, self.staff_profile.id)
        self.assertEqual(result, "success")
        
        # 测试用户访问自己
        request.user = self.staff_user
        result = test_view(request, self.staff_profile.id)
        self.assertEqual(result, "success")
        
        # 测试用户访问他人
        request.user = self.staff_user
        with self.assertRaises(PermissionDenied):
            test_view(request, self.admin_profile.id)


class UserRolePermissionTest(TestCase):
    """用户角色权限测试"""
    
    def setUp(self):
        """测试前准备"""
        # 创建管理员用户
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>'
        )
        self.admin_profile = UserProfile.objects.create(
            user=self.admin_user,
            account_number='0001',
            role='admin',
            is_active=True
        )
        
        # 创建科室
        self.department = Department.objects.create(
            code='TEST',
            name='测试科室',
            created_by=self.admin_user
        )
        
        # 创建科室人员
        self.staff_user = User.objects.create_user(
            username='staff',
            email='<EMAIL>'
        )
        self.staff_profile = UserProfile.objects.create(
            user=self.staff_user,
            account_number='1001',
            department=self.department,
            role='staff',
            is_active=True
        )
    
    def test_admin_role_permissions(self):
        """测试管理员角色权限"""
        permissions = get_user_role_permissions(self.admin_profile)
        
        # 管理员应该有所有权限
        expected_permissions = {
            'can_manage_users': True,
            'can_manage_departments': True,
            'can_view_reports': True,
            'can_create_reports': True,
            'can_view_own_profile': True,
            'can_edit_own_profile': True,
        }
        
        for perm, expected in expected_permissions.items():
            self.assertEqual(permissions.get(perm), expected, f"管理员权限 {perm} 不正确")
    
    def test_staff_role_permissions(self):
        """测试科室人员角色权限"""
        permissions = get_user_role_permissions(self.staff_profile)
        
        # 科室人员应该有限制的权限
        expected_permissions = {
            'can_manage_users': False,
            'can_manage_departments': False,
            'can_view_reports': True,
            'can_create_reports': True,
            'can_view_own_profile': True,
            'can_edit_own_profile': True,
        }
        
        for perm, expected in expected_permissions.items():
            self.assertEqual(permissions.get(perm), expected, f"科室人员权限 {perm} 不正确")
    
    def test_inactive_user_permissions(self):
        """测试禁用用户权限"""
        # 禁用用户
        self.staff_profile.is_active = False
        self.staff_profile.save()
        
        permissions = get_user_role_permissions(self.staff_profile)
        
        # 禁用用户应该没有任何权限
        for perm, value in permissions.items():
            self.assertFalse(value, f"禁用用户不应该有权限 {perm}")


class MiddlewarePermissionTest(TestCase):
    """中间件权限测试"""
    
    def setUp(self):
        """测试前准备"""
        from django.test import Client
        
        self.client = Client()
        
        # 创建管理员用户
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>'
        )
        self.admin_profile = UserProfile.objects.create(
            user=self.admin_user,
            account_number='0001',
            role='admin',
            is_active=True
        )
        
        # 创建科室
        self.department = Department.objects.create(
            code='TEST',
            name='测试科室',
            created_by=self.admin_user
        )
        
        # 创建科室人员
        self.staff_user = User.objects.create_user(
            username='staff',
            email='<EMAIL>'
        )
        self.staff_profile = UserProfile.objects.create(
            user=self.staff_user,
            account_number='1001',
            department=self.department,
            role='staff',
            is_active=True
        )
    
    def test_login_required_middleware(self):
        """测试登录要求中间件"""
        # 未登录用户访问受保护页面
        response = self.client.get('/dashboard/')
        
        # 应该重定向到登录页面
        self.assertEqual(response.status_code, 302)
        self.assertTrue(response.url.startswith('/login/'))
    
    def test_permission_control_middleware_admin(self):
        """测试权限控制中间件 - 管理员"""
        self.client.force_login(self.admin_user)
        
        # 管理员应该能访问用户管理页面
        response = self.client.get('/users/')
        self.assertEqual(response.status_code, 200)
        
        # 管理员应该能访问用户创建页面
        response = self.client.get('/users/create/')
        self.assertEqual(response.status_code, 200)
    
    def test_permission_control_middleware_staff(self):
        """测试权限控制中间件 - 科室人员"""
        self.client.force_login(self.staff_user)
        
        # 科室人员应该能访问用户中心
        response = self.client.get('/dashboard/')
        self.assertEqual(response.status_code, 200)
        
        # 科室人员不应该能访问用户管理页面
        response = self.client.get('/users/')
        self.assertIn(response.status_code, [302, 403])
        
        # 科室人员不应该能访问用户创建页面
        response = self.client.get('/users/create/')
        self.assertIn(response.status_code, [302, 403])
