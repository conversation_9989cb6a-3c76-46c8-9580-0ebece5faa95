/**
 * 个人信息编辑页面JavaScript
 * Profile Edit Page JavaScript
 */

$(document).ready(function() {
    // 初始化表单验证
    initializeFormValidation();
    
    // 初始化修改密码功能
    initializeChangePassword();
    
    // 初始化事件监听器
    initializeEventListeners();
    
    // 初始化表单变化监听
    initializeFormChangeTracking();
});

/**
 * 初始化表单验证
 */
function initializeFormValidation() {
    const form = $('#profileEditForm')[0];
    
    if (!form) return;
    
    // 自定义验证规则
    addCustomValidationRules();
    
    // 表单提交事件
    $(form).on('submit', function(event) {
        event.preventDefault();
        
        if (form.checkValidity()) {
            handleFormSubmit();
        } else {
            // 显示验证错误
            form.classList.add('was-validated');
            
            // 滚动到第一个错误字段
            const firstInvalidField = form.querySelector(':invalid');
            if (firstInvalidField) {
                firstInvalidField.scrollIntoView({ behavior: 'smooth', block: 'center' });
                firstInvalidField.focus();
            }
        }
    });
    
    // 实时验证
    $('#first_name, #last_name, #email').on('input', function() {
        validateField(this);
    });
}

/**
 * 添加自定义验证规则
 */
function addCustomValidationRules() {
    // 邮箱验证
    const emailInput = $('#email')[0];
    if (emailInput) {
        emailInput.addEventListener('input', function() {
            const value = this.value.trim();
            
            if (value && !isValidEmail(value)) {
                this.setCustomValidity('请输入有效的邮箱地址');
            } else {
                this.setCustomValidity('');
            }
        });
    }
    
    // 姓名验证
    $('#first_name, #last_name').each(function() {
        this.addEventListener('input', function() {
            const value = this.value.trim();
            
            if (value.length > 30) {
                this.setCustomValidity('姓名不能超过30个字符');
            } else {
                this.setCustomValidity('');
            }
        });
    });
}

/**
 * 验证邮箱格式
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * 验证单个字段
 */
function validateField(field) {
    const $field = $(field);
    const $feedback = $field.siblings('.invalid-feedback');
    
    if (field.checkValidity()) {
        $field.removeClass('is-invalid').addClass('is-valid');
    } else {
        $field.removeClass('is-valid').addClass('is-invalid');
        
        // 显示自定义错误消息
        if (field.validationMessage) {
            $feedback.text(field.validationMessage);
        }
    }
}

/**
 * 处理表单提交
 */
function handleFormSubmit() {
    const form = $('#profileEditForm');
    const submitBtn = $('#submitBtn');
    
    // 显示加载状态
    LoadingIndicator.show(submitBtn[0]);
    
    // 收集表单数据
    const formData = {
        first_name: $('#first_name').val().trim(),
        last_name: $('#last_name').val().trim(),
        email: $('#email').val().trim()
    };
    
    // 发送AJAX请求
    $.ajax({
        url: '/profile/edit/',
        method: 'POST',
        headers: {
            'X-CSRFToken': getCsrfToken(),
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        data: formData,
        success: function(response) {
            // 标记表单为已保存
            markFormAsSaved();
            
            // 显示成功消息
            showSuccessMessage('个人信息更新成功');
            
            // 延迟跳转到个人信息页面
            setTimeout(() => {
                window.location.href = '/profile/';
            }, 1500);
        },
        error: function(xhr, status, error) {
            console.error('表单提交失败:', error);
            
            // 处理验证错误
            if (xhr.status === 400 && xhr.responseJSON) {
                handleValidationErrors(xhr.responseJSON);
            } else {
                showErrorMessage('更新个人信息失败，请检查输入信息后重试');
            }
        },
        complete: function() {
            LoadingIndicator.hide(submitBtn[0]);
        }
    });
}

/**
 * 处理验证错误
 */
function handleValidationErrors(errors) {
    // 清除之前的错误状态
    $('.is-invalid').removeClass('is-invalid');
    
    // 显示字段错误
    Object.keys(errors).forEach(field => {
        const $field = $(`#${field}`);
        const $feedback = $field.siblings('.invalid-feedback');
        
        if ($field.length > 0) {
            $field.addClass('is-invalid');
            $feedback.text(errors[field][0] || errors[field]);
        }
    });
    
    // 滚动到第一个错误字段
    const firstErrorField = $('.is-invalid').first();
    if (firstErrorField.length > 0) {
        firstErrorField[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
        firstErrorField.focus();
    }
    
    // 显示通用错误消息
    showErrorMessage('请检查表单中的错误信息');
}

/**
 * 初始化修改密码功能
 */
function initializeChangePassword() {
    const changePasswordForm = $('#changePasswordForm');
    
    if (!changePasswordForm.length) return;
    
    // 表单提交事件
    changePasswordForm.on('submit', function(e) {
        e.preventDefault();
        handlePasswordChange();
    });
    
    // 密码确认验证
    $('#confirm_password').on('input', function() {
        validatePasswordConfirm();
    });
    
    // 新密码输入验证
    $('#new_password').on('input', function() {
        validateNewPassword();
        validatePasswordConfirm(); // 重新验证确认密码
    });
    
    // 模态框显示时重置表单
    $('#changePasswordModal').on('show.bs.modal', function() {
        changePasswordForm[0].reset();
        changePasswordForm.removeClass('was-validated');
        $('.is-invalid').removeClass('is-invalid');
    });
}

/**
 * 处理密码修改
 */
function handlePasswordChange() {
    const form = $('#changePasswordForm')[0];
    
    // 验证表单
    if (!form.checkValidity()) {
        form.classList.add('was-validated');
        return;
    }
    
    // 验证密码确认
    if (!validatePasswordConfirm()) {
        return;
    }
    
    const formData = {
        current_password: $('#current_password').val(),
        new_password: $('#new_password').val(),
        confirm_password: $('#confirm_password').val()
    };
    
    // 显示加载状态
    const submitBtn = $('#changePasswordForm button[type="submit"]');
    LoadingIndicator.show(submitBtn[0]);
    
    // 发送AJAX请求
    $.ajax({
        url: '/change-password/',
        method: 'POST',
        headers: {
            'X-CSRFToken': getCsrfToken(),
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        data: formData,
        success: function(response) {
            if (response.success) {
                $('#changePasswordModal').modal('hide');
                showSuccessMessage(response.message || '密码修改成功，请重新登录');
                
                // 延迟跳转到登录页面
                setTimeout(() => {
                    window.location.href = '/login/';
                }, 2000);
            } else {
                showErrorMessage(response.error || '密码修改失败');
            }
        },
        error: function(xhr, status, error) {
            console.error('密码修改失败:', error);
            
            if (xhr.responseJSON && xhr.responseJSON.error) {
                showErrorMessage(xhr.responseJSON.error);
            } else {
                showErrorMessage('密码修改失败，请重试');
            }
        },
        complete: function() {
            LoadingIndicator.hide(submitBtn[0]);
        }
    });
}

/**
 * 验证新密码
 */
function validateNewPassword() {
    const newPassword = $('#new_password');
    const value = newPassword.val();
    
    if (value.length > 0 && value.length < 6) {
        newPassword[0].setCustomValidity('密码长度至少6位');
        newPassword.addClass('is-invalid');
        return false;
    } else {
        newPassword[0].setCustomValidity('');
        newPassword.removeClass('is-invalid');
        return true;
    }
}

/**
 * 验证密码确认
 */
function validatePasswordConfirm() {
    const newPassword = $('#new_password').val();
    const confirmPassword = $('#confirm_password');
    const confirmValue = confirmPassword.val();
    
    if (confirmValue.length > 0 && confirmValue !== newPassword) {
        confirmPassword[0].setCustomValidity('两次输入的密码不一致');
        confirmPassword.addClass('is-invalid');
        return false;
    } else {
        confirmPassword[0].setCustomValidity('');
        confirmPassword.removeClass('is-invalid');
        return true;
    }
}

/**
 * 初始化事件监听器
 */
function initializeEventListeners() {
    // 取消按钮确认
    $('a[href*="profile"]').on('click', function(event) {
        if (hasFormChanges()) {
            if (!confirm('您有未保存的更改，确定要离开吗？')) {
                event.preventDefault();
                return false;
            }
        }
    });
    
    // 页面离开前确认
    window.addEventListener('beforeunload', function(event) {
        if (hasFormChanges()) {
            event.preventDefault();
            event.returnValue = '您有未保存的更改，确定要离开吗？';
            return event.returnValue;
        }
    });
}

/**
 * 初始化表单变化监听
 */
function initializeFormChangeTracking() {
    // 记录初始值
    const initialValues = {};
    $('#profileEditForm input').each(function() {
        initialValues[this.name] = $(this).val();
    });
    
    // 监听表单变化
    $('#profileEditForm input').on('change input', function() {
        checkFormChanges(initialValues);
    });
    
    // 保存初始值到表单数据
    $('#profileEditForm').data('initial-values', initialValues);
}

/**
 * 检查表单变化
 */
function checkFormChanges(initialValues) {
    let hasChanges = false;
    
    $('#profileEditForm input').each(function() {
        if (initialValues[this.name] !== $(this).val()) {
            hasChanges = true;
            return false; // 跳出循环
        }
    });
    
    $('#profileEditForm').data('has-changes', hasChanges);
}

/**
 * 检查表单是否有变化
 */
function hasFormChanges() {
    return $('#profileEditForm').data('has-changes') === true;
}

/**
 * 标记表单为已保存
 */
function markFormAsSaved() {
    $('#profileEditForm').data('has-changes', false);
}
