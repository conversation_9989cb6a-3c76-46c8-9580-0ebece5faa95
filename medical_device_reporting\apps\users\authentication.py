"""
自定义认证后端
Custom Authentication Backend for Medical Device Reporting Platform
"""

import logging
from django.contrib.auth.backends import BaseBackend
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError

from apps.common.utils import ValidationUtils
from .models import UserProfile

logger = logging.getLogger('apps.users')


class AccountNumberBackend(BaseBackend):
    """
    4位数账号认证后端
    
    支持通过4位数账号无密码登录
    """
    
    def authenticate(self, request, account_number=None, **kwargs):
        """
        认证用户
        
        Args:
            request: HTTP请求对象
            account_number: 4位数账号
            **kwargs: 其他参数
            
        Returns:
            User: 认证成功的用户对象，失败返回None
        """
        
        if not account_number:
            logger.debug('认证失败：未提供账号')
            return None
        
        # 验证账号格式
        if not ValidationUtils.validate_account_number(account_number):
            logger.warning(f'认证失败：账号格式错误 - {account_number}')
            return None
        
        try:
            # 查找用户配置文件
            user_profile = UserProfile.objects.select_related('user', 'department').get(
                account_number=account_number,
                is_deleted=False
            )
            
            # 检查用户状态
            if not user_profile.is_active:
                logger.warning(f'认证失败：用户已禁用 - {account_number}')
                return None
            
            # 检查关联的Django用户状态
            user = user_profile.user
            if not user.is_active:
                logger.warning(f'认证失败：Django用户已禁用 - {account_number}')
                return None
            
            # 记录成功登录
            logger.info(
                f'用户认证成功: {account_number} - {user.username}',
                extra={
                    'account_number': account_number,
                    'username': user.username,
                    'user_id': user.id,
                    'department': user_profile.department.name if user_profile.department else None,
                    'role': user_profile.role,
                    'ip_address': self._get_client_ip(request) if request else None,
                }
            )
            
            return user
            
        except UserProfile.DoesNotExist:
            logger.warning(f'认证失败：账号不存在 - {account_number}')
            return None
        
        except Exception as e:
            logger.error(f'认证过程发生错误: {str(e)}', exc_info=True)
            return None
    
    def get_user(self, user_id):
        """
        根据用户ID获取用户对象
        
        Args:
            user_id: 用户ID
            
        Returns:
            User: 用户对象，不存在返回None
        """
        
        try:
            user = User.objects.get(pk=user_id, is_active=True)
            
            # 检查用户配置文件状态
            if hasattr(user, 'profile') and not user.profile.is_active:
                return None
            
            return user
            
        except User.DoesNotExist:
            return None
        except Exception as e:
            logger.error(f'获取用户失败: {str(e)}', exc_info=True)
            return None
    
    def _get_client_ip(self, request):
        """
        获取客户端IP地址
        
        Args:
            request: HTTP请求对象
            
        Returns:
            str: IP地址
        """
        
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR')
        
        return ip


class DualAuthenticationBackend(BaseBackend):
    """
    双重认证后端
    
    支持4位数账号认证和传统用户名密码认证
    """
    
    def authenticate(self, request, username=None, password=None, account_number=None, **kwargs):
        """
        认证用户
        
        Args:
            request: HTTP请求对象
            username: 用户名
            password: 密码
            account_number: 4位数账号
            **kwargs: 其他参数
            
        Returns:
            User: 认证成功的用户对象，失败返回None
        """
        
        # 如果提供了账号，使用账号认证
        if account_number:
            account_backend = AccountNumberBackend()
            return account_backend.authenticate(request, account_number=account_number)
        
        # 如果提供了用户名和密码，使用传统认证
        if username and password:
            try:
                user = User.objects.get(username=username)
                if user.check_password(password) and user.is_active:
                    # 检查用户配置文件状态
                    if hasattr(user, 'profile') and not user.profile.is_active:
                        logger.warning(f'传统认证失败：用户配置文件已禁用 - {username}')
                        return None
                    
                    logger.info(f'传统认证成功: {username}')
                    return user
                    
            except User.DoesNotExist:
                pass
        
        return None
    
    def get_user(self, user_id):
        """
        根据用户ID获取用户对象
        
        Args:
            user_id: 用户ID
            
        Returns:
            User: 用户对象，不存在返回None
        """
        
        account_backend = AccountNumberBackend()
        return account_backend.get_user(user_id)
