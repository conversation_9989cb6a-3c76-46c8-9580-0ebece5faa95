"""
用户管理数据选择器
User Management Selectors for Medical Device Reporting Platform
"""

from typing import Optional, List, Dict, Any
from django.db.models import QuerySet, Q
from django.contrib.auth.models import User

from .models import UserProfile, Department


def user_list(
    *,
    department_id: Optional[int] = None,
    role: Optional[str] = None,
    is_active: Optional[bool] = None,
    search: Optional[str] = None,
    ordering: str = 'account_number'
) -> QuerySet[UserProfile]:
    """
    获取用户列表
    
    Args:
        department_id: 科室ID筛选
        role: 角色筛选
        is_active: 状态筛选
        search: 搜索关键词
        ordering: 排序字段
        
    Returns:
        QuerySet[UserProfile]: 用户配置文件查询集
    """
    
    queryset = UserProfile.objects.select_related(
        'user', 'department', 'created_by', 'updated_by'
    ).filter(is_deleted=False)
    
    # 科室筛选
    if department_id is not None:
        queryset = queryset.filter(department_id=department_id)
    
    # 角色筛选
    if role is not None:
        queryset = queryset.filter(role=role)
    
    # 状态筛选
    if is_active is not None:
        queryset = queryset.filter(is_active=is_active)
    
    # 搜索
    if search:
        search_q = Q()
        search_q |= Q(account_number__icontains=search)
        search_q |= Q(user__username__icontains=search)
        search_q |= Q(user__first_name__icontains=search)
        search_q |= Q(user__last_name__icontains=search)
        search_q |= Q(user__email__icontains=search)

        search_q |= Q(department__name__icontains=search)
        
        queryset = queryset.filter(search_q)
    
    # 排序
    if ordering:
        queryset = queryset.order_by(ordering)
    
    return queryset


def user_get_by_id(user_profile_id: int) -> Optional[UserProfile]:
    """
    根据ID获取用户
    
    Args:
        user_profile_id: 用户配置文件ID
        
    Returns:
        Optional[UserProfile]: 用户配置文件或None
    """
    
    try:
        return UserProfile.objects.select_related(
            'user', 'department', 'created_by', 'updated_by'
        ).get(id=user_profile_id, is_deleted=False)
    except UserProfile.DoesNotExist:
        return None


def user_get_by_account_number(account_number: str) -> Optional[UserProfile]:
    """
    根据账号获取用户
    
    Args:
        account_number: 4位数账号
        
    Returns:
        Optional[UserProfile]: 用户配置文件或None
    """
    
    try:
        return UserProfile.objects.select_related(
            'user', 'department'
        ).get(account_number=account_number, is_deleted=False)
    except UserProfile.DoesNotExist:
        return None


def user_exists_by_account_number(account_number: str) -> bool:
    """
    检查账号是否存在
    
    Args:
        account_number: 4位数账号
        
    Returns:
        bool: 账号是否存在
    """
    
    return UserProfile.objects.filter(
        account_number=account_number,
        is_deleted=False
    ).exists()


def department_list(
    *,
    is_active: Optional[bool] = None,
    search: Optional[str] = None,
    ordering: str = 'code'
) -> QuerySet[Department]:
    """
    获取科室列表
    
    Args:
        is_active: 状态筛选
        search: 搜索关键词
        ordering: 排序字段
        
    Returns:
        QuerySet[Department]: 科室查询集
    """
    
    queryset = Department.objects.filter(is_deleted=False)
    
    # 状态筛选
    if is_active is not None:
        queryset = queryset.filter(is_active=is_active)
    
    # 搜索
    if search:
        search_q = Q()
        search_q |= Q(name__icontains=search)
        search_q |= Q(code__icontains=search)

        
        queryset = queryset.filter(search_q)
    
    # 排序
    if ordering:
        queryset = queryset.order_by(ordering)
    
    return queryset


def department_get_by_id(department_id: int) -> Optional[Department]:
    """
    根据ID获取科室
    
    Args:
        department_id: 科室ID
        
    Returns:
        Optional[Department]: 科室对象或None
    """
    
    try:
        return Department.objects.get(id=department_id, is_deleted=False)
    except Department.DoesNotExist:
        return None


def user_statistics() -> Dict[str, Any]:
    """
    获取用户统计信息
    
    Returns:
        Dict[str, Any]: 统计信息字典
    """
    
    total_users = UserProfile.objects.filter(is_deleted=False).count()
    active_users = UserProfile.objects.filter(is_deleted=False, is_active=True).count()
    admin_users = UserProfile.objects.filter(is_deleted=False, role='admin').count()
    staff_users = UserProfile.objects.filter(is_deleted=False, role='staff').count()
    
    total_departments = Department.objects.filter(is_deleted=False).count()
    active_departments = Department.objects.filter(is_deleted=False, is_active=True).count()
    
    return {
        'total_users': total_users,
        'active_users': active_users,
        'inactive_users': total_users - active_users,
        'admin_users': admin_users,
        'staff_users': staff_users,
        'total_departments': total_departments,
        'active_departments': active_departments,
        'inactive_departments': total_departments - active_departments,
    }


def user_list_by_department(department_id: int) -> QuerySet[UserProfile]:
    """
    获取指定科室的用户列表
    
    Args:
        department_id: 科室ID
        
    Returns:
        QuerySet[UserProfile]: 用户配置文件查询集
    """
    
    return UserProfile.objects.select_related('user').filter(
        department_id=department_id,
        is_deleted=False,
        is_active=True
    ).order_by('account_number')


def user_search_suggestions(query: str, limit: int = 10) -> List[Dict[str, Any]]:
    """
    用户搜索建议
    
    Args:
        query: 搜索查询
        limit: 结果限制数量
        
    Returns:
        List[Dict[str, Any]]: 搜索建议列表
    """
    
    if not query or len(query) < 2:
        return []
    
    search_q = Q()
    search_q |= Q(account_number__icontains=query)
    search_q |= Q(user__username__icontains=query)
    search_q |= Q(user__first_name__icontains=query)
    search_q |= Q(user__last_name__icontains=query)
    
    users = UserProfile.objects.select_related('user', 'department').filter(
        search_q,
        is_deleted=False,
        is_active=True
    )[:limit]
    
    suggestions = []
    for user in users:
        suggestions.append({
            'id': user.id,
            'account_number': user.account_number,
            'display_name': user.display_name,
            'department_name': user.department.name if user.department else '',
            'role_display': user.get_role_display(),
        })
    
    return suggestions


# ==================== 高级查询选择器 ====================

def user_list_paginated(
    *,
    department_id: Optional[int] = None,
    role: Optional[str] = None,
    is_active: Optional[bool] = None,
    search: Optional[str] = None,
    ordering: str = 'account_number',
    page: int = 1,
    page_size: int = 20
) -> Dict[str, Any]:
    """
    获取分页用户列表

    Args:
        department_id: 科室ID筛选
        role: 角色筛选
        is_active: 状态筛选
        search: 搜索关键词
        ordering: 排序字段
        page: 页码（从1开始）
        page_size: 每页大小

    Returns:
        Dict[str, Any]: 包含分页信息的结果字典
    """

    from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger

    # 获取基础查询集
    queryset = user_list(
        department_id=department_id,
        role=role,
        is_active=is_active,
        search=search,
        ordering=ordering
    )

    # 创建分页器
    paginator = Paginator(queryset, page_size)

    try:
        users = paginator.page(page)
    except PageNotAnInteger:
        users = paginator.page(1)
    except EmptyPage:
        users = paginator.page(paginator.num_pages)

    return {
        'users': users.object_list,
        'page': users.number,
        'total_pages': paginator.num_pages,
        'total_count': paginator.count,
        'has_previous': users.has_previous(),
        'has_next': users.has_next(),
        'previous_page': users.previous_page_number() if users.has_previous() else None,
        'next_page': users.next_page_number() if users.has_next() else None,
        'page_size': page_size,
        'start_index': users.start_index(),
        'end_index': users.end_index(),
    }


def user_list_with_counts(
    *,
    department_id: Optional[int] = None,
    role: Optional[str] = None,
    is_active: Optional[bool] = None,
    search: Optional[str] = None,
    ordering: str = 'account_number'
) -> Dict[str, Any]:
    """
    获取用户列表及统计信息

    Args:
        department_id: 科室ID筛选
        role: 角色筛选
        is_active: 状态筛选
        search: 搜索关键词
        ordering: 排序字段

    Returns:
        Dict[str, Any]: 包含用户列表和统计信息的字典
    """

    from django.db.models import Count, Case, When, IntegerField

    # 获取基础查询集
    queryset = user_list(
        department_id=department_id,
        role=role,
        is_active=is_active,
        search=search,
        ordering=ordering
    )

    # 计算统计信息
    stats = queryset.aggregate(
        total_count=Count('id'),
        active_count=Count(
            Case(When(is_active=True, then=1), output_field=IntegerField())
        ),
        inactive_count=Count(
            Case(When(is_active=False, then=1), output_field=IntegerField())
        ),
        admin_count=Count(
            Case(When(role='admin', then=1), output_field=IntegerField())
        ),
        staff_count=Count(
            Case(When(role='staff', then=1), output_field=IntegerField())
        ),
    )

    return {
        'users': queryset,
        'stats': stats
    }


def user_list_by_role_and_department() -> Dict[str, Any]:
    """
    按角色和科室分组的用户列表

    Returns:
        Dict[str, Any]: 分组的用户数据
    """

    from django.db.models import Count

    # 按科室分组统计
    department_stats = Department.objects.filter(
        is_deleted=False,
        is_active=True
    ).annotate(
        user_count=Count('users', filter=Q(users__is_deleted=False, users__is_active=True)),
        admin_count=Count('users', filter=Q(users__is_deleted=False, users__is_active=True, users__role='admin')),
        staff_count=Count('users', filter=Q(users__is_deleted=False, users__is_active=True, users__role='staff'))
    ).order_by('code')

    # 按角色分组统计
    role_stats = UserProfile.objects.filter(
        is_deleted=False,
        is_active=True
    ).values('role').annotate(
        count=Count('id')
    ).order_by('role')

    # 无科室用户统计
    no_department_count = UserProfile.objects.filter(
        is_deleted=False,
        is_active=True,
        department__isnull=True
    ).count()

    return {
        'department_stats': department_stats,
        'role_stats': role_stats,
        'no_department_count': no_department_count
    }


def user_list_recent_activity(
    *,
    days: int = 30,
    limit: int = 50
) -> QuerySet[UserProfile]:
    """
    获取最近活动的用户列表

    Args:
        days: 最近天数
        limit: 结果限制数量

    Returns:
        QuerySet[UserProfile]: 最近活动的用户查询集
    """

    from django.utils import timezone
    from datetime import timedelta

    since_date = timezone.now() - timedelta(days=days)

    return UserProfile.objects.select_related(
        'user', 'department'
    ).filter(
        is_deleted=False,
        updated_at__gte=since_date
    ).order_by('-updated_at')[:limit]


def user_list_by_creation_date(
    *,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    ordering: str = '-created_at'
) -> QuerySet[UserProfile]:
    """
    按创建日期筛选用户列表

    Args:
        start_date: 开始日期 (YYYY-MM-DD)
        end_date: 结束日期 (YYYY-MM-DD)
        ordering: 排序字段

    Returns:
        QuerySet[UserProfile]: 用户查询集
    """

    from datetime import datetime

    queryset = UserProfile.objects.select_related(
        'user', 'department', 'created_by'
    ).filter(is_deleted=False)

    if start_date:
        try:
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            queryset = queryset.filter(created_at__gte=start_dt)
        except ValueError:
            pass

    if end_date:
        try:
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')
            queryset = queryset.filter(created_at__lte=end_dt)
        except ValueError:
            pass

    return queryset.order_by(ordering)


def user_advanced_search(
    *,
    account_number: Optional[str] = None,
    username: Optional[str] = None,
    email: Optional[str] = None,
    first_name: Optional[str] = None,
    last_name: Optional[str] = None,
    department_ids: Optional[List[int]] = None,
    roles: Optional[List[str]] = None,
    is_active: Optional[bool] = None,
    created_after: Optional[str] = None,
    created_before: Optional[str] = None,
    ordering: str = 'account_number'
) -> QuerySet[UserProfile]:
    """
    高级用户搜索

    Args:
        account_number: 账号（精确匹配）
        username: 用户名（模糊匹配）
        email: 邮箱（模糊匹配）
        first_name: 名（模糊匹配）
        last_name: 姓（模糊匹配）
        department_ids: 科室ID列表
        roles: 角色列表
        is_active: 状态筛选
        created_after: 创建时间之后 (YYYY-MM-DD)
        created_before: 创建时间之前 (YYYY-MM-DD)
        ordering: 排序字段

    Returns:
        QuerySet[UserProfile]: 用户查询集
    """

    from datetime import datetime

    queryset = UserProfile.objects.select_related(
        'user', 'department', 'created_by', 'updated_by'
    ).filter(is_deleted=False)

    # 精确匹配账号
    if account_number:
        queryset = queryset.filter(account_number=account_number)

    # 模糊匹配用户信息
    if username:
        queryset = queryset.filter(user__username__icontains=username)

    if email:
        queryset = queryset.filter(user__email__icontains=email)

    if first_name:
        queryset = queryset.filter(user__first_name__icontains=first_name)

    if last_name:
        queryset = queryset.filter(user__last_name__icontains=last_name)

    # 科室筛选
    if department_ids:
        queryset = queryset.filter(department_id__in=department_ids)

    # 角色筛选
    if roles:
        queryset = queryset.filter(role__in=roles)

    # 状态筛选
    if is_active is not None:
        queryset = queryset.filter(is_active=is_active)

    # 创建时间筛选
    if created_after:
        try:
            after_dt = datetime.strptime(created_after, '%Y-%m-%d')
            queryset = queryset.filter(created_at__gte=after_dt)
        except ValueError:
            pass

    if created_before:
        try:
            before_dt = datetime.strptime(created_before, '%Y-%m-%d')
            queryset = queryset.filter(created_at__lte=before_dt)
        except ValueError:
            pass

    return queryset.order_by(ordering)


# ==================== 科室查询选择器 ====================

def department_list_with_user_counts(
    *,
    is_active: Optional[bool] = None,
    search: Optional[str] = None,
    ordering: str = 'code'
) -> QuerySet[Department]:
    """
    获取带用户数量统计的科室列表

    Args:
        is_active: 状态筛选
        search: 搜索关键词
        ordering: 排序字段

    Returns:
        QuerySet[Department]: 科室查询集（包含用户数量注解）
    """

    from django.db.models import Count

    queryset = Department.objects.annotate(
        total_users=Count('users', filter=Q(users__is_deleted=False)),
        active_users=Count('users', filter=Q(users__is_deleted=False, users__is_active=True)),
        admin_users=Count('users', filter=Q(users__is_deleted=False, users__is_active=True, users__role='admin')),
        staff_users=Count('users', filter=Q(users__is_deleted=False, users__is_active=True, users__role='staff'))
    ).filter(is_deleted=False)

    # 状态筛选
    if is_active is not None:
        queryset = queryset.filter(is_active=is_active)

    # 搜索
    if search:
        search_q = Q()
        search_q |= Q(name__icontains=search)
        search_q |= Q(code__icontains=search)
        queryset = queryset.filter(search_q)

    # 排序
    if ordering:
        queryset = queryset.order_by(ordering)

    return queryset


def department_list_empty() -> QuerySet[Department]:
    """
    获取没有用户的科室列表

    Returns:
        QuerySet[Department]: 空科室查询集
    """

    from django.db.models import Count

    return Department.objects.annotate(
        user_count=Count('users', filter=Q(users__is_deleted=False, users__is_active=True))
    ).filter(
        is_deleted=False,
        is_active=True,
        user_count=0
    ).order_by('code')


def department_list_with_recent_activity(
    *,
    days: int = 30,
    ordering: str = 'code'
) -> QuerySet[Department]:
    """
    获取最近有活动的科室列表

    Args:
        days: 最近天数
        ordering: 排序字段

    Returns:
        QuerySet[Department]: 科室查询集
    """

    from django.utils import timezone
    from datetime import timedelta

    since_date = timezone.now() - timedelta(days=days)

    return Department.objects.filter(
        is_deleted=False,
        is_active=True,
        users__updated_at__gte=since_date,
        users__is_deleted=False
    ).distinct().order_by(ordering)


def department_get_by_code(code: str) -> Optional[Department]:
    """
    根据代码获取科室

    Args:
        code: 科室代码

    Returns:
        Optional[Department]: 科室对象或None
    """

    try:
        return Department.objects.get(code=code.upper(), is_deleted=False)
    except Department.DoesNotExist:
        return None


def department_exists_by_code(code: str) -> bool:
    """
    检查科室代码是否存在

    Args:
        code: 科室代码

    Returns:
        bool: 代码是否存在
    """

    return Department.objects.filter(
        code=code.upper(),
        is_deleted=False
    ).exists()


def department_exists_by_name(name: str) -> bool:
    """
    检查科室名称是否存在

    Args:
        name: 科室名称

    Returns:
        bool: 名称是否存在
    """

    return Department.objects.filter(
        name=name.strip(),
        is_deleted=False
    ).exists()


# ==================== 性能优化查询选择器 ====================

def user_list_optimized(
    *,
    department_id: Optional[int] = None,
    role: Optional[str] = None,
    is_active: Optional[bool] = None,
    search: Optional[str] = None,
    ordering: str = 'account_number',
    select_related_fields: Optional[List[str]] = None,
    prefetch_related_fields: Optional[List[str]] = None
) -> QuerySet[UserProfile]:
    """
    性能优化的用户列表查询

    Args:
        department_id: 科室ID筛选
        role: 角色筛选
        is_active: 状态筛选
        search: 搜索关键词
        ordering: 排序字段
        select_related_fields: select_related字段列表
        prefetch_related_fields: prefetch_related字段列表

    Returns:
        QuerySet[UserProfile]: 优化后的用户查询集
    """

    # 默认的select_related字段
    if select_related_fields is None:
        select_related_fields = ['user', 'department', 'created_by', 'updated_by']

    # 默认的prefetch_related字段
    if prefetch_related_fields is None:
        prefetch_related_fields = ['user__groups', 'user__user_permissions']

    queryset = UserProfile.objects.select_related(*select_related_fields)

    if prefetch_related_fields:
        queryset = queryset.prefetch_related(*prefetch_related_fields)

    queryset = queryset.filter(is_deleted=False)

    # 应用筛选条件
    if department_id is not None:
        queryset = queryset.filter(department_id=department_id)

    if role is not None:
        queryset = queryset.filter(role=role)

    if is_active is not None:
        queryset = queryset.filter(is_active=is_active)

    # 搜索优化
    if search:
        search_q = Q()
        search_q |= Q(account_number__icontains=search)
        search_q |= Q(user__username__icontains=search)
        search_q |= Q(user__first_name__icontains=search)
        search_q |= Q(user__last_name__icontains=search)
        search_q |= Q(user__email__icontains=search)
        search_q |= Q(department__name__icontains=search)

        queryset = queryset.filter(search_q)

    # 排序
    if ordering:
        queryset = queryset.order_by(ordering)

    return queryset


def user_list_minimal(
    *,
    department_id: Optional[int] = None,
    role: Optional[str] = None,
    is_active: Optional[bool] = None,
    ordering: str = 'account_number'
) -> QuerySet[UserProfile]:
    """
    最小化字段的用户列表查询（用于下拉选择等场景）

    Args:
        department_id: 科室ID筛选
        role: 角色筛选
        is_active: 状态筛选
        ordering: 排序字段

    Returns:
        QuerySet[UserProfile]: 最小化的用户查询集
    """

    queryset = UserProfile.objects.only(
        'id', 'account_number', 'role', 'is_active',
        'user__id', 'user__username', 'user__first_name', 'user__last_name',
        'department__id', 'department__name'
    ).select_related('user', 'department').filter(is_deleted=False)

    if department_id is not None:
        queryset = queryset.filter(department_id=department_id)

    if role is not None:
        queryset = queryset.filter(role=role)

    if is_active is not None:
        queryset = queryset.filter(is_active=is_active)

    return queryset.order_by(ordering)


def department_list_minimal(
    *,
    is_active: Optional[bool] = None,
    ordering: str = 'code'
) -> QuerySet[Department]:
    """
    最小化字段的科室列表查询

    Args:
        is_active: 状态筛选
        ordering: 排序字段

    Returns:
        QuerySet[Department]: 最小化的科室查询集
    """

    queryset = Department.objects.only(
        'id', 'name', 'code', 'is_active'
    ).filter(is_deleted=False)

    if is_active is not None:
        queryset = queryset.filter(is_active=is_active)

    return queryset.order_by(ordering)


def get_user_choices_for_select() -> List[tuple]:
    """
    获取用户选择列表（用于表单选择）

    Returns:
        List[tuple]: (id, display_name) 元组列表
    """

    users = user_list_minimal(is_active=True)

    choices = []
    for user in users:
        display_name = f"{user.account_number} - {user.display_name}"
        if user.department:
            display_name += f" ({user.department.name})"
        choices.append((user.id, display_name))

    return choices


def get_department_choices_for_select() -> List[tuple]:
    """
    获取科室选择列表（用于表单选择）

    Returns:
        List[tuple]: (id, display_name) 元组列表
    """

    departments = department_list_minimal(is_active=True)

    choices = []
    for dept in departments:
        display_name = f"{dept.code} - {dept.name}"
        choices.append((dept.id, display_name))

    return choices


# ==================== 缓存查询选择器 ====================

def get_cached_user_statistics() -> Dict[str, Any]:
    """
    获取缓存的用户统计信息

    Returns:
        Dict[str, Any]: 统计信息字典
    """

    from django.core.cache import cache

    cache_key = 'user_statistics'
    stats = cache.get(cache_key)

    if stats is None:
        stats = user_statistics()
        # 缓存5分钟
        cache.set(cache_key, stats, 300)

    return stats


def get_cached_department_list() -> List[Dict[str, Any]]:
    """
    获取缓存的科室列表

    Returns:
        List[Dict[str, Any]]: 科室信息列表
    """

    from django.core.cache import cache

    cache_key = 'active_departments'
    departments = cache.get(cache_key)

    if departments is None:
        dept_queryset = department_list_minimal(is_active=True)
        departments = [
            {
                'id': dept.id,
                'name': dept.name,
                'code': dept.code,
            }
            for dept in dept_queryset
        ]
        # 缓存10分钟
        cache.set(cache_key, departments, 600)

    return departments


def invalidate_user_cache():
    """
    清除用户相关缓存
    """

    from django.core.cache import cache

    cache_keys = [
        'user_statistics',
        'active_departments',
    ]

    for key in cache_keys:
        cache.delete(key)
