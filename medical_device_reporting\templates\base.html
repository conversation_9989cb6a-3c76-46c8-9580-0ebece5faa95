<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="医疗器械不良事件上报平台">
    <meta name="author" content="Medical Device Reporting Platform">
    
    <title>{% block title %}医疗器械不良事件上报平台{% endblock %}</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">
    
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    
    <!-- Custom CSS -->
    {% load static %}
{% load permission_tags %}
    <link href="{% static 'css/custom.css' %}" rel="stylesheet">
    
    <!-- Additional CSS -->
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{% url 'admin:index' %}">
                <i class="bi bi-shield-check"></i>
                医疗器械不良事件上报平台
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <!-- 主导航菜单 -->
                <ul class="navbar-nav me-auto">
                    {% block main_nav %}
                    {% if user.is_authenticated %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'users:dashboard' %}">
                            <i class="bi bi-house me-1"></i>
                            用户中心
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'reports:dashboard' %}">
                            <i class="bi bi-file-medical me-1"></i>
                            报告管理
                        </a>
                    </li>
                    {% if user|is_admin %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-gear me-1"></i>
                            系统管理
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'users:user_list' %}"><i class="bi bi-people me-2"></i>用户管理</a></li>
                            <li><a class="dropdown-item" href="{% url 'users:department_list' %}"><i class="bi bi-building me-2"></i>科室管理</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'reports:pending_review' %}"><i class="bi bi-eye me-2"></i>报告审核</a></li>
                            <li><a class="dropdown-item" href="{% url 'reports:serious_events' %}"><i class="bi bi-exclamation-triangle me-2"></i>严重事件</a></li>
                        </ul>
                    </li>
                    {% endif %}
                    {% endif %}
                    {% endblock %}
                </ul>

                <!-- 用户菜单 -->
                <ul class="navbar-nav">
                    {% block user_nav %}
                    {% user_menu %}
                    {% endblock %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container-fluid">
        {% block content %}
        <div class="row justify-content-center mt-5">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="bi bi-shield-check"></i>
                            欢迎使用医疗器械不良事件上报平台
                        </h4>
                    </div>
                    <div class="card-body">
                        <p class="card-text">
                            本平台用于医疗器械不良事件的上报、管理和分析。
                        </p>
                        <p class="card-text">
                            <small class="text-muted">
                                基于Django + MySQL + Bootstrap5技术栈构建
                            </small>
                        </p>
                    </div>
                </div>
            </div>
        </div>
        {% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-light mt-5 py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2024 医疗器械不良事件上报平台. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-end">
                    <small class="text-muted">
                        Powered by Django + Bootstrap 5
                    </small>
                </div>
            </div>
        </div>
    </footer>

    <!-- jQuery (必须在Bootstrap和DataTables之前加载) -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>

    <!-- Bootstrap 5 JavaScript Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" crossorigin="anonymous"></script>

    <!-- Custom JavaScript -->
    <script src="{% static 'js/custom.js' %}"></script>
    <script src="{% static 'js/navigation.js' %}"></script>

    <!-- Additional JavaScript -->
    {% block extra_js %}{% endblock %}
</body>
</html>
