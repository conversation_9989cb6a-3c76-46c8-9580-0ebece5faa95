/* 
 * 报告管理模块样式文件
 * Reports Management Module Styles
 */

/* 基础样式 */
.reports-container {
    min-height: calc(100vh - 200px);
}

/* 统计卡片样式 */
.stat-card {
    transition: all 0.3s ease;
    border: none;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.stat-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: white;
    font-size: 28px;
    position: relative;
    overflow: hidden;
}

.stat-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.3), rgba(255,255,255,0.1));
    transform: rotate(45deg);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 8px;
    line-height: 1;
}

.stat-label {
    color: #6c757d;
    font-weight: 500;
    margin-bottom: 0;
    font-size: 0.95rem;
}

/* 功能卡片样式 */
.feature-link {
    text-decoration: none;
    color: inherit;
    display: block;
}

.feature-card {
    text-align: center;
    padding: 25px 20px;
    border-radius: 12px;
    transition: all 0.3s ease;
    border: 2px solid #e9ecef;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    height: 100%;
}

.feature-card:hover {
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transform: translateY(-3px);
    border-color: #0d6efd;
}

.feature-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: white;
    font-size: 24px;
    position: relative;
    overflow: hidden;
}

.feature-title {
    font-weight: 600;
    margin-bottom: 10px;
    color: #2c3e50;
}

.feature-desc {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 0;
    line-height: 1.5;
}

/* 表单样式增强 */
.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.form-control:focus, .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);
    transform: translateY(-1px);
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

/* 卡片头部样式 */
.card-header {
    position: relative;
    overflow: hidden;
    border-bottom: none;
    font-weight: 600;
}

.card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
    pointer-events: none;
}

/* 按钮样式增强 */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    border-width: 2px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn-lg {
    padding: 12px 30px;
    font-size: 1.1rem;
}

/* 表格样式 */
.table {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.table thead th {
    border-bottom: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
    transform: scale(1.01);
}

/* 徽章样式 */
.badge {
    font-weight: 500;
    padding: 6px 12px;
    border-radius: 20px;
}

/* 时间线样式 */
.timeline {
    position: relative;
    padding-left: 35px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 18px;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(to bottom, #0d6efd, #6c757d);
    border-radius: 2px;
}

.timeline-item {
    position: relative;
    margin-bottom: 25px;
}

.timeline-marker {
    position: absolute;
    left: -42px;
    top: 8px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 3px solid #fff;
    box-shadow: 0 0 0 3px #dee2e6;
    z-index: 1;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px 20px;
    border-radius: 12px;
    border-left: 4px solid #0d6efd;
    position: relative;
}

.timeline-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 5px;
    color: #2c3e50;
}

.timeline-text {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 5px;
}

.timeline-time {
    font-size: 0.8rem;
    color: #adb5bd;
    font-weight: 500;
}

/* 信息项样式 */
.info-item {
    display: flex;
    margin-bottom: 15px;
    align-items: flex-start;
    padding: 8px 0;
    border-bottom: 1px solid #f1f3f4;
}

.info-item:last-child {
    border-bottom: none;
}

.info-item label {
    min-width: 130px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 0;
    flex-shrink: 0;
}

.info-item span, .info-item div {
    flex: 1;
    color: #2c3e50;
}

/* 筛选面板样式 */
.filter-panel {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
}

.filter-panel .card-header {
    background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%);
    color: white;
}

/* 分页样式 */
.pagination .page-link {
    border-radius: 8px;
    margin: 0 2px;
    border: 2px solid #e9ecef;
    color: #0d6efd;
    font-weight: 500;
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.pagination .page-link:hover {
    background-color: #e7f1ff;
    border-color: #0d6efd;
    transform: translateY(-1px);
}

/* 模态框样式 */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 40px rgba(0,0,0,0.2);
}

.modal-header {
    border-bottom: 2px solid #f1f3f4;
    border-radius: 15px 15px 0 0;
}

.modal-footer {
    border-top: 2px solid #f1f3f4;
    border-radius: 0 0 15px 15px;
}

/* Toast 容器样式 */
.toast-container .toast {
    border-radius: 12px;
    border: none;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .stat-number {
        font-size: 2rem;
    }
    
    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
    
    .feature-card {
        padding: 20px 15px;
    }
    
    .feature-icon {
        width: 45px;
        height: 45px;
        font-size: 18px;
    }
    
    .btn-group {
        flex-direction: column;
        width: 100%;
    }
    
    .btn-group .btn {
        margin-bottom: 8px;
        border-radius: 8px !important;
    }
    
    .info-item {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .info-item label {
        min-width: auto;
        margin-bottom: 5px;
    }
    
    .table-responsive {
        font-size: 0.85rem;
    }
    
    .timeline {
        padding-left: 25px;
    }
    
    .timeline-marker {
        left: -32px;
        width: 12px;
        height: 12px;
    }
}

@media (max-width: 576px) {
    .reports-container {
        padding: 0 10px;
    }
    
    .card {
        margin-bottom: 15px;
    }
    
    .btn-lg {
        padding: 10px 20px;
        font-size: 1rem;
    }
}

/* 打印样式 */
@media print {
    .btn, .card-header, .timeline, .page-header, nav, .filter-panel {
        display: none !important;
    }
    
    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
        page-break-inside: avoid;
    }
    
    .info-item {
        border-bottom: 1px solid #dee2e6;
    }
    
    .table {
        font-size: 12px;
    }
    
    .badge {
        border: 1px solid #000;
        color: #000 !important;
        background: transparent !important;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .feature-card {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        border-color: #495057;
        color: #f8f9fa;
    }
    
    .feature-title {
        color: #f8f9fa;
    }
    
    .timeline-content {
        background: #495057;
        color: #f8f9fa;
    }
    
    .info-item {
        border-bottom-color: #495057;
    }
}
