/* 
 * 报告管理模块样式文件
 * Reports Management Module Styles
 */

/* 基础样式 */
.reports-container {
    min-height: calc(100vh - 200px);
}

/* 统计卡片样式 */
.stat-card {
    transition: all 0.3s ease;
    border: none;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.stat-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: white;
    font-size: 28px;
    position: relative;
    overflow: hidden;
}

.stat-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.3), rgba(255,255,255,0.1));
    transform: rotate(45deg);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 8px;
    line-height: 1;
}

.stat-label {
    color: #6c757d;
    font-weight: 500;
    margin-bottom: 0;
    font-size: 0.95rem;
}

/* 功能卡片样式 */
.feature-link {
    text-decoration: none;
    color: inherit;
    display: block;
}

.feature-card {
    text-align: center;
    padding: 25px 20px;
    border-radius: 12px;
    transition: all 0.3s ease;
    border: 2px solid #e9ecef;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    height: 100%;
}

.feature-card:hover {
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transform: translateY(-3px);
    border-color: #0d6efd;
}

.feature-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: white;
    font-size: 24px;
    position: relative;
    overflow: hidden;
}

.feature-title {
    font-weight: 600;
    margin-bottom: 10px;
    color: #2c3e50;
}

.feature-desc {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 0;
    line-height: 1.5;
}

/* 表单样式增强 */
.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.form-control:focus, .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);
    transform: translateY(-1px);
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

/* 卡片头部样式 */
.card-header {
    position: relative;
    overflow: hidden;
    border-bottom: none;
    font-weight: 600;
}

.card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
    pointer-events: none;
}

/* 按钮样式增强 */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    border-width: 2px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn-lg {
    padding: 12px 30px;
    font-size: 1.1rem;
}

/* 表格样式 */
.table {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.table thead th {
    border-bottom: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
    transform: scale(1.01);
}

/* 徽章样式 */
.badge {
    font-weight: 500;
    padding: 6px 12px;
    border-radius: 20px;
}

/* 时间线样式 */
.timeline {
    position: relative;
    padding-left: 35px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 18px;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(to bottom, #0d6efd, #6c757d);
    border-radius: 2px;
}

.timeline-item {
    position: relative;
    margin-bottom: 25px;
}

.timeline-marker {
    position: absolute;
    left: -42px;
    top: 8px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 3px solid #fff;
    box-shadow: 0 0 0 3px #dee2e6;
    z-index: 1;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px 20px;
    border-radius: 12px;
    border-left: 4px solid #0d6efd;
    position: relative;
}

.timeline-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 5px;
    color: #2c3e50;
}

.timeline-text {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 5px;
}

.timeline-time {
    font-size: 0.8rem;
    color: #adb5bd;
    font-weight: 500;
}

/* 信息项样式 */
.info-item {
    display: flex;
    margin-bottom: 15px;
    align-items: flex-start;
    padding: 8px 0;
    border-bottom: 1px solid #f1f3f4;
}

.info-item:last-child {
    border-bottom: none;
}

.info-item label {
    min-width: 130px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 0;
    flex-shrink: 0;
}

.info-item span, .info-item div {
    flex: 1;
    color: #2c3e50;
}

/* 筛选面板样式 */
.filter-panel {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
}

.filter-panel .card-header {
    background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%);
    color: white;
}

/* 分页样式 */
.pagination .page-link {
    border-radius: 8px;
    margin: 0 2px;
    border: 2px solid #e9ecef;
    color: #0d6efd;
    font-weight: 500;
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.pagination .page-link:hover {
    background-color: #e7f1ff;
    border-color: #0d6efd;
    transform: translateY(-1px);
}

/* 模态框样式 */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 40px rgba(0,0,0,0.2);
}

.modal-header {
    border-bottom: 2px solid #f1f3f4;
    border-radius: 15px 15px 0 0;
}

.modal-footer {
    border-top: 2px solid #f1f3f4;
    border-radius: 0 0 15px 15px;
}

/* Toast 容器样式 */
.toast-container .toast {
    border-radius: 12px;
    border: none;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .stat-number {
        font-size: 2rem;
    }
    
    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
    
    .feature-card {
        padding: 20px 15px;
    }
    
    .feature-icon {
        width: 45px;
        height: 45px;
        font-size: 18px;
    }
    
    .btn-group {
        flex-direction: column;
        width: 100%;
    }
    
    .btn-group .btn {
        margin-bottom: 8px;
        border-radius: 8px !important;
    }
    
    .info-item {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .info-item label {
        min-width: auto;
        margin-bottom: 5px;
    }
    
    .table-responsive {
        font-size: 0.85rem;
    }
    
    .timeline {
        padding-left: 25px;
    }
    
    .timeline-marker {
        left: -32px;
        width: 12px;
        height: 12px;
    }
}

@media (max-width: 576px) {
    .reports-container {
        padding: 0 10px;
    }
    
    .card {
        margin-bottom: 15px;
    }
    
    .btn-lg {
        padding: 10px 20px;
        font-size: 1rem;
    }
}

/* 打印样式 */
@media print {
    .btn, .card-header, .timeline, .page-header, nav, .filter-panel {
        display: none !important;
    }
    
    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
        page-break-inside: avoid;
    }
    
    .info-item {
        border-bottom: 1px solid #dee2e6;
    }
    
    .table {
        font-size: 12px;
    }
    
    .badge {
        border: 1px solid #000;
        color: #000 !important;
        background: transparent !important;
    }
}

/* 图表相关样式 */
.chart-container {
    position: relative;
    height: 400px;
    margin-bottom: 20px;
}

.chart-container.small {
    height: 300px;
}

.chart-container.large {
    height: 500px;
}

.chart-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #6c757d;
}

.chart-loading .spinner-border {
    width: 3rem;
    height: 3rem;
}

.chart-error {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #dc3545;
}

.chart-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.chart-toolbar .btn-group {
    gap: 5px;
}

.chart-toolbar .btn-sm {
    padding: 4px 8px;
    font-size: 0.8rem;
}

.filter-controls {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.filter-controls .row {
    align-items: end;
}

.filter-controls .form-label {
    font-size: 0.9rem;
    font-weight: 600;
    color: #495057;
}

.filter-controls .form-control,
.filter-controls .form-select {
    font-size: 0.9rem;
    border-radius: 6px;
}

.statistics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.statistics-card {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.statistics-card:hover {
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.statistics-card .card-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 8px;
}

.statistics-card .card-title i {
    color: #0d6efd;
}

.chart-legend {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 3px;
    flex-shrink: 0;
}

.data-table-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 8px;
}

.data-table {
    margin-bottom: 0;
}

.data-table th {
    position: sticky;
    top: 0;
    background: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    font-size: 0.9rem;
}

.data-table td {
    font-size: 0.9rem;
    vertical-align: middle;
}

.trend-indicator {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    font-size: 0.9rem;
    font-weight: 500;
}

.trend-indicator.up {
    color: #198754;
}

.trend-indicator.down {
    color: #dc3545;
}

.trend-indicator.stable {
    color: #6c757d;
}

.metric-value {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.metric-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 0;
}

.metric-change {
    font-size: 0.8rem;
    margin-top: 5px;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .feature-card {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        border-color: #495057;
        color: #f8f9fa;
    }

    .feature-title {
        color: #f8f9fa;
    }

    .timeline-content {
        background: #495057;
        color: #f8f9fa;
    }

    .info-item {
        border-bottom-color: #495057;
    }

    .statistics-card {
        background: #2c3e50;
        border-color: #495057;
        color: #f8f9fa;
    }

    .filter-controls {
        background: #2c3e50;
        border-color: #495057;
        color: #f8f9fa;
    }

    .chart-toolbar {
        background: #495057;
        border-color: #6c757d;
        color: #f8f9fa;
    }
}

/* 日期输入框增强样式 */
input[type="date"],
input[type="datetime-local"] {
    cursor: pointer !important;
    transition: all 0.2s ease;
    position: relative;
}

input[type="date"]:hover,
input[type="datetime-local"]:hover {
    background-color: #f8f9fa !important;
    border-color: #86b7fe !important;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.15) !important;
}

input[type="date"]:focus,
input[type="datetime-local"]:focus {
    background-color: #fff !important;
    border-color: #86b7fe !important;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25) !important;
}

/* 日期输入框的日历图标样式优化 */
input[type="date"]::-webkit-calendar-picker-indicator,
input[type="datetime-local"]::-webkit-calendar-picker-indicator {
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

input[type="date"]:hover::-webkit-calendar-picker-indicator,
input[type="datetime-local"]:hover::-webkit-calendar-picker-indicator {
    opacity: 1;
}

/* 为日期输入框添加提示文本 */
input[type="date"]::before,
input[type="datetime-local"]::before {
    content: attr(placeholder);
    color: #6c757d;
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
    transition: all 0.2s ease;
}

input[type="date"]:focus::before,
input[type="date"]:valid::before,
input[type="datetime-local"]:focus::before,
input[type="datetime-local"]:valid::before {
    opacity: 0;
}

/* 表单组中的日期输入框 */
.input-group input[type="date"],
.input-group input[type="datetime-local"] {
    border-radius: 0 0.375rem 0.375rem 0 !important;
}

.input-group input[type="date"]:hover,
.input-group input[type="datetime-local"]:hover {
    z-index: 2;
}
