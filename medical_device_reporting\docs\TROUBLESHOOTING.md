# 故障排除指南

本文档记录了医疗器械不良事件上报平台开发和使用过程中遇到的常见问题及解决方案。

## 📋 目录

- [最新修复问题 (2025-06-21)](#最新修复问题-2025-06-21)
- [用户管理相关问题](#用户管理相关问题)
- [前端JavaScript问题](#前端javascript问题)
- [CSRF Token问题](#csrf-token问题)
- [权限和认证问题](#权限和认证问题)
- [数据库相关问题](#数据库相关问题)
- [静态文件问题](#静态文件问题)
- [调试工具和方法](#调试工具和方法)

## 最新修复问题 (2025-06-21)

### 问题A: URL路由错误修复

**问题描述**: `django.urls.exceptions.NoReverseMatch: Reverse for 'step_create' not found`

**症状**:
- 点击"分步创建"按钮时出现NoReverseMatch错误
- 模板中使用了不存在的URL名称

**根本原因**:
- 模板中使用了 `{% url 'reports:step_create' %}`
- 但URL配置中只有 `report_step_create` 这个URL名称
- 缺少直接的分步创建入口URL

**解决方案**:

1. **添加新的URL入口**:
```python
# apps/reports/urls.py
path('create/step/', views.report_step_create_entry_view, name='step_create'),
```

2. **创建入口视图函数**:
```python
@department_member_or_admin_required
def report_step_create_entry_view(request):
    """分步创建报告入口视图"""
    # 清除之前的表单数据
    if 'report_form_data' in request.session:
        del request.session['report_form_data']
    # 重定向到第一步
    return redirect('reports:report_step_create', step=1)
```

3. **更新模板URL引用**:
   - `templates/reports/dashboard.html`
   - `templates/reports/report_list.html`
   - `templates/users/dashboard.html`

**URL结构说明**:
- `/reports/create/step/` → 分步创建入口（清除数据并重定向到第一步）
- `/reports/create/step/1/` → 分步表单第一步
- `/reports/create/step/2/` → 分步表单第二步
- `/reports/create/step/3/` → 分步表单第三步
- `/reports/create/step/4/` → 分步表单第四步

**验证方法**:
```bash
# 测试URL解析
python manage.py shell -c "from django.urls import reverse; print(reverse('reports:step_create'))"

# 检查系统状态
python manage.py check
```

### 问题B: 模板语法错误修复

**问题描述**: `django.template.exceptions.TemplateSyntaxError: 'block' tag with name 'page_heading' appears more than once`

**症状**:
- 访问报告相关页面时出现模板语法错误
- Django无法渲染模板

**根本原因**:
- 在 `templates/reports/base.html` 文件中，`page_heading` block 被定义了两次
- 第一次在 `page_header` block 内（第64行）
- 第二次在独立的页面标题部分（第81行）
- Django模板系统不允许重复定义相同名称的 block

**解决方案**:

删除重复的页面标题部分，保留在 `page_header` block 内的定义:

```html
<!-- 修复前 - 有重复定义 -->
{% block page_header %}
    <h2>{% block page_heading %}报告管理{% endblock %}</h2>
{% endblock %}
<!-- 重复的页面标题部分 -->
<h2>{% block page_heading %}报告管理{% endblock %}</h2>  <!-- 重复! -->

<!-- 修复后 - 唯一定义 -->
{% block page_header %}
    <h2>{% block page_heading %}报告管理{% endblock %}</h2>
{% endblock %}
```

**验证方法**:
```bash
# 检查模板编译
python manage.py check

# 测试模板渲染
python manage.py shell -c "from django.template.loader import get_template; get_template('reports/base.html')"

# 启动服务器测试
python manage.py runserver
```

**影响范围**:
- `reports/base.html` - 基础模板（已修复）
- `reports/dashboard.html` - 报告仪表板
- `reports/report_list.html` - 报告列表
- `reports/report_detail.html` - 报告详情
- `reports/report_form.html` - 报告表单
- `reports/report_step_form.html` - 分步表单

## 用户管理相关问题

### 问题1: 用户状态切换403错误

**症状**: 点击用户列表中的禁用/启用按钮时出现HTTP 403 Forbidden错误

**发生时间**: 2024-06-20

**根本原因**: 
1. JavaScript文件缓存导致使用旧版本代码
2. 错误的API调用路径（调用DRF API而非Django视图）
3. CSRF token获取失败或传递错误

**解决步骤**:

1. **修复JavaScript缓存问题**:
   ```html
   <!-- 在模板中添加动态时间戳 -->
   <script src="{% static 'users/js/user_management.js' %}?v={{ timestamp }}"></script>
   ```

2. **修正API调用路径**:
   ```javascript
   // 错误的调用方式
   const url = `/api/users/${userId}/${endpoint}/`;
   
   // 正确的调用方式
   const url = `/users/${userId}/toggle-status/`;
   ```

3. **增强CSRF token获取**:
   ```javascript
   function getCsrfToken() {
       // 尝试多种方式获取CSRF token
       let token = '';
       
       // 方式1: 从meta标签获取
       const metaToken = document.querySelector('meta[name=csrf-token]')?.getAttribute('content');
       if (metaToken) token = metaToken;
       
       // 方式2: 从表单字段获取
       if (!token) {
           const inputToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value;
           if (inputToken) token = inputToken;
       }
       
       // 方式3: 从cookie获取
       if (!token) {
           const cookieToken = getCookieValue('csrftoken');
           if (cookieToken) token = cookieToken;
       }
       
       return token;
   }
   ```

4. **双重CSRF保护**:
   ```javascript
   fetch(url, {
       method: 'POST',
       headers: {
           'X-CSRFToken': csrfToken,
           'Content-Type': 'application/x-www-form-urlencoded',
       },
       body: `csrfmiddlewaretoken=${encodeURIComponent(csrfToken)}`
   })
   ```

**验证方法**:
1. 检查浏览器控制台是否显示版本信息
2. 确认API调用路径正确
3. 验证CSRF token获取成功
4. 测试用户状态切换功能

## 前端JavaScript问题

### 问题2: JavaScript文件缓存

**症状**: 修改JavaScript文件后浏览器仍使用旧版本

**解决方案**:
1. 添加版本号或时间戳到文件URL
2. 重启Django开发服务器
3. 硬刷新浏览器 (Ctrl+F5)
4. 清除浏览器缓存

### 问题3: DataTables初始化失败

**症状**: 用户列表页面显示空白或错误

**常见原因**:
- API端点返回错误格式数据
- 列配置与API响应不匹配
- JavaScript依赖库加载失败

**解决方法**:
1. 检查API响应格式
2. 验证列配置正确性
3. 确认所有依赖库正确加载

### 问题4: 导入按钮无响应

**症状**: 点击"导入Excel"按钮没有任何反应

**发生时间**: 2024-06-20

**根本原因**:
1. 外部JavaScript文件加载失败或语法错误
2. 事件绑定在DOM加载前执行
3. JavaScript依赖库冲突或版本问题

**解决步骤**:

1. **检查JavaScript加载**:
   ```javascript
   // 在浏览器控制台检查
   console.log('jQuery版本:', typeof $ !== 'undefined' ? $.fn.jquery : '未加载');
   console.log('Bootstrap版本:', typeof bootstrap !== 'undefined' ? 'Bootstrap 5' : '未加载');
   ```

2. **使用内联JavaScript替代外部文件**:
   ```html
   <script>
   $(document).ready(function() {
       $('#importBtn').on('click', function() {
           $('#importModal').modal('show');
       });
   });
   </script>
   ```

3. **添加调试信息**:
   ```javascript
   console.log('按钮元素:', $('#importBtn').length);
   console.log('模态框元素:', $('#importModal').length);
   ```

**预防措施**:
- 使用内联JavaScript处理关键功能
- 添加详细的调试日志
- 确保DOM完全加载后再绑定事件

## CSRF Token问题

### 问题4: CSRF token获取失败

**症状**: 表单提交或AJAX请求返回403错误，提示CSRF token问题

**解决步骤**:
1. 确认模板中包含CSRF token:
   ```html
   <meta name="csrf-token" content="{{ csrf_token }}">
   ```

2. 检查JavaScript获取方式:
   ```javascript
   // 多种获取方式确保成功
   const token = document.querySelector('meta[name=csrf-token]')?.getAttribute('content') ||
                 document.querySelector('[name=csrfmiddlewaretoken]')?.value ||
                 getCookieValue('csrftoken');
   ```

3. 验证HTTP头设置:
   ```javascript
   headers: {
       'X-CSRFToken': token,
       'Content-Type': 'application/x-www-form-urlencoded',
   }
   ```

## 权限和认证问题

### 问题5: 用户未登录导致的403错误

**症状**: 访问需要认证的页面或API时返回403错误

**检查方法**:
1. 访问调试端点: `/debug-permissions/`
2. 检查返回的用户信息
3. 确认用户已正确登录

**解决方案**:
1. 使用管理员账号登录
2. 检查会话是否过期
3. 验证用户权限配置

### 问题6: 权限中间件拦截

**症状**: 有权限的用户仍被拒绝访问

**解决方法**:
1. 检查中间件配置
2. 确认权限检查逻辑
3. 验证用户角色和权限

## 数据库相关问题

### 问题7: 数据库连接失败

**常见原因**:
- 数据库服务未启动
- 连接配置错误
- 权限不足

**解决步骤**:
1. 检查数据库服务状态
2. 验证连接配置
3. 测试数据库连接

## 静态文件问题

### 问题8: 静态文件404错误

**解决方案**:
1. 运行 `python manage.py collectstatic`
2. 检查静态文件URL配置
3. 验证文件路径正确性

## 调试工具和方法

### 浏览器调试

1. **开发者工具**:
   - 控制台: 查看JavaScript错误
   - 网络: 检查API请求和响应
   - 应用: 查看cookie和存储

2. **常用调试代码**:
   ```javascript
   // 添加调试日志
   console.log('调试信息:', data);
   
   // 检查元素存在性
   console.log('元素存在:', !!document.querySelector('#element'));
   ```

### 服务器端调试

1. **Django日志**:
   ```python
   import logging
   logger = logging.getLogger(__name__)
   logger.info('调试信息: %s', data)
   ```

2. **调试视图**:
   ```python
   def debug_view(request):
       return JsonResponse({
           'user': request.user.username,
           'is_authenticated': request.user.is_authenticated,
           'permissions': list(request.user.get_all_permissions())
       })
   ```

### 系统检查命令

```bash
# 检查系统配置
python manage.py check

# 检查数据库迁移
python manage.py showmigrations

# 收集静态文件
python manage.py collectstatic

# 创建超级用户
python manage.py createsuperuser
```

## 预防措施

1. **定期备份数据库**
2. **使用版本控制管理代码**
3. **编写单元测试**
4. **监控系统日志**
5. **定期更新依赖包**

## 联系支持

如果遇到本文档未涵盖的问题，请：

1. 检查项目日志文件
2. 搜索相关错误信息
3. 查看Django官方文档
4. 提交详细的问题报告

---

**最后更新**: 2024-06-20
**维护者**: 开发团队
