/**
 * Dashboard JavaScript for Medical Device Reporting Platform
 * 医疗器械不良事件上报平台仪表板脚本
 */

document.addEventListener('DOMContentLoaded', function() {
    // 初始化仪表板
    initializeDashboard();
    
    /**
     * 初始化仪表板
     */
    function initializeDashboard() {
        // 绑定事件监听器
        bindEventListeners();
        
        // 初始化统计卡片动画
        initializeStatCards();
        
        // 初始化功能卡片
        initializeFeatureCards();
        
        // 加载仪表板数据
        loadDashboardData();
        
        // 设置自动刷新
        setupAutoRefresh();
        
        console.log('仪表板初始化完成');
    }
    
    /**
     * 绑定事件监听器
     */
    function bindEventListeners() {
        // 功能卡片点击事件
        const featureLinks = document.querySelectorAll('.feature-link');
        featureLinks.forEach(link => {
            link.addEventListener('click', handleFeatureClick);
        });
        
        // 统计卡片点击事件
        const statCards = document.querySelectorAll('.stat-card');
        statCards.forEach(card => {
            card.addEventListener('click', handleStatCardClick);
        });
        
        // 最近报告链接点击事件
        const reportLinks = document.querySelectorAll('.list-group-item a');
        reportLinks.forEach(link => {
            link.addEventListener('click', handleReportLinkClick);
        });
        
        // 刷新按钮（如果存在）
        const refreshBtn = document.getElementById('refreshDashboard');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', function() {
                loadDashboardData();
            });
        }
    }
    
    /**
     * 初始化统计卡片动画
     */
    function initializeStatCards() {
        const statCards = document.querySelectorAll('.stat-card');
        
        statCards.forEach((card, index) => {
            // 初始状态
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            
            // 延迟动画
            setTimeout(() => {
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
                
                // 数字计数动画
                const numberElement = card.querySelector('.stat-number');
                if (numberElement) {
                    animateNumber(numberElement);
                }
            }, index * 200);
        });
    }
    
    /**
     * 数字计数动画
     */
    function animateNumber(element) {
        const finalNumber = parseInt(element.textContent) || 0;
        const duration = 1000; // 1秒
        const steps = 30;
        const stepValue = finalNumber / steps;
        let currentNumber = 0;
        
        element.textContent = '0';
        
        const timer = setInterval(() => {
            currentNumber += stepValue;
            if (currentNumber >= finalNumber) {
                currentNumber = finalNumber;
                clearInterval(timer);
            }
            element.textContent = Math.floor(currentNumber);
        }, duration / steps);
    }
    
    /**
     * 初始化功能卡片
     */
    function initializeFeatureCards() {
        const featureCards = document.querySelectorAll('.feature-card');
        
        featureCards.forEach((card, index) => {
            // 初始状态
            card.style.opacity = '0';
            card.style.transform = 'scale(0.9)';
            
            // 延迟动画
            setTimeout(() => {
                card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                card.style.opacity = '1';
                card.style.transform = 'scale(1)';
            }, 800 + index * 100);
        });
    }
    
    /**
     * 处理功能卡片点击
     */
    function handleFeatureClick(event) {
        const card = event.currentTarget.querySelector('.feature-card');
        
        // 点击动画效果
        if (card) {
            card.style.transform = 'scale(0.95)';
            setTimeout(() => {
                card.style.transform = 'scale(1)';
            }, 150);
        }
        
        // 记录用户行为（可选）
        const featureTitle = card ? card.querySelector('.feature-title')?.textContent : '';
        if (featureTitle) {
            console.log(`用户点击功能: ${featureTitle}`);
        }
    }
    
    /**
     * 处理统计卡片点击
     */
    function handleStatCardClick(event) {
        const card = event.currentTarget;
        const statLabel = card.querySelector('.stat-label')?.textContent;
        
        // 根据统计类型跳转到相应页面
        switch (statLabel) {
            case '总报告数':
                window.location.href = '/reports/list/';
                break;
            case '草稿':
                window.location.href = '/reports/list/?status=draft';
                break;
            case '已提交':
                window.location.href = '/reports/list/?status=submitted';
                break;
            case '已批准':
                window.location.href = '/reports/list/?status=approved';
                break;
            default:
                console.log(`点击统计卡片: ${statLabel}`);
        }
    }
    
    /**
     * 处理报告链接点击
     */
    function handleReportLinkClick(event) {
        const link = event.target;
        
        // 添加点击效果
        link.style.transform = 'scale(0.98)';
        setTimeout(() => {
            link.style.transform = 'scale(1)';
        }, 100);
    }
    
    /**
     * 加载仪表板数据
     */
    function loadDashboardData() {
        // 显示加载状态
        showLoadingState();
        
        // 获取仪表板数据
        fetch('/reports/api/dashboard/', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken()
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            updateDashboardData(data);
            hideLoadingState();
        })
        .catch(error => {
            console.error('加载仪表板数据失败:', error);
            hideLoadingState();
            showErrorMessage('加载数据失败，请刷新页面重试');
        });
    }
    
    /**
     * 更新仪表板数据
     */
    function updateDashboardData(data) {
        // 更新统计数字
        updateStatNumbers(data.stats);
        
        // 更新最近报告
        updateRecentReports(data.recent_reports);
        
        // 更新待审核报告（如果是管理员）
        if (data.pending_reports) {
            updatePendingReports(data.pending_reports);
        }
        
        // 更新严重事件（如果是管理员）
        if (data.serious_events) {
            updateSeriousEvents(data.serious_events);
        }
    }
    
    /**
     * 更新统计数字
     */
    function updateStatNumbers(stats) {
        const statMappings = {
            'total_count': '.stat-number:eq(0)',
            'draft_count': '.stat-number:eq(1)',
            'submitted_count': '.stat-number:eq(2)',
            'approved_count': '.stat-number:eq(3)'
        };
        
        Object.entries(statMappings).forEach(([key, selector]) => {
            const element = document.querySelector(selector);
            if (element && stats[key] !== undefined) {
                const newValue = stats[key];
                const currentValue = parseInt(element.textContent) || 0;
                
                if (newValue !== currentValue) {
                    animateNumberChange(element, currentValue, newValue);
                }
            }
        });
    }
    
    /**
     * 数字变化动画
     */
    function animateNumberChange(element, fromValue, toValue) {
        const duration = 500;
        const steps = 20;
        const stepValue = (toValue - fromValue) / steps;
        let currentValue = fromValue;
        
        const timer = setInterval(() => {
            currentValue += stepValue;
            if ((stepValue > 0 && currentValue >= toValue) || 
                (stepValue < 0 && currentValue <= toValue)) {
                currentValue = toValue;
                clearInterval(timer);
            }
            element.textContent = Math.round(currentValue);
        }, duration / steps);
    }
    
    /**
     * 更新最近报告
     */
    function updateRecentReports(reports) {
        const container = document.querySelector('.list-group');
        if (!container || !reports) return;
        
        // 清空现有内容
        container.innerHTML = '';
        
        if (reports.length === 0) {
            container.innerHTML = `
                <div class="text-center py-4">
                    <i class="bi bi-inbox display-4 text-muted"></i>
                    <p class="text-muted mt-2">暂无报告</p>
                </div>
            `;
            return;
        }
        
        // 添加报告项
        reports.forEach(report => {
            const reportItem = createReportItem(report);
            container.appendChild(reportItem);
        });
    }
    
    /**
     * 创建报告项元素
     */
    function createReportItem(report) {
        const item = document.createElement('div');
        item.className = 'list-group-item border-0 px-0';
        
        item.innerHTML = `
            <div class="row align-items-center">
                <div class="col">
                    <h6 class="mb-1">
                        <a href="/reports/${report.id}/" class="text-decoration-none">
                            ${report.report_number}
                        </a>
                    </h6>
                    <p class="mb-1 text-muted small">${report.device_name}</p>
                    <small class="text-muted">${formatDate(report.created_at)}</small>
                </div>
                <div class="col-auto">
                    <span class="badge bg-${getStatusColor(report.status)}">
                        ${report.status_display}
                    </span>
                </div>
            </div>
        `;
        
        return item;
    }
    
    /**
     * 获取状态颜色
     */
    function getStatusColor(status) {
        const colorMap = {
            'draft': 'secondary',
            'submitted': 'info',
            'under_review': 'warning',
            'approved': 'success',
            'rejected': 'danger'
        };
        return colorMap[status] || 'secondary';
    }
    
    /**
     * 格式化日期
     */
    function formatDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffTime = Math.abs(now - date);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        if (diffDays === 1) {
            return '今天';
        } else if (diffDays === 2) {
            return '昨天';
        } else if (diffDays <= 7) {
            return `${diffDays}天前`;
        } else {
            return date.toLocaleDateString();
        }
    }
    
    /**
     * 显示加载状态
     */
    function showLoadingState() {
        const statNumbers = document.querySelectorAll('.stat-number');
        statNumbers.forEach(element => {
            element.style.opacity = '0.5';
        });
    }
    
    /**
     * 隐藏加载状态
     */
    function hideLoadingState() {
        const statNumbers = document.querySelectorAll('.stat-number');
        statNumbers.forEach(element => {
            element.style.opacity = '1';
        });
    }
    
    /**
     * 设置自动刷新
     */
    function setupAutoRefresh() {
        // 每5分钟自动刷新一次数据
        setInterval(() => {
            loadDashboardData();
        }, 5 * 60 * 1000);
    }
    
    /**
     * 获取CSRF Token
     */
    function getCsrfToken() {
        const token = document.querySelector('[name=csrfmiddlewaretoken]');
        return token ? token.value : '';
    }
    
    /**
     * 显示错误消息
     */
    function showErrorMessage(message) {
        if (typeof showMessage === 'function') {
            showMessage(message, 'danger', 5000);
        }
    }
});
