/**
 * Custom JavaScript for Medical Device Reporting Platform
 * 医疗器械不良事件上报平台自定义脚本
 */

// 等待DOM加载完成
document.addEventListener('DOMContentLoaded', function() {
    console.log('医疗器械不良事件上报平台已加载');
    
    // 初始化工具提示
    initializeTooltips();
    
    // 初始化弹出框
    initializePopovers();
    
    // 初始化表单验证
    initializeFormValidation();
    
    // 初始化确认对话框
    initializeConfirmDialogs();
});

/**
 * 初始化Bootstrap工具提示
 */
function initializeTooltips() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * 初始化Bootstrap弹出框
 */
function initializePopovers() {
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
}

/**
 * 初始化表单验证
 */
function initializeFormValidation() {
    // 获取所有需要验证的表单
    var forms = document.querySelectorAll('.needs-validation');
    
    // 为每个表单添加验证事件
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });
}

/**
 * 初始化确认对话框
 */
function initializeConfirmDialogs() {
    // 为所有带有data-confirm属性的元素添加确认对话框
    var confirmElements = document.querySelectorAll('[data-confirm]');
    
    confirmElements.forEach(function(element) {
        element.addEventListener('click', function(event) {
            var message = element.getAttribute('data-confirm');
            if (!confirm(message)) {
                event.preventDefault();
                return false;
            }
        });
    });
}

/**
 * 显示成功消息
 * @param {string} message - 消息内容
 * @param {number} duration - 显示时长（毫秒）
 */
function showSuccessMessage(message, duration = 3000) {
    showMessage(message, 'success', duration);
}

/**
 * 显示错误消息
 * @param {string} message - 消息内容
 * @param {number} duration - 显示时长（毫秒）
 */
function showErrorMessage(message, duration = 5000) {
    showMessage(message, 'danger', duration);
}

/**
 * 显示警告消息
 * @param {string} message - 消息内容
 * @param {number} duration - 显示时长（毫秒）
 */
function showWarningMessage(message, duration = 4000) {
    showMessage(message, 'warning', duration);
}

/**
 * 显示信息消息
 * @param {string} message - 消息内容
 * @param {number} duration - 显示时长（毫秒）
 */
function showInfoMessage(message, duration = 3000) {
    showMessage(message, 'info', duration);
}

/**
 * 通用消息显示函数
 * @param {string} message - 消息内容
 * @param {string} type - 消息类型 (success, danger, warning, info)
 * @param {number} duration - 显示时长（毫秒）
 */
function showMessage(message, type, duration) {
    // 创建消息容器
    var alertContainer = document.getElementById('alert-container');
    if (!alertContainer) {
        alertContainer = document.createElement('div');
        alertContainer.id = 'alert-container';
        alertContainer.style.position = 'fixed';
        alertContainer.style.top = '20px';
        alertContainer.style.right = '20px';
        alertContainer.style.zIndex = '9999';
        alertContainer.style.maxWidth = '400px';
        document.body.appendChild(alertContainer);
    }
    
    // 创建消息元素
    var alertElement = document.createElement('div');
    alertElement.className = `alert alert-${type} alert-dismissible fade show`;
    alertElement.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    // 添加到容器
    alertContainer.appendChild(alertElement);
    
    // 自动隐藏
    setTimeout(function() {
        if (alertElement.parentNode) {
            var alert = new bootstrap.Alert(alertElement);
            alert.close();
        }
    }, duration);
}

/**
 * 加载指示器
 */
var LoadingIndicator = {
    show: function(element) {
        if (element) {
            element.disabled = true;
            var originalText = element.innerHTML;
            element.setAttribute('data-original-text', originalText);
            element.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 加载中...';
        }
    },
    
    hide: function(element) {
        if (element) {
            element.disabled = false;
            var originalText = element.getAttribute('data-original-text');
            if (originalText) {
                element.innerHTML = originalText;
                element.removeAttribute('data-original-text');
            }
        }
    }
};

/**
 * AJAX请求封装
 * @param {string} url - 请求URL
 * @param {Object} options - 请求选项
 */
function ajaxRequest(url, options = {}) {
    const defaultOptions = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        }
    };
    
    const finalOptions = Object.assign(defaultOptions, options);
    
    return fetch(url, finalOptions)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .catch(error => {
            console.error('AJAX请求错误:', error);
            showErrorMessage('请求失败，请稍后重试');
            throw error;
        });
}

/**
 * 获取CSRF令牌
 */
function getCsrfToken() {
    var csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
    return csrfToken ? csrfToken.value : '';
}

/**
 * 格式化日期
 * @param {Date} date - 日期对象
 * @param {string} format - 格式字符串
 */
function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
    if (!(date instanceof Date)) {
        date = new Date(date);
    }
    
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    
    return format
        .replace('YYYY', year)
        .replace('MM', month)
        .replace('DD', day)
        .replace('HH', hours)
        .replace('mm', minutes)
        .replace('ss', seconds);
}
