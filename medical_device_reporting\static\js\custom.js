/**
 * Custom JavaScript for Medical Device Reporting Platform
 * 医疗器械不良事件上报平台自定义脚本
 */

// 等待DOM加载完成
document.addEventListener('DOMContentLoaded', function() {
    console.log('医疗器械不良事件上报平台已加载');
    
    // 初始化工具提示
    initializeTooltips();
    
    // 初始化弹出框
    initializePopovers();
    
    // 初始化表单验证
    initializeFormValidation();
    
    // 初始化确认对话框
    initializeConfirmDialogs();

    // 初始化日期输入框增强
    initializeDateInputEnhancements();

    // 设置DOM变化监听器，确保动态加载的日期输入框也能被增强
    setupDateInputObserver();
});

/**
 * 初始化Bootstrap工具提示
 */
function initializeTooltips() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * 初始化Bootstrap弹出框
 */
function initializePopovers() {
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
}

/**
 * 初始化表单验证
 */
function initializeFormValidation() {
    // 获取所有需要验证的表单
    var forms = document.querySelectorAll('.needs-validation');
    
    // 为每个表单添加验证事件
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });
}

/**
 * 初始化确认对话框
 */
function initializeConfirmDialogs() {
    // 为所有带有data-confirm属性的元素添加确认对话框
    var confirmElements = document.querySelectorAll('[data-confirm]');
    
    confirmElements.forEach(function(element) {
        element.addEventListener('click', function(event) {
            var message = element.getAttribute('data-confirm');
            if (!confirm(message)) {
                event.preventDefault();
                return false;
            }
        });
    });
}

/**
 * 显示成功消息
 * @param {string} message - 消息内容
 * @param {number} duration - 显示时长（毫秒）
 */
function showSuccessMessage(message, duration = 3000) {
    showMessage(message, 'success', duration);
}

/**
 * 显示错误消息
 * @param {string} message - 消息内容
 * @param {number} duration - 显示时长（毫秒）
 */
function showErrorMessage(message, duration = 5000) {
    showMessage(message, 'danger', duration);
}

/**
 * 显示警告消息
 * @param {string} message - 消息内容
 * @param {number} duration - 显示时长（毫秒）
 */
function showWarningMessage(message, duration = 4000) {
    showMessage(message, 'warning', duration);
}

/**
 * 显示信息消息
 * @param {string} message - 消息内容
 * @param {number} duration - 显示时长（毫秒）
 */
function showInfoMessage(message, duration = 3000) {
    showMessage(message, 'info', duration);
}

/**
 * 通用消息显示函数
 * @param {string} message - 消息内容
 * @param {string} type - 消息类型 (success, danger, warning, info)
 * @param {number} duration - 显示时长（毫秒）
 */
function showMessage(message, type, duration) {
    // 创建消息容器
    var alertContainer = document.getElementById('alert-container');
    if (!alertContainer) {
        alertContainer = document.createElement('div');
        alertContainer.id = 'alert-container';
        alertContainer.style.position = 'fixed';
        alertContainer.style.top = '20px';
        alertContainer.style.right = '20px';
        alertContainer.style.zIndex = '9999';
        alertContainer.style.maxWidth = '400px';
        document.body.appendChild(alertContainer);
    }
    
    // 创建消息元素
    var alertElement = document.createElement('div');
    alertElement.className = `alert alert-${type} alert-dismissible fade show`;
    alertElement.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    // 添加到容器
    alertContainer.appendChild(alertElement);
    
    // 自动隐藏
    setTimeout(function() {
        if (alertElement.parentNode) {
            var alert = new bootstrap.Alert(alertElement);
            alert.close();
        }
    }, duration);
}

/**
 * 加载指示器
 */
var LoadingIndicator = {
    show: function(element) {
        if (element) {
            element.disabled = true;
            var originalText = element.innerHTML;
            element.setAttribute('data-original-text', originalText);
            element.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 加载中...';
        }
    },
    
    hide: function(element) {
        if (element) {
            element.disabled = false;
            var originalText = element.getAttribute('data-original-text');
            if (originalText) {
                element.innerHTML = originalText;
                element.removeAttribute('data-original-text');
            }
        }
    }
};

/**
 * AJAX请求封装
 * @param {string} url - 请求URL
 * @param {Object} options - 请求选项
 */
function ajaxRequest(url, options = {}) {
    const defaultOptions = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        }
    };
    
    const finalOptions = Object.assign(defaultOptions, options);
    
    return fetch(url, finalOptions)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .catch(error => {
            console.error('AJAX请求错误:', error);
            showErrorMessage('请求失败，请稍后重试');
            throw error;
        });
}

/**
 * 获取CSRF令牌
 */
function getCsrfToken() {
    var csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
    return csrfToken ? csrfToken.value : '';
}

/**
 * 格式化日期
 * @param {Date} date - 日期对象
 * @param {string} format - 格式字符串
 */
function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
    if (!(date instanceof Date)) {
        date = new Date(date);
    }
    
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    
    return format
        .replace('YYYY', year)
        .replace('MM', month)
        .replace('DD', day)
        .replace('HH', hours)
        .replace('mm', minutes)
        .replace('ss', seconds);
}

/**
 * 初始化日期输入框增强功能
 */
function initializeDateInputEnhancements() {
    // 使用更广泛的选择器来确保捕获所有日期输入框
    const dateInputs = document.querySelectorAll('input[type="date"], input[type="datetime-local"], input[id*="date"], input[name*="date"]');

    console.log('找到日期输入框数量:', dateInputs.length);

    dateInputs.forEach((input, index) => {
        console.log(`处理日期输入框 ${index + 1}:`, input.id, input.name, input.type);

        // 确保输入框类型正确
        if (input.type !== 'date' && input.type !== 'datetime-local') {
            // 如果不是日期类型，尝试设置为日期类型
            if (input.name && (input.name.includes('date') || input.id.includes('date'))) {
                // 所有日期字段都使用date类型（只需要年月日）
                input.type = 'date';
                console.log(`已将输入框类型设置为: ${input.type}`);
            }
        }

        // 移除现有的事件监听器（如果有）
        input.removeEventListener('click', handleDateInputClick);
        input.removeEventListener('focus', handleDateInputFocus);
        input.removeEventListener('keydown', handleDateInputKeydown);

        // 添加新的事件监听器
        input.addEventListener('click', handleDateInputClick);
        input.addEventListener('focus', handleDateInputFocus);
        input.addEventListener('keydown', handleDateInputKeydown);

        // 改善视觉反馈
        input.style.cursor = 'pointer';
        input.style.transition = 'all 0.2s ease';

        // 添加hover效果
        input.addEventListener('mouseenter', function() {
            if (!this.disabled && !this.readOnly) {
                this.style.backgroundColor = '#f8f9fa';
                this.style.borderColor = '#86b7fe';
            }
        });

        input.addEventListener('mouseleave', function() {
            if (!this.disabled && !this.readOnly) {
                this.style.backgroundColor = '';
                this.style.borderColor = '';
            }
        });

        // 添加焦点效果
        input.addEventListener('focus', function() {
            this.style.backgroundColor = '#fff';
        });

        input.addEventListener('blur', function() {
            this.style.backgroundColor = '';
        });

        console.log(`日期输入框 ${index + 1} 增强完成`);
    });
}

/**
 * 处理日期输入框点击事件
 */
function handleDateInputClick(e) {
    console.log('日期输入框被点击:', this.id, this.name);

    // 阻止默认行为，我们要自己处理
    e.preventDefault();

    // 先聚焦到输入框
    this.focus();

    // 延迟一点时间再尝试打开日期选择器
    setTimeout(() => {
        if (this.showPicker && typeof this.showPicker === 'function') {
            try {
                console.log('尝试调用 showPicker()');
                this.showPicker();
            } catch (e) {
                console.log('showPicker() 调用失败:', e);
                // 如果showPicker失败，尝试模拟点击日历图标
                simulateCalendarIconClick(this);
            }
        } else {
            console.log('showPicker() 不可用，尝试其他方法');
            // 如果showPicker不可用，尝试其他方法
            simulateCalendarIconClick(this);
        }
    }, 50);
}

/**
 * 处理日期输入框聚焦事件
 */
function handleDateInputFocus(e) {
    console.log('日期输入框获得焦点:', this.id, this.name);

    // 当输入框获得焦点时，也尝试打开日期选择器
    setTimeout(() => {
        if (this.showPicker && typeof this.showPicker === 'function') {
            try {
                this.showPicker();
            } catch (e) {
                console.debug('Focus时showPicker失败:', e);
            }
        }
    }, 100);
}

/**
 * 处理日期输入框键盘事件
 */
function handleDateInputKeydown(e) {
    // 按下空格键或回车键时打开日期选择器
    if (e.key === ' ' || e.key === 'Enter') {
        e.preventDefault();
        console.log('键盘触发日期选择器:', e.key);

        if (this.showPicker && typeof this.showPicker === 'function') {
            try {
                this.showPicker();
            } catch (e) {
                console.debug('键盘触发showPicker失败:', e);
                simulateCalendarIconClick(this);
            }
        } else {
            simulateCalendarIconClick(this);
        }
    }
}

/**
 * 模拟点击日历图标
 */
function simulateCalendarIconClick(input) {
    console.log('尝试模拟点击日历图标');

    // 方法1: 尝试触发原生日期选择器
    try {
        const event = new MouseEvent('click', {
            view: window,
            bubbles: true,
            cancelable: true,
            clientX: input.offsetLeft + input.offsetWidth - 20,
            clientY: input.offsetTop + input.offsetHeight / 2
        });
        input.dispatchEvent(event);
    } catch (e) {
        console.debug('模拟点击失败:', e);
    }

    // 方法2: 尝试设置焦点并触发特殊事件
    try {
        input.focus();
        input.click();

        // 触发input事件
        const inputEvent = new Event('input', { bubbles: true });
        input.dispatchEvent(inputEvent);

        // 触发change事件
        const changeEvent = new Event('change', { bubbles: true });
        input.dispatchEvent(changeEvent);
    } catch (e) {
        console.debug('备用方法失败:', e);
    }
}

/**
 * 设置DOM变化监听器，监听新添加的日期输入框
 */
function setupDateInputObserver() {
    // 创建一个观察器实例并传入回调函数
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            // 检查是否有新节点添加
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                mutation.addedNodes.forEach(function(node) {
                    // 如果添加的是元素节点
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        // 检查是否是日期输入框
                        if (node.tagName === 'INPUT' && (node.type === 'date' || node.type === 'datetime-local' ||
                            node.id.includes('date') || node.name.includes('date'))) {
                            console.log('检测到新的日期输入框:', node.id, node.name);
                            enhanceSingleDateInput(node);
                        }

                        // 检查子元素中是否有日期输入框
                        const dateInputs = node.querySelectorAll && node.querySelectorAll('input[type="date"], input[type="datetime-local"], input[id*="date"], input[name*="date"]');
                        if (dateInputs && dateInputs.length > 0) {
                            console.log('在新添加的元素中找到日期输入框:', dateInputs.length);
                            dateInputs.forEach(enhanceSingleDateInput);
                        }
                    }
                });
            }
        });
    });

    // 开始观察
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    console.log('DOM变化监听器已设置');
}

/**
 * 增强单个日期输入框
 */
function enhanceSingleDateInput(input) {
    // 避免重复增强
    if (input.dataset.dateEnhanced === 'true') {
        return;
    }

    console.log('增强单个日期输入框:', input.id, input.name, input.type);

    // 确保输入框类型正确
    if (input.type !== 'date' && input.type !== 'datetime-local') {
        if (input.name && (input.name.includes('date') || input.id.includes('date'))) {
            // 所有日期字段都使用date类型（只需要年月日）
            input.type = 'date';
            console.log(`已将输入框类型设置为: ${input.type}`);
        }
    }

    // 移除现有的事件监听器（如果有）
    input.removeEventListener('click', handleDateInputClick);
    input.removeEventListener('focus', handleDateInputFocus);
    input.removeEventListener('keydown', handleDateInputKeydown);

    // 添加新的事件监听器
    input.addEventListener('click', handleDateInputClick);
    input.addEventListener('focus', handleDateInputFocus);
    input.addEventListener('keydown', handleDateInputKeydown);

    // 改善视觉反馈
    input.style.cursor = 'pointer';
    input.style.transition = 'all 0.2s ease';

    // 添加hover效果
    input.addEventListener('mouseenter', function() {
        if (!this.disabled && !this.readOnly) {
            this.style.backgroundColor = '#f8f9fa';
            this.style.borderColor = '#86b7fe';
        }
    });

    input.addEventListener('mouseleave', function() {
        if (!this.disabled && !this.readOnly) {
            this.style.backgroundColor = '';
            this.style.borderColor = '';
        }
    });

    // 添加焦点效果
    input.addEventListener('focus', function() {
        this.style.backgroundColor = '#fff';
    });

    input.addEventListener('blur', function() {
        this.style.backgroundColor = '';
    });

    // 标记为已增强
    input.dataset.dateEnhanced = 'true';

    console.log('单个日期输入框增强完成:', input.id, input.name);
}
