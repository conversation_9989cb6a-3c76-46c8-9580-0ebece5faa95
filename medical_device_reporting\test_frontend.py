#!/usr/bin/env python
"""
前端界面测试脚本
Frontend Interface Test Script for Medical Device Reporting Platform
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from apps.users.models import UserProfile, Department
from apps.users.services import initialize_user_groups

def setup_test_data():
    """设置测试数据"""
    
    print("设置测试数据...")
    
    # 初始化用户组和权限
    initialize_user_groups()
    
    # 创建测试科室
    department, created = Department.objects.get_or_create(
        code='FRONTEND',
        defaults={
            'name': '前端测试科室',
            'is_active': True
        }
    )
    
    # 创建测试用户
    user, created = User.objects.get_or_create(
        username='frontend_user',
        defaults={
            'first_name': '前端',
            'last_name': '测试用户',
            'email': '<EMAIL>',
            'is_active': True
        }
    )
    
    user_profile, created = UserProfile.objects.get_or_create(
        account_number='7777',
        defaults={
            'user': user,
            'department': department,
            'role': 'staff',
            'is_active': True
        }
    )
    
    return user, user_profile, department

def test_frontend():
    """测试前端界面"""
    
    print("=== 前端界面测试 ===\n")
    
    # 设置测试数据
    user, user_profile, department = setup_test_data()
    
    # 创建测试客户端
    client = Client()
    
    # 1. 测试登录页面
    print("1. 测试登录页面...")
    try:
        response = client.get('/login/')
        if response.status_code == 200:
            content = response.content.decode('utf-8')
            
            # 检查关键元素
            checks = [
                ('登录表单', 'id="loginForm"' in content),
                ('账号输入框', 'id="account_number"' in content),
                ('登录按钮', 'id="loginBtn"' in content),
                ('CSS样式', 'login.css' in content),
                ('JavaScript脚本', 'login.js' in content),
                ('Bootstrap图标', 'bi-' in content),
            ]
            
            for check_name, result in checks:
                status = "✅" if result else "❌"
                print(f"   {status} {check_name}: {'通过' if result else '失败'}")
            
            print(f"   ✅ 登录页面加载成功 (状态码: {response.status_code})")
        else:
            print(f"   ❌ 登录页面加载失败 (状态码: {response.status_code})")
    except Exception as e:
        print(f"   ❌ 登录页面测试异常: {str(e)}")
    
    # 2. 测试登录功能
    print("\n2. 测试登录功能...")
    try:
        response = client.post('/login/', {
            'account_number': '7777'
        })
        
        if response.status_code == 302:  # 重定向表示登录成功
            print(f"   ✅ 登录功能正常 (重定向到: {response.url})")
        else:
            print(f"   ❌ 登录功能异常 (状态码: {response.status_code})")
    except Exception as e:
        print(f"   ❌ 登录功能测试异常: {str(e)}")
    
    # 3. 测试用户中心页面
    print("\n3. 测试用户中心页面...")
    try:
        # 先登录用户
        client.force_login(user)
        
        response = client.get('/dashboard/')
        if response.status_code == 200:
            content = response.content.decode('utf-8')
            
            # 检查关键元素
            checks = [
                ('欢迎横幅', 'welcome-banner' in content),
                ('统计卡片', 'stat-card' in content),
                ('功能菜单', 'feature-card' in content),
                ('个人信息', 'user-info' in content),
                ('CSS样式', 'dashboard.css' in content),
                ('JavaScript脚本', 'dashboard.js' in content),
                ('用户名显示', user.get_full_name() in content or user.username in content),
                ('账号显示', user_profile.account_number in content),
            ]
            
            for check_name, result in checks:
                status = "✅" if result else "❌"
                print(f"   {status} {check_name}: {'通过' if result else '失败'}")
            
            print(f"   ✅ 用户中心页面加载成功 (状态码: {response.status_code})")
        else:
            print(f"   ❌ 用户中心页面加载失败 (状态码: {response.status_code})")
    except Exception as e:
        print(f"   ❌ 用户中心页面测试异常: {str(e)}")
    
    # 4. 测试静态文件
    print("\n4. 测试静态文件...")
    try:
        static_files = [
            '/static/users/css/login.css',
            '/static/users/css/dashboard.css',
            '/static/users/js/login.js',
            '/static/users/js/dashboard.js',
            '/static/css/custom.css',
        ]
        
        for static_file in static_files:
            try:
                response = client.get(static_file)
                if response.status_code == 200:
                    print(f"   ✅ {static_file}: 可访问")
                else:
                    print(f"   ❌ {static_file}: 不可访问 (状态码: {response.status_code})")
            except Exception as e:
                print(f"   ❌ {static_file}: 访问异常 - {str(e)}")
    except Exception as e:
        print(f"   ❌ 静态文件测试异常: {str(e)}")
    
    # 5. 测试响应式设计
    print("\n5. 测试响应式设计...")
    try:
        # 模拟移动设备访问
        mobile_headers = {
            'HTTP_USER_AGENT': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
        }
        
        response = client.get('/login/', **mobile_headers)
        if response.status_code == 200:
            content = response.content.decode('utf-8')
            
            # 检查响应式元素
            responsive_checks = [
                ('视口元标签', 'viewport' in content),
                ('Bootstrap响应式', 'col-md-' in content or 'col-lg-' in content),
                ('移动端样式', '@media' in content or 'responsive' in content.lower()),
            ]
            
            for check_name, result in checks:
                status = "✅" if result else "❌"
                print(f"   {status} {check_name}: {'通过' if result else '失败'}")
            
            print(f"   ✅ 移动端访问正常")
        else:
            print(f"   ❌ 移动端访问异常 (状态码: {response.status_code})")
    except Exception as e:
        print(f"   ❌ 响应式设计测试异常: {str(e)}")
    
    # 6. 测试模板继承
    print("\n6. 测试模板继承...")
    try:
        response = client.get('/dashboard/')
        if response.status_code == 200:
            content = response.content.decode('utf-8')
            
            # 检查基础模板元素
            base_checks = [
                ('HTML文档类型', '<!DOCTYPE html>' in content),
                ('页面标题', '<title>' in content),
                ('Bootstrap CSS', 'bootstrap' in content.lower()),
                ('自定义CSS', 'custom.css' in content),
                ('导航栏', 'navbar' in content),
                ('页脚', 'footer' in content or 'card-footer' in content),
            ]
            
            for check_name, result in base_checks:
                status = "✅" if result else "❌"
                print(f"   {status} {check_name}: {'通过' if result else '失败'}")
            
            print(f"   ✅ 模板继承正常")
        else:
            print(f"   ❌ 模板继承测试失败")
    except Exception as e:
        print(f"   ❌ 模板继承测试异常: {str(e)}")
    
    print("\n=== 前端界面测试完成 ===")
    
    return True

if __name__ == '__main__':
    try:
        test_frontend()
        print("\n🎉 前端界面测试完成！")
    except Exception as e:
        print(f"\n❌ 前端测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
