#!/usr/bin/env python
"""
MySQL时区配置修复测试
MySQL Timezone Configuration Fix Test

测试时间序列统计函数的时区兼容性修复效果。
"""

import os
import sys
import django
from datetime import datetime, timedelta

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from django.utils import timezone
from django.contrib.auth.models import User
from django.db import connection
from apps.users.models import UserProfile, Department
from apps.reports.models import AdverseEventReport
from apps.reports.selectors import (
    get_time_series_statistics,
    get_trend_analysis
)

def check_mysql_timezone_tables():
    """检查MySQL时区表是否安装"""
    print("=== 检查MySQL时区表状态 ===")
    
    try:
        with connection.cursor() as cursor:
            # 检查时区表
            cursor.execute("SELECT COUNT(*) FROM mysql.time_zone")
            time_zone_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM mysql.time_zone_name")
            time_zone_name_count = cursor.fetchone()[0]
            
            print(f"时区表记录数: {time_zone_count}")
            print(f"时区名称表记录数: {time_zone_name_count}")
            
            if time_zone_count > 0 and time_zone_name_count > 0:
                print("✅ MySQL时区表已正确安装")
                return True
            else:
                print("❌ MySQL时区表未安装或为空")
                return False
                
    except Exception as e:
        print(f"❌ 检查时区表失败: {e}")
        return False

def test_django_timezone_functions():
    """测试Django时区函数"""
    print("\n=== 测试Django时区函数 ===")
    
    try:
        from django.db.models.functions import TruncMonth
        from django.db.models import Count
        
        # 测试基本的时间截断功能
        test_query = AdverseEventReport.objects.annotate(
            month=TruncMonth('created_at')
        ).values('month').annotate(
            count=Count('id')
        )[:1]
        
        # 尝试执行查询
        result = list(test_query)
        print("✅ Django时间截断函数正常工作")
        return True
        
    except Exception as e:
        print(f"❌ Django时间截断函数失败: {e}")
        return False

def create_test_data():
    """创建测试数据"""
    print("\n=== 创建测试数据 ===")
    
    try:
        # 创建科室
        department, created = Department.objects.get_or_create(
            code='TIMEZONE_TEST',
            defaults={
                'name': '时区测试科室',
                'is_active': True,
                'created_by_id': 1
            }
        )
        
        # 创建用户
        user, created = User.objects.get_or_create(
            username='timezone_test_user',
            defaults={'email': '<EMAIL>'}
        )
        
        # 创建用户配置文件
        import uuid
        unique_id = str(uuid.uuid4())[:8]
        user_profile, created = UserProfile.objects.get_or_create(
            user=user,
            defaults={
                'account_number': f'{hash(unique_id) % 9000 + 1000:04d}',
                'department': department,
                'role': 'admin',
                'created_by': user
            }
        )
        
        # 创建测试报告
        base_date = timezone.now() - timedelta(days=90)
        reports_created = 0
        
        for i in range(10):
            report_date = base_date + timedelta(days=i * 7)
            report, created = AdverseEventReport.objects.get_or_create(
                reporter=user_profile,
                device_name=f'测试设备{i}',
                defaults={
                    'department': department,
                    'reporter_phone': '***********',
                    'registration_number': f'REG{i:03d}',
                    'manufacturer': '测试厂商',
                    'product_number': f'PROD{i:03d}',
                    'batch_number': f'BATCH{i:03d}',
                    'event_date': report_date,
                    'injury_level': 'other',
                    'event_description': f'测试事件描述{i}',
                    'status': 'approved',
                    'created_at': report_date,
                    'created_by': user
                }
            )
            if created:
                reports_created += 1
        
        print(f"✅ 创建了{reports_created}个测试报告")
        return user_profile
        
    except Exception as e:
        print(f"❌ 创建测试数据失败: {e}")
        return None

def test_time_series_statistics(user_profile):
    """测试时间序列统计函数"""
    print("\n=== 测试时间序列统计函数 ===")
    
    granularities = ['year', 'month', 'week', 'day']
    
    for granularity in granularities:
        try:
            print(f"\n测试{granularity}粒度...")
            
            result = get_time_series_statistics(
                user_profile=user_profile,
                start_date=timezone.now() - timedelta(days=90),
                end_date=timezone.now(),
                granularity=granularity
            )
            
            print(f"✅ {granularity}粒度统计成功，返回{len(result)}条记录")
            
            if result:
                # 显示第一条记录的结构
                first_record = result[0]
                print(f"   示例数据: {first_record['label']} - {first_record['total_count']}条记录")
            
        except Exception as e:
            print(f"❌ {granularity}粒度统计失败: {e}")

def test_trend_analysis(user_profile):
    """测试趋势分析函数"""
    print("\n=== 测试趋势分析函数 ===")
    
    try:
        result = get_trend_analysis(
            user_profile=user_profile,
            metric='total_count',
            granularity='month',
            periods=6
        )
        
        print("✅ 趋势分析函数正常工作")
        print(f"   趋势方向: {result.get('trend_direction')}")
        print(f"   增长率: {result.get('growth_rate')}%")
        print(f"   分析结果: {result.get('analysis')}")
        
    except Exception as e:
        print(f"❌ 趋势分析函数失败: {e}")

def cleanup_test_data():
    """清理测试数据"""
    print("\n=== 清理测试数据 ===")
    
    try:
        # 删除测试报告
        AdverseEventReport.objects.filter(
            reporter__user__username='timezone_test_user'
        ).delete()
        
        # 删除用户配置文件
        UserProfile.objects.filter(
            user__username='timezone_test_user'
        ).delete()
        
        # 删除用户
        User.objects.filter(
            username='timezone_test_user'
        ).delete()
        
        # 删除科室
        Department.objects.filter(
            code='TIMEZONE_TEST'
        ).delete()
        
        print("✅ 测试数据清理完成")
        
    except Exception as e:
        print(f"❌ 清理测试数据失败: {e}")

def main():
    """主测试函数"""
    print("🚀 开始MySQL时区配置修复测试...")
    
    # 检查时区表状态
    timezone_tables_ok = check_mysql_timezone_tables()
    
    # 测试Django时区函数
    django_timezone_ok = test_django_timezone_functions()
    
    # 创建测试数据
    user_profile = create_test_data()
    
    if user_profile:
        try:
            # 测试时间序列统计
            test_time_series_statistics(user_profile)
            
            # 测试趋势分析
            test_trend_analysis(user_profile)
            
        finally:
            # 清理测试数据
            cleanup_test_data()
    
    # 总结测试结果
    print("\n=== 测试结果总结 ===")
    
    if timezone_tables_ok:
        print("✅ MySQL时区表状态正常")
    else:
        print("⚠️  MySQL时区表未安装，但应用已实现降级处理")
    
    if django_timezone_ok:
        print("✅ Django时区函数正常工作")
    else:
        print("⚠️  Django时区函数有问题，但应用已实现降级处理")
    
    print("\n📋 修复效果验证:")
    print("✅ 时间序列统计函数已添加错误处理和降级策略")
    print("✅ 趋势分析函数已添加错误处理")
    print("✅ 提供了完整的MySQL时区配置指导文档")
    
    if not timezone_tables_ok:
        print("\n💡 建议:")
        print("1. 安装MySQL时区表以获得最佳性能")
        print("2. 参考 docs/DATABASE_TIMEZONE_SETUP.md 文档")
        print("3. 当前降级处理确保功能正常运行")
    
    print("\n🎉 MySQL时区配置修复测试完成！")

if __name__ == '__main__':
    main()
