"""
用户管理信号处理器
User Management Signal Handlers for Medical Device Reporting Platform
"""

import logging
from django.db.models.signals import post_save, post_delete, pre_save
from django.dispatch import receiver
from django.contrib.auth.models import User, Group
from django.utils import timezone

from .models import UserProfile, Department

logger = logging.getLogger('apps.users')


@receiver(post_save, sender=User)
def user_post_save_handler(sender, instance, created, **kwargs):
    """
    用户保存后的信号处理器
    
    Args:
        sender: 发送信号的模型类
        instance: 保存的实例
        created: 是否为新创建的实例
        **kwargs: 其他参数
    """
    if created:
        logger.info(f'Django用户创建: {instance.username} (ID: {instance.id})')
    else:
        logger.info(f'Django用户更新: {instance.username} (ID: {instance.id})')


@receiver(post_save, sender=UserProfile)
def user_profile_post_save_handler(sender, instance, created, **kwargs):
    """
    用户配置文件保存后的信号处理器
    
    Args:
        sender: 发送信号的模型类
        instance: 保存的实例
        created: 是否为新创建的实例
        **kwargs: 其他参数
    """
    if created:
        logger.info(
            f'用户配置文件创建: {instance.account_number} - {instance.user.username}',
            extra={
                'account_number': instance.account_number,
                'username': instance.user.username,
                'role': instance.role,
                'department': instance.department.name if instance.department else None,
            }
        )
        
        # 自动分配用户组
        _assign_user_group_on_create(instance)
        
    else:
        logger.info(
            f'用户配置文件更新: {instance.account_number} - {instance.user.username}',
            extra={
                'account_number': instance.account_number,
                'username': instance.user.username,
                'role': instance.role,
                'is_active': instance.is_active,
            }
        )


@receiver(post_delete, sender=UserProfile)
def user_profile_post_delete_handler(sender, instance, **kwargs):
    """
    用户配置文件删除后的信号处理器
    
    Args:
        sender: 发送信号的模型类
        instance: 删除的实例
        **kwargs: 其他参数
    """
    logger.info(
        f'用户配置文件删除: {instance.account_number} - {instance.user.username}',
        extra={
            'account_number': instance.account_number,
            'username': instance.user.username,
        }
    )


@receiver(post_save, sender=Department)
def department_post_save_handler(sender, instance, created, **kwargs):
    """
    科室保存后的信号处理器
    
    Args:
        sender: 发送信号的模型类
        instance: 保存的实例
        created: 是否为新创建的实例
        **kwargs: 其他参数
    """
    if created:
        logger.info(
            f'科室创建: {instance.code} - {instance.name}',
            extra={
                'department_code': instance.code,
                'department_name': instance.name,
            }
        )
    else:
        logger.info(
            f'科室更新: {instance.code} - {instance.name}',
            extra={
                'department_code': instance.code,
                'department_name': instance.name,
                'is_active': instance.is_active,
            }
        )


def _assign_user_group_on_create(user_profile):
    """
    创建用户时自动分配用户组
    
    Args:
        user_profile: 用户配置文件实例
    """
    try:
        user = user_profile.user
        role = user_profile.role
        
        # 清除现有组
        user.groups.clear()
        
        # 根据角色分配组
        if role == 'admin':
            try:
                admin_group = Group.objects.get(name='管理员')
                user.groups.add(admin_group)
                logger.info(f'用户 {user_profile.account_number} 已分配到管理员组')
            except Group.DoesNotExist:
                logger.warning('管理员用户组不存在，请检查应用初始化')
        
        elif role == 'staff':
            try:
                staff_group = Group.objects.get(name='科室人员')
                user.groups.add(staff_group)
                logger.info(f'用户 {user_profile.account_number} 已分配到科室人员组')
            except Group.DoesNotExist:
                logger.warning('科室人员用户组不存在，请检查应用初始化')
                
    except Exception as e:
        logger.error(f'分配用户组失败: {str(e)}')


@receiver(pre_save, sender=UserProfile)
def user_profile_pre_save_handler(sender, instance, **kwargs):
    """
    用户配置文件保存前的信号处理器
    
    Args:
        sender: 发送信号的模型类
        instance: 即将保存的实例
        **kwargs: 其他参数
    """
    # 检查角色变更
    if instance.pk:
        try:
            old_instance = UserProfile.objects.get(pk=instance.pk)
            if old_instance.role != instance.role:
                logger.info(
                    f'用户角色变更: {instance.account_number} 从 {old_instance.get_role_display()} 变更为 {instance.get_role_display()}',
                    extra={
                        'account_number': instance.account_number,
                        'old_role': old_instance.role,
                        'new_role': instance.role,
                    }
                )
                
                # 重新分配用户组
                _reassign_user_group(instance, old_instance.role)
                
        except UserProfile.DoesNotExist:
            pass


def _reassign_user_group(user_profile, old_role):
    """
    重新分配用户组
    
    Args:
        user_profile: 用户配置文件实例
        old_role: 旧角色
    """
    try:
        user = user_profile.user
        new_role = user_profile.role
        
        # 清除现有组
        user.groups.clear()
        
        # 根据新角色分配组
        if new_role == 'admin':
            try:
                admin_group = Group.objects.get(name='管理员')
                user.groups.add(admin_group)
            except Group.DoesNotExist:
                logger.warning('管理员用户组不存在')
        
        elif new_role == 'staff':
            try:
                staff_group = Group.objects.get(name='科室人员')
                user.groups.add(staff_group)
            except Group.DoesNotExist:
                logger.warning('科室人员用户组不存在')
                
        logger.info(f'用户 {user_profile.account_number} 用户组已重新分配')
        
    except Exception as e:
        logger.error(f'重新分配用户组失败: {str(e)}')
