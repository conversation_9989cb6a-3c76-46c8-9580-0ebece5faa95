# Generated by Django 4.2.23 on 2025-06-20 14:50

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("users", "0002_remove_department_description_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="AdverseEventReport",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True,
                        help_text="记录创建的时间",
                        verbose_name="创建时间",
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True,
                        help_text="记录最后更新的时间",
                        verbose_name="更新时间",
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(
                        default=False,
                        help_text="软删除标记，True表示已删除",
                        verbose_name="是否删除",
                    ),
                ),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="记录删除的时间",
                        null=True,
                        verbose_name="删除时间",
                    ),
                ),
                (
                    "report_number",
                    models.CharField(
                        help_text="系统自动生成的唯一报告编号",
                        max_length=20,
                        unique=True,
                        verbose_name="报告编号",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "草稿"),
                            ("submitted", "已提交"),
                            ("under_review", "审核中"),
                            ("approved", "已批准"),
                            ("rejected", "已拒绝"),
                        ],
                        default="draft",
                        help_text="报告当前的处理状态",
                        max_length=20,
                        verbose_name="报告状态",
                    ),
                ),
                (
                    "reporter_phone",
                    models.CharField(
                        help_text="上报人的联系电话",
                        max_length=20,
                        verbose_name="上报人联系电话",
                    ),
                ),
                (
                    "patient_name",
                    models.CharField(
                        help_text="发生不良事件的患者姓名",
                        max_length=100,
                        verbose_name="患者姓名",
                    ),
                ),
                (
                    "patient_age",
                    models.PositiveIntegerField(
                        help_text="患者年龄（岁）", verbose_name="患者年龄"
                    ),
                ),
                (
                    "patient_gender",
                    models.CharField(
                        choices=[("male", "男"), ("female", "女"), ("unknown", "未知")],
                        help_text="患者的性别",
                        max_length=10,
                        verbose_name="患者性别",
                    ),
                ),
                (
                    "patient_contact",
                    models.CharField(
                        blank=True,
                        help_text="患者或其家属的联系方式",
                        max_length=200,
                        verbose_name="患者或其家属联系方式",
                    ),
                ),
                (
                    "device_malfunction",
                    models.TextField(
                        help_text="详细描述医疗器械的故障表现",
                        verbose_name="器械故障表现",
                    ),
                ),
                (
                    "event_date",
                    models.DateTimeField(
                        help_text="不良事件发生的具体日期和时间",
                        verbose_name="事件发生日期",
                    ),
                ),
                (
                    "injury_level",
                    models.CharField(
                        choices=[
                            ("death", "死亡"),
                            ("serious_injury", "严重伤害"),
                            ("other", "其他"),
                        ],
                        help_text="不良事件造成的伤害程度",
                        max_length=20,
                        verbose_name="伤害程度",
                    ),
                ),
                (
                    "injury_description",
                    models.TextField(
                        blank=True,
                        help_text="如造成伤害需填写具体的伤害表现",
                        verbose_name="伤害表现",
                    ),
                ),
                (
                    "event_description",
                    models.TextField(
                        help_text="详细陈述事件情况，包括器械使用时间、使用目的、使用依据、使用情况、出现的不良事件情况、对受害者影响、采取的治疗措施、器械联合使用情况",
                        verbose_name="事件陈述",
                    ),
                ),
                (
                    "initial_cause_analysis",
                    models.TextField(
                        blank=True,
                        help_text="对事件发生原因的初步分析",
                        verbose_name="事件发生初步原因分析",
                    ),
                ),
                (
                    "initial_treatment",
                    models.TextField(
                        blank=True,
                        help_text="对事件的初步处理措施和情况",
                        verbose_name="事件初步处理情况",
                    ),
                ),
                (
                    "device_name",
                    models.CharField(
                        help_text="发生不良事件的医疗器械名称",
                        max_length=200,
                        verbose_name="医疗器械名称",
                    ),
                ),
                (
                    "registration_number",
                    models.CharField(
                        help_text="医疗器械注册证号",
                        max_length=50,
                        verbose_name="注册证号",
                    ),
                ),
                (
                    "manufacturer",
                    models.CharField(
                        help_text="医疗器械生产企业名称",
                        max_length=200,
                        verbose_name="生产企业名称",
                    ),
                ),
                (
                    "specification",
                    models.CharField(
                        blank=True,
                        help_text="医疗器械规格",
                        max_length=100,
                        verbose_name="规格",
                    ),
                ),
                (
                    "model",
                    models.CharField(
                        blank=True,
                        help_text="医疗器械型号",
                        max_length=100,
                        verbose_name="型号",
                    ),
                ),
                (
                    "product_number",
                    models.CharField(
                        blank=True,
                        help_text="医疗器械产品编号",
                        max_length=100,
                        verbose_name="产品编号",
                    ),
                ),
                (
                    "batch_number",
                    models.CharField(
                        blank=True,
                        help_text="医疗器械产品批号",
                        max_length=100,
                        verbose_name="产品批号",
                    ),
                ),
                (
                    "production_date",
                    models.DateField(
                        blank=True,
                        help_text="医疗器械生产日期",
                        null=True,
                        verbose_name="生产日期",
                    ),
                ),
                (
                    "expiry_date",
                    models.DateField(
                        blank=True,
                        help_text="医疗器械有效期截止日期",
                        null=True,
                        verbose_name="有效期至",
                    ),
                ),
                (
                    "reviewed_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="报告审核的时间",
                        null=True,
                        verbose_name="审核时间",
                    ),
                ),
                (
                    "review_comments",
                    models.TextField(
                        blank=True,
                        help_text="审核人的审核意见和建议",
                        verbose_name="审核意见",
                    ),
                ),
                (
                    "submitted_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="报告提交的时间",
                        null=True,
                        verbose_name="提交时间",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="创建者",
                    ),
                ),
                (
                    "department",
                    models.ForeignKey(
                        help_text="上报人所属科室",
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="department_reports",
                        to="users.department",
                        verbose_name="上报科室",
                    ),
                ),
                (
                    "reporter",
                    models.ForeignKey(
                        help_text="上报此事件的用户",
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="reported_events",
                        to="users.userprofile",
                        verbose_name="上报人",
                    ),
                ),
                (
                    "reviewed_by",
                    models.ForeignKey(
                        blank=True,
                        help_text="审核此报告的用户",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="reviewed_reports",
                        to="users.userprofile",
                        verbose_name="审核人",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="最后修改者",
                    ),
                ),
            ],
            options={
                "verbose_name": "不良事件上报",
                "verbose_name_plural": "不良事件上报",
                "db_table": "reports_adverse_event",
                "ordering": ["-created_at", "-report_number"],
                "permissions": [
                    ("can_submit_report", "可以提交报告"),
                    ("can_review_report", "可以审核报告"),
                    ("can_view_all_reports", "可以查看所有报告"),
                    ("can_export_reports", "可以导出报告"),
                ],
                "indexes": [
                    models.Index(
                        fields=["report_number"], name="reports_adv_report__7b6731_idx"
                    ),
                    models.Index(
                        fields=["status"], name="reports_adv_status_7ac04c_idx"
                    ),
                    models.Index(
                        fields=["reporter"], name="reports_adv_reporte_1519fa_idx"
                    ),
                    models.Index(
                        fields=["department"], name="reports_adv_departm_c19c66_idx"
                    ),
                    models.Index(
                        fields=["event_date"], name="reports_adv_event_d_ff6cd6_idx"
                    ),
                    models.Index(
                        fields=["device_name"], name="reports_adv_device__687f4b_idx"
                    ),
                    models.Index(
                        fields=["registration_number"],
                        name="reports_adv_registr_4a6465_idx",
                    ),
                    models.Index(
                        fields=["created_at"], name="reports_adv_created_4fdac2_idx"
                    ),
                    models.Index(
                        fields=["submitted_at"], name="reports_adv_submitt_549947_idx"
                    ),
                ],
            },
        ),
    ]
