# 故障排除指南
# Troubleshooting Guide

## 概述

本文档提供医疗器械不良事件报告系统的常见问题故障排除指南，帮助快速诊断和解决系统问题。

## 数据库相关问题

### 1. MySQL时区配置问题

#### 问题症状
```
Database returned an invalid datetime value. 
Are time zone definitions for your database installed?
```

#### 诊断步骤
```sql
-- 检查时区表
SELECT COUNT(*) FROM mysql.time_zone;
SELECT COUNT(*) FROM mysql.time_zone_name;

-- 检查当前时区设置
SELECT @@global.time_zone, @@session.time_zone;
```

#### 解决方案
```bash
# Linux/macOS
mysql_tzinfo_to_sql /usr/share/zoneinfo | mysql -u root -p mysql

# Windows - 下载时区数据
# 访问: https://dev.mysql.com/downloads/timezones.html

# Docker环境
docker exec -it mysql_container bash
mysql_tzinfo_to_sql /usr/share/zoneinfo | mysql -u root -p mysql
```

#### 验证修复
```python
# 测试Django时区功能
python manage.py shell
>>> from django.db.models.functions import TruncMonth
>>> from apps.reports.models import AdverseEventReport
>>> AdverseEventReport.objects.annotate(month=TruncMonth('created_at')).count()
```

### 2. 数据库连接问题

#### 问题症状
```
django.db.utils.OperationalError: (2003, "Can't connect to MySQL server")
```

#### 诊断步骤
```bash
# 检查MySQL服务状态
sudo systemctl status mysql

# 检查端口
netstat -tlnp | grep 3306

# 测试连接
mysql -h localhost -u root -p
```

#### 解决方案
```bash
# 启动MySQL服务
sudo systemctl start mysql

# 检查配置文件
sudo nano /etc/mysql/mysql.conf.d/mysqld.cnf

# 确保bind-address设置正确
bind-address = 127.0.0.1
```

### 3. 数据库权限问题

#### 问题症状
```
django.db.utils.OperationalError: (1045, "Access denied for user")
```

#### 解决方案
```sql
-- 创建数据库用户
CREATE USER 'medical_device_user'@'localhost' IDENTIFIED BY 'your_password';

-- 授予权限
GRANT ALL PRIVILEGES ON medical_device_reports.* TO 'medical_device_user'@'localhost';
FLUSH PRIVILEGES;
```

## 缓存相关问题

### 1. Redis连接失败

#### 问题症状
```
redis.exceptions.ConnectionError: Error connecting to Redis
```

#### 诊断步骤
```bash
# 检查Redis服务
sudo systemctl status redis

# 测试连接
redis-cli ping

# 检查配置
redis-cli CONFIG GET "*"
```

#### 解决方案
```bash
# 启动Redis服务
sudo systemctl start redis

# 检查配置文件
sudo nano /etc/redis/redis.conf

# 确保绑定地址正确
bind 127.0.0.1
```

### 2. 缓存失效警告

#### 问题症状
```
WARNING 无法使用模式删除，跳过: stats:get_time_series_statistics*
```

#### 解决方案
已在系统中修复，使用SCAN命令替代KEYS命令。如果仍有问题：

```python
# 检查缓存配置
from django.core.cache import cache
from apps.common.cache_utils import StatisticsCacheManager

# 测试缓存失效
StatisticsCacheManager.invalidate_cache('test_function')
```

### 3. 缓存性能问题

#### 问题症状
- 页面加载缓慢
- 缓存命中率低

#### 诊断步骤
```python
# 获取缓存统计
from apps.common.cache_utils import StatisticsCacheManager
stats = StatisticsCacheManager.get_cache_stats()
print(stats)
```

#### 解决方案
```python
# 预热缓存
from apps.common.cache_utils import warm_up_cache
warm_up_cache()

# 调整缓存超时时间
CACHE_TIMEOUTS = {
    'report_statistics': 3600,  # 增加到1小时
}
```

## 应用程序问题

### 1. 静态文件加载失败

#### 问题症状
- CSS/JS文件404错误
- 页面样式丢失

#### 解决方案
```bash
# 收集静态文件
python manage.py collectstatic --noinput

# 检查静态文件配置
# settings.py
STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
```

### 2. 权限错误

#### 问题症状
```
PermissionError: [Errno 13] Permission denied
```

#### 解决方案
```bash
# 检查文件权限
ls -la /path/to/project

# 修改权限
sudo chown -R www-data:www-data /path/to/project
sudo chmod -R 755 /path/to/project

# 日志目录权限
sudo mkdir -p /var/log/medical_device_reports
sudo chown www-data:www-data /var/log/medical_device_reports
```

### 3. 内存不足

#### 问题症状
```
MemoryError: Unable to allocate memory
```

#### 解决方案
```bash
# 检查内存使用
free -h
top

# 优化Django设置
# settings.py
DATA_UPLOAD_MAX_MEMORY_SIZE = 10 * 1024 * 1024  # 10MB
FILE_UPLOAD_MAX_MEMORY_SIZE = 10 * 1024 * 1024  # 10MB
```

## 性能问题

### 1. 查询性能慢

#### 诊断步骤
```python
# 启用查询日志
# settings.py
LOGGING = {
    'loggers': {
        'django.db.backends': {
            'level': 'DEBUG',
            'handlers': ['console'],
        },
    },
}
```

#### 解决方案
```python
# 优化查询
# 使用select_related和prefetch_related
reports = AdverseEventReport.objects.select_related(
    'reporter', 'department'
).prefetch_related('attachments')

# 添加数据库索引
class AdverseEventReport(models.Model):
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    status = models.CharField(max_length=20, db_index=True)
```

### 2. 页面加载慢

#### 解决方案
```python
# 启用缓存中间件
MIDDLEWARE = [
    'django.middleware.cache.UpdateCacheMiddleware',
    # ... 其他中间件
    'django.middleware.cache.FetchFromCacheMiddleware',
]

# 页面缓存
CACHE_MIDDLEWARE_SECONDS = 300
```

## 部署问题

### 1. Nginx配置问题

#### 问题症状
- 502 Bad Gateway
- 静态文件无法访问

#### 解决方案
```nginx
# /etc/nginx/sites-available/medical_device_reports
server {
    listen 80;
    server_name your_domain.com;

    location /static/ {
        alias /path/to/project/staticfiles/;
        expires 30d;
    }

    location /media/ {
        alias /path/to/project/media/;
        expires 30d;
    }

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 2. Gunicorn配置问题

#### 解决方案
```bash
# gunicorn.conf.py
bind = "127.0.0.1:8000"
workers = 4
worker_class = "sync"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 30
keepalive = 2
```

### 3. 环境变量问题

#### 解决方案
```bash
# .env文件
DEBUG=False
SECRET_KEY=your_secret_key_here
DATABASE_URL=mysql://user:password@localhost/dbname
REDIS_URL=redis://localhost:6379/1
ALLOWED_HOSTS=your_domain.com,www.your_domain.com
```

## 日志分析

### 1. 查看应用日志

```bash
# Django日志
tail -f /var/log/medical_device_reports/django.log

# Nginx日志
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log

# MySQL日志
tail -f /var/log/mysql/error.log
```

### 2. 日志级别配置

```python
# settings.py
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': '/var/log/medical_device_reports/django.log',
            'formatter': 'verbose',
        },
    },
    'root': {
        'handlers': ['file'],
    },
    'loggers': {
        'apps': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}
```

## 监控和告警

### 1. 健康检查端点

```python
# apps/common/views.py
def health_check(request):
    """系统健康检查"""
    checks = {
        'database': check_database_connection(),
        'cache': check_cache_connection(),
        'disk_space': check_disk_space(),
    }
    
    all_healthy = all(checks.values())
    status_code = 200 if all_healthy else 503
    
    return JsonResponse(checks, status=status_code)
```

### 2. 性能监控

```python
# 监控脚本
def monitor_system():
    """系统监控"""
    import psutil
    
    # CPU使用率
    cpu_percent = psutil.cpu_percent(interval=1)
    
    # 内存使用率
    memory = psutil.virtual_memory()
    
    # 磁盘使用率
    disk = psutil.disk_usage('/')
    
    print(f"CPU: {cpu_percent}%")
    print(f"Memory: {memory.percent}%")
    print(f"Disk: {disk.percent}%")
```

## 快速诊断清单

### 系统启动检查
- [ ] MySQL服务运行正常
- [ ] Redis服务运行正常
- [ ] Django应用启动成功
- [ ] 静态文件可访问
- [ ] 数据库连接正常
- [ ] 缓存连接正常

### 性能检查
- [ ] 页面响应时间 < 2秒
- [ ] 数据库查询时间 < 500ms
- [ ] 缓存命中率 > 80%
- [ ] 内存使用率 < 80%
- [ ] CPU使用率 < 70%

### 安全检查
- [ ] DEBUG模式已关闭
- [ ] 数据库密码安全
- [ ] Redis密码设置
- [ ] HTTPS配置正确
- [ ] 防火墙规则正确

## 联系支持

如果问题仍然存在，请提供以下信息：

1. 错误信息的完整日志
2. 系统环境信息（操作系统、Python版本、Django版本）
3. 数据库和缓存配置
4. 重现问题的步骤
5. 最近的系统变更

技术支持邮箱：<EMAIL>
