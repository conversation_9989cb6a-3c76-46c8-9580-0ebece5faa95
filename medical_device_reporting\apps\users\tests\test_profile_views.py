"""
个人设置视图测试
Profile Views Tests
"""

from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
import json

from apps.users.models import UserProfile, Department


class ProfileViewTest(TestCase):
    """个人信息视图测试"""
    
    def setUp(self):
        """测试前准备"""
        self.client = Client()
        
        # 创建管理员用户
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            first_name='管理',
            last_name='员'
        )
        self.admin_profile = UserProfile.objects.create(
            user=self.admin_user,
            account_number='0001',
            role='admin',
            is_active=True
        )
        
        # 创建科室
        self.department = Department.objects.create(
            code='TEST',
            name='测试科室',
            created_by=self.admin_user
        )
        
        # 创建科室人员
        self.staff_user = User.objects.create_user(
            username='staff',
            email='<EMAIL>',
            first_name='科室',
            last_name='人员'
        )
        self.staff_profile = UserProfile.objects.create(
            user=self.staff_user,
            account_number='1001',
            department=self.department,
            role='staff',
            is_active=True
        )
    
    def test_profile_view_self_access(self):
        """测试用户查看自己的个人信息"""
        self.client.force_login(self.staff_user)
        
        response = self.client.get(reverse('users:profile'))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '个人信息')
        self.assertContains(response, self.staff_user.first_name)
        self.assertContains(response, self.staff_profile.account_number)
        self.assertTemplateUsed(response, 'users/profile.html')
    
    def test_profile_view_admin_access_other_user(self):
        """测试管理员查看其他用户信息"""
        self.client.force_login(self.admin_user)
        
        response = self.client.get(reverse('users:user_profile', args=[self.staff_profile.id]))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '用户信息')
        self.assertContains(response, self.staff_user.first_name)
        self.assertContains(response, self.staff_profile.account_number)
        self.assertTemplateUsed(response, 'users/profile.html')
    
    def test_profile_view_staff_access_other_user_denied(self):
        """测试科室人员查看其他用户信息被拒绝"""
        self.client.force_login(self.staff_user)
        
        response = self.client.get(reverse('users:user_profile', args=[self.admin_profile.id]))
        
        # 应该被拒绝访问
        self.assertIn(response.status_code, [302, 403])
    
    def test_profile_edit_view_get(self):
        """测试个人信息编辑页面GET请求"""
        self.client.force_login(self.staff_user)
        
        response = self.client.get(reverse('users:profile_edit'))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '编辑个人信息')
        self.assertContains(response, 'name="first_name"')
        self.assertContains(response, 'name="last_name"')
        self.assertContains(response, 'name="email"')
        self.assertTemplateUsed(response, 'users/profile_edit.html')
    
    def test_profile_edit_view_post_success(self):
        """测试个人信息编辑POST请求成功"""
        self.client.force_login(self.staff_user)
        
        data = {
            'first_name': '更新的名',
            'last_name': '更新的姓',
            'email': '<EMAIL>'
        }
        
        response = self.client.post(reverse('users:profile_edit'), data)
        
        # 应该重定向到个人信息页面
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('users:profile'))
        
        # 验证用户信息被更新
        self.staff_user.refresh_from_db()
        self.assertEqual(self.staff_user.first_name, '更新的名')
        self.assertEqual(self.staff_user.last_name, '更新的姓')
        self.assertEqual(self.staff_user.email, '<EMAIL>')
    
    def test_profile_edit_view_post_invalid_email(self):
        """测试个人信息编辑POST请求无效邮箱"""
        self.client.force_login(self.staff_user)
        
        data = {
            'first_name': '测试',
            'last_name': '用户',
            'email': 'invalid-email'  # 无效邮箱格式
        }
        
        response = self.client.post(reverse('users:profile_edit'), data)
        
        # 应该返回表单页面并显示错误
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '请输入有效的邮箱地址')
    
    def test_user_settings_view_get(self):
        """测试用户设置页面GET请求"""
        self.client.force_login(self.staff_user)
        
        response = self.client.get(reverse('users:user_settings'))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '个人设置')
        self.assertContains(response, '基本设置')
        self.assertContains(response, '偏好设置')
        self.assertContains(response, '安全设置')
        self.assertContains(response, '通知设置')
        self.assertTemplateUsed(response, 'users/user_settings.html')
    
    def test_user_settings_view_post_preferences(self):
        """测试用户设置POST请求偏好设置"""
        self.client.force_login(self.staff_user)
        
        data = {
            'type': 'preferences',
            'value': {
                'theme': 'dark',
                'language': 'zh-cn',
                'timezone': 'Asia/Shanghai',
                'page_size': '20'
            }
        }
        
        response = self.client.post(
            reverse('users:user_settings'),
            data=json.dumps(data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        response_data = json.loads(response.content)
        self.assertTrue(response_data.get('success'))
    
    def test_user_settings_view_post_notifications(self):
        """测试用户设置POST请求通知设置"""
        self.client.force_login(self.staff_user)
        
        data = {
            'type': 'notifications',
            'value': {
                'email_notifications': True,
                'email_reports': True,
                'email_system': False,
                'browser_notifications': True
            }
        }
        
        response = self.client.post(
            reverse('users:user_settings'),
            data=json.dumps(data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        response_data = json.loads(response.content)
        self.assertTrue(response_data.get('success'))


class ChangePasswordViewTest(TestCase):
    """修改密码视图测试"""
    
    def setUp(self):
        """测试前准备"""
        self.client = Client()
        
        # 创建测试用户
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='oldpassword123'
        )
        self.user_profile = UserProfile.objects.create(
            user=self.user,
            account_number='1001',
            role='staff',
            is_active=True
        )
    
    def test_change_password_view_success(self):
        """测试修改密码成功"""
        self.client.force_login(self.user)
        
        data = {
            'current_password': 'oldpassword123',
            'new_password': 'newpassword123',
            'confirm_password': 'newpassword123'
        }
        
        response = self.client.post(reverse('users:change_password'), data)
        
        self.assertEqual(response.status_code, 200)
        response_data = json.loads(response.content)
        self.assertTrue(response_data.get('success'))
        
        # 验证密码被更改
        self.user.refresh_from_db()
        self.assertTrue(self.user.check_password('newpassword123'))
    
    def test_change_password_view_wrong_current_password(self):
        """测试修改密码当前密码错误"""
        self.client.force_login(self.user)
        
        data = {
            'current_password': 'wrongpassword',
            'new_password': 'newpassword123',
            'confirm_password': 'newpassword123'
        }
        
        response = self.client.post(reverse('users:change_password'), data)
        
        self.assertEqual(response.status_code, 400)
        response_data = json.loads(response.content)
        self.assertIn('当前密码不正确', response_data.get('error', ''))
    
    def test_change_password_view_password_mismatch(self):
        """测试修改密码新密码不匹配"""
        self.client.force_login(self.user)
        
        data = {
            'current_password': 'oldpassword123',
            'new_password': 'newpassword123',
            'confirm_password': 'differentpassword123'
        }
        
        response = self.client.post(reverse('users:change_password'), data)
        
        self.assertEqual(response.status_code, 400)
        response_data = json.loads(response.content)
        self.assertIn('两次输入的新密码不一致', response_data.get('error', ''))
    
    def test_change_password_view_password_too_short(self):
        """测试修改密码新密码太短"""
        self.client.force_login(self.user)
        
        data = {
            'current_password': 'oldpassword123',
            'new_password': '123',  # 太短
            'confirm_password': '123'
        }
        
        response = self.client.post(reverse('users:change_password'), data)
        
        self.assertEqual(response.status_code, 400)
        response_data = json.loads(response.content)
        self.assertIn('密码长度至少6位', response_data.get('error', ''))


class ProfilePermissionTest(TestCase):
    """个人设置权限测试"""
    
    def setUp(self):
        """测试前准备"""
        self.client = Client()
        
        # 创建管理员用户
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>'
        )
        self.admin_profile = UserProfile.objects.create(
            user=self.admin_user,
            account_number='0001',
            role='admin',
            is_active=True
        )
        
        # 创建科室人员
        self.staff_user = User.objects.create_user(
            username='staff',
            email='<EMAIL>'
        )
        self.staff_profile = UserProfile.objects.create(
            user=self.staff_user,
            account_number='1001',
            role='staff',
            is_active=True
        )
        
        # 创建禁用用户
        self.inactive_user = User.objects.create_user(
            username='inactive',
            email='<EMAIL>'
        )
        self.inactive_profile = UserProfile.objects.create(
            user=self.inactive_user,
            account_number='1002',
            role='staff',
            is_active=False
        )
    
    def test_unauthenticated_access_redirects(self):
        """测试未认证用户访问个人设置页面重定向"""
        protected_urls = [
            reverse('users:profile'),
            reverse('users:profile_edit'),
            reverse('users:user_settings'),
        ]
        
        for url in protected_urls:
            response = self.client.get(url)
            self.assertEqual(response.status_code, 302)
            self.assertTrue(response.url.startswith('/login/'))
    
    def test_inactive_user_access_denied(self):
        """测试禁用用户访问被拒绝"""
        self.client.force_login(self.inactive_user)
        
        response = self.client.get(reverse('users:profile'))
        
        # 应该被拒绝访问
        self.assertIn(response.status_code, [302, 403])
    
    def test_all_users_can_access_own_profile(self):
        """测试所有用户都能访问自己的个人设置"""
        users = [self.admin_user, self.staff_user]
        
        for user in users:
            self.client.force_login(user)
            
            # 个人信息页面
            response = self.client.get(reverse('users:profile'))
            self.assertEqual(response.status_code, 200)
            
            # 个人信息编辑页面
            response = self.client.get(reverse('users:profile_edit'))
            self.assertEqual(response.status_code, 200)
            
            # 用户设置页面
            response = self.client.get(reverse('users:user_settings'))
            self.assertEqual(response.status_code, 200)
    
    def test_admin_can_view_other_users(self):
        """测试管理员可以查看其他用户信息"""
        self.client.force_login(self.admin_user)
        
        response = self.client.get(reverse('users:user_profile', args=[self.staff_profile.id]))
        self.assertEqual(response.status_code, 200)
    
    def test_staff_cannot_view_other_users(self):
        """测试科室人员不能查看其他用户信息"""
        self.client.force_login(self.staff_user)
        
        response = self.client.get(reverse('users:user_profile', args=[self.admin_profile.id]))
        self.assertIn(response.status_code, [302, 403])
