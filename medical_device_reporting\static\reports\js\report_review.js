/**
 * Report Review JavaScript for Medical Device Reporting Platform
 * 医疗器械不良事件上报平台报告审核脚本
 */

document.addEventListener('DOMContentLoaded', function() {
    // 初始化报告审核页面
    initializeReportReview();
    
    /**
     * 初始化报告审核页面
     */
    function initializeReportReview() {
        // 绑定事件监听器
        bindEventListeners();
        
        // 初始化表单验证
        initializeValidation();
        
        // 设置审核指南交互
        setupReviewGuide();
        
        console.log('报告审核页面初始化完成');
    }
    
    /**
     * 绑定事件监听器
     */
    function bindEventListeners() {
        // 审核表单提交事件
        const reviewForm = document.getElementById('reviewForm');
        if (reviewForm) {
            reviewForm.addEventListener('submit', handleReviewSubmit);
        }
        
        // 确认审核按钮
        const confirmSubmitBtn = document.getElementById('confirmSubmitBtn');
        if (confirmSubmitBtn) {
            confirmSubmitBtn.addEventListener('click', confirmReviewSubmit);
        }
        
        // 审核决定选择事件
        const actionSelect = document.getElementById('id_action');
        if (actionSelect) {
            actionSelect.addEventListener('change', handleActionChange);
        }
        
        // 审核意见输入事件
        const commentsTextarea = document.getElementById('id_comments');
        if (commentsTextarea) {
            commentsTextarea.addEventListener('input', handleCommentsInput);
        }
        
        // 键盘快捷键
        document.addEventListener('keydown', handleKeyboardShortcuts);
    }
    
    /**
     * 处理审核表单提交
     */
    function handleReviewSubmit(event) {
        event.preventDefault();
        
        // 验证表单
        if (!validateReviewForm()) {
            return false;
        }
        
        // 获取审核决定
        const actionSelect = document.getElementById('id_action');
        const action = actionSelect ? actionSelect.value : '';
        const actionText = actionSelect ? actionSelect.options[actionSelect.selectedIndex].text : '';
        
        // 显示确认对话框
        const modal = document.getElementById('confirmModal');
        const confirmMessage = document.getElementById('confirmMessage');
        
        if (modal && confirmMessage) {
            confirmMessage.textContent = `确定要${actionText}此报告吗？此操作不可撤销。`;
            
            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();
        }
    }
    
    /**
     * 确认审核提交
     */
    function confirmReviewSubmit() {
        const form = document.getElementById('reviewForm');
        if (!form) return;
        
        // 显示加载状态
        const confirmBtn = document.getElementById('confirmSubmitBtn');
        showLoading(confirmBtn);
        
        // 获取表单数据
        const formData = new FormData(form);
        
        // 提交审核
        fetch(form.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': getCsrfToken()
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            hideLoading(confirmBtn);
            
            // 关闭确认对话框
            const modal = document.getElementById('confirmModal');
            if (modal) {
                const bootstrapModal = bootstrap.Modal.getInstance(modal);
                if (bootstrapModal) {
                    bootstrapModal.hide();
                }
            }
            
            if (data.success) {
                showSuccessMessage('审核完成');
                
                // 延迟跳转到报告详情页面
                setTimeout(() => {
                    window.location.href = data.redirect_url || '/reports/pending-review/';
                }, 1500);
            } else {
                showErrorMessage(data.error || '审核失败');
            }
        })
        .catch(error => {
            hideLoading(confirmBtn);
            console.error('审核失败:', error);
            showErrorMessage('审核失败，请稍后重试');
        });
    }
    
    /**
     * 处理审核决定变化
     */
    function handleActionChange(event) {
        const action = event.target.value;
        const commentsTextarea = document.getElementById('id_comments');
        const commentsLabel = document.querySelector('label[for="id_comments"]');
        
        if (!commentsTextarea || !commentsLabel) return;
        
        if (action === 'reject') {
            // 拒绝时意见为必填
            commentsTextarea.required = true;
            commentsTextarea.placeholder = '请详细说明拒绝原因和需要修改的内容...';
            commentsLabel.innerHTML = '审核意见 <span class="text-danger">*</span>';
            
            // 添加拒绝原因模板
            addRejectionTemplates();
        } else if (action === 'approve') {
            // 批准时意见为可选
            commentsTextarea.required = false;
            commentsTextarea.placeholder = '请填写审核意见和建议（可选）...';
            commentsLabel.textContent = '审核意见';
            
            // 移除拒绝原因模板
            removeRejectionTemplates();
        } else if (action === 'start_review') {
            // 开始审核时意见为可选
            commentsTextarea.required = false;
            commentsTextarea.placeholder = '请填写审核意见...';
            commentsLabel.textContent = '审核意见';
            
            // 移除拒绝原因模板
            removeRejectionTemplates();
        }
        
        // 更新字段验证状态
        validateField(commentsTextarea);
    }
    
    /**
     * 处理审核意见输入
     */
    function handleCommentsInput(event) {
        const textarea = event.target;
        const maxLength = 1000;
        const currentLength = textarea.value.length;
        
        // 显示字符计数
        let counter = textarea.parentElement.querySelector('.char-counter');
        if (!counter) {
            counter = document.createElement('div');
            counter.className = 'char-counter text-muted small mt-1';
            textarea.parentElement.appendChild(counter);
        }
        
        counter.textContent = `${currentLength}/${maxLength} 字符`;
        
        // 超出长度时显示警告
        if (currentLength > maxLength) {
            counter.classList.add('text-danger');
            textarea.value = textarea.value.substring(0, maxLength);
        } else {
            counter.classList.remove('text-danger');
        }
    }
    
    /**
     * 处理键盘快捷键
     */
    function handleKeyboardShortcuts(event) {
        // Ctrl + Enter: 提交审核
        if (event.ctrlKey && event.key === 'Enter') {
            event.preventDefault();
            const submitButton = document.querySelector('#reviewForm button[type="submit"]');
            if (submitButton) {
                submitButton.click();
            }
        }
        
        // Escape: 取消/返回
        if (event.key === 'Escape') {
            const modal = document.querySelector('.modal.show');
            if (modal) {
                const bootstrapModal = bootstrap.Modal.getInstance(modal);
                if (bootstrapModal) {
                    bootstrapModal.hide();
                }
            } else {
                // 返回报告详情页面
                const reportId = getReportIdFromUrl();
                if (reportId) {
                    window.location.href = `/reports/${reportId}/`;
                }
            }
        }
    }
    
    /**
     * 验证审核表单
     */
    function validateReviewForm() {
        let isValid = true;
        
        // 验证审核决定
        const actionSelect = document.getElementById('id_action');
        if (!actionSelect || !actionSelect.value) {
            updateFieldValidation(actionSelect, false, '请选择审核决定');
            isValid = false;
        } else {
            updateFieldValidation(actionSelect, true);
        }
        
        // 验证审核意见
        const commentsTextarea = document.getElementById('id_comments');
        if (commentsTextarea) {
            const isCommentsValid = validateField(commentsTextarea);
            if (!isCommentsValid) {
                isValid = false;
            }
        }
        
        // 添加Bootstrap验证类
        const form = document.getElementById('reviewForm');
        if (form) {
            form.classList.add('was-validated');
        }
        
        return isValid;
    }
    
    /**
     * 验证单个字段
     */
    function validateField(field) {
        const value = field.value.trim();
        const isRequired = field.hasAttribute('required');
        
        if (isRequired && !value) {
            updateFieldValidation(field, false, '此字段为必填项');
            return false;
        }
        
        updateFieldValidation(field, true);
        return true;
    }
    
    /**
     * 更新字段验证状态
     */
    function updateFieldValidation(input, isValid, message = '') {
        if (!input) return;
        
        if (isValid) {
            input.classList.remove('is-invalid');
            input.classList.add('is-valid');
        } else {
            input.classList.remove('is-valid');
            input.classList.add('is-invalid');
            
            // 更新错误消息
            let feedback = input.parentElement.querySelector('.invalid-feedback');
            if (!feedback) {
                feedback = document.createElement('div');
                feedback.className = 'invalid-feedback';
                input.parentElement.appendChild(feedback);
            }
            feedback.textContent = message;
        }
    }
    
    /**
     * 添加拒绝原因模板
     */
    function addRejectionTemplates() {
        const commentsTextarea = document.getElementById('id_comments');
        if (!commentsTextarea) return;
        
        // 检查是否已存在模板
        if (commentsTextarea.parentElement.querySelector('.rejection-templates')) {
            return;
        }
        
        const templates = document.createElement('div');
        templates.className = 'rejection-templates mt-2';
        templates.innerHTML = `
            <small class="text-muted">常用拒绝原因：</small>
            <div class="btn-group-vertical btn-group-sm mt-1" role="group">
                <button type="button" class="btn btn-outline-secondary template-btn" data-template="事件描述不够详细，请补充具体情况">事件描述不详细</button>
                <button type="button" class="btn btn-outline-secondary template-btn" data-template="器械信息不完整，请补充注册证号、批号等信息">器械信息不完整</button>
                <button type="button" class="btn btn-outline-secondary template-btn" data-template="伤害程度评估不准确，请重新评估">伤害程度评估有误</button>
                <button type="button" class="btn btn-outline-secondary template-btn" data-template="缺少初步原因分析，请补充分析内容">缺少原因分析</button>
            </div>
        `;
        
        commentsTextarea.parentElement.appendChild(templates);
        
        // 绑定模板按钮事件
        const templateButtons = templates.querySelectorAll('.template-btn');
        templateButtons.forEach(button => {
            button.addEventListener('click', function() {
                const template = this.getAttribute('data-template');
                if (commentsTextarea.value) {
                    commentsTextarea.value += '\n' + template;
                } else {
                    commentsTextarea.value = template;
                }
                commentsTextarea.focus();
            });
        });
    }
    
    /**
     * 移除拒绝原因模板
     */
    function removeRejectionTemplates() {
        const templates = document.querySelector('.rejection-templates');
        if (templates) {
            templates.remove();
        }
    }
    
    /**
     * 初始化表单验证
     */
    function initializeValidation() {
        // 实时验证
        const inputs = document.querySelectorAll('#reviewForm input, #reviewForm select, #reviewForm textarea');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                validateField(this);
            });
        });
        
        // 触发初始的审核决定变化事件
        const actionSelect = document.getElementById('id_action');
        if (actionSelect && actionSelect.value) {
            handleActionChange({ target: actionSelect });
        }
    }
    
    /**
     * 设置审核指南交互
     */
    function setupReviewGuide() {
        const guideItems = document.querySelectorAll('.guide-item');
        
        guideItems.forEach(item => {
            item.addEventListener('click', function() {
                // 高亮点击的指南项
                guideItems.forEach(i => i.classList.remove('active'));
                this.classList.add('active');
                
                // 可以添加更多交互效果
                this.style.transform = 'scale(1.02)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 200);
            });
        });
    }
    
    /**
     * 从URL获取报告ID
     */
    function getReportIdFromUrl() {
        const pathParts = window.location.pathname.split('/');
        const reportsIndex = pathParts.indexOf('reports');
        if (reportsIndex !== -1 && reportsIndex + 1 < pathParts.length) {
            return pathParts[reportsIndex + 1];
        }
        return null;
    }
    
    /**
     * 显示加载状态
     */
    function showLoading(button) {
        if (button) {
            button.disabled = true;
            const originalText = button.innerHTML;
            button.setAttribute('data-original-text', originalText);
            button.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status"></span>处理中...';
        }
    }
    
    /**
     * 隐藏加载状态
     */
    function hideLoading(button) {
        if (button) {
            button.disabled = false;
            const originalText = button.getAttribute('data-original-text');
            if (originalText) {
                button.innerHTML = originalText;
                button.removeAttribute('data-original-text');
            }
        }
    }
    
    /**
     * 获取CSRF Token
     */
    function getCsrfToken() {
        const token = document.querySelector('[name=csrfmiddlewaretoken]');
        return token ? token.value : '';
    }
    
    /**
     * 显示成功消息
     */
    function showSuccessMessage(message) {
        if (typeof showMessage === 'function') {
            showMessage(message, 'success', 3000);
        }
    }
    
    /**
     * 显示错误消息
     */
    function showErrorMessage(message) {
        if (typeof showMessage === 'function') {
            showMessage(message, 'danger', 5000);
        }
    }
});
