"""
URL configuration for Medical Device Reporting Platform.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.http import HttpResponse

# 处理 styles/index.less 的404问题
def handle_styles_index_less(request):
    """处理 /styles/index.less 请求，返回空CSS响应"""
    return HttpResponse('/* Empty CSS file */', content_type='text/css')

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', include('apps.users.urls')),  # 包含用户应用的所有URL
    path('reports/', include('apps.reports.urls')),  # 包含报告管理应用的所有URL
    path('styles/index.less', handle_styles_index_less, name='styles_index_less'),  # 处理404问题
]

# Serve media and static files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.BASE_DIR / 'static')

    # Django Debug Toolbar
    import debug_toolbar
    urlpatterns = [
        path('__debug__/', include(debug_toolbar.urls)),
    ] + urlpatterns
