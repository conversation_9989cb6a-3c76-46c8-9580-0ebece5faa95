<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>医疗器械不良事件报告 - {{ report.report_number }}</title>
    <style>
        /* A4打印样式 */
        @page {
            size: A4;
            margin: 15mm;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: "Microsoft YaHei", "SimSun", Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            background: white;
        }
        
        .print-container {
            width: 100%;
            max-width: 210mm;
            margin: 0 auto;
            padding: 0;
        }
        
        /* 标题样式 */
        .report-header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
        }
        
        .report-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .report-number {
            font-size: 14px;
            color: #666;
        }
        
        /* 信息区块样式 */
        .info-section {
            margin-bottom: 15px;
            page-break-inside: avoid;
        }
        
        .section-title {
            font-size: 14px;
            font-weight: bold;
            background-color: #f5f5f5;
            padding: 5px 8px;
            border-left: 4px solid #007bff;
            margin-bottom: 8px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 8px;
        }
        
        .info-grid.single-column {
            grid-template-columns: 1fr;
        }
        
        .info-grid.three-column {
            grid-template-columns: 1fr 1fr 1fr;
        }
        
        .info-item {
            display: flex;
            margin-bottom: 5px;
        }
        
        .info-label {
            font-weight: bold;
            min-width: 80px;
            margin-right: 8px;
            color: #555;
        }
        
        .info-value {
            flex: 1;
            word-wrap: break-word;
        }
        
        /* 长文本区域 */
        .text-area {
            border: 1px solid #ddd;
            padding: 8px;
            background-color: #fafafa;
            min-height: 40px;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        
        /* 表格样式 */
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
        }
        
        .info-table td {
            border: 1px solid #ddd;
            padding: 5px 8px;
            vertical-align: top;
        }
        
        .info-table .label-cell {
            background-color: #f8f9fa;
            font-weight: bold;
            width: 25%;
        }
        
        /* 打印时隐藏不需要的元素 */
        @media print {
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            
            .no-print {
                display: none !important;
            }
            
            .page-break {
                page-break-before: always;
            }
        }
        
        /* 屏幕预览样式 */
        @media screen {
            body {
                background-color: #f5f5f5;
                padding: 20px;
            }
            
            .print-container {
                background: white;
                box-shadow: 0 0 10px rgba(0,0,0,0.1);
                padding: 20px;
                margin: 20px auto;
            }
            
            .print-actions {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1000;
            }
            
            .btn {
                display: inline-block;
                padding: 8px 16px;
                margin: 0 5px;
                background-color: #007bff;
                color: white;
                text-decoration: none;
                border-radius: 4px;
                border: none;
                cursor: pointer;
                font-size: 14px;
            }
            
            .btn:hover {
                background-color: #0056b3;
            }
            
            .btn-secondary {
                background-color: #6c757d;
            }
            
            .btn-secondary:hover {
                background-color: #545b62;
            }
        }
    </style>
</head>
<body>
    <!-- 打印操作按钮 (仅屏幕显示) -->
    <div class="print-actions no-print">
        <button class="btn" onclick="window.print()">打印</button>
        <a href="{% url 'reports:report_detail' report_id=report.id %}" class="btn btn-secondary">返回</a>
    </div>

    <div class="print-container">
        <!-- 报告标题 -->
        <div class="report-header">
            <div class="report-title">医疗器械不良事件报告</div>
            <div class="report-number">报告编号：{{ report.report_number }}</div>
        </div>

        <!-- 上报人信息 -->
        <div class="info-section">
            <div class="section-title">上报人信息</div>
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">姓名：</span>
                    <span class="info-value">{{ report.reporter.display_name }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">科室：</span>
                    <span class="info-value">{{ report.department.name|default:"未指定" }}</span>
                </div>
            </div>
            <div class="info-grid single-column">
                <div class="info-item">
                    <span class="info-label">联系电话：</span>
                    <span class="info-value">{{ report.reporter_phone|default:"未填写" }}</span>
                </div>
            </div>
        </div>

        <!-- 患者信息 -->
        <div class="info-section">
            <div class="section-title">患者信息</div>
            <div class="info-grid three-column">
                <div class="info-item">
                    <span class="info-label">患者姓名：</span>
                    <span class="info-value">{{ report.patient_name|default:"未填写" }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">患者年龄：</span>
                    <span class="info-value">{% if report.patient_age %}{{ report.patient_age }}岁{% else %}未填写{% endif %}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">患者性别：</span>
                    <span class="info-value">{{ report.get_patient_gender_display|default:"未填写" }}</span>
                </div>
            </div>
        </div>

        <!-- 不良事件情况 -->
        <div class="info-section">
            <div class="section-title">不良事件情况</div>
            
            <!-- 器械故障表现 -->
            <div class="info-item" style="margin-bottom: 10px;">
                <span class="info-label">器械故障表现：</span>
            </div>
            <div class="text-area">{{ report.device_malfunction|default:"未填写" }}</div>
            
            <div class="info-grid" style="margin-top: 10px;">
                <div class="info-item">
                    <span class="info-label">事件发生日期：</span>
                    <span class="info-value">{{ report.event_date|date:"Y年m月d日" }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">伤害程度：</span>
                    <span class="info-value">{{ report.get_injury_level_display }}</span>
                </div>
            </div>
            
            <!-- 伤害表现 -->
            {% if report.injury_description %}
            <div class="info-item" style="margin-top: 10px; margin-bottom: 5px;">
                <span class="info-label">伤害表现：</span>
            </div>
            <div class="text-area">{{ report.injury_description }}</div>
            {% endif %}
            
            <!-- 事件陈述 -->
            <div class="info-item" style="margin-top: 10px; margin-bottom: 5px;">
                <span class="info-label">事件陈述：</span>
            </div>
            <div class="text-area">{{ report.event_description }}</div>
            
            <!-- 事件发生初步原因分析 -->
            {% if report.initial_cause_analysis %}
            <div class="info-item" style="margin-top: 10px; margin-bottom: 5px;">
                <span class="info-label">事件发生初步原因分析：</span>
            </div>
            <div class="text-area">{{ report.initial_cause_analysis }}</div>
            {% endif %}
            
            <!-- 事件初步处理情况 -->
            {% if report.initial_treatment %}
            <div class="info-item" style="margin-top: 10px; margin-bottom: 5px;">
                <span class="info-label">事件初步处理情况：</span>
            </div>
            <div class="text-area">{{ report.initial_treatment }}</div>
            {% endif %}
        </div>

        <!-- 医疗器械信息 -->
        <div class="info-section">
            <div class="section-title">医疗器械信息</div>
            
            <div class="info-grid three-column">
                <div class="info-item">
                    <span class="info-label">医疗器械名称：</span>
                    <span class="info-value">{{ report.device_name }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">注册证号：</span>
                    <span class="info-value">{{ report.registration_number }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">生产企业名称：</span>
                    <span class="info-value">{{ report.manufacturer }}</span>
                </div>
            </div>
            
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">规格：</span>
                    <span class="info-value">{{ report.specification|default:"未填写" }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">型号：</span>
                    <span class="info-value">{{ report.model|default:"未填写" }}</span>
                </div>
            </div>
            
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">产品编号：</span>
                    <span class="info-value">{{ report.product_number|default:"未填写" }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">产品批号：</span>
                    <span class="info-value">{{ report.batch_number|default:"未填写" }}</span>
                </div>
            </div>
            
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">生产日期：</span>
                    <span class="info-value">{{ report.production_date|date:"Y年m月d日"|default:"未填写" }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">有效期至：</span>
                    <span class="info-value">{{ report.expiry_date|date:"Y年m月d日"|default:"未填写" }}</span>
                </div>
            </div>
        </div>
        
        <!-- 页脚信息 -->
        <div style="margin-top: 30px; text-align: center; font-size: 10px; color: #666; border-top: 1px solid #ddd; padding-top: 10px;">
            <p>医疗器械不良事件上报平台</p>
            <p>打印时间：{{ "now"|date:"Y年m月d日 H:i" }}</p>
        </div>
    </div>

    <script>
        // 自动聚焦到打印按钮，方便键盘操作
        document.addEventListener('DOMContentLoaded', function() {
            // 如果是通过打印参数访问，自动打印
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('auto_print') === 'true') {
                setTimeout(() => {
                    window.print();
                }, 500);
            }
        });
    </script>
</body>
</html>
