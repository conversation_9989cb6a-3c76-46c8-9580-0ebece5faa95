"""
Common应用配置
Common app configuration for Medical Device Reporting Platform
"""

from django.apps import AppConfig


class CommonConfig(AppConfig):
    """
    Common应用配置类
    
    配置通用组件应用的基本信息和初始化逻辑
    """
    
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.common'
    verbose_name = '通用组件'
    verbose_name_plural = '通用组件'
    
    def ready(self):
        """
        应用准备就绪时的初始化逻辑
        
        在Django启动时执行一次，用于注册信号处理器、
        初始化缓存、注册自定义检查等
        """
        
        # 导入信号处理器
        try:
            from . import signals
        except ImportError:
            pass
        
        # 注册自定义检查
        self._register_checks()
        
        # 初始化日志
        self._setup_logging()
    
    def _register_checks(self):
        """
        注册自定义系统检查
        """
        from django.core.checks import register, Tags
        from .checks import check_common_settings
        
        register(check_common_settings, Tags.compatibility)
    
    def _setup_logging(self):
        """
        设置应用特定的日志配置
        """
        import logging
        
        logger = logging.getLogger('apps.common')
        logger.info('Common应用已初始化')
