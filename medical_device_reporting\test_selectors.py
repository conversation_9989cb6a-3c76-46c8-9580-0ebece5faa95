#!/usr/bin/env python
"""
数据查询选择器测试脚本
Data Query Selectors Test Script for Medical Device Reporting Platform
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from apps.users.models import UserProfile, Department
from apps.users.selectors import (
    user_list, user_list_paginated, user_list_with_counts, user_list_by_role_and_department,
    user_list_recent_activity, user_list_by_creation_date, user_advanced_search,
    user_list_optimized, user_list_minimal, user_get_by_id, user_get_by_account_number,
    user_exists_by_account_number, user_search_suggestions,
    department_list, department_list_with_user_counts, department_list_empty,
    department_list_with_recent_activity, department_get_by_id, department_get_by_code,
    department_exists_by_code, department_exists_by_name,
    get_user_choices_for_select, get_department_choices_for_select,
    get_cached_user_statistics, get_cached_department_list, invalidate_user_cache,
    user_statistics
)

def test_selectors():
    """测试选择器功能"""
    
    print("=== 数据查询选择器测试 ===\n")
    
    # 1. 测试基础用户查询
    print("1. 测试基础用户查询...")
    
    try:
        users = user_list()
        print(f"   ✅ 用户列表查询成功: 共 {users.count()} 个用户")
        
        # 测试筛选
        active_users = user_list(is_active=True)
        print(f"   ✅ 活跃用户查询: 共 {active_users.count()} 个")
        
        admin_users = user_list(role='admin')
        print(f"   ✅ 管理员用户查询: 共 {admin_users.count()} 个")
        
    except Exception as e:
        print(f"   ❌ 基础用户查询失败: {str(e)}")
    
    # 2. 测试分页查询
    print("\n2. 测试分页查询...")
    
    try:
        paginated_result = user_list_paginated(page=1, page_size=5)
        print(f"   ✅ 分页查询成功:")
        print(f"      总数: {paginated_result['total_count']}")
        print(f"      总页数: {paginated_result['total_pages']}")
        print(f"      当前页: {paginated_result['page']}")
        print(f"      当前页用户数: {len(paginated_result['users'])}")
        
    except Exception as e:
        print(f"   ❌ 分页查询失败: {str(e)}")
    
    # 3. 测试统计查询
    print("\n3. 测试统计查询...")
    
    try:
        stats_result = user_list_with_counts()
        print(f"   ✅ 统计查询成功:")
        print(f"      统计信息: {stats_result['stats']}")
        
        role_dept_stats = user_list_by_role_and_department()
        print(f"   ✅ 角色科室统计成功:")
        print(f"      科室统计数: {role_dept_stats['department_stats'].count()}")
        print(f"      角色统计: {list(role_dept_stats['role_stats'])}")
        print(f"      无科室用户: {role_dept_stats['no_department_count']}")
        
    except Exception as e:
        print(f"   ❌ 统计查询失败: {str(e)}")
    
    # 4. 测试高级搜索
    print("\n4. 测试高级搜索...")
    
    try:
        # 搜索建议
        suggestions = user_search_suggestions('test', limit=5)
        print(f"   ✅ 搜索建议成功: 共 {len(suggestions)} 个建议")
        
        # 高级搜索
        advanced_results = user_advanced_search(
            roles=['admin', 'staff'],
            is_active=True
        )
        print(f"   ✅ 高级搜索成功: 共 {advanced_results.count()} 个结果")
        
    except Exception as e:
        print(f"   ❌ 高级搜索失败: {str(e)}")
    
    # 5. 测试科室查询
    print("\n5. 测试科室查询...")
    
    try:
        departments = department_list()
        print(f"   ✅ 科室列表查询成功: 共 {departments.count()} 个科室")
        
        dept_with_counts = department_list_with_user_counts()
        print(f"   ✅ 科室用户统计查询成功: 共 {dept_with_counts.count()} 个科室")
        
        empty_depts = department_list_empty()
        print(f"   ✅ 空科室查询成功: 共 {empty_depts.count()} 个空科室")
        
    except Exception as e:
        print(f"   ❌ 科室查询失败: {str(e)}")
    
    # 6. 测试性能优化查询
    print("\n6. 测试性能优化查询...")
    
    try:
        optimized_users = user_list_optimized(
            select_related_fields=['user', 'department'],
            prefetch_related_fields=['user__groups']
        )
        print(f"   ✅ 优化查询成功: 共 {optimized_users.count()} 个用户")
        
        minimal_users = user_list_minimal()
        print(f"   ✅ 最小化查询成功: 共 {minimal_users.count()} 个用户")
        
    except Exception as e:
        print(f"   ❌ 性能优化查询失败: {str(e)}")
    
    # 7. 测试选择列表
    print("\n7. 测试选择列表...")
    
    try:
        user_choices = get_user_choices_for_select()
        print(f"   ✅ 用户选择列表成功: 共 {len(user_choices)} 个选项")
        
        dept_choices = get_department_choices_for_select()
        print(f"   ✅ 科室选择列表成功: 共 {len(dept_choices)} 个选项")
        
    except Exception as e:
        print(f"   ❌ 选择列表失败: {str(e)}")
    
    # 8. 测试缓存功能
    print("\n8. 测试缓存功能...")
    
    try:
        # 清除缓存
        invalidate_user_cache()
        print("   ✅ 缓存清除成功")
        
        # 获取缓存统计
        cached_stats = get_cached_user_statistics()
        print(f"   ✅ 缓存统计查询成功: {cached_stats}")
        
        # 获取缓存科室列表
        cached_depts = get_cached_department_list()
        print(f"   ✅ 缓存科室列表成功: 共 {len(cached_depts)} 个科室")
        
    except Exception as e:
        print(f"   ❌ 缓存功能失败: {str(e)}")
    
    # 9. 测试单个查询
    print("\n9. 测试单个查询...")
    
    try:
        # 获取第一个用户进行测试
        first_user = UserProfile.objects.filter(is_deleted=False).first()
        if first_user:
            # 按ID查询
            user_by_id = user_get_by_id(first_user.id)
            print(f"   ✅ 按ID查询用户成功: {user_by_id}")
            
            # 按账号查询
            user_by_account = user_get_by_account_number(first_user.account_number)
            print(f"   ✅ 按账号查询用户成功: {user_by_account}")
            
            # 检查账号存在性
            exists = user_exists_by_account_number(first_user.account_number)
            print(f"   ✅ 账号存在性检查: {exists}")
        
        # 获取第一个科室进行测试
        first_dept = Department.objects.filter(is_deleted=False).first()
        if first_dept:
            # 按ID查询科室
            dept_by_id = department_get_by_id(first_dept.id)
            print(f"   ✅ 按ID查询科室成功: {dept_by_id}")
            
            # 按代码查询科室
            dept_by_code = department_get_by_code(first_dept.code)
            print(f"   ✅ 按代码查询科室成功: {dept_by_code}")
            
            # 检查科室代码存在性
            code_exists = department_exists_by_code(first_dept.code)
            print(f"   ✅ 科室代码存在性检查: {code_exists}")
            
            # 检查科室名称存在性
            name_exists = department_exists_by_name(first_dept.name)
            print(f"   ✅ 科室名称存在性检查: {name_exists}")
        
    except Exception as e:
        print(f"   ❌ 单个查询失败: {str(e)}")
    
    # 10. 测试查询性能
    print("\n10. 测试查询性能...")
    
    try:
        import time
        
        # 测试基础查询性能
        start_time = time.time()
        basic_users = list(user_list()[:100])
        basic_time = time.time() - start_time
        print(f"   ✅ 基础查询100个用户耗时: {basic_time:.4f}秒")
        
        # 测试优化查询性能
        start_time = time.time()
        optimized_users = list(user_list_optimized()[:100])
        optimized_time = time.time() - start_time
        print(f"   ✅ 优化查询100个用户耗时: {optimized_time:.4f}秒")
        
        # 测试最小化查询性能
        start_time = time.time()
        minimal_users = list(user_list_minimal()[:100])
        minimal_time = time.time() - start_time
        print(f"   ✅ 最小化查询100个用户耗时: {minimal_time:.4f}秒")
        
    except Exception as e:
        print(f"   ❌ 性能测试失败: {str(e)}")
    
    print("\n=== 数据查询选择器测试完成 ===")
    
    return True

if __name__ == '__main__':
    try:
        test_selectors()
        print("\n🎉 所有测试通过！")
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
