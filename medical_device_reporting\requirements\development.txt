# Development requirements for Medical Device Reporting Platform
# 医疗器械不良事件上报平台开发环境依赖

# Include base requirements
-r base.txt

# Django debug toolbar for development debugging
django-debug-toolbar>=4.0.0

# Testing framework
pytest-django>=4.5.0
pytest>=7.0.0

# Code formatting
black>=23.0.0

# Code linting
flake8>=6.0.0

# Import sorting
isort>=5.12.0

# Development server enhancements
django-extensions>=3.2.0

# Factory for creating test data
factory-boy>=3.2.0
