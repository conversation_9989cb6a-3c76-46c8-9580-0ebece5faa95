/**
 * 个人信息页面JavaScript
 * Profile Page JavaScript
 */

$(document).ready(function() {
    // 初始化修改密码功能
    initializeChangePassword();
    
    // 初始化状态切换功能（管理员查看模式）
    if ($('.toggle-status-btn').length > 0) {
        initializeStatusToggle();
    }
    
    // 初始化事件监听器
    initializeEventListeners();
});

/**
 * 初始化修改密码功能
 */
function initializeChangePassword() {
    const changePasswordForm = $('#changePasswordForm');
    
    if (!changePasswordForm.length) return;
    
    // 表单提交事件
    changePasswordForm.on('submit', function(e) {
        e.preventDefault();
        handlePasswordChange();
    });
    
    // 密码确认验证
    $('#confirm_password').on('input', function() {
        validatePasswordConfirm();
    });
    
    // 新密码输入验证
    $('#new_password').on('input', function() {
        validateNewPassword();
        validatePasswordConfirm(); // 重新验证确认密码
    });
    
    // 模态框显示时重置表单
    $('#changePasswordModal').on('show.bs.modal', function() {
        changePasswordForm[0].reset();
        changePasswordForm.removeClass('was-validated');
        $('.is-invalid').removeClass('is-invalid');
    });
}

/**
 * 处理密码修改
 */
function handlePasswordChange() {
    const form = $('#changePasswordForm')[0];
    
    // 验证表单
    if (!form.checkValidity()) {
        form.classList.add('was-validated');
        return;
    }
    
    // 验证密码确认
    if (!validatePasswordConfirm()) {
        return;
    }
    
    const formData = {
        current_password: $('#current_password').val(),
        new_password: $('#new_password').val(),
        confirm_password: $('#confirm_password').val()
    };
    
    // 显示加载状态
    const submitBtn = $('#changePasswordForm button[type="submit"]');
    LoadingIndicator.show(submitBtn[0]);
    
    // 发送AJAX请求
    $.ajax({
        url: '/change-password/',
        method: 'POST',
        headers: {
            'X-CSRFToken': getCsrfToken(),
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        data: formData,
        success: function(response) {
            if (response.success) {
                $('#changePasswordModal').modal('hide');
                showSuccessMessage(response.message || '密码修改成功，请重新登录');
                
                // 延迟跳转到登录页面
                setTimeout(() => {
                    window.location.href = '/login/';
                }, 2000);
            } else {
                showErrorMessage(response.error || '密码修改失败');
            }
        },
        error: function(xhr, status, error) {
            console.error('密码修改失败:', error);
            
            if (xhr.responseJSON && xhr.responseJSON.error) {
                showErrorMessage(xhr.responseJSON.error);
            } else {
                showErrorMessage('密码修改失败，请重试');
            }
        },
        complete: function() {
            LoadingIndicator.hide(submitBtn[0]);
        }
    });
}

/**
 * 验证新密码
 */
function validateNewPassword() {
    const newPassword = $('#new_password');
    const value = newPassword.val();
    
    if (value.length > 0 && value.length < 6) {
        newPassword[0].setCustomValidity('密码长度至少6位');
        newPassword.addClass('is-invalid');
        return false;
    } else {
        newPassword[0].setCustomValidity('');
        newPassword.removeClass('is-invalid');
        return true;
    }
}

/**
 * 验证密码确认
 */
function validatePasswordConfirm() {
    const newPassword = $('#new_password').val();
    const confirmPassword = $('#confirm_password');
    const confirmValue = confirmPassword.val();
    
    if (confirmValue.length > 0 && confirmValue !== newPassword) {
        confirmPassword[0].setCustomValidity('两次输入的密码不一致');
        confirmPassword.addClass('is-invalid');
        return false;
    } else {
        confirmPassword[0].setCustomValidity('');
        confirmPassword.removeClass('is-invalid');
        return true;
    }
}

/**
 * 初始化状态切换功能（管理员查看模式）
 */
function initializeStatusToggle() {
    $('.toggle-status-btn').on('click', function() {
        const btn = $(this);
        const userId = btn.data('user-id');
        const currentStatus = btn.data('current-status');
        const newStatus = !currentStatus;
        const actionText = newStatus ? '启用' : '禁用';
        
        if (!confirm(`确定要${actionText}这个用户吗？`)) {
            return;
        }
        
        // 显示加载状态
        LoadingIndicator.show(btn[0]);
        
        // 发送AJAX请求
        $.ajax({
            url: `/users/${userId}/toggle-status/`,
            method: 'POST',
            headers: {
                'X-CSRFToken': getCsrfToken()
            },
            success: function(response) {
                if (response.success) {
                    showSuccessMessage(response.message);
                    
                    // 更新按钮状态
                    btn.data('current-status', response.is_active);
                    updateStatusButton(btn, response.is_active);
                    
                    // 更新页面状态显示
                    updatePageStatus(response.is_active);
                } else {
                    showErrorMessage(response.error || `${actionText}用户失败`);
                }
            },
            error: function(xhr, status, error) {
                console.error('状态切换失败:', error);
                showErrorMessage(`${actionText}用户失败，请重试`);
            },
            complete: function() {
                LoadingIndicator.hide(btn[0]);
            }
        });
    });
}

/**
 * 更新状态按钮
 */
function updateStatusButton(btn, isActive) {
    const icon = btn.find('i');
    
    if (isActive) {
        icon.removeClass('bi-play-circle').addClass('bi-pause-circle');
        btn.attr('title', '禁用用户');
    } else {
        icon.removeClass('bi-pause-circle').addClass('bi-play-circle');
        btn.attr('title', '启用用户');
    }
}

/**
 * 更新页面状态显示
 */
function updatePageStatus(isActive) {
    const statusBadges = $('.badge:contains("正常"), .badge:contains("禁用")');
    
    statusBadges.each(function() {
        const badge = $(this);
        if (isActive) {
            badge.removeClass('bg-danger bg-secondary')
                 .addClass('bg-success')
                 .html('<i class="bi bi-check-circle me-1"></i>正常');
        } else {
            badge.removeClass('bg-success')
                 .addClass('bg-secondary')
                 .html('<i class="bi bi-x-circle me-1"></i>禁用');
        }
    });
}

/**
 * 初始化事件监听器
 */
function initializeEventListeners() {
    // 快速操作按钮点击效果
    $('.btn-group .btn').on('click', function() {
        $(this).addClass('shadow').removeClass('shadow-sm');
        setTimeout(() => {
            $(this).removeClass('shadow').addClass('shadow-sm');
        }, 200);
    });
    
    // 信息卡片悬停效果
    $('.card').on('mouseenter', function() {
        $(this).addClass('shadow-lg').removeClass('shadow-sm');
    }).on('mouseleave', function() {
        $(this).removeClass('shadow-lg').addClass('shadow-sm');
    });
    
    // 复制账号功能
    $('.badge:contains("账号")').parent().on('click', function() {
        const accountNumber = $(this).find('.badge').text();
        copyToClipboard(accountNumber);
        showInfoMessage('账号已复制到剪贴板');
    });
    
    // 邮箱链接点击统计
    $('a[href^="mailto:"]').on('click', function() {
        console.log('邮箱链接点击:', $(this).attr('href'));
    });
}

/**
 * 复制到剪贴板
 */
function copyToClipboard(text) {
    if (navigator.clipboard && window.isSecureContext) {
        // 使用现代API
        navigator.clipboard.writeText(text);
    } else {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        try {
            document.execCommand('copy');
        } catch (err) {
            console.error('复制失败:', err);
        }
        
        document.body.removeChild(textArea);
    }
}

/**
 * 页面可见性变化处理
 */
document.addEventListener('visibilitychange', function() {
    if (!document.hidden) {
        // 页面重新可见时，可以刷新一些实时数据
        console.log('页面重新可见');
    }
});
