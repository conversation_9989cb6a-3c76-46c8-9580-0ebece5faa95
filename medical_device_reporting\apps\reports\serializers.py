"""
不良事件上报管理序列化器
Adverse Event Reports Management Serializers for Medical Device Reporting Platform
"""

from rest_framework import serializers
from django.contrib.auth.models import User
from django.utils import timezone

from apps.common.utils import ValidationUtils
from apps.users.models import UserProfile, Department
from apps.users.serializers import UserSerializer, DepartmentSerializer
from .models import AdverseEventReport


class AdverseEventReportListSerializer(serializers.ModelSerializer):
    """
    不良事件报告列表序列化器
    """

    reporter_name = serializers.Char<PERSON>ield(source='reporter.display_name', read_only=True)
    reporter_account = serializers.CharField(source='reporter.account_number', read_only=True)
    department_name = serializers.CharField(source='department.name', read_only=True)
    department_code = serializers.CharField(source='department.code', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    injury_level_display = serializers.Cha<PERSON><PERSON><PERSON>(source='get_injury_level_display', read_only=True)
    patient_gender_display = serializers.Char<PERSON>ield(source='get_patient_gender_display', read_only=True)
    reviewed_by_name = serializers.CharField(source='reviewed_by.display_name', read_only=True)

    # 状态属性
    is_draft = serializers.BooleanField(read_only=True)
    is_submitted = serializers.BooleanField(read_only=True)
    is_under_review = serializers.BooleanField(read_only=True)
    is_approved = serializers.BooleanField(read_only=True)
    is_rejected = serializers.BooleanField(read_only=True)
    can_edit = serializers.BooleanField(read_only=True)
    can_submit = serializers.BooleanField(read_only=True)
    can_review = serializers.BooleanField(read_only=True)
    is_serious_event = serializers.BooleanField(read_only=True)

    class Meta:
        model = AdverseEventReport
        fields = [
            'id', 'report_number', 'status', 'status_display',
            'reporter_name', 'reporter_account', 'department_name', 'department_code',
            'patient_name', 'patient_age', 'patient_gender_display',
            'device_name', 'manufacturer', 'registration_number',
            'injury_level', 'injury_level_display', 'event_date',
            'reviewed_by_name', 'submitted_at', 'reviewed_at',
            'created_at', 'updated_at',
            # 状态属性
            'is_draft', 'is_submitted', 'is_under_review', 'is_approved', 'is_rejected',
            'can_edit', 'can_submit', 'can_review', 'is_serious_event'
        ]
        read_only_fields = ['id', 'report_number', 'created_at', 'updated_at']


class AdverseEventReportDetailSerializer(serializers.ModelSerializer):
    """
    不良事件报告详情序列化器
    """

    reporter = UserSerializer(read_only=True)
    department = DepartmentSerializer(read_only=True)
    reviewed_by = UserSerializer(read_only=True)

    status_display = serializers.CharField(source='get_status_display', read_only=True)
    injury_level_display = serializers.CharField(source='get_injury_level_display', read_only=True)
    patient_gender_display = serializers.CharField(source='get_patient_gender_display', read_only=True)

    # 业务属性
    reporter_info = serializers.CharField(read_only=True)
    patient_info = serializers.CharField(read_only=True)
    device_info = serializers.CharField(read_only=True)

    # 状态属性
    is_draft = serializers.BooleanField(read_only=True)
    is_submitted = serializers.BooleanField(read_only=True)
    is_under_review = serializers.BooleanField(read_only=True)
    is_approved = serializers.BooleanField(read_only=True)
    is_rejected = serializers.BooleanField(read_only=True)
    can_edit = serializers.BooleanField(read_only=True)
    can_submit = serializers.BooleanField(read_only=True)
    can_review = serializers.BooleanField(read_only=True)
    is_serious_event = serializers.BooleanField(read_only=True)

    created_by_username = serializers.CharField(source='created_by.username', read_only=True)
    updated_by_username = serializers.CharField(source='updated_by.username', read_only=True)

    class Meta:
        model = AdverseEventReport
        fields = '__all__'
        read_only_fields = [
            'id', 'report_number', 'status', 'submitted_at', 'reviewed_at',
            'reviewed_by', 'review_comments', 'created_at', 'updated_at',
            'created_by', 'updated_by', 'is_deleted'
        ]


class AdverseEventReportCreateSerializer(serializers.Serializer):
    """
    不良事件报告创建序列化器
    """

    # 上报人信息
    reporter_id = serializers.IntegerField()
    department_id = serializers.IntegerField(required=False, allow_null=True)
    reporter_phone = serializers.CharField(max_length=20)

    # 患者信息
    patient_name = serializers.CharField(max_length=100)
    patient_age = serializers.IntegerField(min_value=0, max_value=150)
    patient_gender = serializers.ChoiceField(choices=AdverseEventReport.GENDER_CHOICES)
    patient_contact = serializers.CharField(max_length=200, required=False, allow_blank=True)

    # 事件信息
    device_malfunction = serializers.CharField()
    event_date = serializers.DateTimeField()
    injury_level = serializers.ChoiceField(choices=AdverseEventReport.INJURY_LEVEL_CHOICES)
    injury_description = serializers.CharField(required=False, allow_blank=True)
    event_description = serializers.CharField()
    initial_cause_analysis = serializers.CharField(required=False, allow_blank=True)
    initial_treatment = serializers.CharField(required=False, allow_blank=True)

    # 器械信息
    device_name = serializers.CharField(max_length=200)
    registration_number = serializers.CharField(max_length=100)
    manufacturer = serializers.CharField(max_length=200)
    specification = serializers.CharField(max_length=100, required=False, allow_blank=True)
    model = serializers.CharField(max_length=100, required=False, allow_blank=True)
    product_number = serializers.CharField(max_length=100, required=False, allow_blank=True)
    batch_number = serializers.CharField(max_length=100, required=False, allow_blank=True)
    production_date = serializers.DateField(required=False, allow_null=True)
    expiry_date = serializers.DateField(required=False, allow_null=True)

    def validate_reporter_id(self, value):
        """验证上报人"""
        try:
            reporter = UserProfile.objects.get(
                id=value,
                is_active=True,
                is_deleted=False
            )
        except UserProfile.DoesNotExist:
            raise serializers.ValidationError('上报人不存在或已禁用')

        return value

    def validate_department_id(self, value):
        """验证科室"""
        if value is not None:
            try:
                Department.objects.get(id=value, is_active=True, is_deleted=False)
            except Department.DoesNotExist:
                raise serializers.ValidationError('科室不存在或已禁用')

        return value

    def validate_reporter_phone(self, value):
        """验证上报人联系电话"""
        if value and not ValidationUtils.validate_phone(value):
            raise serializers.ValidationError('请输入正确的手机号码格式')
        return value

    def validate_patient_name(self, value):
        """验证患者姓名"""
        if not value or not value.strip():
            raise serializers.ValidationError('患者姓名不能为空')

        value = value.strip()
        if len(value) < 2:
            raise serializers.ValidationError('患者姓名至少需要2个字符')

        return value

    def validate_event_date(self, value):
        """验证事件发生日期"""
        if value and value > timezone.now():
            raise serializers.ValidationError('事件发生日期不能是未来时间')
        return value

    def validate_device_malfunction(self, value):
        """验证器械故障表现"""
        if not value or not value.strip():
            raise serializers.ValidationError('器械故障表现不能为空')

        value = value.strip()
        if len(value) < 10:
            raise serializers.ValidationError('器械故障表现描述至少需要10个字符')

        return value

    def validate_event_description(self, value):
        """验证事件陈述"""
        if not value or not value.strip():
            raise serializers.ValidationError('事件陈述不能为空')

        value = value.strip()
        if len(value) < 20:
            raise serializers.ValidationError('事件陈述至少需要20个字符')

        return value

    def validate_device_name(self, value):
        """验证医疗器械名称"""
        if not value or not value.strip():
            raise serializers.ValidationError('医疗器械名称不能为空')

        value = value.strip()
        if len(value) < 2:
            raise serializers.ValidationError('医疗器械名称至少需要2个字符')

        return value

    def validate_registration_number(self, value):
        """验证注册证号"""
        if not value or not value.strip():
            raise serializers.ValidationError('注册证号不能为空')

        value = value.strip()
        if len(value) < 5:
            raise serializers.ValidationError('注册证号至少需要5个字符')

        return value

    def validate_manufacturer(self, value):
        """验证生产企业名称"""
        if not value or not value.strip():
            raise serializers.ValidationError('生产企业名称不能为空')

        value = value.strip()
        if len(value) < 2:
            raise serializers.ValidationError('生产企业名称至少需要2个字符')

        return value

    def validate(self, attrs):
        """整体验证"""
        # 验证伤害描述
        injury_level = attrs.get('injury_level')
        injury_description = attrs.get('injury_description')

        if injury_level in ['death', 'serious_injury'] and not injury_description:
            raise serializers.ValidationError('伤害程度为死亡或严重伤害时，必须填写伤害表现')

        # 验证日期逻辑
        production_date = attrs.get('production_date')
        expiry_date = attrs.get('expiry_date')

        if production_date and expiry_date and production_date >= expiry_date:
            raise serializers.ValidationError('有效期必须晚于生产日期')

        # 验证上报人和科室关系
        reporter_id = attrs.get('reporter_id')
        department_id = attrs.get('department_id')

        if reporter_id and department_id:
            try:
                reporter = UserProfile.objects.get(id=reporter_id)
                if reporter.role == 'staff' and reporter.department_id != department_id:
                    raise serializers.ValidationError('科室人员只能为本科室提交报告')
            except UserProfile.DoesNotExist:
                pass  # 这个错误会在字段验证中捕获

        return attrs


class AdverseEventReportUpdateSerializer(serializers.Serializer):
    """
    不良事件报告更新序列化器
    """

    # 上报人信息
    reporter_phone = serializers.CharField(max_length=20, required=False)

    # 患者信息
    patient_name = serializers.CharField(max_length=100, required=False)
    patient_age = serializers.IntegerField(min_value=0, max_value=150, required=False)
    patient_gender = serializers.ChoiceField(choices=AdverseEventReport.GENDER_CHOICES, required=False)
    patient_contact = serializers.CharField(max_length=200, required=False, allow_blank=True)

    # 事件信息
    device_malfunction = serializers.CharField(required=False)
    event_date = serializers.DateTimeField(required=False)
    injury_level = serializers.ChoiceField(choices=AdverseEventReport.INJURY_LEVEL_CHOICES, required=False)
    injury_description = serializers.CharField(required=False, allow_blank=True)
    event_description = serializers.CharField(required=False)
    initial_cause_analysis = serializers.CharField(required=False, allow_blank=True)
    initial_treatment = serializers.CharField(required=False, allow_blank=True)

    # 器械信息
    device_name = serializers.CharField(max_length=200, required=False)
    registration_number = serializers.CharField(max_length=100, required=False)
    manufacturer = serializers.CharField(max_length=200, required=False)
    specification = serializers.CharField(max_length=100, required=False, allow_blank=True)
    model = serializers.CharField(max_length=100, required=False, allow_blank=True)
    product_number = serializers.CharField(max_length=100, required=False, allow_blank=True)
    batch_number = serializers.CharField(max_length=100, required=False, allow_blank=True)
    production_date = serializers.DateField(required=False, allow_null=True)
    expiry_date = serializers.DateField(required=False, allow_null=True)

    def validate_reporter_phone(self, value):
        """验证上报人联系电话"""
        if value and not ValidationUtils.validate_phone(value):
            raise serializers.ValidationError('请输入正确的手机号码格式')
        return value

    def validate_patient_name(self, value):
        """验证患者姓名"""
        if value is not None:
            value = value.strip()
            if not value:
                raise serializers.ValidationError('患者姓名不能为空')
            if len(value) < 2:
                raise serializers.ValidationError('患者姓名至少需要2个字符')
        return value

    def validate_event_date(self, value):
        """验证事件发生日期"""
        if value and value > timezone.now():
            raise serializers.ValidationError('事件发生日期不能是未来时间')
        return value

    def validate_device_malfunction(self, value):
        """验证器械故障表现"""
        if value is not None:
            value = value.strip()
            if not value:
                raise serializers.ValidationError('器械故障表现不能为空')
            if len(value) < 10:
                raise serializers.ValidationError('器械故障表现描述至少需要10个字符')
        return value

    def validate_event_description(self, value):
        """验证事件陈述"""
        if value is not None:
            value = value.strip()
            if not value:
                raise serializers.ValidationError('事件陈述不能为空')
            if len(value) < 20:
                raise serializers.ValidationError('事件陈述至少需要20个字符')
        return value


class AdverseEventReportReviewSerializer(serializers.Serializer):
    """
    不良事件报告审核序列化器
    """

    reviewer_id = serializers.IntegerField()
    action = serializers.ChoiceField(choices=['start_review', 'approve', 'reject'])
    comments = serializers.CharField(required=False, allow_blank=True)

    def validate_reviewer_id(self, value):
        """验证审核人"""
        try:
            reviewer = UserProfile.objects.get(
                id=value,
                is_active=True,
                is_deleted=False
            )
            # 验证审核权限
            if not reviewer.is_admin:
                raise serializers.ValidationError('只有管理员可以审核报告')
        except UserProfile.DoesNotExist:
            raise serializers.ValidationError('审核人不存在或已禁用')

        return value

    def validate(self, attrs):
        """整体验证"""
        action = attrs.get('action')
        comments = attrs.get('comments')

        if action == 'reject' and not comments:
            raise serializers.ValidationError('拒绝报告时必须填写审核意见')

        return attrs


class ReportStatisticsSerializer(serializers.Serializer):
    """
    报告统计序列化器
    """

    total_count = serializers.IntegerField(read_only=True)
    draft_count = serializers.IntegerField(read_only=True)
    submitted_count = serializers.IntegerField(read_only=True)
    under_review_count = serializers.IntegerField(read_only=True)
    approved_count = serializers.IntegerField(read_only=True)
    rejected_count = serializers.IntegerField(read_only=True)
    death_count = serializers.IntegerField(read_only=True)
    serious_injury_count = serializers.IntegerField(read_only=True)
    other_injury_count = serializers.IntegerField(read_only=True)


class ReportSearchSerializer(serializers.Serializer):
    """
    报告搜索序列化器
    """

    search = serializers.CharField(max_length=200, required=False, allow_blank=True)
    status = serializers.ChoiceField(
        choices=[('', '全部状态')] + AdverseEventReport.STATUS_CHOICES,
        required=False,
        allow_blank=True
    )
    department_id = serializers.IntegerField(required=False, allow_null=True)
    reporter_id = serializers.IntegerField(required=False, allow_null=True)
    injury_level = serializers.ChoiceField(
        choices=[('', '全部伤害程度')] + AdverseEventReport.INJURY_LEVEL_CHOICES,
        required=False,
        allow_blank=True
    )
    device_name = serializers.CharField(max_length=200, required=False, allow_blank=True)
    start_date = serializers.DateField(required=False, allow_null=True)
    end_date = serializers.DateField(required=False, allow_null=True)
    ordering = serializers.CharField(max_length=50, required=False, default='-created_at')
    page = serializers.IntegerField(min_value=1, required=False, default=1)
    page_size = serializers.IntegerField(min_value=1, max_value=100, required=False, default=20)

    def validate(self, attrs):
        """整体验证"""
        start_date = attrs.get('start_date')
        end_date = attrs.get('end_date')

        if start_date and end_date and start_date > end_date:
            raise serializers.ValidationError('开始日期不能晚于结束日期')

        return attrs


class ReportBulkActionSerializer(serializers.Serializer):
    """
    报告批量操作序列化器
    """

    report_ids = serializers.ListField(
        child=serializers.IntegerField(),
        min_length=1,
        max_length=100
    )
    action = serializers.ChoiceField(choices=['export', 'bulk_review'])

    def validate_report_ids(self, value):
        """验证报告ID列表"""
        if not value:
            raise serializers.ValidationError('报告ID列表不能为空')

        # 检查报告是否存在
        existing_ids = set(
            AdverseEventReport.objects.filter(
                id__in=value,
                is_deleted=False
            ).values_list('id', flat=True)
        )

        invalid_ids = set(value) - existing_ids
        if invalid_ids:
            raise serializers.ValidationError(f'以下报告ID不存在: {list(invalid_ids)}')

        return value


class ReportExportSerializer(serializers.Serializer):
    """
    报告导出序列化器
    """

    format = serializers.ChoiceField(
        choices=['excel', 'csv', 'pdf'],
        default='excel'
    )
    include_fields = serializers.ListField(
        child=serializers.CharField(),
        required=False,
        default=list
    )
    date_range = serializers.CharField(max_length=50, required=False)

    def validate_include_fields(self, value):
        """验证包含字段"""
        if not value:
            # 默认包含所有主要字段
            return [
                'report_number', 'status', 'reporter_name', 'department_name',
                'patient_name', 'patient_age', 'patient_gender',
                'device_name', 'manufacturer', 'registration_number',
                'injury_level', 'event_date', 'created_at'
            ]

        # 验证字段是否有效
        valid_fields = [
            'report_number', 'status', 'reporter_name', 'department_name',
            'patient_name', 'patient_age', 'patient_gender', 'patient_contact',
            'device_malfunction', 'event_date', 'injury_level', 'injury_description',
            'event_description', 'initial_cause_analysis', 'initial_treatment',
            'device_name', 'registration_number', 'manufacturer',
            'specification', 'model', 'product_number', 'batch_number',
            'production_date', 'expiry_date', 'review_comments',
            'submitted_at', 'reviewed_at', 'created_at', 'updated_at'
        ]

        invalid_fields = set(value) - set(valid_fields)
        if invalid_fields:
            raise serializers.ValidationError(f'无效的字段: {list(invalid_fields)}')

        return value


class ReportSummarySerializer(serializers.Serializer):
    """
    报告摘要序列化器（用于仪表板等）
    """

    id = serializers.IntegerField(read_only=True)
    report_number = serializers.CharField(read_only=True)
    patient_name = serializers.CharField(read_only=True)
    device_name = serializers.CharField(read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    injury_level_display = serializers.CharField(source='get_injury_level_display', read_only=True)
    event_date = serializers.DateTimeField(read_only=True)
    created_at = serializers.DateTimeField(read_only=True)
    is_serious_event = serializers.BooleanField(read_only=True)

    class Meta:
        model = AdverseEventReport
        fields = [
            'id', 'report_number', 'patient_name', 'device_name',
            'status_display', 'injury_level_display', 'event_date',
            'created_at', 'is_serious_event'
        ]
