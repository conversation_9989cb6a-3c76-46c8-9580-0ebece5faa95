/**
 * Navigation JavaScript for Medical Device Reporting Platform
 * 医疗器械不良事件上报平台导航脚本
 */

document.addEventListener('DOMContentLoaded', function() {
    // 初始化导航
    initializeNavigation();
    
    /**
     * 初始化导航功能
     */
    function initializeNavigation() {
        // 高亮当前页面
        highlightCurrentPage();
        
        // 绑定导航事件
        bindNavigationEvents();
        
        // 初始化面包屑
        initializeBreadcrumb();
        
        console.log('导航系统初始化完成');
    }
    
    /**
     * 高亮当前页面导航项
     */
    function highlightCurrentPage() {
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
        
        navLinks.forEach(link => {
            const href = link.getAttribute('href');
            
            // 移除现有的active类
            link.classList.remove('active');
            link.parentElement.classList.remove('active');
            
            // 检查是否匹配当前路径
            if (href && isCurrentPage(href, currentPath)) {
                link.classList.add('active');
                link.parentElement.classList.add('active');
                
                // 如果是下拉菜单项，也高亮父级
                const dropdown = link.closest('.dropdown');
                if (dropdown) {
                    const dropdownToggle = dropdown.querySelector('.dropdown-toggle');
                    if (dropdownToggle) {
                        dropdownToggle.classList.add('active');
                    }
                }
            }
        });
        
        // 处理下拉菜单项
        const dropdownItems = document.querySelectorAll('.dropdown-item');
        dropdownItems.forEach(item => {
            const href = item.getAttribute('href');
            
            if (href && isCurrentPage(href, currentPath)) {
                item.classList.add('active');
                
                // 高亮父级下拉菜单
                const dropdown = item.closest('.dropdown');
                if (dropdown) {
                    const dropdownToggle = dropdown.querySelector('.dropdown-toggle');
                    if (dropdownToggle) {
                        dropdownToggle.classList.add('active');
                    }
                }
            }
        });
    }
    
    /**
     * 检查是否为当前页面
     * @param {string} href - 链接地址
     * @param {string} currentPath - 当前路径
     * @returns {boolean} 是否匹配
     */
    function isCurrentPage(href, currentPath) {
        // 精确匹配
        if (href === currentPath) {
            return true;
        }
        
        // 路径前缀匹配（用于子页面）
        if (currentPath.startsWith(href) && href !== '/') {
            return true;
        }
        
        // 特殊匹配规则
        const matchRules = {
            '/users/': ['/users/', '/profile/', '/settings/'],
            '/reports/': ['/reports/'],
            '/dashboard/': ['/dashboard/']
        };
        
        for (const [pattern, paths] of Object.entries(matchRules)) {
            if (href.includes(pattern)) {
                return paths.some(path => currentPath.startsWith(path));
            }
        }
        
        return false;
    }
    
    /**
     * 绑定导航事件
     */
    function bindNavigationEvents() {
        // 导航链接点击事件
        const navLinks = document.querySelectorAll('.nav-link, .dropdown-item');
        navLinks.forEach(link => {
            link.addEventListener('click', function(event) {
                // 添加点击效果
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
                
                // 记录导航行为（可选）
                const href = this.getAttribute('href');
                const text = this.textContent.trim();
                console.log(`导航点击: ${text} -> ${href}`);
            });
        });
        
        // 移动端导航切换
        const navbarToggler = document.querySelector('.navbar-toggler');
        const navbarCollapse = document.querySelector('.navbar-collapse');
        
        if (navbarToggler && navbarCollapse) {
            // 点击导航项时自动收起菜单（移动端）
            const navItems = navbarCollapse.querySelectorAll('.nav-link, .dropdown-item');
            navItems.forEach(item => {
                item.addEventListener('click', function() {
                    if (window.innerWidth < 992) { // Bootstrap lg断点
                        const bsCollapse = bootstrap.Collapse.getInstance(navbarCollapse);
                        if (bsCollapse) {
                            bsCollapse.hide();
                        }
                    }
                });
            });
        }
        
        // 用户菜单交互增强
        const userDropdown = document.querySelector('.nav-item.dropdown');
        if (userDropdown) {
            const dropdownToggle = userDropdown.querySelector('.dropdown-toggle');
            const dropdownMenu = userDropdown.querySelector('.dropdown-menu');
            
            if (dropdownToggle && dropdownMenu) {
                // 鼠标悬停效果（桌面端）
                if (window.innerWidth >= 992) {
                    userDropdown.addEventListener('mouseenter', function() {
                        dropdownToggle.classList.add('show');
                        dropdownMenu.classList.add('show');
                    });
                    
                    userDropdown.addEventListener('mouseleave', function() {
                        setTimeout(() => {
                            if (!userDropdown.matches(':hover')) {
                                dropdownToggle.classList.remove('show');
                                dropdownMenu.classList.remove('show');
                            }
                        }, 300);
                    });
                }
            }
        }
    }
    
    /**
     * 初始化面包屑
     */
    function initializeBreadcrumb() {
        const breadcrumb = document.querySelector('.breadcrumb');
        if (!breadcrumb) return;
        
        // 添加面包屑动画
        const breadcrumbItems = breadcrumb.querySelectorAll('.breadcrumb-item');
        breadcrumbItems.forEach((item, index) => {
            item.style.opacity = '0';
            item.style.transform = 'translateX(-20px)';
            
            setTimeout(() => {
                item.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                item.style.opacity = '1';
                item.style.transform = 'translateX(0)';
            }, index * 100);
        });
        
        // 面包屑链接点击效果
        const breadcrumbLinks = breadcrumb.querySelectorAll('a');
        breadcrumbLinks.forEach(link => {
            link.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 100);
            });
        });
    }
    
    /**
     * 更新页面标题
     * @param {string} title - 新标题
     */
    function updatePageTitle(title) {
        document.title = `${title} - 医疗器械不良事件上报平台`;
        
        // 更新页面标题元素（如果存在）
        const pageTitle = document.querySelector('.page-title, h1, h2');
        if (pageTitle) {
            pageTitle.textContent = title;
        }
    }
    
    /**
     * 显示加载状态
     */
    function showNavigationLoading() {
        const navbar = document.querySelector('.navbar');
        if (navbar) {
            navbar.style.opacity = '0.7';
            navbar.style.pointerEvents = 'none';
        }
    }
    
    /**
     * 隐藏加载状态
     */
    function hideNavigationLoading() {
        const navbar = document.querySelector('.navbar');
        if (navbar) {
            navbar.style.opacity = '1';
            navbar.style.pointerEvents = 'auto';
        }
    }
    
    /**
     * 添加导航项
     * @param {Object} item - 导航项配置
     */
    function addNavigationItem(item) {
        const navList = document.querySelector('.navbar-nav');
        if (!navList) return;
        
        const navItem = document.createElement('li');
        navItem.className = 'nav-item';
        
        const navLink = document.createElement('a');
        navLink.className = 'nav-link';
        navLink.href = item.href || '#';
        navLink.innerHTML = `${item.icon ? `<i class="${item.icon} me-1"></i>` : ''}${item.text}`;
        
        navItem.appendChild(navLink);
        navList.appendChild(navItem);
        
        // 重新绑定事件
        bindNavigationEvents();
    }
    
    /**
     * 移除导航项
     * @param {string} selector - 选择器
     */
    function removeNavigationItem(selector) {
        const item = document.querySelector(selector);
        if (item) {
            item.remove();
        }
    }
    
    // 导出全局函数
    window.Navigation = {
        updatePageTitle,
        showNavigationLoading,
        hideNavigationLoading,
        addNavigationItem,
        removeNavigationItem,
        highlightCurrentPage
    };
    
    // 页面切换时重新高亮导航
    window.addEventListener('popstate', function() {
        setTimeout(highlightCurrentPage, 100);
    });
});
