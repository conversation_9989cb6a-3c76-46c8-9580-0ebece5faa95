{% extends 'base.html' %}
{% load static %}

{% block title %}个人设置 - 医疗器械不良事件上报平台{% endblock %}

{% block extra_css %}
<link href="{% static 'users/css/user_management.css' %}" rel="stylesheet">
{% endblock %}

{% block nav_items %}
<li class="nav-item">
    <a class="nav-link" href="{% url 'users:dashboard' %}">
        <i class="bi bi-house"></i>
        用户中心
    </a>
</li>
{% if user.profile.is_admin %}
<li class="nav-item">
    <a class="nav-link" href="{% url 'users:user_list' %}">
        <i class="bi bi-people"></i>
        用户管理
    </a>
</li>
<li class="nav-item">
    <a class="nav-link" href="{% url 'users:department_list' %}">
        <i class="bi bi-building"></i>
        科室管理
    </a>
</li>
{% endif %}
<li class="nav-item dropdown">
    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
        <i class="bi bi-person-circle"></i>
        {{ user.get_full_name|default:user.username }}
    </a>
    <ul class="dropdown-menu">
        <li><a class="dropdown-item" href="{% url 'users:profile' %}"><i class="bi bi-person me-2"></i>个人信息</a></li>
        <li><a class="dropdown-item" href="{% url 'users:user_settings' %}"><i class="bi bi-gear me-2"></i>个人设置</a></li>
        <li><hr class="dropdown-divider"></li>
        <li><a class="dropdown-item" href="{% url 'users:logout' %}"><i class="bi bi-box-arrow-right me-2"></i>退出登录</a></li>
    </ul>
</li>
{% endblock %}

{% block content %}
<div class="user-settings-container">
    <!-- 页面标题 -->
    <div class="page-header mb-4">
        <div class="row align-items-center">
            <div class="col">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'users:dashboard' %}">用户中心</a></li>
                        <li class="breadcrumb-item active">个人设置</li>
                    </ol>
                </nav>
                <h2 class="page-title">
                    <i class="bi bi-gear me-2"></i>
                    个人设置
                </h2>
                <p class="page-subtitle text-muted">管理您的个人偏好和账户设置</p>
            </div>
            <div class="col-auto">
                <a href="{% url 'users:profile' %}" class="btn btn-outline-secondary">
                    <i class="bi bi-person me-2"></i>
                    个人信息
                </a>
            </div>
        </div>
    </div>

    <!-- 设置标签页 -->
    <div class="row">
        <div class="col-lg-10 mx-auto">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <!-- 标签页导航 -->
                    <ul class="nav nav-tabs card-header-tabs" id="settingsTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="basic-tab" data-bs-toggle="tab" 
                                    data-bs-target="#basic-pane" type="button" role="tab">
                                <i class="bi bi-person-lines-fill me-2"></i>
                                基本设置
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="preferences-tab" data-bs-toggle="tab" 
                                    data-bs-target="#preferences-pane" type="button" role="tab">
                                <i class="bi bi-sliders me-2"></i>
                                偏好设置
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="security-tab" data-bs-toggle="tab" 
                                    data-bs-target="#security-pane" type="button" role="tab">
                                <i class="bi bi-shield-check me-2"></i>
                                安全设置
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="notifications-tab" data-bs-toggle="tab" 
                                    data-bs-target="#notifications-pane" type="button" role="tab">
                                <i class="bi bi-bell me-2"></i>
                                通知设置
                            </button>
                        </li>
                    </ul>
                </div>
                
                <div class="card-body">
                    <!-- 标签页内容 -->
                    <div class="tab-content" id="settingsTabContent">
                        <!-- 基本设置 -->
                        <div class="tab-pane fade show active" id="basic-pane" role="tabpanel">
                            <div class="settings-section">
                                <h6 class="settings-title">
                                    <i class="bi bi-person-lines-fill me-2"></i>
                                    基本信息设置
                                </h6>
                                <p class="text-muted mb-4">管理您的基本个人信息</p>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="info-card">
                                            <h6>当前信息</h6>
                                            <dl class="row">
                                                <dt class="col-sm-4">姓名：</dt>
                                                <dd class="col-sm-8">{{ user.get_full_name|default:"未设置" }}</dd>
                                                
                                                <dt class="col-sm-4">邮箱：</dt>
                                                <dd class="col-sm-8">{{ user.email|default:"未设置" }}</dd>
                                                
                                                <dt class="col-sm-4">账号：</dt>
                                                <dd class="col-sm-8">{{ user_profile.account_number }}</dd>
                                                
                                                <dt class="col-sm-4">角色：</dt>
                                                <dd class="col-sm-8">
                                                    <span class="badge bg-primary">{{ user_profile.get_role_display }}</span>
                                                </dd>
                                            </dl>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="action-card">
                                            <h6>快速操作</h6>
                                            <div class="d-grid gap-2">
                                                <a href="{% url 'users:profile_edit' %}" class="btn btn-outline-primary">
                                                    <i class="bi bi-pencil me-2"></i>
                                                    编辑个人信息
                                                </a>
                                                <button type="button" class="btn btn-outline-warning" 
                                                        data-bs-toggle="modal" data-bs-target="#changePasswordModal">
                                                    <i class="bi bi-key me-2"></i>
                                                    修改密码
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 偏好设置 -->
                        <div class="tab-pane fade" id="preferences-pane" role="tabpanel">
                            <div class="settings-section">
                                <h6 class="settings-title">
                                    <i class="bi bi-sliders me-2"></i>
                                    界面偏好设置
                                </h6>
                                <p class="text-muted mb-4">自定义您的使用体验</p>
                                
                                <form id="preferencesForm">
                                    {% csrf_token %}
                                    <div class="row">
                                        <div class="col-md-6">
                                            <!-- 主题设置 -->
                                            <div class="preference-item mb-4">
                                                <label class="form-label">
                                                    <i class="bi bi-palette me-2"></i>
                                                    界面主题
                                                </label>
                                                <select class="form-select" id="theme" name="theme">
                                                    <option value="light" selected>浅色主题</option>
                                                    <option value="dark">深色主题</option>
                                                    <option value="auto">跟随系统</option>
                                                </select>
                                                <div class="form-text">选择您喜欢的界面主题</div>
                                            </div>

                                            <!-- 语言设置 -->
                                            <div class="preference-item mb-4">
                                                <label class="form-label">
                                                    <i class="bi bi-translate me-2"></i>
                                                    界面语言
                                                </label>
                                                <select class="form-select" id="language" name="language">
                                                    <option value="zh-cn" selected>简体中文</option>
                                                    <option value="en">English</option>
                                                </select>
                                                <div class="form-text">选择界面显示语言</div>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <!-- 时区设置 -->
                                            <div class="preference-item mb-4">
                                                <label class="form-label">
                                                    <i class="bi bi-clock me-2"></i>
                                                    时区设置
                                                </label>
                                                <select class="form-select" id="timezone" name="timezone">
                                                    <option value="Asia/Shanghai" selected>北京时间 (UTC+8)</option>
                                                    <option value="UTC">协调世界时 (UTC)</option>
                                                </select>
                                                <div class="form-text">选择您所在的时区</div>
                                            </div>

                                            <!-- 页面大小设置 -->
                                            <div class="preference-item mb-4">
                                                <label class="form-label">
                                                    <i class="bi bi-list me-2"></i>
                                                    每页显示数量
                                                </label>
                                                <select class="form-select" id="page_size" name="page_size">
                                                    <option value="10">10条/页</option>
                                                    <option value="20" selected>20条/页</option>
                                                    <option value="50">50条/页</option>
                                                    <option value="100">100条/页</option>
                                                </select>
                                                <div class="form-text">设置列表页面的显示数量</div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="text-end">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-check-circle me-2"></i>
                                            保存偏好设置
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- 安全设置 -->
                        <div class="tab-pane fade" id="security-pane" role="tabpanel">
                            <div class="settings-section">
                                <h6 class="settings-title">
                                    <i class="bi bi-shield-check me-2"></i>
                                    账户安全设置
                                </h6>
                                <p class="text-muted mb-4">管理您的账户安全选项</p>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <!-- 密码安全 -->
                                        <div class="security-item mb-4">
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="bi bi-key-fill text-warning me-2"></i>
                                                <h6 class="mb-0">密码安全</h6>
                                            </div>
                                            <p class="text-muted mb-3">定期修改密码可以提高账户安全性</p>
                                            <button type="button" class="btn btn-outline-warning" 
                                                    data-bs-toggle="modal" data-bs-target="#changePasswordModal">
                                                <i class="bi bi-key me-2"></i>
                                                修改密码
                                            </button>
                                        </div>

                                        <!-- 登录记录 -->
                                        <div class="security-item mb-4">
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="bi bi-clock-history text-info me-2"></i>
                                                <h6 class="mb-0">登录记录</h6>
                                            </div>
                                            <p class="text-muted mb-2">最后登录时间：
                                                {% if user.last_login %}
                                                    {{ user.last_login|date:"Y-m-d H:i:s" }}
                                                {% else %}
                                                    从未登录
                                                {% endif %}
                                            </p>
                                            <p class="text-muted mb-3">登录IP：
                                                {% if user_profile.last_login_ip %}
                                                    <code>{{ user_profile.last_login_ip }}</code>
                                                {% else %}
                                                    无记录
                                                {% endif %}
                                            </p>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <!-- 账户状态 -->
                                        <div class="security-item mb-4">
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="bi bi-person-check text-success me-2"></i>
                                                <h6 class="mb-0">账户状态</h6>
                                            </div>
                                            <p class="text-muted mb-2">当前状态：
                                                {% if user_profile.is_active %}
                                                    <span class="badge bg-success">正常</span>
                                                {% else %}
                                                    <span class="badge bg-danger">禁用</span>
                                                {% endif %}
                                            </p>
                                            <p class="text-muted mb-3">注册时间：{{ user.date_joined|date:"Y-m-d H:i:s" }}</p>
                                        </div>

                                        <!-- 权限信息 -->
                                        <div class="security-item mb-4">
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="bi bi-shield-fill text-primary me-2"></i>
                                                <h6 class="mb-0">权限信息</h6>
                                            </div>
                                            <p class="text-muted mb-2">当前角色：
                                                <span class="badge bg-primary">{{ user_profile.get_role_display }}</span>
                                            </p>
                                            <p class="text-muted mb-3">所属科室：
                                                {% if user_profile.department %}
                                                    {{ user_profile.department.name }}
                                                {% else %}
                                                    未分配
                                                {% endif %}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 通知设置 -->
                        <div class="tab-pane fade" id="notifications-pane" role="tabpanel">
                            <div class="settings-section">
                                <h6 class="settings-title">
                                    <i class="bi bi-bell me-2"></i>
                                    通知偏好设置
                                </h6>
                                <p class="text-muted mb-4">管理您接收通知的方式和类型</p>
                                
                                <form id="notificationsForm">
                                    {% csrf_token %}
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6 class="mb-3">邮件通知</h6>
                                            
                                            <div class="form-check form-switch mb-3">
                                                <input class="form-check-input" type="checkbox" id="email_notifications" checked>
                                                <label class="form-check-label" for="email_notifications">
                                                    启用邮件通知
                                                </label>
                                            </div>
                                            
                                            <div class="form-check form-switch mb-3">
                                                <input class="form-check-input" type="checkbox" id="email_reports" checked>
                                                <label class="form-check-label" for="email_reports">
                                                    报告状态更新通知
                                                </label>
                                            </div>
                                            
                                            <div class="form-check form-switch mb-3">
                                                <input class="form-check-input" type="checkbox" id="email_system">
                                                <label class="form-check-label" for="email_system">
                                                    系统维护通知
                                                </label>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <h6 class="mb-3">系统通知</h6>
                                            
                                            <div class="form-check form-switch mb-3">
                                                <input class="form-check-input" type="checkbox" id="browser_notifications" checked>
                                                <label class="form-check-label" for="browser_notifications">
                                                    浏览器通知
                                                </label>
                                            </div>
                                            
                                            <div class="form-check form-switch mb-3">
                                                <input class="form-check-input" type="checkbox" id="desktop_notifications">
                                                <label class="form-check-label" for="desktop_notifications">
                                                    桌面通知
                                                </label>
                                            </div>
                                            
                                            <div class="form-check form-switch mb-3">
                                                <input class="form-check-input" type="checkbox" id="sound_notifications">
                                                <label class="form-check-label" for="sound_notifications">
                                                    声音提醒
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="text-end">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-check-circle me-2"></i>
                                            保存通知设置
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 修改密码模态框 -->
<div class="modal fade" id="changePasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-key me-2"></i>
                    修改密码
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="changePasswordForm">
                <div class="modal-body">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="current_password" class="form-label">当前密码</label>
                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                        <div class="invalid-feedback">请输入当前密码</div>
                    </div>
                    <div class="mb-3">
                        <label for="new_password" class="form-label">新密码</label>
                        <input type="password" class="form-control" id="new_password" name="new_password" 
                               minlength="6" required>
                        <div class="form-text">密码长度至少6位</div>
                        <div class="invalid-feedback">请输入新密码（至少6位）</div>
                    </div>
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">确认新密码</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                               minlength="6" required>
                        <div class="invalid-feedback">请确认新密码</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-circle me-2"></i>
                        确认修改
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'users/js/user_settings.js' %}"></script>
{% endblock %}
