#!/usr/bin/env python
"""
测试运行脚本
Test Runner Script for Medical Device Reporting Platform
"""

import os
import sys
import django
import subprocess
from django.conf import settings
from django.test.utils import get_runner

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.test')
django.setup()


def run_specific_tests():
    """运行特定测试模块"""
    
    test_modules = [
        'apps.users.tests.test_models',
        'apps.users.tests.test_services', 
        'apps.users.tests.test_apis',
        'apps.users.tests.test_views',
        'apps.users.tests.test_permissions',
    ]
    
    print("🧪 运行用户管理模块测试")
    print("=" * 60)
    
    total_tests = 0
    passed_tests = 0
    failed_modules = []
    
    for module in test_modules:
        print(f"\n📋 运行测试模块: {module}")
        print("-" * 40)
        
        try:
            # 运行测试
            result = subprocess.run([
                sys.executable, 'manage.py', 'test', module,
                '--settings=config.settings.test',
                '--verbosity=2'
            ], capture_output=True, text=True, cwd=os.getcwd())
            
            if result.returncode == 0:
                print(f"✅ {module} - 测试通过")
                # 解析测试数量（简单解析）
                output_lines = result.stderr.split('\n')
                for line in output_lines:
                    if 'Ran' in line and 'test' in line:
                        try:
                            test_count = int(line.split()[1])
                            total_tests += test_count
                            passed_tests += test_count
                        except:
                            pass
            else:
                print(f"❌ {module} - 测试失败")
                print("错误输出:")
                print(result.stderr)
                failed_modules.append(module)
                
        except Exception as e:
            print(f"❌ {module} - 运行异常: {e}")
            failed_modules.append(module)
    
    # 输出总结
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("=" * 60)
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败模块: {len(failed_modules)}")
    
    if failed_modules:
        print("\n❌ 失败的测试模块:")
        for module in failed_modules:
            print(f"  - {module}")
        return False
    else:
        print("\n🎉 所有测试通过！")
        return True


def run_coverage_test():
    """运行测试覆盖率检查"""
    
    print("\n🔍 运行测试覆盖率检查")
    print("=" * 60)
    
    try:
        # 检查是否安装了coverage
        import coverage
        
        # 运行覆盖率测试
        result = subprocess.run([
            'coverage', 'run', '--source=apps.users',
            'manage.py', 'test', 'apps.users.tests',
            '--settings=config.settings.test'
        ], capture_output=True, text=True, cwd=os.getcwd())
        
        if result.returncode == 0:
            print("✅ 覆盖率测试完成")
            
            # 生成覆盖率报告
            report_result = subprocess.run([
                'coverage', 'report', '--show-missing'
            ], capture_output=True, text=True, cwd=os.getcwd())
            
            print("\n📈 覆盖率报告:")
            print(report_result.stdout)
            
            # 生成HTML报告
            html_result = subprocess.run([
                'coverage', 'html', '--directory=htmlcov'
            ], capture_output=True, text=True, cwd=os.getcwd())
            
            if html_result.returncode == 0:
                print("📄 HTML覆盖率报告已生成: htmlcov/index.html")
            
            return True
        else:
            print("❌ 覆盖率测试失败")
            print(result.stderr)
            return False
            
    except ImportError:
        print("⚠️ 未安装coverage包，跳过覆盖率检查")
        print("安装命令: pip install coverage")
        return True
    except Exception as e:
        print(f"❌ 覆盖率检查异常: {e}")
        return False


def run_django_check():
    """运行Django系统检查"""
    
    print("\n🔧 运行Django系统检查")
    print("=" * 60)
    
    try:
        result = subprocess.run([
            sys.executable, 'manage.py', 'check',
            '--settings=config.settings.test'
        ], capture_output=True, text=True, cwd=os.getcwd())
        
        if result.returncode == 0:
            print("✅ Django系统检查通过")
            if result.stdout.strip():
                print(result.stdout)
            return True
        else:
            print("❌ Django系统检查失败")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Django系统检查异常: {e}")
        return False


def run_migration_check():
    """检查数据库迁移状态"""
    
    print("\n🗄️ 检查数据库迁移状态")
    print("=" * 60)
    
    try:
        result = subprocess.run([
            sys.executable, 'manage.py', 'showmigrations',
            '--settings=config.settings.test'
        ], capture_output=True, text=True, cwd=os.getcwd())
        
        if result.returncode == 0:
            print("✅ 数据库迁移状态检查完成")
            
            # 检查是否有未应用的迁移
            if '[ ]' in result.stdout:
                print("⚠️ 发现未应用的迁移:")
                print(result.stdout)
                return False
            else:
                print("✅ 所有迁移已应用")
                return True
        else:
            print("❌ 迁移状态检查失败")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 迁移状态检查异常: {e}")
        return False


def main():
    """主函数"""
    
    print("🚀 医疗器械不良事件上报平台 - 测试套件")
    print("Medical Device Reporting Platform - Test Suite")
    print("=" * 80)
    
    success_count = 0
    total_checks = 4
    
    # 1. Django系统检查
    if run_django_check():
        success_count += 1
    
    # 2. 数据库迁移检查
    if run_migration_check():
        success_count += 1
    
    # 3. 运行测试
    if run_specific_tests():
        success_count += 1
    
    # 4. 覆盖率检查
    if run_coverage_test():
        success_count += 1
    
    # 输出最终结果
    print("\n" + "=" * 80)
    print("🏁 测试套件执行完成")
    print("=" * 80)
    print(f"完成检查: {success_count}/{total_checks}")
    
    if success_count == total_checks:
        print("🎉 所有检查通过！系统状态良好。")
        print("\n💡 下一步建议:")
        print("  1. 查看覆盖率报告: htmlcov/index.html")
        print("  2. 运行性能测试")
        print("  3. 进行用户验收测试")
        return True
    else:
        print("❌ 部分检查失败，请查看上述错误信息。")
        print("\n🔧 故障排除建议:")
        print("  1. 检查数据库连接")
        print("  2. 确保所有依赖已安装")
        print("  3. 检查Django配置")
        print("  4. 查看详细错误日志")
        return False


if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 测试运行异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
