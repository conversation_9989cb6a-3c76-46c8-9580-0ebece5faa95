"""
报告管理视图测试
Reports Management Views Tests
"""

from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from django.utils import timezone
from datetime import date, timedelta

from apps.users.models import UserProfile, Department
from apps.reports.models import AdverseEventReport


class ReportViewsTest(TestCase):
    """报告视图测试"""
    
    def setUp(self):
        """测试前准备"""
        self.client = Client()
        
        # 创建管理员用户
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # 创建普通用户
        self.staff_user = User.objects.create_user(
            username='staff',
            first_name='测试',
            last_name='用户',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # 创建科室
        self.department = Department.objects.create(
            code='TEST',
            name='测试科室',
            created_by=self.admin_user
        )
        
        # 创建用户配置文件
        self.admin_profile = UserProfile.objects.create(
            user=self.admin_user,
            account_number='0001',
            role='admin',
            created_by=self.admin_user
        )
        
        self.staff_profile = UserProfile.objects.create(
            user=self.staff_user,
            account_number='1001',
            department=self.department,
            role='staff',
            created_by=self.admin_user
        )
        
        # 创建测试报告
        self.test_report = AdverseEventReport.objects.create(
            reporter=self.staff_user,
            reporter_name='测试用户',
            reporter_phone='***********',
            department=self.department,
            patient_name='张三',
            patient_age=45,
            patient_gender='male',
            event_date=date.today(),
            injury_level='moderate',
            event_description='测试事件描述',
            device_name='测试器械',
            registration_number='TEST123456',
            manufacturer='测试厂商'
        )
    
    def test_dashboard_view_requires_login(self):
        """测试仪表板视图需要登录"""
        url = reverse('reports:dashboard')
        response = self.client.get(url)
        
        # 应该重定向到登录页面
        self.assertEqual(response.status_code, 302)
        self.assertIn('login', response.url)
    
    def test_dashboard_view_authenticated(self):
        """测试已认证用户访问仪表板"""
        self.client.login(username='staff', password='testpass123')
        url = reverse('reports:dashboard')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '报告管理')
        self.assertContains(response, '仪表板')
    
    def test_report_list_view(self):
        """测试报告列表视图"""
        self.client.login(username='staff', password='testpass123')
        url = reverse('reports:report_list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '报告列表')
        self.assertContains(response, self.test_report.report_number)
    
    def test_report_detail_view(self):
        """测试报告详情视图"""
        self.client.login(username='staff', password='testpass123')
        url = reverse('reports:report_detail', kwargs={'report_id': self.test_report.id})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.test_report.report_number)
        self.assertContains(response, self.test_report.device_name)
        self.assertContains(response, self.test_report.patient_name)
    
    def test_report_create_view_get(self):
        """测试报告创建视图GET请求"""
        self.client.login(username='staff', password='testpass123')
        url = reverse('reports:report_create')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '创建报告')
        self.assertContains(response, 'form')
    
    def test_report_create_view_post(self):
        """测试报告创建视图POST请求"""
        self.client.login(username='staff', password='testpass123')
        url = reverse('reports:report_create')
        
        data = {
            'reporter_name': '新测试用户',
            'reporter_phone': '13900139000',
            'patient_name': '李四',
            'patient_age': 30,
            'patient_gender': 'female',
            'event_date': date.today().strftime('%Y-%m-%d'),
            'injury_level': 'mild',
            'event_description': '新测试事件描述',
            'device_name': '新测试器械',
            'registration_number': 'NEW123456',
            'manufacturer': '新测试厂商',
            'action': 'save'
        }
        
        response = self.client.post(url, data)
        
        # 应该重定向到报告详情页面
        self.assertEqual(response.status_code, 302)
        
        # 检查报告是否创建成功
        new_report = AdverseEventReport.objects.filter(
            reporter=self.staff_user,
            patient_name='李四'
        ).first()
        self.assertIsNotNone(new_report)
        self.assertEqual(new_report.device_name, '新测试器械')
    
    def test_report_edit_view_permission(self):
        """测试报告编辑权限"""
        # 创建另一个用户的报告
        other_user = User.objects.create_user(
            username='other',
            password='testpass123'
        )
        other_profile = UserProfile.objects.create(
            user=other_user,
            account_number='2001',
            department=self.department,
            role='staff',
            created_by=self.admin_user
        )
        
        other_report = AdverseEventReport.objects.create(
            reporter=other_user,
            reporter_name='其他用户',
            patient_name='王五',
            event_date=date.today(),
            injury_level='moderate',
            event_description='其他事件描述',
            device_name='其他器械'
        )
        
        # 普通用户不能编辑其他人的报告
        self.client.login(username='staff', password='testpass123')
        url = reverse('reports:report_edit', kwargs={'report_id': other_report.id})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 403)
    
    def test_report_edit_view_own_report(self):
        """测试编辑自己的报告"""
        self.client.login(username='staff', password='testpass123')
        url = reverse('reports:report_edit', kwargs={'report_id': self.test_report.id})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '编辑报告')
        self.assertContains(response, self.test_report.device_name)
    
    def test_admin_can_view_all_reports(self):
        """测试管理员可以查看所有报告"""
        self.client.login(username='admin', password='testpass123')
        url = reverse('reports:report_detail', kwargs={'report_id': self.test_report.id})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.test_report.report_number)
    
    def test_pending_review_view_admin_only(self):
        """测试待审核视图仅管理员可访问"""
        # 普通用户访问应该被拒绝
        self.client.login(username='staff', password='testpass123')
        url = reverse('reports:pending_review')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 403)
        
        # 管理员可以访问
        self.client.login(username='admin', password='testpass123')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '待审核报告')
    
    def test_serious_events_view_admin_only(self):
        """测试严重事件视图仅管理员可访问"""
        # 创建严重事件报告
        serious_report = AdverseEventReport.objects.create(
            reporter=self.staff_user,
            reporter_name='测试用户',
            patient_name='严重患者',
            event_date=date.today(),
            injury_level='severe',  # 严重伤害
            event_description='严重事件描述',
            device_name='严重器械'
        )
        
        # 普通用户访问应该被拒绝
        self.client.login(username='staff', password='testpass123')
        url = reverse('reports:serious_events')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 403)
        
        # 管理员可以访问
        self.client.login(username='admin', password='testpass123')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '严重事件')
        self.assertContains(response, serious_report.report_number)
    
    def test_report_review_view_admin_only(self):
        """测试报告审核视图仅管理员可访问"""
        # 将报告状态设为已提交
        self.test_report.status = 'submitted'
        self.test_report.save()
        
        # 普通用户访问应该被拒绝
        self.client.login(username='staff', password='testpass123')
        url = reverse('reports:report_review', kwargs={'report_id': self.test_report.id})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 403)
        
        # 管理员可以访问
        self.client.login(username='admin', password='testpass123')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '审核报告')
        self.assertContains(response, self.test_report.report_number)
    
    def test_step_form_views(self):
        """测试分步表单视图"""
        self.client.login(username='staff', password='testpass123')
        
        # 测试步骤1
        url = reverse('reports:step_create')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)  # 重定向到步骤1
        
        # 测试步骤1页面
        url = reverse('reports:step_form', kwargs={'step': 1})
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '步骤 1')
        self.assertContains(response, '上报人信息')
    
    def test_report_search_and_filter(self):
        """测试报告搜索和筛选"""
        self.client.login(username='staff', password='testpass123')
        url = reverse('reports:report_list')
        
        # 测试搜索
        response = self.client.get(url, {'search': '测试器械'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.test_report.report_number)
        
        # 测试状态筛选
        response = self.client.get(url, {'status': 'draft'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.test_report.report_number)
        
        # 测试伤害程度筛选
        response = self.client.get(url, {'injury_level': 'moderate'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.test_report.report_number)
