# Generated by Django 4.2.23 on 2025-06-23 03:10

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("reports", "0001_initial"),
    ]

    operations = [
        migrations.AlterField(
            model_name="adverseeventreport",
            name="device_malfunction",
            field=models.TextField(
                blank=True,
                help_text="详细描述医疗器械的故障表现（无字符限制）",
                verbose_name="器械故障表现",
            ),
        ),
        migrations.AlterField(
            model_name="adverseeventreport",
            name="event_description",
            field=models.TextField(
                help_text="详细陈述事件情况，包括器械使用时间、使用目的、使用依据、使用情况、出现的不良事件情况、对受害者影响、采取的治疗措施、器械联合使用情况（无字符限制）",
                verbose_name="事件陈述",
            ),
        ),
        migrations.AlterField(
            model_name="adverseeventreport",
            name="patient_age",
            field=models.PositiveIntegerField(
                blank=True,
                help_text="患者年龄（岁）（可选）",
                null=True,
                verbose_name="患者年龄",
            ),
        ),
        migrations.AlterField(
            model_name="adverseeventreport",
            name="patient_gender",
            field=models.CharField(
                blank=True,
                choices=[("male", "男"), ("female", "女"), ("unknown", "未知")],
                help_text="患者的性别（可选）",
                max_length=10,
                verbose_name="患者性别",
            ),
        ),
        migrations.AlterField(
            model_name="adverseeventreport",
            name="patient_name",
            field=models.CharField(
                blank=True,
                help_text="发生不良事件的患者姓名（可选）",
                max_length=100,
                verbose_name="患者姓名",
            ),
        ),
    ]
