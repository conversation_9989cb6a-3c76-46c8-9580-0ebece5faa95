{% extends 'reports/base.html' %}
{% load static %}

{% block page_title %}{% if is_edit %}编辑报告{% else %}新建报告{% endif %}{% endblock %}
{% block page_heading %}{% if is_edit %}编辑报告{% else %}新建报告{% endif %}{% endblock %}
{% block page_description %}{% if is_edit %}{{ report.report_number }}{% else %}创建新的不良事件报告{% endif %}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'reports:report_list' %}">报告列表</a></li>
{% if is_edit %}
<li class="breadcrumb-item"><a href="{% url 'reports:report_detail' report_id=report.id %}">{{ report.report_number }}</a></li>
<li class="breadcrumb-item active">编辑</li>
{% else %}
<li class="breadcrumb-item active">新建报告</li>
{% endif %}
{% endblock %}

{% block page_actions %}
<div class="btn-group" role="group">
    <a href="{% url 'reports:report_list' %}" class="btn btn-outline-secondary">
        <i class="bi bi-arrow-left me-2"></i>
        返回列表
    </a>
    {% if is_edit %}
    <a href="{% url 'reports:report_detail' report_id=report.id %}" class="btn btn-outline-info">
        <i class="bi bi-eye me-2"></i>
        查看详情
    </a>
    {% endif %}
</div>
{% endblock %}

{% block reports_content %}
<div class="row justify-content-center">
    <div class="col-lg-10">
        <form method="post" id="reportForm" novalidate>
            {% csrf_token %}
            
            <!-- 上报人信息 -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-person-badge me-2"></i>
                        上报人信息
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.reporter.id_for_label }}" class="form-label">上报人 <span class="text-danger">*</span></label>
                                {{ form.reporter }}
                                {% if form.reporter.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.reporter.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.department.id_for_label }}" class="form-label">科室</label>
                                {{ form.department }}
                                {% if form.department.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.department.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.reporter_phone.id_for_label }}" class="form-label">联系电话</label>
                                {{ form.reporter_phone }}
                                {% if form.reporter_phone.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.reporter_phone.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 患者信息 -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-person me-2"></i>
                        患者信息
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.patient_name.id_for_label }}" class="form-label">患者姓名 <small class="text-muted">(可选)</small></label>
                                {{ form.patient_name }}
                                {% if form.patient_name.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.patient_name.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="{{ form.patient_age.id_for_label }}" class="form-label">年龄 <small class="text-muted">(可选)</small></label>
                                {{ form.patient_age }}
                                {% if form.patient_age.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.patient_age.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="{{ form.patient_gender.id_for_label }}" class="form-label">性别 <small class="text-muted">(可选)</small></label>
                                {{ form.patient_gender }}
                                {% if form.patient_gender.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.patient_gender.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.patient_contact.id_for_label }}" class="form-label">联系方式</label>
                                {{ form.patient_contact }}
                                {% if form.patient_contact.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.patient_contact.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 事件信息 -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        事件信息
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="{{ form.device_malfunction.id_for_label }}" class="form-label">器械故障表现 <small class="text-muted">(可选，无字符限制)</small></label>
                                {{ form.device_malfunction }}
                                {% if form.device_malfunction.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.device_malfunction.errors.0 }}
                                </div>
                                {% endif %}
                                <div class="form-text">请详细描述医疗器械的故障表现</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.event_date.id_for_label }}" class="form-label">事件日期 <span class="text-danger">*</span></label>
                                {{ form.event_date }}
                                {% if form.event_date.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.event_date.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.injury_level.id_for_label }}" class="form-label">伤害程度 <span class="text-danger">*</span></label>
                                {{ form.injury_level }}
                                {% if form.injury_level.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.injury_level.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.injury_description.id_for_label }}" class="form-label">伤害描述</label>
                                {{ form.injury_description }}
                                {% if form.injury_description.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.injury_description.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="{{ form.event_description.id_for_label }}" class="form-label">事件描述 <span class="text-danger">*</span> <small class="text-muted">(无字符限制)</small></label>
                                {{ form.event_description }}
                                {% if form.event_description.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.event_description.errors.0 }}
                                </div>
                                {% endif %}
                                <div class="form-text">请详细描述不良事件的发生过程</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.initial_cause_analysis.id_for_label }}" class="form-label">初步原因分析</label>
                                {{ form.initial_cause_analysis }}
                                {% if form.initial_cause_analysis.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.initial_cause_analysis.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.initial_treatment.id_for_label }}" class="form-label">初步处理措施</label>
                                {{ form.initial_treatment }}
                                {% if form.initial_treatment.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.initial_treatment.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 器械信息 -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-gear me-2"></i>
                        医疗器械信息
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.device_name.id_for_label }}" class="form-label">器械名称 <span class="text-danger">*</span></label>
                                {{ form.device_name }}
                                {% if form.device_name.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.device_name.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.registration_number.id_for_label }}" class="form-label">注册证号 <span class="text-danger">*</span></label>
                                {{ form.registration_number }}
                                {% if form.registration_number.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.registration_number.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.manufacturer.id_for_label }}" class="form-label">生产企业 <span class="text-danger">*</span></label>
                                {{ form.manufacturer }}
                                {% if form.manufacturer.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.manufacturer.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.specification.id_for_label }}" class="form-label">规格型号</label>
                                {{ form.specification }}
                                {% if form.specification.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.specification.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.model.id_for_label }}" class="form-label">产品型号</label>
                                {{ form.model }}
                                {% if form.model.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.model.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.product_number.id_for_label }}" class="form-label">产品编号 <small class="text-warning">(与产品批号至少填写其中一项)</small></label>
                                {{ form.product_number }}
                                {% if form.product_number.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.product_number.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.batch_number.id_for_label }}" class="form-label">产品批号 <small class="text-warning">(与产品编号至少填写其中一项)</small></label>
                                {{ form.batch_number }}
                                {% if form.batch_number.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.batch_number.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.production_date.id_for_label }}" class="form-label">生产日期</label>
                                {{ form.production_date }}
                                {% if form.production_date.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.production_date.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.expiry_date.id_for_label }}" class="form-label">有效期至</label>
                                {{ form.expiry_date }}
                                {% if form.expiry_date.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.expiry_date.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 提交按钮 -->
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="btn-group" role="group">
                        <button type="submit" name="action" value="save" class="btn btn-primary btn-lg">
                            <i class="bi bi-check-circle me-2"></i>
                            {% if is_edit %}更新报告{% else %}保存报告{% endif %}
                        </button>
                        <button type="submit" name="action" value="save_and_submit" class="btn btn-success btn-lg">
                            <i class="bi bi-send me-2"></i>
                            保存并提交
                        </button>
                        <a href="{% url 'reports:report_list' %}" class="btn btn-outline-secondary btn-lg">
                            <i class="bi bi-x-circle me-2"></i>
                            取消
                        </a>
                    </div>
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="bi bi-info-circle me-1"></i>
                            标有 <span class="text-danger">*</span> 的字段为必填项
                        </small>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block reports_extra_css %}
<style>
.form-control:focus, .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-control.is-invalid, .form-select.is-invalid {
    border-color: #dc3545;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.form-control.is-valid, .form-select.is-valid {
    border-color: #198754;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='m2.3 6.73.94-.94 1.38 1.38 3.68-3.68.94.94-4.62 4.62z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.card-header {
    position: relative;
    overflow: hidden;
}

.card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
    pointer-events: none;
}

.btn-group .btn {
    min-width: 120px;
}

.form-text {
    font-size: 0.875rem;
    color: #6c757d;
}

.required-field::after {
    content: ' *';
    color: #dc3545;
}

@media (max-width: 768px) {
    .btn-group {
        flex-direction: column;
    }
    .btn-group .btn {
        margin-bottom: 0.5rem;
    }
}
</style>
{% endblock %}

{% block reports_extra_js %}
<script>
$(document).ready(function() {
    // 表单验证
    const form = document.getElementById('reportForm');

    // Bootstrap 表单验证
    form.addEventListener('submit', function(event) {
        let isValid = form.checkValidity();

        // 验证产品编号或批号
        if (!validateProductInfo()) {
            isValid = false;
            // 显示错误提示
            if (!$('#product-batch-error').length) {
                $('#id_batch_number').closest('.mb-3').append(
                    '<div id="product-batch-error" class="invalid-feedback d-block">产品编号和产品批号至少需要填写其中一项</div>'
                );
            }
        } else {
            $('#product-batch-error').remove();
        }

        if (!isValid) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });

    // 实时验证
    $('input, select, textarea').on('blur', function() {
        validateField(this);
    });

    function validateField(field) {
        const $field = $(field);
        const value = $field.val().trim();
        const isRequired = $field.prop('required');

        // 清除之前的验证状态
        $field.removeClass('is-valid is-invalid');

        if (isRequired && !value) {
            $field.addClass('is-invalid');
            return false;
        }

        // 特定字段验证
        if (field.name === 'patient_age') {
            const age = parseInt(value);
            if (value && (isNaN(age) || age < 0 || age > 150)) {
                $field.addClass('is-invalid');
                return false;
            }
        }

        if (field.name === 'reporter_phone' || field.name === 'patient_contact') {
            const phoneRegex = /^1[3-9]\d{9}$/;
            if (value && !phoneRegex.test(value)) {
                $field.addClass('is-invalid');
                return false;
            }
        }

        if (field.name === 'event_date') {
            const eventDate = new Date(value);
            const today = new Date();
            if (value && eventDate > today) {
                $field.addClass('is-invalid');
                return false;
            }
        }

        if (value || !isRequired) {
            $field.addClass('is-valid');
        }

        return true;
    }

    // 日期字段限制
    const today = new Date().toISOString().split('T')[0];
    $('#id_event_date').attr('max', today);

    // 生产日期和有效期联动
    $('#id_production_date').on('change', function() {
        const productionDate = $(this).val();
        if (productionDate) {
            $('#id_expiry_date').attr('min', productionDate);
        }
    });

    // 伤害程度变化时的提示
    $('#id_injury_level').on('change', function() {
        const level = $(this).val();
        const $injuryDesc = $('#id_injury_description');

        if (level === 'severe' || level === 'death') {
            $injuryDesc.prop('required', true);
            $injuryDesc.closest('.mb-3').find('label').addClass('required-field');
            if (level === 'death') {
                $injuryDesc.attr('placeholder', '请详细描述死亡相关情况...');
            } else {
                $injuryDesc.attr('placeholder', '请详细描述严重伤害情况...');
            }
        } else {
            $injuryDesc.prop('required', false);
            $injuryDesc.closest('.mb-3').find('label').removeClass('required-field');
            $injuryDesc.attr('placeholder', '请描述伤害情况（可选）...');
        }
    });

    // 产品编号和批号验证
    function validateProductInfo() {
        const productNumber = $('#id_product_number').val().trim();
        const batchNumber = $('#id_batch_number').val().trim();
        const $productField = $('#id_product_number');
        const $batchField = $('#id_batch_number');

        // 清除之前的验证状态
        $productField.removeClass('is-valid is-invalid');
        $batchField.removeClass('is-valid is-invalid');

        if (!productNumber && !batchNumber) {
            $productField.addClass('is-invalid');
            $batchField.addClass('is-invalid');
            return false;
        } else {
            if (productNumber) $productField.addClass('is-valid');
            if (batchNumber) $batchField.addClass('is-valid');
            return true;
        }
    }

    // 产品编号和批号字段变化时验证
    $('#id_product_number, #id_batch_number').on('blur', function() {
        validateProductInfo();
    });

    // 页面离开前提醒
    let formChanged = false;
    $('input, select, textarea').on('change', function() {
        formChanged = true;
    });

    $(window).on('beforeunload', function() {
        if (formChanged) {
            return '您有未保存的更改，确定要离开吗？';
        }
    });

    // 表单提交时清除离开提醒
    form.addEventListener('submit', function() {
        formChanged = false;
    });
});
</script>

<!-- Toast 容器 -->
<div class="toast-container position-fixed bottom-0 end-0 p-3"></div>
{% endblock %}
