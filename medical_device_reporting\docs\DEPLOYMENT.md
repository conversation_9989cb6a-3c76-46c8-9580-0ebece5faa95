# 部署文档

## 📋 目录
- [部署概述](#部署概述)
- [环境要求](#环境要求)
- [生产环境部署](#生产环境部署)
- [Docker部署](#docker部署)
- [配置说明](#配置说明)
- [监控与维护](#监控与维护)

## 🎯 部署概述

### 部署架构
本系统支持多种部署方式，推荐使用Docker容器化部署：

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │   Web Server    │    │    Database     │
│    (Nginx)      │────│   (Django)      │────│    (MySQL)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         └──────────────│  Static Files   │──────────────┘
                        │   (Nginx/CDN)   │
                        └─────────────────┘
```

### 部署环境
- **开发环境**：本地开发和测试
- **测试环境**：功能测试和集成测试
- **预生产环境**：生产前的最终验证
- **生产环境**：正式运行环境

## ⚙️ 环境要求

### 系统要求
- **操作系统**：Ubuntu 20.04+ / CentOS 8+ / RHEL 8+
- **Python**：3.11+
- **数据库**：MySQL 8.0+
- **Web服务器**：Nginx 1.18+
- **内存**：最小4GB，推荐8GB+
- **存储**：最小20GB，推荐50GB+

### 软件依赖
```bash
# 系统包
sudo apt update
sudo apt install -y python3.11 python3.11-venv python3.11-dev
sudo apt install -y mysql-server mysql-client
sudo apt install -y nginx
sudo apt install -y git curl wget

# Python包管理
sudo apt install -y python3-pip
pip3 install --upgrade pip setuptools wheel
```

### 数据库配置
```sql
-- 创建数据库和用户
CREATE DATABASE medical_device_reporting CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'mdreport'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON medical_device_reporting.* TO 'mdreport'@'localhost';
FLUSH PRIVILEGES;

-- 优化配置
SET GLOBAL innodb_buffer_pool_size = **********;  -- 1GB
SET GLOBAL max_connections = 200;
SET GLOBAL query_cache_size = 67108864;  -- 64MB
```

## 🚀 生产环境部署

### 1. 代码部署
```bash
# 创建部署目录
sudo mkdir -p /opt/medical_device_reporting
sudo chown $USER:$USER /opt/medical_device_reporting
cd /opt/medical_device_reporting

# 克隆代码
git clone https://github.com/your-org/medical-device-reporting.git .
git checkout main

# 创建虚拟环境
python3.11 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements/production.txt
```

### 2. 环境配置
```bash
# 创建环境变量文件
cat > .env << EOF
# Django设置
DJANGO_SETTINGS_MODULE=config.settings.production
SECRET_KEY=your_very_long_and_secure_secret_key_here
DEBUG=False
ALLOWED_HOSTS=your-domain.com,www.your-domain.com

# 数据库配置
DATABASE_URL=mysql://mdreport:your_secure_password@localhost:3306/medical_device_reporting

# 缓存配置
REDIS_URL=redis://localhost:6379/0

# 邮件配置
EMAIL_HOST=smtp.your-email-provider.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password
EMAIL_USE_TLS=True

# 安全配置
SECURE_SSL_REDIRECT=True
SECURE_HSTS_SECONDS=31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS=True
SECURE_HSTS_PRELOAD=True
EOF

# 设置文件权限
chmod 600 .env
```

### 3. 数据库迁移
```bash
# 激活虚拟环境
source venv/bin/activate

# 运行迁移
python manage.py migrate

# 创建超级用户
python manage.py createsuperuser

# 收集静态文件
python manage.py collectstatic --noinput

# 加载初始数据（可选）
python manage.py loaddata fixtures/initial_data.json
```

### 4. Gunicorn配置
```bash
# 安装Gunicorn
pip install gunicorn

# 创建Gunicorn配置文件
cat > gunicorn.conf.py << EOF
bind = "127.0.0.1:8000"
workers = 4
worker_class = "sync"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 30
keepalive = 2
preload_app = True
daemon = False
user = "www-data"
group = "www-data"
tmp_upload_dir = None
errorlog = "/var/log/gunicorn/error.log"
accesslog = "/var/log/gunicorn/access.log"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
loglevel = "info"
EOF

# 创建日志目录
sudo mkdir -p /var/log/gunicorn
sudo chown www-data:www-data /var/log/gunicorn
```

### 5. Systemd服务配置
```bash
# 创建systemd服务文件
sudo cat > /etc/systemd/system/medical-device-reporting.service << EOF
[Unit]
Description=Medical Device Reporting Django Application
After=network.target mysql.service

[Service]
Type=notify
User=www-data
Group=www-data
WorkingDirectory=/opt/medical_device_reporting
Environment=PATH=/opt/medical_device_reporting/venv/bin
EnvironmentFile=/opt/medical_device_reporting/.env
ExecStart=/opt/medical_device_reporting/venv/bin/gunicorn config.wsgi:application -c gunicorn.conf.py
ExecReload=/bin/kill -s HUP \$MAINPID
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 启动服务
sudo systemctl daemon-reload
sudo systemctl enable medical-device-reporting
sudo systemctl start medical-device-reporting
sudo systemctl status medical-device-reporting
```

### 6. Nginx配置
```bash
# 创建Nginx配置文件
sudo cat > /etc/nginx/sites-available/medical-device-reporting << EOF
upstream django {
    server 127.0.0.1:8000;
}

server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;

    # SSL配置
    ssl_certificate /etc/ssl/certs/your-domain.crt;
    ssl_certificate_key /etc/ssl/private/your-domain.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;

    # 安全头
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";

    # 静态文件
    location /static/ {
        alias /opt/medical_device_reporting/staticfiles/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    location /media/ {
        alias /opt/medical_device_reporting/media/;
        expires 1y;
        add_header Cache-Control "public";
    }

    # Django应用
    location / {
        proxy_pass http://django;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # 文件上传大小限制
    client_max_body_size 10M;

    # 日志
    access_log /var/log/nginx/medical-device-reporting.access.log;
    error_log /var/log/nginx/medical-device-reporting.error.log;
}
EOF

# 启用站点
sudo ln -s /etc/nginx/sites-available/medical-device-reporting /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 🐳 Docker部署

### 1. Dockerfile
```dockerfile
# Dockerfile
FROM python:3.11-slim

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV DJANGO_SETTINGS_MODULE=config.settings.production

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    default-libmysqlclient-dev \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# 创建工作目录
WORKDIR /app

# 复制依赖文件
COPY requirements/ requirements/
RUN pip install --no-cache-dir -r requirements/production.txt

# 复制项目文件
COPY . .

# 收集静态文件
RUN python manage.py collectstatic --noinput

# 创建非root用户
RUN useradd --create-home --shell /bin/bash app
RUN chown -R app:app /app
USER app

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["gunicorn", "config.wsgi:application", "--bind", "0.0.0.0:8000"]
```

### 2. Docker Compose
```yaml
# docker-compose.yml
version: '3.8'

services:
  db:
    image: mysql:8.0
    environment:
      MYSQL_DATABASE: medical_device_reporting
      MYSQL_USER: mdreport
      MYSQL_PASSWORD: secure_password
      MYSQL_ROOT_PASSWORD: root_password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/my.cnf:/etc/mysql/conf.d/my.cnf
    ports:
      - "3306:3306"
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    restart: unless-stopped

  web:
    build: .
    environment:
      - DATABASE_URL=mysql://mdreport:secure_password@db:3306/medical_device_reporting
      - REDIS_URL=redis://redis:6379/0
    volumes:
      - static_volume:/app/staticfiles
      - media_volume:/app/media
    ports:
      - "8000:8000"
    depends_on:
      - db
      - redis
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - static_volume:/app/staticfiles
      - media_volume:/app/media
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - web
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:
  static_volume:
  media_volume:
```

### 3. 部署命令
```bash
# 构建和启动
docker-compose up -d --build

# 运行迁移
docker-compose exec web python manage.py migrate

# 创建超级用户
docker-compose exec web python manage.py createsuperuser

# 查看日志
docker-compose logs -f web

# 停止服务
docker-compose down

# 更新部署
git pull
docker-compose up -d --build
```

## 🔧 配置说明

### 生产环境设置
```python
# config/settings/production.py
import os
from .base import *

# 安全设置
DEBUG = False
SECRET_KEY = os.environ['SECRET_KEY']
ALLOWED_HOSTS = os.environ.get('ALLOWED_HOSTS', '').split(',')

# 数据库配置
DATABASES = {
    'default': dj_database_url.parse(os.environ['DATABASE_URL'])
}

# 缓存配置
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': os.environ.get('REDIS_URL', 'redis://localhost:6379/0'),
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}

# 静态文件配置
STATIC_ROOT = '/opt/medical_device_reporting/staticfiles'
MEDIA_ROOT = '/opt/medical_device_reporting/media'

# 安全配置
SECURE_SSL_REDIRECT = True
SECURE_HSTS_SECONDS = 31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_BROWSER_XSS_FILTER = True
X_FRAME_OPTIONS = 'DENY'

# 日志配置
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': '/var/log/django/medical_device_reporting.log',
            'maxBytes': 1024*1024*15,  # 15MB
            'backupCount': 10,
            'formatter': 'verbose',
        },
    },
    'root': {
        'handlers': ['file'],
        'level': 'INFO',
    },
}
```

### 环境变量模板
```bash
# .env.template
# Django设置
DJANGO_SETTINGS_MODULE=config.settings.production
SECRET_KEY=
DEBUG=False
ALLOWED_HOSTS=

# 数据库配置
DATABASE_URL=mysql://user:password@host:port/database

# 缓存配置
REDIS_URL=redis://localhost:6379/0

# 邮件配置
EMAIL_HOST=
EMAIL_PORT=587
EMAIL_HOST_USER=
EMAIL_HOST_PASSWORD=
EMAIL_USE_TLS=True

# 安全配置
SECURE_SSL_REDIRECT=True
SECURE_HSTS_SECONDS=31536000
```

## 📊 监控与维护

### 1. 系统监控
```bash
# 服务状态检查
sudo systemctl status medical-device-reporting
sudo systemctl status nginx
sudo systemctl status mysql

# 资源使用监控
htop
df -h
free -h

# 日志监控
tail -f /var/log/gunicorn/error.log
tail -f /var/log/nginx/medical-device-reporting.error.log
tail -f /var/log/django/medical_device_reporting.log
```

### 2. 数据库维护
```sql
-- 数据库优化
OPTIMIZE TABLE users_userprofile;
OPTIMIZE TABLE users_department;

-- 备份数据库
mysqldump -u root -p medical_device_reporting > backup_$(date +%Y%m%d_%H%M%S).sql

-- 恢复数据库
mysql -u root -p medical_device_reporting < backup_20240115_120000.sql
```

### 3. 自动化脚本
```bash
#!/bin/bash
# deploy.sh - 自动化部署脚本

set -e

echo "开始部署..."

# 拉取最新代码
git pull origin main

# 激活虚拟环境
source venv/bin/activate

# 安装依赖
pip install -r requirements/production.txt

# 运行迁移
python manage.py migrate

# 收集静态文件
python manage.py collectstatic --noinput

# 重启服务
sudo systemctl restart medical-device-reporting
sudo systemctl reload nginx

echo "部署完成！"
```

### 4. 备份策略
```bash
#!/bin/bash
# backup.sh - 自动备份脚本

BACKUP_DIR="/opt/backups/medical_device_reporting"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
mysqldump -u mdreport -p medical_device_reporting > $BACKUP_DIR/db_$DATE.sql

# 备份媒体文件
tar -czf $BACKUP_DIR/media_$DATE.tar.gz /opt/medical_device_reporting/media/

# 清理旧备份（保留30天）
find $BACKUP_DIR -name "*.sql" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete

echo "备份完成：$DATE"
```

### 5. 性能优化
```python
# 数据库连接池
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'OPTIONS': {
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
            'charset': 'utf8mb4',
        },
        'CONN_MAX_AGE': 600,  # 连接池
    }
}

# 缓存配置
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            'CONNECTION_POOL_KWARGS': {'max_connections': 50},
        }
    }
}
```

通过完整的部署文档，确保系统能够稳定、安全地运行在生产环境中。
