"""
通用基础模型定义
Common base models for Medical Device Reporting Platform
"""

import uuid
from django.db import models
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.contrib.auth.models import User


class BaseModel(models.Model):
    """
    基础模型抽象类
    
    提供所有业务模型的通用字段和方法：
    - 自动时间戳（创建时间、更新时间）
    - UUID主键支持
    - 通用验证方法
    - 软删除支持
    """
    
    # 时间戳字段
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='创建时间',
        help_text='记录创建的时间'
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='更新时间',
        help_text='记录最后更新的时间'
    )
    
    # 软删除支持
    is_deleted = models.BooleanField(
        default=False,
        verbose_name='是否删除',
        help_text='软删除标记，True表示已删除'
    )
    deleted_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='删除时间',
        help_text='记录删除的时间'
    )
    
    class Meta:
        abstract = True
        ordering = ['-created_at']
    
    def clean(self):
        """
        模型验证方法
        子类可以重写此方法添加自定义验证逻辑
        """
        super().clean()
        
        # 验证删除状态的一致性
        if self.is_deleted and not self.deleted_at:
            self.deleted_at = timezone.now()
        elif not self.is_deleted and self.deleted_at:
            self.deleted_at = None
    
    def save(self, *args, **kwargs):
        """
        保存方法，在保存前执行验证
        """
        # 执行模型验证
        self.full_clean()
        
        # 调用父类保存方法
        super().save(*args, **kwargs)
    
    def soft_delete(self, user=None):
        """
        软删除方法
        
        Args:
            user: 执行删除操作的用户（可选）
        """
        self.is_deleted = True
        self.deleted_at = timezone.now()
        self.save(update_fields=['is_deleted', 'deleted_at'])
    
    def restore(self):
        """
        恢复软删除的记录
        """
        self.is_deleted = False
        self.deleted_at = None
        self.save(update_fields=['is_deleted', 'deleted_at'])
    
    @property
    def is_active(self):
        """
        检查记录是否处于活跃状态（未被软删除）
        """
        return not self.is_deleted


class UUIDBaseModel(BaseModel):
    """
    使用UUID作为主键的基础模型
    
    适用于需要全局唯一标识符的业务模型
    """
    
    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
        verbose_name='唯一标识符'
    )
    
    class Meta:
        abstract = True


class AuditableModel(BaseModel):
    """
    可审计的基础模型
    
    记录创建者和最后修改者信息
    """
    
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='%(class)s_created',
        verbose_name='创建者'
    )
    updated_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='%(class)s_updated',
        verbose_name='最后修改者'
    )
    
    class Meta:
        abstract = True
    
    def save(self, *args, **kwargs):
        """
        保存时自动设置创建者和修改者
        """
        # 从kwargs中获取用户信息
        user = kwargs.pop('user', None)
        
        if user and isinstance(user, User):
            if not self.pk:  # 新建记录
                self.created_by = user
            self.updated_by = user
        
        super().save(*args, **kwargs)


class VersionedModel(BaseModel):
    """
    版本控制的基础模型
    
    支持记录版本号和版本说明
    """
    
    version = models.PositiveIntegerField(
        default=1,
        verbose_name='版本号'
    )
    version_notes = models.TextField(
        blank=True,
        verbose_name='版本说明',
        help_text='记录本版本的变更说明'
    )
    
    class Meta:
        abstract = True
    
    def save(self, *args, **kwargs):
        """
        保存时自动递增版本号
        """
        if self.pk:  # 更新现有记录
            # 检查是否有实际字段变更
            if self.has_changed():
                self.version += 1
        
        super().save(*args, **kwargs)
    
    def has_changed(self):
        """
        检查模型是否有字段变更
        """
        if not self.pk:
            return True
        
        try:
            original = self.__class__.objects.get(pk=self.pk)
            for field in self._meta.fields:
                if field.name in ['version', 'updated_at', 'version_notes']:
                    continue
                if getattr(self, field.name) != getattr(original, field.name):
                    return True
            return False
        except self.__class__.DoesNotExist:
            return True


class StatusModel(BaseModel):
    """
    带状态的基础模型
    
    提供通用的状态管理功能
    """
    
    STATUS_CHOICES = [
        ('draft', '草稿'),
        ('active', '活跃'),
        ('inactive', '非活跃'),
        ('archived', '已归档'),
    ]
    
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='draft',
        verbose_name='状态'
    )
    status_changed_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='状态变更时间'
    )
    
    class Meta:
        abstract = True
    
    def save(self, *args, **kwargs):
        """
        保存时检查状态变更
        """
        if self.pk:
            try:
                original = self.__class__.objects.get(pk=self.pk)
                if original.status != self.status:
                    self.status_changed_at = timezone.now()
            except self.__class__.DoesNotExist:
                pass
        
        super().save(*args, **kwargs)
    
    def activate(self):
        """激活记录"""
        self.status = 'active'
        self.save(update_fields=['status', 'status_changed_at'])
    
    def deactivate(self):
        """停用记录"""
        self.status = 'inactive'
        self.save(update_fields=['status', 'status_changed_at'])
    
    def archive(self):
        """归档记录"""
        self.status = 'archived'
        self.save(update_fields=['status', 'status_changed_at'])
    
    @property
    def is_active_status(self):
        """检查是否为活跃状态"""
        return self.status == 'active'
