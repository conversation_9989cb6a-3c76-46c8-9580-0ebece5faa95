{% extends 'reports/base.html' %}
{% load static %}

{% block page_title %}分步创建报告 - 第{{ step }}步{% endblock %}
{% block page_heading %}分步创建报告{% endblock %}
{% block page_description %}{{ step_title }} ({{ step }}/{{ total_steps }}){% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'reports:report_list' %}">报告列表</a></li>
<li class="breadcrumb-item active">分步创建</li>
{% endblock %}

{% block page_actions %}
<div class="btn-group" role="group">
    <a href="{% url 'reports:report_list' %}" class="btn btn-outline-secondary">
        <i class="bi bi-arrow-left me-2"></i>
        返回列表
    </a>
    <a href="{% url 'reports:report_create' %}" class="btn btn-outline-primary">
        <i class="bi bi-file-text me-2"></i>
        完整表单
    </a>
</div>
{% endblock %}

{% block reports_content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <!-- 进度条 -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-body">
                <div class="step-progress">
                    <div class="progress mb-3" style="height: 8px;">
                        <div class="progress-bar bg-primary" role="progressbar" 
                             style="width: {{ progress_percent }}%" 
                             aria-valuenow="{{ progress_percent }}" 
                             aria-valuemin="0" 
                             aria-valuemax="100">
                        </div>
                    </div>
                    <div class="step-indicators">
                        <div class="row text-center">
                            <div class="col-3">
                                <div class="step-indicator {% if step >= 1 %}active{% endif %} {% if step > 1 %}completed{% endif %}">
                                    <div class="step-number">
                                        {% if step > 1 %}
                                            <i class="bi bi-check"></i>
                                        {% else %}
                                            1
                                        {% endif %}
                                    </div>
                                    <div class="step-label">上报人信息</div>
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="step-indicator {% if step >= 2 %}active{% endif %} {% if step > 2 %}completed{% endif %}">
                                    <div class="step-number">
                                        {% if step > 2 %}
                                            <i class="bi bi-check"></i>
                                        {% else %}
                                            2
                                        {% endif %}
                                    </div>
                                    <div class="step-label">患者信息</div>
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="step-indicator {% if step >= 3 %}active{% endif %} {% if step > 3 %}completed{% endif %}">
                                    <div class="step-number">
                                        {% if step > 3 %}
                                            <i class="bi bi-check"></i>
                                        {% else %}
                                            3
                                        {% endif %}
                                    </div>
                                    <div class="step-label">事件信息</div>
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="step-indicator {% if step >= 4 %}active{% endif %} {% if step > 4 %}completed{% endif %}">
                                    <div class="step-number">
                                        {% if step > 4 %}
                                            <i class="bi bi-check"></i>
                                        {% else %}
                                            4
                                        {% endif %}
                                    </div>
                                    <div class="step-label">器械信息</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 表单内容 -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    {% if step == 1 %}
                        <i class="bi bi-person-badge me-2"></i>
                        上报人信息
                    {% elif step == 2 %}
                        <i class="bi bi-person me-2"></i>
                        患者信息
                    {% elif step == 3 %}
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        事件信息
                    {% elif step == 4 %}
                        <i class="bi bi-gear me-2"></i>
                        医疗器械信息
                    {% endif %}
                </h5>
            </div>
            <div class="card-body">
                <form method="post" id="stepForm" novalidate>
                    {% csrf_token %}
                    
                    {% if step == 1 %}
                    <!-- 第一步：上报人信息 -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.reporter.id_for_label }}" class="form-label">上报人 <span class="text-danger">*</span></label>
                                {{ form.reporter }}
                                {% if form.reporter.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.reporter.errors.0 }}
                                </div>
                                {% endif %}
                                <div class="form-text">
                                    <i class="bi bi-info-circle me-1"></i>
                                    当前登录用户：{{ user.userprofile.display_name }} ({{ user.userprofile.account_number }})
                                    <small class="text-muted d-block">此字段已自动设置，无需修改</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.department.id_for_label }}" class="form-label">科室</label>
                                {{ form.department }}
                                {% if form.department.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.department.errors.0 }}
                                </div>
                                {% endif %}
                                <div class="form-text">
                                    <i class="bi bi-info-circle me-1"></i>
                                    当前用户所属科室：{{ user.userprofile.department.name|default:"未分配科室" }}
                                    <small class="text-muted d-block">此字段已自动设置，无需修改</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.reporter_phone.id_for_label }}" class="form-label">联系电话</label>
                                {{ form.reporter_phone }}
                                {% if form.reporter_phone.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.reporter_phone.errors.0 }}
                                </div>
                                {% endif %}
                                <div class="form-text">请输入有效的手机号码</div>
                            </div>
                        </div>
                    </div>
                    
                    {% elif step == 2 %}
                    <!-- 第二步：患者信息 -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.patient_name.id_for_label }}" class="form-label">患者姓名 <small class="text-muted">(可选)</small></label>
                                {{ form.patient_name }}
                                {% if form.patient_name.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.patient_name.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="{{ form.patient_age.id_for_label }}" class="form-label">年龄 <small class="text-muted">(可选)</small></label>
                                {{ form.patient_age }}
                                {% if form.patient_age.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.patient_age.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="{{ form.patient_gender.id_for_label }}" class="form-label">性别 <small class="text-muted">(可选)</small></label>
                                {{ form.patient_gender }}
                                {% if form.patient_gender.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.patient_gender.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.patient_contact.id_for_label }}" class="form-label">联系方式</label>
                                {{ form.patient_contact }}
                                {% if form.patient_contact.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.patient_contact.errors.0 }}
                                </div>
                                {% endif %}
                                <div class="form-text">患者或家属的联系方式</div>
                            </div>
                        </div>
                    </div>
                    
                    {% elif step == 3 %}
                    <!-- 第三步：事件信息 -->
                    <div class="row">
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="{{ form.device_malfunction.id_for_label }}" class="form-label">器械故障表现 <span class="text-danger">*</span></label>
                                {{ form.device_malfunction }}
                                {% if form.device_malfunction.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.device_malfunction.errors.0 }}
                                </div>
                                {% endif %}
                                <div class="form-text">请详细描述医疗器械的故障表现</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.event_date.id_for_label }}" class="form-label">事件日期 <span class="text-danger">*</span></label>
                                {{ form.event_date }}
                                {% if form.event_date.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.event_date.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.injury_level.id_for_label }}" class="form-label">伤害程度 <span class="text-danger">*</span></label>
                                {{ form.injury_level }}
                                {% if form.injury_level.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.injury_level.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.injury_description.id_for_label }}" class="form-label">伤害描述</label>
                                {{ form.injury_description }}
                                {% if form.injury_description.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.injury_description.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="{{ form.event_description.id_for_label }}" class="form-label">事件描述 <span class="text-danger">*</span></label>
                                {{ form.event_description }}
                                {% if form.event_description.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.event_description.errors.0 }}
                                </div>
                                {% endif %}
                                <div class="form-text">请详细描述不良事件的发生过程</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.initial_cause_analysis.id_for_label }}" class="form-label">初步原因分析</label>
                                {{ form.initial_cause_analysis }}
                                {% if form.initial_cause_analysis.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.initial_cause_analysis.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.initial_treatment.id_for_label }}" class="form-label">初步处理措施</label>
                                {{ form.initial_treatment }}
                                {% if form.initial_treatment.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.initial_treatment.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    {% elif step == 4 %}
                    <!-- 第四步：器械信息 -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.device_name.id_for_label }}" class="form-label">器械名称 <span class="text-danger">*</span></label>
                                {{ form.device_name }}
                                {% if form.device_name.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.device_name.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.registration_number.id_for_label }}" class="form-label">注册证号 <span class="text-danger">*</span></label>
                                {{ form.registration_number }}
                                {% if form.registration_number.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.registration_number.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.manufacturer.id_for_label }}" class="form-label">生产企业 <span class="text-danger">*</span></label>
                                {{ form.manufacturer }}
                                {% if form.manufacturer.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.manufacturer.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.specification.id_for_label }}" class="form-label">规格型号</label>
                                {{ form.specification }}
                                {% if form.specification.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.specification.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.model.id_for_label }}" class="form-label">产品型号</label>
                                {{ form.model }}
                                {% if form.model.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.model.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.product_number.id_for_label }}" class="form-label">产品编号 <small class="text-warning">(与产品批号至少填写其中一项)</small></label>
                                {{ form.product_number }}
                                {% if form.product_number.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.product_number.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.batch_number.id_for_label }}" class="form-label">产品批号 <small class="text-warning">(与产品编号至少填写其中一项)</small></label>
                                {{ form.batch_number }}
                                {% if form.batch_number.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.batch_number.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.production_date.id_for_label }}" class="form-label">生产日期</label>
                                {{ form.production_date }}
                                {% if form.production_date.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.production_date.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.expiry_date.id_for_label }}" class="form-label">有效期至</label>
                                {{ form.expiry_date }}
                                {% if form.expiry_date.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.expiry_date.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    <!-- 导航按钮 -->
                    <div class="d-flex justify-content-between mt-4">
                        <div>
                            {% if can_go_back %}
                            <a href="{% url 'reports:report_step_create' step=step|add:'-1' %}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left me-2"></i>
                                上一步
                            </a>
                            {% endif %}
                        </div>
                        <div>
                            {% if step < total_steps %}
                            <button type="submit" class="btn btn-primary">
                                下一步
                                <i class="bi bi-arrow-right ms-2"></i>
                            </button>
                            {% else %}
                            <button type="submit" class="btn btn-success">
                                <i class="bi bi-check-circle me-2"></i>
                                完成创建
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block reports_extra_css %}
<style>
.step-progress {
    margin-bottom: 0;
}

.step-indicators {
    margin-top: 15px;
}

.step-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.step-indicator.active .step-number {
    background-color: #0d6efd;
    color: white;
}

.step-indicator.completed .step-number {
    background-color: #198754;
    color: white;
}

.step-label {
    font-size: 0.875rem;
    color: #6c757d;
    text-align: center;
    font-weight: 500;
}

.step-indicator.active .step-label {
    color: #0d6efd;
    font-weight: 600;
}

.step-indicator.completed .step-label {
    color: #198754;
    font-weight: 600;
}

/* 连接线 */
.step-indicators .row::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 12.5%;
    right: 12.5%;
    height: 2px;
    background-color: #e9ecef;
    z-index: -1;
}

.form-control:focus, .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.card-header {
    position: relative;
    overflow: hidden;
}

.card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
    pointer-events: none;
}

@media (max-width: 768px) {
    .step-label {
        font-size: 0.75rem;
    }

    .step-number {
        width: 35px;
        height: 35px;
        font-size: 0.875rem;
    }

    .d-flex.justify-content-between {
        flex-direction: column;
        gap: 1rem;
    }

    .d-flex.justify-content-between > div {
        text-align: center;
    }
}
</style>
{% endblock %}

{% block reports_extra_js %}
<script>
$(document).ready(function() {
    // 表单验证
    const form = document.getElementById('stepForm');

    // Bootstrap 表单验证
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });

    // 实时验证
    $('input, select, textarea').on('blur', function() {
        validateField(this);
    });

    function validateField(field) {
        const $field = $(field);
        const value = $field.val().trim();
        const isRequired = $field.prop('required');

        // 清除之前的验证状态
        $field.removeClass('is-valid is-invalid');

        if (isRequired && !value) {
            $field.addClass('is-invalid');
            return false;
        }

        // 特定字段验证
        if (field.name === 'patient_age') {
            const age = parseInt(value);
            if (value && (isNaN(age) || age < 0 || age > 150)) {
                $field.addClass('is-invalid');
                return false;
            }
        }

        if (field.name === 'reporter_phone' || field.name === 'patient_contact') {
            const phoneRegex = /^1[3-9]\d{9}$/;
            if (value && !phoneRegex.test(value)) {
                $field.addClass('is-invalid');
                return false;
            }
        }

        if (field.name === 'event_date') {
            const eventDate = new Date(value);
            const today = new Date();
            if (value && eventDate > today) {
                $field.addClass('is-invalid');
                return false;
            }
        }

        if (value || !isRequired) {
            $field.addClass('is-valid');
        }

        return true;
    }

    // 日期字段限制
    const today = new Date().toISOString().split('T')[0];
    $('#id_event_date').attr('max', today);

    // 生产日期和有效期联动
    $('#id_production_date').on('change', function() {
        const productionDate = $(this).val();
        if (productionDate) {
            $('#id_expiry_date').attr('min', productionDate);
        }
    });

    // 伤害程度变化时的提示
    $('#id_injury_level').on('change', function() {
        const level = $(this).val();
        const $injuryDesc = $('#id_injury_description');

        if (level === 'severe' || level === 'death') {
            $injuryDesc.prop('required', true);
            if (level === 'death') {
                $injuryDesc.attr('placeholder', '请详细描述死亡相关情况...');
            } else {
                $injuryDesc.attr('placeholder', '请详细描述严重伤害情况...');
            }
        } else {
            $injuryDesc.prop('required', false);
            $injuryDesc.attr('placeholder', '请描述伤害情况（可选）...');
        }
    });

    // 器械故障变化时的提示
    $('#id_device_malfunction').on('change', function() {
        const isMalfunction = $(this).is(':checked');
        const $eventDesc = $('#id_event_description');

        if (isMalfunction) {
            $eventDesc.attr('placeholder', '请详细描述器械故障的具体情况和发生过程...');
        } else {
            $eventDesc.attr('placeholder', '请详细描述不良事件的发生过程...');
        }
    });

    // 第一步：限制上报人和科室字段的修改
    {% if current_step == 1 %}
    // 为上报人和科室字段添加视觉提示和防止修改
    const $reporterField = $('#id_reporter');
    const $departmentField = $('#id_department');

    // 添加提示样式
    $reporterField.addClass('bg-light');
    $departmentField.addClass('bg-light');

    // 防止用户修改（但保持表单提交功能）
    $reporterField.on('mousedown keydown', function(e) {
        e.preventDefault();
        $(this).blur();
        return false;
    });

    $departmentField.on('mousedown keydown', function(e) {
        e.preventDefault();
        $(this).blur();
        return false;
    });

    // 添加提示信息
    $reporterField.attr('title', '当前登录用户，系统自动设置');
    $departmentField.attr('title', '当前用户所属科室，系统自动设置');
    {% endif %}
});
</script>
{% endblock %}
