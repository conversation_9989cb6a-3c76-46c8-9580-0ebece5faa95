# 医疗器械不良事件上报平台环境变量配置模板
# Medical Device Reporting Platform Environment Variables Template

# =============================================================================
# Django 核心配置
# =============================================================================

# Django密钥 - 生产环境必须使用强密钥
SECRET_KEY=your-secret-key-here-change-in-production

# 调试模式 - 生产环境必须设置为False
DEBUG=True

# 允许的主机列表 - 生产环境必须配置正确的域名
ALLOWED_HOSTS=localhost,127.0.0.1

# =============================================================================
# 数据库配置
# =============================================================================

# MySQL数据库配置
DB_NAME=medical_device_reporting_dev
DB_USER=root
DB_PASSWORD=your-mysql-password-here
DB_HOST=localhost
DB_PORT=3306

# =============================================================================
# 邮件配置
# =============================================================================

# SMTP邮件服务器配置
EMAIL_HOST=smtp.example.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password
DEFAULT_FROM_EMAIL=<EMAIL>

# =============================================================================
# 缓存配置
# =============================================================================

# Redis缓存配置（生产环境）
REDIS_URL=redis://127.0.0.1:6379/1

# =============================================================================
# 安全配置
# =============================================================================

# HTTPS重定向（生产环境）
SECURE_SSL_REDIRECT=True

# =============================================================================
# 第三方服务配置
# =============================================================================

# Sentry错误跟踪（可选）
SENTRY_DSN=https://<EMAIL>/project-id

# =============================================================================
# 文件存储配置
# =============================================================================

# 静态文件和媒体文件配置
STATIC_URL=/static/
MEDIA_URL=/media/

# =============================================================================
# 日志配置
# =============================================================================

# 日志级别
LOG_LEVEL=INFO

# =============================================================================
# 业务配置
# =============================================================================

# 平台相关配置
PLATFORM_NAME=医疗器械不良事件上报平台
PLATFORM_VERSION=1.0.0
CONTACT_EMAIL=<EMAIL>

# 文件上传限制（MB）
MAX_UPLOAD_SIZE=10

# 会话超时时间（秒）
SESSION_TIMEOUT=3600
