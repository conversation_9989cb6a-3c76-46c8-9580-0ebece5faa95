"""
权限检查模板标签
Permission Check Template Tags for Medical Device Reporting Platform
"""

from django import template
from django.contrib.auth.models import User

register = template.Library()


@register.filter
def has_permission(user, permission_name):
    """
    检查用户是否有指定权限
    
    Args:
        user: 用户对象
        permission_name: 权限名称
        
    Returns:
        bool: 是否有权限
        
    Usage:
        {% if user|has_permission:"reports.add_report" %}
            <a href="{% url 'reports:report_create' %}">创建报告</a>
        {% endif %}
    """
    if not user or not user.is_authenticated:
        return False
    
    return user.has_perm(permission_name)


@register.filter
def is_admin(user):
    """
    检查用户是否为管理员
    
    Args:
        user: 用户对象
        
    Returns:
        bool: 是否为管理员
        
    Usage:
        {% if user|is_admin %}
            <a href="{% url 'users:user_list' %}">用户管理</a>
        {% endif %}
    """
    if not user or not user.is_authenticated:
        return False
    
    try:
        return user.profile.is_admin
    except AttributeError:
        return False


@register.filter
def is_department_member(user):
    """
    检查用户是否为科室成员
    
    Args:
        user: 用户对象
        
    Returns:
        bool: 是否为科室成员
        
    Usage:
        {% if user|is_department_member %}
            <span class="badge bg-info">科室成员</span>
        {% endif %}
    """
    if not user or not user.is_authenticated:
        return False
    
    try:
        return user.profile.department is not None
    except AttributeError:
        return False


@register.filter
def can_review_reports(user):
    """
    检查用户是否可以审核报告
    
    Args:
        user: 用户对象
        
    Returns:
        bool: 是否可以审核报告
        
    Usage:
        {% if user|can_review_reports %}
            <a href="{% url 'reports:pending_review' %}">待审核报告</a>
        {% endif %}
    """
    if not user or not user.is_authenticated:
        return False
    
    try:
        # 只有管理员可以审核报告
        return user.profile.is_admin
    except AttributeError:
        return False


@register.filter
def can_manage_users(user):
    """
    检查用户是否可以管理用户
    
    Args:
        user: 用户对象
        
    Returns:
        bool: 是否可以管理用户
        
    Usage:
        {% if user|can_manage_users %}
            <a href="{% url 'users:user_list' %}">用户管理</a>
        {% endif %}
    """
    if not user or not user.is_authenticated:
        return False
    
    try:
        return user.profile.is_admin
    except AttributeError:
        return False


@register.filter
def can_view_statistics(user):
    """
    检查用户是否可以查看统计信息
    
    Args:
        user: 用户对象
        
    Returns:
        bool: 是否可以查看统计信息
        
    Usage:
        {% if user|can_view_statistics %}
            <a href="{% url 'reports:statistics' %}">统计报表</a>
        {% endif %}
    """
    if not user or not user.is_authenticated:
        return False
    
    try:
        return user.profile.is_admin
    except AttributeError:
        return False


@register.simple_tag
def user_role_display(user):
    """
    获取用户角色显示名称
    
    Args:
        user: 用户对象
        
    Returns:
        str: 角色显示名称
        
    Usage:
        {% user_role_display user %}
    """
    if not user or not user.is_authenticated:
        return "未登录"
    
    try:
        if user.profile.is_admin:
            return "管理员"
        elif user.profile.department:
            return f"{user.profile.department.name} 成员"
        else:
            return "普通用户"
    except AttributeError:
        return "普通用户"


@register.simple_tag
def user_department_display(user):
    """
    获取用户科室显示名称
    
    Args:
        user: 用户对象
        
    Returns:
        str: 科室显示名称
        
    Usage:
        {% user_department_display user %}
    """
    if not user or not user.is_authenticated:
        return "未指定"
    
    try:
        if user.profile.department:
            return user.profile.department.name
        else:
            return "未指定科室"
    except AttributeError:
        return "未指定科室"


@register.inclusion_tag('common/user_menu.html', takes_context=True)
def user_menu(context):
    """
    用户菜单组件
    
    Args:
        context: 模板上下文
        
    Returns:
        dict: 菜单数据
        
    Usage:
        {% user_menu %}
    """
    user = context.get('user')
    
    if not user or not user.is_authenticated:
        return {'user': None, 'menu_items': []}
    
    menu_items = []
    
    # 基础菜单项
    menu_items.append({
        'title': '用户中心',
        'url': 'users:dashboard',
        'icon': 'bi-house',
        'permission': None
    })
    
    menu_items.append({
        'title': '报告管理',
        'url': 'reports:dashboard',
        'icon': 'bi-file-medical',
        'permission': None
    })
    
    # 管理员菜单项
    if is_admin(user):
        menu_items.extend([
            {
                'title': '用户管理',
                'url': 'users:user_list',
                'icon': 'bi-people',
                'permission': 'admin'
            },
            {
                'title': '科室管理',
                'url': 'users:department_list',
                'icon': 'bi-building',
                'permission': 'admin'
            },
            {
                'title': '报告审核',
                'url': 'reports:pending_review',
                'icon': 'bi-eye',
                'permission': 'admin'
            }
        ])
    
    return {
        'user': user,
        'menu_items': menu_items,
        'is_admin': is_admin(user),
        'department': user_department_display(user),
        'role': user_role_display(user)
    }


@register.inclusion_tag('common/breadcrumb.html')
def breadcrumb(*args):
    """
    面包屑导航组件
    
    Args:
        *args: 面包屑项目列表，每项为 (title, url) 元组
        
    Returns:
        dict: 面包屑数据
        
    Usage:
        {% breadcrumb "首页,users:dashboard" "报告管理,reports:dashboard" "报告列表," %}
    """
    items = []
    
    for arg in args:
        if ',' in arg:
            title, url = arg.split(',', 1)
            items.append({
                'title': title.strip(),
                'url': url.strip() if url.strip() else None
            })
    
    return {'items': items}
