{% extends 'reports/base.html' %}
{% load static %}

{% block page_title %}严重事件报告{% endblock %}
{% block page_heading %}严重事件报告{% endblock %}
{% block page_description %}严重伤害和死亡事件报告列表{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item active">严重事件报告</li>
{% endblock %}

{% block page_actions %}
<div class="btn-group" role="group">
    <a href="{% url 'reports:report_list' %}" class="btn btn-outline-primary">
        <i class="bi bi-list me-2"></i>
        全部报告
    </a>
    <a href="{% url 'reports:pending_review' %}" class="btn btn-outline-warning">
        <i class="bi bi-eye me-2"></i>
        待审核
    </a>
</div>
{% endblock %}

{% block reports_content %}
<!-- 严重程度统计 -->
<div class="row mb-4">
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="stat-card card border-0 shadow-sm border-danger">
            <div class="card-body text-center">
                <div class="stat-icon bg-danger">
                    <i class="bi bi-heart-pulse"></i>
                </div>
                <h4 class="stat-number text-danger">{{ death_count|default:0 }}</h4>
                <p class="stat-label">死亡事件</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="stat-card card border-0 shadow-sm border-warning">
            <div class="card-body text-center">
                <div class="stat-icon bg-warning">
                    <i class="bi bi-exclamation-triangle"></i>
                </div>
                <h4 class="stat-number text-warning">{{ severe_count|default:0 }}</h4>
                <p class="stat-label">严重伤害</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="stat-card card border-0 shadow-sm border-info">
            <div class="card-body text-center">
                <div class="stat-icon bg-info">
                    <i class="bi bi-calendar-week"></i>
                </div>
                <h4 class="stat-number text-info">{{ week_count|default:0 }}</h4>
                <p class="stat-label">本周新增</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="stat-card card border-0 shadow-sm border-secondary">
            <div class="card-body text-center">
                <div class="stat-icon bg-secondary">
                    <i class="bi bi-graph-up"></i>
                </div>
                <h4 class="stat-number text-secondary">{{ total_count|default:0 }}</h4>
                <p class="stat-label">总计</p>
            </div>
        </div>
    </div>
</div>

<!-- 筛选面板 -->
<div class="filter-panel card mb-4">
    <div class="card-header bg-danger text-white">
        <h6 class="card-title mb-0">
            <i class="bi bi-funnel me-2"></i>
            筛选条件
        </h6>
    </div>
    <div class="card-body">
        <form id="filterForm" class="row g-3">
            <div class="col-md-3">
                <label for="injuryLevelFilter" class="form-label">伤害程度</label>
                <select class="form-select" id="injuryLevelFilter" name="injury_level">
                    <option value="">全部严重事件</option>
                    <option value="severe" {% if request.GET.injury_level == 'severe' %}selected{% endif %}>严重伤害</option>
                    <option value="death" {% if request.GET.injury_level == 'death' %}selected{% endif %}>死亡</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="dateRangeFilter" class="form-label">时间范围</label>
                <select class="form-select" id="dateRangeFilter" name="days">
                    <option value="">全部时间</option>
                    <option value="7" {% if request.GET.days == '7' %}selected{% endif %}>最近7天</option>
                    <option value="30" {% if request.GET.days == '30' %}selected{% endif %}>最近30天</option>
                    <option value="90" {% if request.GET.days == '90' %}selected{% endif %}>最近90天</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="statusFilter" class="form-label">状态</label>
                <select class="form-select" id="statusFilter" name="status">
                    <option value="">全部状态</option>
                    <option value="submitted" {% if request.GET.status == 'submitted' %}selected{% endif %}>已提交</option>
                    <option value="under_review" {% if request.GET.status == 'under_review' %}selected{% endif %}>审核中</option>
                    <option value="approved" {% if request.GET.status == 'approved' %}selected{% endif %}>已批准</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="searchInput" class="form-label">搜索</label>
                <div class="input-group">
                    <input type="text" class="form-control" id="searchInput" name="search" value="{{ request.GET.search }}" placeholder="报告编号、器械名称">
                    <button class="btn btn-outline-secondary" type="submit">
                        <i class="bi bi-search"></i>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 严重事件列表 -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-danger text-white">
        <div class="row align-items-center">
            <div class="col">
                <h5 class="card-title mb-0">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    严重事件列表
                    <span class="badge bg-light text-dark ms-2">{{ total_count|default:0 }}</span>
                </h5>
            </div>
            <div class="col-auto">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-light btn-sm" id="refreshBtn">
                        <i class="bi bi-arrow-clockwise me-1"></i>
                        刷新
                    </button>
                    <button type="button" class="btn btn-outline-light btn-sm" id="exportBtn">
                        <i class="bi bi-download me-1"></i>
                        导出
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        {% if reports %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>报告编号</th>
                        <th>器械名称</th>
                        <th>上报人</th>
                        <th>科室</th>
                        <th>患者信息</th>
                        <th>事件日期</th>
                        <th>伤害程度</th>
                        <th>状态</th>
                        <th>提交时间</th>
                        <th width="120">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for report in reports %}
                    <tr class="{% if report.injury_level == 'death' %}table-danger{% else %}table-warning{% endif %}">
                        <td>
                            <a href="{% url 'reports:report_detail' report_id=report.id %}" class="text-decoration-none fw-bold">
                                {{ report.report_number }}
                            </a>
                            {% if report.injury_level == 'death' %}
                            <span class="badge bg-danger ms-1">死亡</span>
                            {% else %}
                            <span class="badge bg-warning ms-1">严重</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="device-info">
                                <div class="fw-bold">{{ report.device_name|truncatechars:20 }}</div>
                                <small class="text-muted">{{ report.manufacturer|truncatechars:15 }}</small>
                            </div>
                        </td>
                        <td>{{ report.reporter.username }}</td>
                        <td>{{ report.department.name|default:"未指定" }}</td>
                        <td>
                            <div class="patient-info">
                                <div>{{ report.patient_name }}</div>
                                <small class="text-muted">{{ report.patient_age }}岁 {{ report.get_patient_gender_display }}</small>
                            </div>
                        </td>
                        <td>{{ report.event_date|date:"Y-m-d" }}</td>
                        <td>
                            <span class="badge bg-{% if report.injury_level == 'death' %}danger{% else %}warning{% endif %} fs-6">
                                {{ report.get_injury_level_display }}
                            </span>
                        </td>
                        <td>
                            <span class="badge bg-{% if report.status == 'approved' %}success{% elif report.status == 'under_review' %}warning{% elif report.status == 'submitted' %}info{% else %}secondary{% endif %}">
                                {{ report.get_status_display }}
                            </span>
                        </td>
                        <td>
                            <div>{{ report.submitted_at|date:"m-d H:i" }}</div>
                            <small class="text-muted">{{ report.submitted_at|timesince }}前</small>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{% url 'reports:report_detail' report_id=report.id %}" class="btn btn-outline-primary btn-sm" title="查看详情">
                                    <i class="bi bi-eye"></i>
                                </a>
                                {% if report.can_review and user.profile.is_admin %}
                                <a href="{% url 'reports:report_review' report_id=report.id %}" class="btn btn-warning btn-sm" title="审核">
                                    <i class="bi bi-eye-fill"></i>
                                </a>
                                {% endif %}
                                <button type="button" class="btn btn-outline-info btn-sm" onclick="showEventDetails('{{ report.id }}')" title="事件详情">
                                    <i class="bi bi-info-circle"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- 分页 -->
        {% if reports.has_other_pages %}
        <nav aria-label="严重事件报告分页" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if reports.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">首页</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ reports.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">上一页</a>
                </li>
                {% endif %}

                {% for num in reports.paginator.page_range %}
                {% if reports.number == num %}
                <li class="page-item active">
                    <span class="page-link">{{ num }}</span>
                </li>
                {% elif num > reports.number|add:'-3' and num < reports.number|add:'3' %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ num }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ num }}</a>
                </li>
                {% endif %}
                {% endfor %}

                {% if reports.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ reports.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">下一页</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ reports.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">末页</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
        
        {% else %}
        <!-- 空状态 -->
        <div class="text-center py-5">
            <i class="bi bi-shield-check display-1 text-success"></i>
            <h4 class="mt-3 text-muted">暂无严重事件</h4>
            <p class="text-muted">在选定的时间范围内没有严重伤害或死亡事件</p>
            <a href="{% url 'reports:report_list' %}" class="btn btn-primary">
                <i class="bi bi-list me-2"></i>
                查看全部报告
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- 事件详情模态框 -->
<div class="modal fade" id="eventDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    严重事件详情
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="eventDetailsContent">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="viewFullReportBtn">查看完整报告</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block reports_extra_css %}
<style>
.stat-card {
    transition: transform 0.2s;
}
.stat-card:hover {
    transform: translateY(-2px);
}
.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    color: white;
    font-size: 24px;
}
.stat-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}
.stat-label {
    color: #6c757d;
    margin-bottom: 0;
}

.table-danger {
    --bs-table-bg: rgba(220, 53, 69, 0.1);
}

.table-warning {
    --bs-table-bg: rgba(255, 193, 7, 0.1);
}

.device-info, .patient-info {
    line-height: 1.2;
}

.btn-group .btn {
    border-radius: 0.25rem;
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

.filter-panel .card-header {
    background: linear-gradient(45deg, #dc3545, #c82333) !important;
}

@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn-group .btn {
        padding: 0.25rem 0.5rem;
    }
    
    .stat-number {
        font-size: 1.5rem;
    }
    
    .device-info, .patient-info {
        font-size: 0.8rem;
    }
}
</style>
{% endblock %}

{% block reports_extra_js %}
<script>
$(document).ready(function() {
    let currentReportId = null;
    
    // 筛选表单提交
    $('#filterForm').on('submit', function(e) {
        e.preventDefault();
        const formData = $(this).serialize();
        window.location.href = '?' + formData;
    });
    
    // 筛选条件变化时自动提交
    $('#injuryLevelFilter, #dateRangeFilter, #statusFilter').on('change', function() {
        $('#filterForm').submit();
    });

    // 刷新按钮
    $('#refreshBtn').on('click', function() {
        location.reload();
    });

    // 导出按钮
    $('#exportBtn').on('click', function() {
        // TODO: 实现导出功能
        alert('导出功能开发中...');
    });

    // 查看完整报告
    $('#viewFullReportBtn').on('click', function() {
        if (currentReportId) {
            window.open(`/reports/${currentReportId}/`, '_blank');
        }
    });
});

// 显示事件详情
function showEventDetails(reportId) {
    currentReportId = reportId;
    $('#eventDetailsModal').modal('show');
    
    // 加载事件详情
    $.ajax({
        url: `/reports/api/reports/${reportId}/`,
        type: 'GET',
        success: function(response) {
            const content = `
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-danger">基本信息</h6>
                        <p><strong>报告编号：</strong>${response.report_number}</p>
                        <p><strong>器械名称：</strong>${response.device_name}</p>
                        <p><strong>事件日期：</strong>${response.event_date}</p>
                        <p><strong>伤害程度：</strong><span class="badge bg-${response.injury_level === 'death' ? 'danger' : 'warning'}">${response.injury_level_display}</span></p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-danger">患者信息</h6>
                        <p><strong>患者姓名：</strong>${response.patient_name}</p>
                        <p><strong>年龄性别：</strong>${response.patient_age}岁 ${response.patient_gender_display}</p>
                        <p><strong>联系方式：</strong>${response.patient_contact || '未填写'}</p>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <h6 class="text-danger">事件描述</h6>
                        <div class="border rounded p-3 bg-light">
                            ${response.event_description.replace(/\n/g, '<br>')}
                        </div>
                    </div>
                </div>
                ${response.injury_description ? `
                <div class="row mt-3">
                    <div class="col-12">
                        <h6 class="text-danger">伤害描述</h6>
                        <div class="border rounded p-3 bg-light">
                            ${response.injury_description.replace(/\n/g, '<br>')}
                        </div>
                    </div>
                </div>
                ` : ''}
            `;
            $('#eventDetailsContent').html(content);
        },
        error: function() {
            $('#eventDetailsContent').html('<div class="alert alert-danger">加载事件详情失败</div>');
        }
    });
}
</script>
{% endblock %}
