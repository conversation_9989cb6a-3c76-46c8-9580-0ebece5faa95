#!/usr/bin/env python
"""
用户管理界面测试脚本
User Management Interface Test Script for Medical Device Reporting Platform
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from django.test import Client
from django.contrib.auth.models import User, Group
from apps.users.models import UserProfile, Department
from apps.users.services import initialize_user_groups

def setup_test_data():
    """设置测试数据"""
    
    print("设置测试数据...")
    
    # 初始化用户组和权限
    initialize_user_groups()
    
    # 创建测试科室
    departments = []
    for i, dept_name in enumerate(['内科', '外科', '儿科'], 1):
        department, created = Department.objects.get_or_create(
            code=f'DEPT{i:02d}',
            defaults={
                'name': dept_name,
                'is_active': True
            }
        )
        departments.append(department)
    
    # 创建测试管理员用户
    admin_user, created = User.objects.get_or_create(
        username='test_admin',
        defaults={
            'first_name': '测试',
            'last_name': '管理员',
            'email': '<EMAIL>',
            'is_active': True
        }
    )
    
    admin_profile, created = UserProfile.objects.get_or_create(
        account_number='0001',
        defaults={
            'user': admin_user,
            'role': 'admin',
            'is_active': True
        }
    )
    
    # 分配管理员权限
    admin_group = Group.objects.get(name='管理员')
    admin_user.groups.add(admin_group)
    
    # 创建测试科室人员
    staff_users = []
    for i, dept in enumerate(departments, 2):
        staff_user, created = User.objects.get_or_create(
            username=f'test_staff_{i}',
            defaults={
                'first_name': f'测试{dept.name}',
                'last_name': '医生',
                'email': f'staff{i}@test.com',
                'is_active': True
            }
        )
        
        staff_profile, created = UserProfile.objects.get_or_create(
            account_number=f'000{i}',
            defaults={
                'user': staff_user,
                'department': dept,
                'role': 'staff',
                'is_active': True
            }
        )
        
        # 分配科室人员权限
        staff_group = Group.objects.get(name='科室人员')
        staff_user.groups.add(staff_group)
        
        staff_users.append((staff_user, staff_profile))
    
    return admin_user, admin_profile, departments, staff_users

def test_user_management_interface():
    """测试用户管理界面"""
    
    print("=== 用户管理界面测试 ===\n")
    
    # 设置测试数据
    admin_user, admin_profile, departments, staff_users = setup_test_data()
    
    # 创建测试客户端
    client = Client()
    
    # 1. 测试用户列表页面
    print("1. 测试用户列表页面...")
    try:
        # 先登录管理员用户
        client.force_login(admin_user)
        
        response = client.get('/users/')
        if response.status_code == 200:
            content = response.content.decode('utf-8')
            
            # 检查关键元素
            checks = [
                ('页面标题', '用户管理' in content),
                ('筛选面板', 'filter-panel' in content),
                ('用户列表', 'userTable' in content),
                ('新建用户按钮', '新建用户' in content),
                ('DataTables CSS', 'dataTables.bootstrap5' in content),
                ('用户管理CSS', 'user_management.css' in content),
                ('用户管理JS', 'user_management.js' in content),
                ('科室筛选', 'departmentFilter' in content),
                ('角色筛选', 'roleFilter' in content),
                ('状态筛选', 'statusFilter' in content),
            ]
            
            for check_name, result in checks:
                status = "✅" if result else "❌"
                print(f"   {status} {check_name}: {'通过' if result else '失败'}")
            
            print(f"   ✅ 用户列表页面加载成功 (状态码: {response.status_code})")
        else:
            print(f"   ❌ 用户列表页面加载失败 (状态码: {response.status_code})")
    except Exception as e:
        print(f"   ❌ 用户列表页面测试异常: {str(e)}")
    
    # 2. 测试用户创建页面
    print("\n2. 测试用户创建页面...")
    try:
        response = client.get('/users/create/')
        if response.status_code == 200:
            content = response.content.decode('utf-8')
            
            # 检查关键元素
            checks = [
                ('页面标题', '新建用户' in content),
                ('用户表单', 'userForm' in content),
                ('账号输入框', 'account_number' in content),
                ('用户名输入框', 'username' in content),
                ('科室选择', 'department_id' in content),
                ('角色选择', 'role' in content),
                ('状态开关', 'is_active' in content),
                ('表单验证', 'needs-validation' in content),
                ('用户表单JS', 'user_form.js' in content),
                ('面包屑导航', 'breadcrumb' in content),
            ]
            
            for check_name, result in checks:
                status = "✅" if result else "❌"
                print(f"   {status} {check_name}: {'通过' if result else '失败'}")
            
            print(f"   ✅ 用户创建页面加载成功 (状态码: {response.status_code})")
        else:
            print(f"   ❌ 用户创建页面加载失败 (状态码: {response.status_code})")
    except Exception as e:
        print(f"   ❌ 用户创建页面测试异常: {str(e)}")
    
    # 3. 测试用户编辑页面
    print("\n3. 测试用户编辑页面...")
    try:
        # 使用第一个科室人员进行测试
        if staff_users:
            staff_user, staff_profile = staff_users[0]
            response = client.get(f'/users/{staff_profile.id}/edit/')
            if response.status_code == 200:
                content = response.content.decode('utf-8')
                
                # 检查关键元素
                checks = [
                    ('页面标题', '编辑用户' in content),
                    ('用户表单', 'userForm' in content),
                    ('账号只读', 'readonly' in content),
                    ('用户信息预填充', staff_profile.account_number in content),
                    ('编辑模式标识', 'is_edit' in content),
                    ('创建信息显示', '创建时间' in content),
                    ('更新按钮', '更新用户' in content),
                ]
                
                for check_name, result in checks:
                    status = "✅" if result else "❌"
                    print(f"   {status} {check_name}: {'通过' if result else '失败'}")
                
                print(f"   ✅ 用户编辑页面加载成功 (状态码: {response.status_code})")
            else:
                print(f"   ❌ 用户编辑页面加载失败 (状态码: {response.status_code})")
        else:
            print("   ⚠️ 没有测试用户，跳过编辑页面测试")
    except Exception as e:
        print(f"   ❌ 用户编辑页面测试异常: {str(e)}")
    
    # 4. 测试API接口集成
    print("\n4. 测试API接口集成...")
    try:
        # 测试用户列表API
        response = client.get('/api/users/')
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 用户列表API: 返回 {len(data.get('results', []))} 个用户")
        else:
            print(f"   ❌ 用户列表API失败: {response.status_code}")
        
        # 测试科室列表API
        response = client.get('/api/departments/')
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 科室列表API: 返回 {len(data)} 个科室")
        else:
            print(f"   ❌ 科室列表API失败: {response.status_code}")
        
        # 测试统计API
        response = client.get('/api/users/statistics/')
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 统计API: 总用户数 {data.get('total_users', 0)}")
        else:
            print(f"   ❌ 统计API失败: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ API接口测试异常: {str(e)}")
    
    # 5. 测试权限控制
    print("\n5. 测试权限控制...")
    try:
        # 测试科室人员访问用户管理
        if staff_users:
            staff_user, staff_profile = staff_users[0]
            client.force_login(staff_user)
            
            response = client.get('/users/')
            if response.status_code == 403:
                print("   ✅ 科室人员无法访问用户管理页面")
            else:
                print(f"   ❌ 权限控制失效: 科室人员可以访问用户管理 (状态码: {response.status_code})")
            
            # 重新登录管理员
            client.force_login(admin_user)
        else:
            print("   ⚠️ 没有科室人员，跳过权限测试")
            
    except Exception as e:
        print(f"   ❌ 权限控制测试异常: {str(e)}")
    
    # 6. 测试响应式设计
    print("\n6. 测试响应式设计...")
    try:
        # 模拟移动设备访问
        mobile_headers = {
            'HTTP_USER_AGENT': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
        }
        
        response = client.get('/users/', **mobile_headers)
        if response.status_code == 200:
            content = response.content.decode('utf-8')
            
            # 检查响应式元素
            responsive_checks = [
                ('视口元标签', 'viewport' in content),
                ('Bootstrap响应式', 'col-md-' in content or 'col-lg-' in content),
                ('响应式表格', 'table-responsive' in content),
                ('移动端样式', '@media' in content or 'responsive' in content.lower()),
            ]
            
            for check_name, result in responsive_checks:
                status = "✅" if result else "❌"
                print(f"   {status} {check_name}: {'通过' if result else '失败'}")
            
            print(f"   ✅ 移动端访问正常")
        else:
            print(f"   ❌ 移动端访问异常 (状态码: {response.status_code})")
    except Exception as e:
        print(f"   ❌ 响应式设计测试异常: {str(e)}")
    
    print("\n=== 用户管理界面测试完成 ===")
    
    return True

if __name__ == '__main__':
    try:
        test_user_management_interface()
        print("\n🎉 用户管理界面测试完成！")
    except Exception as e:
        print(f"\n❌ 用户管理界面测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
