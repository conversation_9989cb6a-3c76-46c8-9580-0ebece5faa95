# 功能特性详细说明

## 📋 目录
- [报告管理功能](#报告管理功能) - **新增**
- [科室管理功能](#科室管理功能)
- [个人设置功能](#个人设置功能)
- [用户管理功能](#用户管理功能)
- [权限控制系统](#权限控制系统)
- [前端交互特性](#前端交互特性)

## 📋 报告管理功能

### 核心功能
报告管理模块是系统的核心功能，提供完整的医疗器械不良事件报告管理流程。

#### 1. 报告创建功能
- **标准表单创建**：提供完整的报告创建表单，包含所有必要字段
- **分步向导创建**：4步向导式创建流程，降低用户操作复杂度
  - 步骤1：上报人信息（姓名、电话、科室）
  - 步骤2：患者信息（姓名、年龄、性别、联系方式）
  - 步骤3：事件信息（事件日期、伤害程度、事件描述）
  - 步骤4：器械信息（器械名称、注册证号、生产厂商等）
- **数据验证**：前后端双重验证，确保数据完整性和准确性
- **自动编号**：系统自动生成唯一的报告编号（格式：YYYYMMDD-XXXX）

#### 2. 报告管理功能
- **报告列表**：DataTables展示报告列表，支持分页、排序、搜索
- **多维筛选**：按状态、伤害程度、上报人、科室、日期范围筛选
- **批量操作**：支持批量提交、导出等操作
- **状态管理**：草稿、已提交、审核中、已批准、已拒绝状态流转

#### 3. 报告审核流程
- **审核队列**：管理员可查看待审核报告列表
- **审核操作**：批准、拒绝、要求修改等审核动作
- **审核意见**：支持添加详细的审核意见和建议
- **状态跟踪**：完整的审核历史和状态变更记录

#### 4. 严重事件管理
- **自动识别**：系统自动识别严重伤害和死亡事件
- **特别标记**：严重事件在列表中特别标记显示
- **快速通道**：严重事件优先审核和处理
- **统计分析**：严重事件的统计和趋势分析

#### 5. 报告详情查看
- **完整信息展示**：报告的所有详细信息
- **状态历史**：报告状态变更的完整历史
- **操作记录**：创建、修改、提交、审核等操作记录
- **权限控制**：基于角色的查看权限控制

### 技术特性
- **工作流引擎**：完整的报告状态流转管理
- **权限控制**：基于角色和对象的精细化权限控制
- **数据完整性**：严格的数据验证和约束
- **审计日志**：完整的操作记录和审计追踪
- **响应式设计**：适配桌面和移动设备访问

### URL路由结构 (已修复)
```
/reports/                     → 报告管理仪表板
/reports/list/                → 报告列表页面
/reports/create/              → 标准表单创建
/reports/create/step/         → 分步创建入口 (2025-06-21修复)
/reports/create/step/1/       → 分步表单第一步
/reports/create/step/2/       → 分步表单第二步
/reports/create/step/3/       → 分步表单第三步
/reports/create/step/4/       → 分步表单第四步
/reports/{id}/                → 报告详情页面
/reports/{id}/edit/           → 报告编辑页面
/reports/{id}/submit/         → 报告提交操作
/reports/{id}/review/         → 报告审核页面
/reports/pending-review/      → 待审核报告列表
/reports/serious-events/      → 严重事件报告列表
```

### 最新修复 (2025-06-21)
- ✅ **URL路由修复**：解决分步创建入口URL缺失问题
- ✅ **模板语法修复**：解决base.html中重复block定义问题
- ✅ **用户体验优化**：分步创建流程自动清除之前的表单数据
- ✅ **错误处理改进**：完善了错误提示和用户反馈机制

## 🏥 科室管理功能

### 核心功能
科室管理模块提供完整的医院科室信息管理功能，支持科室的创建、编辑、查看、删除等操作。

#### 1. 科室列表管理
- **数据展示**：使用DataTables展示科室列表，支持分页、排序、搜索
- **实时筛选**：按科室状态（启用/禁用）筛选，关键词搜索
- **统计信息**：显示总科室数、启用科室数、总用户数、活跃用户数
- **批量操作**：支持批量启用/禁用科室
- **导出功能**：支持Excel格式导出科室数据

#### 2. 科室信息管理
- **基本信息**：科室代码、科室名称、启用状态
- **用户统计**：显示科室下的用户数量和活跃用户数
- **创建信息**：记录创建人、创建时间、更新人、更新时间
- **状态管理**：支持启用/禁用科室状态切换

#### 3. 科室详情页面
- **科室信息展示**：完整的科室基本信息和统计数据
- **用户列表管理**：显示科室下所有用户，支持用户操作
- **快速操作**：编辑科室、添加用户、查看用户详情
- **数据可视化**：用户统计卡片，直观展示科室数据

#### 4. Excel导入导出
- **模板下载**：提供标准Excel导入模板
- **拖拽上传**：现代化的文件上传体验，支持拖拽操作
- **进度反馈**：实时显示导入进度和状态
- **错误处理**：详细的错误信息和导入结果反馈
- **数据验证**：科室代码唯一性检查、必填字段验证

### 技术特性
- **权限控制**：仅管理员可访问科室管理功能
- **数据验证**：前后端双重验证，确保数据完整性
- **AJAX操作**：异步状态切换、删除确认、数据刷新
- **响应式设计**：适配桌面和移动设备
- **文件处理**：支持.xlsx和.xls格式，文件大小限制

## 👤 个人设置功能

### 核心功能
个人设置模块提供用户个人信息管理、偏好设置和安全设置功能。

#### 1. 个人信息管理
- **信息查看**：显示用户基本信息、角色、科室、登录记录
- **信息编辑**：允许用户修改姓名、邮箱等基本信息
- **权限控制**：用户只能修改自己的信息，管理员可查看所有用户
- **双模式支持**：用户查看自己信息 vs 管理员查看其他用户信息

#### 2. 用户偏好设置
- **界面主题**：支持浅色、深色、跟随系统主题
- **语言设置**：支持简体中文、英文（预留扩展）
- **时区设置**：支持不同时区选择
- **页面设置**：自定义每页显示数量
- **自动保存**：设置项自动保存到本地和服务器

#### 3. 安全设置
- **密码修改**：当前密码验证、新密码强度检查
- **登录记录**：显示最后登录时间、登录IP
- **账户状态**：显示账户状态、注册时间
- **权限信息**：显示当前角色、所属科室

#### 4. 通知设置
- **邮件通知**：报告状态更新、系统维护通知
- **系统通知**：浏览器通知、桌面通知、声音提醒
- **个性化配置**：用户可自定义通知类型和方式

### 技术特性
- **标签页组织**：使用Bootstrap5 Nav tabs组织不同设置类别
- **本地存储**：用户偏好设置本地缓存，提升响应速度
- **实时验证**：表单字段实时验证，即时错误反馈
- **AJAX保存**：设置项异步保存，无需页面刷新
- **安全保护**：密码修改后强制重新登录

## 👥 用户管理功能

### 核心功能
用户管理模块提供完整的用户生命周期管理功能。

#### 1. 用户列表管理
- **数据展示**：DataTables展示用户列表，支持高级筛选
- **多维筛选**：按角色、科室、状态筛选用户
- **批量操作**：批量启用/禁用用户、批量分配科室
- **导出功能**：支持Excel格式导出用户数据

#### 2. 用户信息管理
- **基本信息**：账号、姓名、邮箱、电话等
- **角色管理**：管理员、科室人员角色分配
- **科室分配**：用户与科室的关联管理
- **状态控制**：用户启用/禁用状态管理

#### 3. 用户操作记录
- **创建记录**：记录用户创建人、创建时间
- **更新记录**：记录用户更新人、更新时间
- **登录记录**：最后登录时间、登录IP
- **操作审计**：完整的用户操作日志

### 技术特性
- **4位数账号**：自动生成唯一4位数账号
- **无密码登录**：基于账号的安全登录机制
- **数据关联**：用户与科室的多对一关联
- **级联操作**：科室删除时的用户处理

## 🔐 权限控制系统

### 权限架构
系统采用基于角色的访问控制（RBAC）模型。

#### 1. 角色定义
- **管理员 (admin)**：拥有所有权限，可管理用户、科室和系统设置
- **科室人员 (staff)**：只能访问基础功能，必须隶属于某个科室

#### 2. 权限控制层次
- **页面权限**：基于角色的页面访问控制
- **API权限**：RESTful API的精细化权限管理
- **对象权限**：数据级别的访问控制
- **字段权限**：表单字段的编辑权限控制

#### 3. 安全机制
- **会话管理**：登录状态验证、会话超时控制
- **IP检查**：登录IP地址记录和验证
- **CSRF保护**：跨站请求伪造防护
- **XSS防护**：跨站脚本攻击防护

### 权限矩阵

| 功能模块 | 管理员 | 科室人员 | 未登录用户 |
|---------|--------|----------|------------|
| 用户管理 | ✅ 完全访问 | ❌ 禁止访问 | ❌ 重定向登录 |
| 科室管理 | ✅ 完全访问 | ❌ 禁止访问 | ❌ 重定向登录 |
| 个人设置 | ✅ 完全访问 | ✅ 仅自己信息 | ❌ 重定向登录 |
| 系统设置 | ✅ 完全访问 | ❌ 禁止访问 | ❌ 重定向登录 |

## 🎨 前端交互特性

### 用户体验优化
系统注重用户体验，提供现代化的Web交互功能。

#### 1. 响应式设计
- **Bootstrap5框架**：使用最新的Bootstrap5组件
- **移动端适配**：完美适配手机、平板、桌面设备
- **弹性布局**：自适应不同屏幕尺寸
- **触摸友好**：移动设备触摸操作优化

#### 2. 交互功能
- **AJAX操作**：异步数据加载、表单提交、状态切换
- **实时验证**：表单字段实时验证、即时错误反馈
- **加载状态**：操作过程中的加载指示器
- **进度反馈**：文件上传进度、操作进度显示

#### 3. 数据展示
- **DataTables集成**：专业的数据表格组件
- **分页导航**：完整的分页控件和URL参数保持
- **搜索筛选**：实时搜索、多条件筛选
- **排序功能**：列排序、自定义排序规则

#### 4. 用户反馈
- **消息提示**：成功、错误、警告、信息等不同类型提示
- **确认对话框**：危险操作的二次确认
- **模态框**：用户详情、密码修改等功能的模态框展示
- **工具提示**：操作按钮的友好提示

#### 5. 性能优化
- **防抖处理**：搜索输入、自动保存的防抖优化
- **本地缓存**：用户偏好设置的本地存储
- **懒加载**：大数据量的分页加载
- **压缩优化**：JavaScript和CSS文件压缩

### 技术栈
- **前端框架**：Bootstrap 5.3+
- **JavaScript库**：jQuery 3.6+, DataTables 1.13+
- **图标系统**：Bootstrap Icons
- **样式预处理**：原生CSS + Bootstrap变量
- **构建工具**：Django静态文件管理

## 🔧 扩展性设计

### 模块化架构
系统采用模块化设计，便于功能扩展和维护。

#### 1. 后端架构
- **Django应用分离**：按功能模块组织Django应用
- **服务层分离**：业务逻辑与视图层分离
- **数据访问层**：统一的数据查询和操作接口
- **API设计**：RESTful API支持前后端分离

#### 2. 前端架构
- **模板继承**：统一的页面布局和样式
- **组件化设计**：可复用的UI组件
- **JavaScript模块化**：按页面组织JavaScript文件
- **样式管理**：统一的CSS样式管理

#### 3. 配置管理
- **环境分离**：开发、测试、生产环境配置分离
- **参数化配置**：可配置的系统参数
- **功能开关**：可控制的功能启用/禁用
- **国际化支持**：多语言扩展框架

### 未来扩展方向
- **移动应用**：基于API的移动端应用开发
- **微服务架构**：模块拆分为独立的微服务
- **实时通信**：WebSocket支持实时消息推送
- **数据分析**：用户行为分析和报表功能
- **第三方集成**：与其他医疗系统的集成接口
