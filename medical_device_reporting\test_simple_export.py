#!/usr/bin/env python
"""
简单的导出API测试
Simple Export API Test
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from django.urls import reverse
from apps.users.models import UserProfile, Department

def test_simple_export():
    """简单的导出API测试"""
    print("=== 简单导出API测试 ===")
    
    try:
        # 创建测试用户
        import uuid
        unique_id = str(uuid.uuid4())[:8]
        
        # 创建科室
        department = Department.objects.create(
            code=f'DEPT_{unique_id[:4]}',
            name=f'测试科室_{unique_id[:4]}',
            is_active=True,
            created_by_id=1
        )
        
        # 创建管理员用户
        admin_user = User.objects.create_user(
            username=f'admin_{unique_id}', 
            email='<EMAIL>'
        )
        admin_profile = UserProfile.objects.create(
            user=admin_user,
            account_number=f'{hash(unique_id) % 9000 + 1000:04d}',
            department=department,
            role='admin',
            created_by=admin_user
        )
        
        # 创建客户端并登录
        client = Client()
        client.force_login(admin_user)
        
        # 测试1: 检查URL反向解析
        try:
            export_url = reverse('reports:api_report_export')
            print(f"✅ URL反向解析成功: {export_url}")
        except Exception as e:
            print(f"❌ URL反向解析失败: {e}")
            return False
        
        # 测试2: 直接访问API
        print("测试直接访问API...")
        response = client.get(export_url, {'format': 'excel', 'analysis_type': 'summary'})
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            print(f"✅ API访问成功")
            print(f"Content-Type: {response.get('Content-Type', 'N/A')}")
            print(f"Content-Length: {len(response.content)} bytes")
            return True
        elif response.status_code == 404:
            print(f"❌ API返回404 - URL路由问题")
            
            # 检查所有可用的URL
            from django.urls import get_resolver
            resolver = get_resolver()
            print("可用的URL模式:")
            for pattern in resolver.url_patterns:
                print(f"  {pattern}")
            
            return False
        elif response.status_code == 403:
            print(f"❌ API返回403 - 权限问题")
            return False
        elif response.status_code == 500:
            print(f"❌ API返回500 - 服务器错误")
            print(f"错误内容: {response.content.decode('utf-8')}")
            return False
        else:
            print(f"❌ API返回未知状态码: {response.status_code}")
            print(f"响应内容: {response.content.decode('utf-8')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理测试数据
        try:
            UserProfile.objects.filter(created_by=admin_user).delete()
            Department.objects.filter(code__contains=unique_id[:4]).delete()
            User.objects.filter(username__contains=unique_id).delete()
        except:
            pass

def main():
    """主测试函数"""
    print("🔧 开始简单导出API测试...")
    
    success = test_simple_export()
    
    if success:
        print("🎉 导出API测试成功！")
    else:
        print("❌ 导出API测试失败")
    
    return success

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
