"""
不良事件上报管理选择器
Adverse Event Reports Management Selectors for Medical Device Reporting Platform
"""

from django.db.models import QuerySet, Q, Count, Prefetch, Case, When, IntegerField, FloatField, F, Avg, <PERSON>, Min
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List

from apps.users.models import UserProfile, Department
from .models import AdverseEventReport
from apps.common.cache_utils import cache_statistics_result


def report_list(
    *,
    user_profile: Optional[UserProfile] = None,
    status: Optional[str] = None,
    department_id: Optional[int] = None,
    reporter_id: Optional[int] = None,
    injury_level: Optional[str] = None,
    device_name: Optional[str] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    search: Optional[str] = None,
    ordering: str = '-created_at'
) -> QuerySet[AdverseEventReport]:
    """
    获取报告列表

    Args:
        user_profile: 当前用户配置文件（用于权限过滤）
        status: 报告状态筛选
        department_id: 科室ID筛选
        reporter_id: 上报人ID筛选
        injury_level: 伤害程度筛选
        device_name: 器械名称筛选
        start_date: 开始日期筛选
        end_date: 结束日期筛选
        search: 搜索关键词
        ordering: 排序字段

    Returns:
        QuerySet[AdverseEventReport]: 报告查询集
    """

    # 基础查询集，包含关联数据优化
    queryset = AdverseEventReport.objects.select_related(
        'reporter', 'reporter__user', 'department',
        'reviewed_by', 'reviewed_by__user',
        'created_by', 'updated_by'
    ).filter(is_deleted=False)

    # 权限过滤
    if user_profile:
        queryset = _apply_permission_filter(queryset, user_profile)

    # 状态筛选
    if status:
        queryset = queryset.filter(status=status)

    # 科室筛选
    if department_id:
        queryset = queryset.filter(department_id=department_id)

    # 上报人筛选
    if reporter_id:
        queryset = queryset.filter(reporter_id=reporter_id)

    # 伤害程度筛选
    if injury_level:
        queryset = queryset.filter(injury_level=injury_level)

    # 器械名称筛选
    if device_name:
        queryset = queryset.filter(device_name__icontains=device_name)

    # 日期范围筛选
    if start_date:
        queryset = queryset.filter(event_date__gte=start_date)
    if end_date:
        queryset = queryset.filter(event_date__lte=end_date)

    # 搜索
    if search:
        search_q = Q()
        search_q |= Q(report_number__icontains=search)
        search_q |= Q(patient_name__icontains=search)
        search_q |= Q(device_name__icontains=search)
        search_q |= Q(manufacturer__icontains=search)
        search_q |= Q(registration_number__icontains=search)
        search_q |= Q(reporter__user__first_name__icontains=search)
        search_q |= Q(reporter__user__last_name__icontains=search)
        search_q |= Q(reporter__account_number__icontains=search)
        search_q |= Q(department__name__icontains=search)
        search_q |= Q(event_description__icontains=search)

        queryset = queryset.filter(search_q)

    # 排序
    if ordering:
        queryset = queryset.order_by(ordering)

    return queryset


def report_detail(report_id: int, user_profile: Optional[UserProfile] = None) -> Optional[AdverseEventReport]:
    """
    获取报告详情

    Args:
        report_id: 报告ID
        user_profile: 当前用户配置文件（用于权限验证）

    Returns:
        Optional[AdverseEventReport]: 报告对象或None
    """

    try:
        queryset = AdverseEventReport.objects.select_related(
            'reporter', 'reporter__user', 'reporter__department',
            'department',
            'reviewed_by', 'reviewed_by__user',
            'created_by', 'updated_by'
        ).filter(is_deleted=False)

        # 权限过滤
        if user_profile:
            queryset = _apply_permission_filter(queryset, user_profile)

        return queryset.get(id=report_id)

    except AdverseEventReport.DoesNotExist:
        return None


def report_get_by_number(
    report_number: str,
    user_profile: Optional[UserProfile] = None
) -> Optional[AdverseEventReport]:
    """
    根据报告编号获取报告

    Args:
        report_number: 报告编号
        user_profile: 当前用户配置文件（用于权限验证）

    Returns:
        Optional[AdverseEventReport]: 报告对象或None
    """

    try:
        queryset = AdverseEventReport.objects.select_related(
            'reporter', 'reporter__user', 'department',
            'reviewed_by', 'reviewed_by__user'
        ).filter(is_deleted=False)

        # 权限过滤
        if user_profile:
            queryset = _apply_permission_filter(queryset, user_profile)

        return queryset.get(report_number=report_number)

    except AdverseEventReport.DoesNotExist:
        return None


def user_reports(
    user_profile: UserProfile,
    *,
    status: Optional[str] = None,
    ordering: str = '-created_at'
) -> QuerySet[AdverseEventReport]:
    """
    获取用户相关的报告

    Args:
        user_profile: 用户配置文件
        status: 报告状态筛选
        ordering: 排序字段

    Returns:
        QuerySet[AdverseEventReport]: 报告查询集
    """

    queryset = AdverseEventReport.objects.select_related(
        'reporter', 'department', 'reviewed_by'
    ).filter(is_deleted=False)

    # 权限过滤
    queryset = _apply_permission_filter(queryset, user_profile)

    # 状态筛选
    if status:
        queryset = queryset.filter(status=status)

    # 排序
    if ordering:
        queryset = queryset.order_by(ordering)

    return queryset


def department_reports(
    department_id: int,
    user_profile: Optional[UserProfile] = None,
    *,
    status: Optional[str] = None,
    ordering: str = '-created_at'
) -> QuerySet[AdverseEventReport]:
    """
    获取科室报告

    Args:
        department_id: 科室ID
        user_profile: 当前用户配置文件（用于权限验证）
        status: 报告状态筛选
        ordering: 排序字段

    Returns:
        QuerySet[AdverseEventReport]: 报告查询集
    """

    queryset = AdverseEventReport.objects.select_related(
        'reporter', 'department', 'reviewed_by'
    ).filter(
        is_deleted=False,
        department_id=department_id
    )

    # 权限过滤
    if user_profile:
        queryset = _apply_permission_filter(queryset, user_profile)

    # 状态筛选
    if status:
        queryset = queryset.filter(status=status)

    # 排序
    if ordering:
        queryset = queryset.order_by(ordering)

    return queryset


@cache_statistics_result(timeout=1800)  # 缓存30分钟
def report_statistics(
    user_profile: Optional[UserProfile] = None,
    *,
    department_id: Optional[int] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None
) -> Dict[str, Any]:
    """
    获取报告统计信息

    Args:
        user_profile: 当前用户配置文件（用于权限过滤）
        department_id: 科室ID筛选
        start_date: 开始日期
        end_date: 结束日期

    Returns:
        Dict[str, Any]: 统计信息字典
    """

    # 基础查询集
    queryset = AdverseEventReport.objects.filter(is_deleted=False)

    # 权限过滤
    if user_profile:
        queryset = _apply_permission_filter(queryset, user_profile)

    # 科室筛选
    if department_id:
        queryset = queryset.filter(department_id=department_id)

    # 日期筛选
    if start_date:
        queryset = queryset.filter(created_at__gte=start_date)
    if end_date:
        queryset = queryset.filter(created_at__lte=end_date)

    # 计算统计信息
    stats = queryset.aggregate(
        total_count=Count('id'),
        draft_count=Count(
            Case(When(status='draft', then=1), output_field=IntegerField())
        ),
        submitted_count=Count(
            Case(When(status='submitted', then=1), output_field=IntegerField())
        ),
        under_review_count=Count(
            Case(When(status='under_review', then=1), output_field=IntegerField())
        ),
        approved_count=Count(
            Case(When(status='approved', then=1), output_field=IntegerField())
        ),
        rejected_count=Count(
            Case(When(status='rejected', then=1), output_field=IntegerField())
        ),
        death_count=Count(
            Case(When(injury_level='death', then=1), output_field=IntegerField())
        ),
        serious_injury_count=Count(
            Case(When(injury_level='serious_injury', then=1), output_field=IntegerField())
        ),
        other_injury_count=Count(
            Case(When(injury_level='other', then=1), output_field=IntegerField())
        )
    )

    return stats


def report_list_paginated(
    *,
    user_profile: Optional[UserProfile] = None,
    status: Optional[str] = None,
    department_id: Optional[int] = None,
    reporter_id: Optional[int] = None,
    injury_level: Optional[str] = None,
    device_name: Optional[str] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    search: Optional[str] = None,
    ordering: str = '-created_at',
    page: int = 1,
    page_size: int = 20
) -> Dict[str, Any]:
    """
    获取分页报告列表

    Args:
        user_profile: 当前用户配置文件（用于权限过滤）
        status: 报告状态筛选
        department_id: 科室ID筛选
        reporter_id: 上报人ID筛选
        injury_level: 伤害程度筛选
        device_name: 器械名称筛选
        start_date: 开始日期筛选
        end_date: 结束日期筛选
        search: 搜索关键词
        ordering: 排序字段
        page: 页码（从1开始）
        page_size: 每页大小

    Returns:
        Dict[str, Any]: 包含分页信息的结果字典
    """

    from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger

    # 获取基础查询集
    queryset = report_list(
        user_profile=user_profile,
        status=status,
        department_id=department_id,
        reporter_id=reporter_id,
        injury_level=injury_level,
        device_name=device_name,
        start_date=start_date,
        end_date=end_date,
        search=search,
        ordering=ordering
    )

    # 创建分页器
    paginator = Paginator(queryset, page_size)

    try:
        reports = paginator.page(page)
    except PageNotAnInteger:
        reports = paginator.page(1)
    except EmptyPage:
        reports = paginator.page(paginator.num_pages)

    return {
        'reports': reports.object_list,
        'page': reports.number,
        'total_pages': paginator.num_pages,
        'total_count': paginator.count,
        'has_previous': reports.has_previous(),
        'has_next': reports.has_next(),
        'previous_page': reports.previous_page_number() if reports.has_previous() else None,
        'next_page': reports.next_page_number() if reports.has_next() else None,
        'page_size': page_size,
        'start_index': reports.start_index(),
        'end_index': reports.end_index(),
    }


def report_search_suggestions(
    query: str,
    user_profile: Optional[UserProfile] = None,
    limit: int = 10
) -> List[Dict[str, Any]]:
    """
    报告搜索建议

    Args:
        query: 搜索查询
        user_profile: 当前用户配置文件（用于权限过滤）
        limit: 结果限制数量

    Returns:
        List[Dict[str, Any]]: 搜索建议列表
    """

    if not query or len(query) < 2:
        return []

    search_q = Q()
    search_q |= Q(report_number__icontains=query)
    search_q |= Q(patient_name__icontains=query)
    search_q |= Q(device_name__icontains=query)
    search_q |= Q(manufacturer__icontains=query)

    queryset = AdverseEventReport.objects.select_related(
        'reporter', 'department'
    ).filter(search_q, is_deleted=False)

    # 权限过滤
    if user_profile:
        queryset = _apply_permission_filter(queryset, user_profile)

    reports = queryset[:limit]

    suggestions = []
    for report in reports:
        suggestions.append({
            'id': report.id,
            'report_number': report.report_number,
            'patient_name': report.patient_name,
            'device_name': report.device_name,
            'status_display': report.get_status_display(),
            'reporter_name': report.reporter.display_name if report.reporter else '',
            'department_name': report.department.name if report.department else '',
            'event_date': report.event_date.strftime('%Y-%m-%d') if report.event_date else '',
        })

    return suggestions


def report_list_by_status_and_department(
    user_profile: Optional[UserProfile] = None
) -> Dict[str, Any]:
    """
    按状态和科室分组的报告统计

    Args:
        user_profile: 当前用户配置文件（用于权限过滤）

    Returns:
        Dict[str, Any]: 分组统计数据
    """

    # 基础查询集
    queryset = AdverseEventReport.objects.filter(is_deleted=False)

    # 权限过滤
    if user_profile:
        queryset = _apply_permission_filter(queryset, user_profile)

    # 按科室分组统计
    department_stats = Department.objects.filter(
        is_deleted=False,
        is_active=True
    ).annotate(
        total_reports=Count('department_reports', filter=Q(department_reports__is_deleted=False)),
        draft_reports=Count('department_reports', filter=Q(
            department_reports__is_deleted=False,
            department_reports__status='draft'
        )),
        submitted_reports=Count('department_reports', filter=Q(
            department_reports__is_deleted=False,
            department_reports__status='submitted'
        )),
        approved_reports=Count('department_reports', filter=Q(
            department_reports__is_deleted=False,
            department_reports__status='approved'
        )),
        serious_events=Count('department_reports', filter=Q(
            department_reports__is_deleted=False,
            department_reports__injury_level__in=['death', 'serious_injury']
        ))
    ).order_by('code')

    # 按状态分组统计
    status_stats = queryset.values('status').annotate(
        count=Count('id')
    ).order_by('status')

    # 按伤害程度分组统计
    injury_stats = queryset.values('injury_level').annotate(
        count=Count('id')
    ).order_by('injury_level')

    return {
        'department_stats': department_stats,
        'status_stats': status_stats,
        'injury_stats': injury_stats
    }


def report_list_recent_activity(
    user_profile: Optional[UserProfile] = None,
    *,
    days: int = 30,
    limit: int = 50
) -> QuerySet[AdverseEventReport]:
    """
    获取最近活动的报告列表

    Args:
        user_profile: 当前用户配置文件（用于权限过滤）
        days: 最近天数
        limit: 结果限制数量

    Returns:
        QuerySet[AdverseEventReport]: 最近活动的报告查询集
    """

    since_date = timezone.now() - timedelta(days=days)

    queryset = AdverseEventReport.objects.select_related(
        'reporter', 'department', 'reviewed_by'
    ).filter(
        is_deleted=False,
        updated_at__gte=since_date
    )

    # 权限过滤
    if user_profile:
        queryset = _apply_permission_filter(queryset, user_profile)

    return queryset.order_by('-updated_at')[:limit]


def report_list_by_date_range(
    user_profile: Optional[UserProfile] = None,
    *,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    date_field: str = 'created_at',
    ordering: str = '-created_at'
) -> QuerySet[AdverseEventReport]:
    """
    按日期范围筛选报告列表

    Args:
        user_profile: 当前用户配置文件（用于权限过滤）
        start_date: 开始日期 (YYYY-MM-DD)
        end_date: 结束日期 (YYYY-MM-DD)
        date_field: 日期字段 ('created_at', 'event_date', 'submitted_at')
        ordering: 排序字段

    Returns:
        QuerySet[AdverseEventReport]: 报告查询集
    """

    queryset = AdverseEventReport.objects.select_related(
        'reporter', 'department', 'reviewed_by'
    ).filter(is_deleted=False)

    # 权限过滤
    if user_profile:
        queryset = _apply_permission_filter(queryset, user_profile)

    # 日期筛选
    if start_date:
        try:
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            filter_kwargs = {f'{date_field}__gte': start_dt}
            queryset = queryset.filter(**filter_kwargs)
        except ValueError:
            pass

    if end_date:
        try:
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')
            filter_kwargs = {f'{date_field}__lte': end_dt}
            queryset = queryset.filter(**filter_kwargs)
        except ValueError:
            pass

    return queryset.order_by(ordering)


def report_list_pending_review(
    user_profile: Optional[UserProfile] = None
) -> QuerySet[AdverseEventReport]:
    """
    获取待审核的报告列表

    Args:
        user_profile: 当前用户配置文件（用于权限过滤）

    Returns:
        QuerySet[AdverseEventReport]: 待审核报告查询集
    """

    queryset = AdverseEventReport.objects.select_related(
        'reporter', 'department'
    ).filter(
        is_deleted=False,
        status__in=['submitted', 'under_review']
    )

    # 权限过滤
    if user_profile:
        queryset = _apply_permission_filter(queryset, user_profile)

    return queryset.order_by('submitted_at', 'created_at')


def report_list_serious_events(
    user_profile: Optional[UserProfile] = None,
    *,
    days: Optional[int] = None
) -> QuerySet[AdverseEventReport]:
    """
    获取严重事件报告列表

    Args:
        user_profile: 当前用户配置文件（用于权限过滤）
        days: 最近天数筛选

    Returns:
        QuerySet[AdverseEventReport]: 严重事件报告查询集
    """

    queryset = AdverseEventReport.objects.select_related(
        'reporter', 'department', 'reviewed_by'
    ).filter(
        is_deleted=False,
        injury_level__in=['death', 'serious_injury']
    )

    # 权限过滤
    if user_profile:
        queryset = _apply_permission_filter(queryset, user_profile)

    # 日期筛选
    if days:
        since_date = timezone.now() - timedelta(days=days)
        queryset = queryset.filter(event_date__gte=since_date)

    return queryset.order_by('-event_date', '-created_at')


def report_exists_by_number(report_number: str) -> bool:
    """
    检查报告编号是否存在

    Args:
        report_number: 报告编号

    Returns:
        bool: 报告编号是否存在
    """

    return AdverseEventReport.objects.filter(
        report_number=report_number,
        is_deleted=False
    ).exists()


# ==================== 权限过滤函数 ====================

def _apply_permission_filter(
    queryset: QuerySet[AdverseEventReport],
    user_profile: UserProfile
) -> QuerySet[AdverseEventReport]:
    """
    应用权限过滤

    Args:
        queryset: 原始查询集
        user_profile: 用户配置文件

    Returns:
        QuerySet[AdverseEventReport]: 过滤后的查询集
    """

    if user_profile.is_admin:
        # 管理员可以查看所有报告
        return queryset
    elif user_profile.is_staff_member and user_profile.department:
        # 科室人员只能查看本科室的报告
        return queryset.filter(department=user_profile.department)
    else:
        # 其他用户只能查看自己的报告
        return queryset.filter(reporter=user_profile)


# ==================== 统计分析函数 ====================

def get_monthly_report_trends(
    user_profile: Optional[UserProfile] = None,
    *,
    months: int = 12
) -> List[Dict[str, Any]]:
    """
    获取月度报告趋势

    Args:
        user_profile: 当前用户配置文件（用于权限过滤）
        months: 月份数量

    Returns:
        List[Dict[str, Any]]: 月度趋势数据
    """

    from django.db.models import TruncMonth

    # 计算开始日期
    start_date = timezone.now() - timedelta(days=months * 30)

    queryset = AdverseEventReport.objects.filter(
        is_deleted=False,
        created_at__gte=start_date
    )

    # 权限过滤
    if user_profile:
        queryset = _apply_permission_filter(queryset, user_profile)

    # 按月分组统计
    monthly_data = queryset.annotate(
        month=TruncMonth('created_at')
    ).values('month').annotate(
        total_count=Count('id'),
        serious_count=Count(
            Case(
                When(injury_level__in=['death', 'serious_injury'], then=1),
                output_field=IntegerField()
            )
        ),
        approved_count=Count(
            Case(When(status='approved', then=1), output_field=IntegerField())
        )
    ).order_by('month')

    return list(monthly_data)


@cache_statistics_result(timeout=3600)  # 缓存1小时
def get_device_statistics(
    user_profile: Optional[UserProfile] = None,
    *,
    limit: int = 20,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    include_manufacturer: bool = False
) -> List[Dict[str, Any]]:
    """
    获取器械统计信息（扩展版）

    Args:
        user_profile: 当前用户配置文件（用于权限过滤）
        limit: 结果限制数量
        start_date: 开始日期筛选
        end_date: 结束日期筛选
        include_manufacturer: 是否包含制造商统计

    Returns:
        List[Dict[str, Any]]: 器械统计数据
    """

    queryset = AdverseEventReport.objects.filter(is_deleted=False)

    # 权限过滤
    if user_profile:
        queryset = _apply_permission_filter(queryset, user_profile)

    # 日期筛选
    if start_date:
        queryset = queryset.filter(created_at__gte=start_date)
    if end_date:
        queryset = queryset.filter(created_at__lte=end_date)

    # 按器械名称分组统计
    device_stats = queryset.values('device_name').annotate(
        report_count=Count('id'),
        serious_count=Count(
            Case(
                When(injury_level__in=['death', 'serious_injury'], then=1),
                output_field=IntegerField()
            )
        ),
        death_count=Count(
            Case(When(injury_level='death', then=1), output_field=IntegerField())
        ),
        risk_ratio=Case(
            When(report_count__gt=0, then=F('serious_count') * 100.0 / F('report_count')),
            default=0.0,
            output_field=FloatField()
        )
    ).order_by('-report_count')[:limit]

    result = list(device_stats)

    # 如果需要制造商统计
    if include_manufacturer:
        manufacturer_stats = queryset.values('manufacturer').annotate(
            report_count=Count('id'),
            serious_count=Count(
                Case(
                    When(injury_level__in=['death', 'serious_injury'], then=1),
                    output_field=IntegerField()
                )
            ),
            device_types=Count('device_name', distinct=True)
        ).order_by('-report_count')[:limit]

        return {
            'device_stats': result,
            'manufacturer_stats': list(manufacturer_stats)
        }

    return result


@cache_statistics_result(timeout=3600)  # 缓存1小时
def get_time_series_statistics(
    user_profile: Optional[UserProfile] = None,
    *,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    granularity: str = 'month',
    date_field: str = 'created_at'
) -> List[Dict[str, Any]]:
    """
    获取时间序列统计数据

    Args:
        user_profile: 当前用户配置文件（用于权限过滤）
        start_date: 开始日期
        end_date: 结束日期
        granularity: 时间粒度 ('year', 'month', 'week', 'day')
        date_field: 日期字段 ('created_at', 'event_date', 'submitted_at')

    Returns:
        List[Dict[str, Any]]: 时间序列数据，格式符合Chart.js要求
    """
    from django.db.models.functions import TruncYear, TruncMonth, TruncWeek, TruncDay
    from django.db import connection

    # 基础查询集
    queryset = AdverseEventReport.objects.filter(is_deleted=False)

    # 权限过滤
    if user_profile:
        queryset = _apply_permission_filter(queryset, user_profile)

    # 先检查是否有任何数据
    if not queryset.exists():
        return []

    # 设置默认时间范围
    if not start_date:
        if granularity == 'year':
            start_date = timezone.now() - timedelta(days=365 * 3)  # 3年
        elif granularity == 'month':
            start_date = timezone.now() - timedelta(days=365)  # 1年
        elif granularity == 'week':
            start_date = timezone.now() - timedelta(days=90)  # 3个月
        else:  # day
            start_date = timezone.now() - timedelta(days=30)  # 30天

    if not end_date:
        end_date = timezone.now()

    # 日期筛选
    filter_kwargs = {
        f'{date_field}__gte': start_date,
        f'{date_field}__lte': end_date
    }
    queryset = queryset.filter(**filter_kwargs)

    # 再次检查筛选后是否有数据
    if not queryset.exists():
        return []

    # 尝试使用Django时间截断函数，如果失败则使用原生SQL
    try:
        # 选择时间截断函数
        trunc_func_map = {
            'year': TruncYear,
            'month': TruncMonth,
            'week': TruncWeek,
            'day': TruncDay
        }
        trunc_func = trunc_func_map.get(granularity, TruncMonth)

        # 按时间分组统计
        time_series_data = queryset.annotate(
            period=trunc_func(date_field)
        ).values('period').annotate(
            total_count=Count('id'),
            draft_count=Count(
                Case(When(status='draft', then=1), output_field=IntegerField())
            ),
            submitted_count=Count(
                Case(When(status='submitted', then=1), output_field=IntegerField())
            ),
            approved_count=Count(
                Case(When(status='approved', then=1), output_field=IntegerField())
            ),
            rejected_count=Count(
                Case(When(status='rejected', then=1), output_field=IntegerField())
            ),
            serious_count=Count(
                Case(
                    When(injury_level__in=['death', 'serious_injury'], then=1),
                    output_field=IntegerField()
                )
            ),
            death_count=Count(
                Case(When(injury_level='death', then=1), output_field=IntegerField())
            )
        ).order_by('period')

        # 测试查询是否能正常执行
        list(time_series_data)

    except Exception as e:
        # 如果时区相关错误，使用原生SQL降级处理
        import logging
        logger = logging.getLogger(__name__)
        logger.warning(f'获取时间序列数据失败: {e}')
        return _get_time_series_statistics_fallback(
            queryset, date_field, granularity, start_date, end_date
        )

    # 格式化为Chart.js兼容格式
    result = []
    for item in time_series_data:
        period = item['period']
        if granularity == 'year':
            label = period.strftime('%Y')
        elif granularity == 'month':
            label = period.strftime('%Y-%m')
        elif granularity == 'week':
            label = f"{period.strftime('%Y-%m-%d')} (W{period.isocalendar()[1]})"
        else:  # day
            label = period.strftime('%Y-%m-%d')

        result.append({
            'period': period.isoformat() if period else None,
            'label': label,
            'total_count': item['total_count'],
            'draft_count': item['draft_count'],
            'submitted_count': item['submitted_count'],
            'approved_count': item['approved_count'],
            'rejected_count': item['rejected_count'],
            'serious_count': item['serious_count'],
            'death_count': item['death_count'],
            'serious_ratio': round(
                (item['serious_count'] / item['total_count'] * 100) if item['total_count'] > 0 else 0,
                2
            )
        })

    return result


def _get_time_series_statistics_fallback(
    queryset,
    date_field: str,
    granularity: str,
    start_date: Optional[datetime],
    end_date: Optional[datetime]
) -> List[Dict[str, Any]]:
    """
    时间序列统计的降级处理函数
    当MySQL时区表未安装时使用原生SQL实现
    """
    from django.db import connection

    # 构建SQL格式化字符串
    format_map = {
        'year': '%Y',
        'month': '%Y-%m',
        'week': '%Y-%u',  # 年-周数
        'day': '%Y-%m-%d'
    }
    date_format = format_map.get(granularity, '%Y-%m')

    # 构建原生SQL查询
    sql = f"""
    SELECT
        DATE_FORMAT({date_field}, %s) as period_str,
        COUNT(*) as total_count,
        SUM(CASE WHEN status = 'draft' THEN 1 ELSE 0 END) as draft_count,
        SUM(CASE WHEN status = 'submitted' THEN 1 ELSE 0 END) as submitted_count,
        SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved_count,
        SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected_count,
        SUM(CASE WHEN injury_level IN ('death', 'serious_injury') THEN 1 ELSE 0 END) as serious_count,
        SUM(CASE WHEN injury_level = 'death' THEN 1 ELSE 0 END) as death_count
    FROM {queryset.model._meta.db_table}
    WHERE is_deleted = 0
    """

    params = [date_format]

    # 添加日期筛选
    if start_date:
        sql += f" AND {date_field} >= %s"
        params.append(start_date)
    if end_date:
        sql += f" AND {date_field} <= %s"
        params.append(end_date)

    sql += f" GROUP BY period_str ORDER BY period_str"

    # 执行原生SQL
    with connection.cursor() as cursor:
        cursor.execute(sql, params)
        rows = cursor.fetchall()

    # 格式化结果
    result = []
    for row in rows:
        period_str = row[0]

        # 解析期间字符串为日期对象
        try:
            if granularity == 'year':
                period = datetime.strptime(period_str, '%Y').replace(month=1, day=1)
                label = period_str
            elif granularity == 'month':
                period = datetime.strptime(period_str, '%Y-%m').replace(day=1)
                label = period_str
            elif granularity == 'week':
                year, week = period_str.split('-')
                period = datetime.strptime(f'{year}-W{week}-1', '%Y-W%W-%w')
                label = f"{period_str} (W{week})"
            else:  # day
                period = datetime.strptime(period_str, '%Y-%m-%d')
                label = period_str
        except ValueError:
            # 如果解析失败，使用字符串
            period = None
            label = period_str

        total_count = row[1]
        serious_count = row[6]

        result.append({
            'period': period.isoformat() if period else None,
            'label': label,
            'total_count': total_count,
            'draft_count': row[2],
            'submitted_count': row[3],
            'approved_count': row[4],
            'rejected_count': row[5],
            'serious_count': serious_count,
            'death_count': row[7],
            'serious_ratio': round(
                (serious_count / total_count * 100) if total_count > 0 else 0,
                2
            )
        })

    return result


@cache_statistics_result(timeout=2700)  # 缓存45分钟
def get_cross_dimension_statistics(
    user_profile: Optional[UserProfile] = None,
    *,
    dimension1: str = 'department',
    dimension2: str = 'injury_level',
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    limit: int = 50
) -> Dict[str, Any]:
    """
    获取交叉维度统计分析

    Args:
        user_profile: 当前用户配置文件（用于权限过滤）
        dimension1: 第一个维度 ('department', 'status', 'injury_level', 'device_name', 'manufacturer')
        dimension2: 第二个维度 ('department', 'status', 'injury_level', 'device_name', 'manufacturer')
        start_date: 开始日期
        end_date: 结束日期
        limit: 结果限制数量

    Returns:
        Dict[str, Any]: 交叉维度分析数据
    """

    # 基础查询集
    queryset = AdverseEventReport.objects.filter(is_deleted=False)

    # 权限过滤
    if user_profile:
        queryset = _apply_permission_filter(queryset, user_profile)

    # 日期筛选
    if start_date:
        queryset = queryset.filter(created_at__gte=start_date)
    if end_date:
        queryset = queryset.filter(created_at__lte=end_date)

    # 维度字段映射
    dimension_field_map = {
        'department': 'department__name',
        'status': 'status',
        'injury_level': 'injury_level',
        'device_name': 'device_name',
        'manufacturer': 'manufacturer',
        'reporter': 'reporter__display_name'
    }

    # 获取字段名
    field1 = dimension_field_map.get(dimension1, dimension1)
    field2 = dimension_field_map.get(dimension2, dimension2)

    # 交叉维度统计
    cross_stats = queryset.values(field1, field2).annotate(
        count=Count('id'),
        serious_count=Count(
            Case(
                When(injury_level__in=['death', 'serious_injury'], then=1),
                output_field=IntegerField()
            )
        )
    ).order_by('-count')[:limit]

    # 获取各维度的总计
    dimension1_totals = queryset.values(field1).annotate(
        total_count=Count('id')
    ).order_by('-total_count')

    dimension2_totals = queryset.values(field2).annotate(
        total_count=Count('id')
    ).order_by('-total_count')

    # 格式化结果
    result = {
        'cross_data': list(cross_stats),
        'dimension1_totals': list(dimension1_totals),
        'dimension2_totals': list(dimension2_totals),
        'dimension1_name': dimension1,
        'dimension2_name': dimension2,
        'total_records': queryset.count()
    }

    return result


@cache_statistics_result(timeout=1800)  # 缓存30分钟
def get_department_statistics(
    user_profile: Optional[UserProfile] = None,
    *,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    include_efficiency: bool = True
) -> List[Dict[str, Any]]:
    """
    获取科室统计分析

    Args:
        user_profile: 当前用户配置文件（用于权限过滤）
        start_date: 开始日期
        end_date: 结束日期
        include_efficiency: 是否包含效率分析

    Returns:
        List[Dict[str, Any]]: 科室统计数据
    """
    from django.db.models import Avg, Max, Min

    # 基础查询集
    queryset = AdverseEventReport.objects.filter(is_deleted=False)

    # 权限过滤
    if user_profile:
        queryset = _apply_permission_filter(queryset, user_profile)

    # 日期筛选
    if start_date:
        queryset = queryset.filter(created_at__gte=start_date)
    if end_date:
        queryset = queryset.filter(created_at__lte=end_date)

    # 构建权限过滤条件
    permission_filter = Q()
    if user_profile and not user_profile.is_admin:
        if user_profile.is_staff_member and user_profile.department:
            # 科室人员只能查看本科室的报告
            permission_filter = Q(department_reports__department=user_profile.department)
        else:
            # 其他用户只能查看自己的报告
            permission_filter = Q(department_reports__reporter=user_profile)

    # 构建基础过滤条件
    base_filter = Q(department_reports__is_deleted=False) & permission_filter

    # 添加日期过滤条件
    date_filter = Q()
    if start_date:
        date_filter &= Q(department_reports__created_at__gte=start_date)
    if end_date:
        date_filter &= Q(department_reports__created_at__lte=end_date)

    # 组合所有过滤条件
    final_filter = base_filter & date_filter

    # 科室统计
    department_stats = Department.objects.filter(
        is_deleted=False,
        is_active=True
    ).annotate(
        total_reports=Count(
            'department_reports',
            filter=final_filter
        ),
        draft_reports=Count(
            'department_reports',
            filter=final_filter & Q(department_reports__status='draft')
        ),
        submitted_reports=Count(
            'department_reports',
            filter=final_filter & Q(department_reports__status='submitted')
        ),
        approved_reports=Count(
            'department_reports',
            filter=final_filter & Q(department_reports__status='approved')
        ),
        rejected_reports=Count(
            'department_reports',
            filter=final_filter & Q(department_reports__status='rejected')
        ),
        serious_events=Count(
            'department_reports',
            filter=final_filter & Q(department_reports__injury_level__in=['death', 'serious_injury'])
        ),
        death_events=Count(
            'department_reports',
            filter=final_filter & Q(department_reports__injury_level='death')
        )
    ).order_by('-total_reports')

    result = []
    for dept in department_stats:
        dept_data = {
            'department_id': dept.id,
            'department_code': dept.code,
            'department_name': dept.name,
            'total_reports': dept.total_reports,
            'draft_reports': dept.draft_reports,
            'submitted_reports': dept.submitted_reports,
            'approved_reports': dept.approved_reports,
            'rejected_reports': dept.rejected_reports,
            'serious_events': dept.serious_events,
            'death_events': dept.death_events,
            'serious_ratio': round(
                (dept.serious_events / dept.total_reports * 100) if dept.total_reports > 0 else 0,
                2
            ),
            'approval_ratio': round(
                (dept.approved_reports / (dept.submitted_reports + dept.approved_reports + dept.rejected_reports) * 100)
                if (dept.submitted_reports + dept.approved_reports + dept.rejected_reports) > 0 else 0,
                2
            )
        }

        # 如果需要效率分析
        if include_efficiency and dept.total_reports > 0:
            # 计算平均处理时间
            dept_reports = queryset.filter(department=dept, submitted_at__isnull=False, reviewed_at__isnull=False)
            if dept_reports.exists():
                processing_times = []
                for report in dept_reports:
                    if report.submitted_at and report.reviewed_at:
                        processing_time = (report.reviewed_at - report.submitted_at).total_seconds() / 3600  # 小时
                        processing_times.append(processing_time)

                if processing_times:
                    dept_data.update({
                        'avg_processing_hours': round(sum(processing_times) / len(processing_times), 2),
                        'min_processing_hours': round(min(processing_times), 2),
                        'max_processing_hours': round(max(processing_times), 2)
                    })

        result.append(dept_data)

    return result


@cache_statistics_result(timeout=7200)  # 缓存2小时
def get_trend_analysis(
    user_profile: Optional[UserProfile] = None,
    *,
    metric: str = 'total_count',
    granularity: str = 'month',
    periods: int = 12
) -> Dict[str, Any]:
    """
    获取趋势分析数据

    Args:
        user_profile: 当前用户配置文件（用于权限过滤）
        metric: 分析指标 ('total_count', 'serious_count', 'approval_ratio')
        granularity: 时间粒度 ('month', 'week', 'day')
        periods: 时间周期数

    Returns:
        Dict[str, Any]: 趋势分析数据，包含增长率、预测等
    """

    # 计算时间范围
    if granularity == 'month':
        start_date = timezone.now() - timedelta(days=periods * 30)
    elif granularity == 'week':
        start_date = timezone.now() - timedelta(weeks=periods)
    else:  # day
        start_date = timezone.now() - timedelta(days=periods)

    # 获取时间序列数据
    try:
        time_series = get_time_series_statistics(
            user_profile=user_profile,
            start_date=start_date,
            granularity=granularity
        )
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.warning(f'获取趋势分析数据失败: {e}')
        return {
            'trend_data': [],
            'growth_rate': 0,
            'trend_direction': 'stable',
            'analysis': '暂无数据进行趋势分析（时区配置问题）'
        }

    if not time_series:
        return {
            'trend_data': [],
            'growth_rate': 0,
            'trend_direction': 'stable',
            'analysis': '暂无数据进行趋势分析'
        }

    # 提取指标数据
    values = [item.get(metric, 0) for item in time_series]
    labels = [item['label'] for item in time_series]

    # 计算增长率
    if len(values) >= 2:
        # 计算期间增长率
        first_half = values[:len(values)//2]
        second_half = values[len(values)//2:]

        first_avg = sum(first_half) / len(first_half) if first_half else 0
        second_avg = sum(second_half) / len(second_half) if second_half else 0

        growth_rate = ((second_avg - first_avg) / first_avg * 100) if first_avg > 0 else 0

        # 判断趋势方向
        if growth_rate > 5:
            trend_direction = 'increasing'
        elif growth_rate < -5:
            trend_direction = 'decreasing'
        else:
            trend_direction = 'stable'
    else:
        growth_rate = 0
        trend_direction = 'stable'

    # 计算移动平均
    moving_avg = []
    window_size = min(3, len(values))
    for i in range(len(values)):
        start_idx = max(0, i - window_size + 1)
        avg = sum(values[start_idx:i+1]) / (i - start_idx + 1)
        moving_avg.append(round(avg, 2))

    # 生成分析文本
    total_reports = sum(values)
    avg_per_period = total_reports / len(values) if values else 0

    # 指标中文映射
    if metric == 'total_count':
        metric_name = '报告总数'
    elif metric == 'serious_count':
        metric_name = '严重事件数'
    elif metric == 'approval_ratio':
        metric_name = '批准率'
    else:
        metric_name = metric

    # 时间粒度中文映射
    granularity_names = {
        'year': '年',
        'month': '月',
        'week': '周',
        'day': '天'
    }
    granularity_name = granularity_names.get(granularity, granularity)

    # 趋势方向中文映射
    trend_direction_names = {
        'increasing': '上升',
        'decreasing': '下降',
        'stable': '稳定'
    }
    trend_direction_name = trend_direction_names.get(trend_direction, trend_direction)

    analysis_text = f"在过去{periods}个{granularity_name}中，{metric_name}呈现{trend_direction_name}趋势，"
    analysis_text += f"平均每{granularity_name}{avg_per_period:.1f}，"
    analysis_text += f"增长率为{growth_rate:.1f}%。"

    return {
        'trend_data': [
            {
                'label': labels[i],
                'value': values[i],
                'moving_avg': moving_avg[i]
            }
            for i in range(len(values))
        ],
        'growth_rate': round(growth_rate, 2),
        'trend_direction': trend_direction,
        'total_value': total_reports,
        'average_per_period': round(avg_per_period, 2),
        'analysis': analysis_text,
        'metric': metric,
        'granularity': granularity,
        'periods': periods
    }
