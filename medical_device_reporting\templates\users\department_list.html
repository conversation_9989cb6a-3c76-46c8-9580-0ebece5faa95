{% extends 'base.html' %}
{% load static %}

{% block title %}科室管理 - 医疗器械不良事件上报平台{% endblock %}

{% block extra_css %}
<link href="{% static 'users/css/user_management.css' %}" rel="stylesheet">
<!-- DataTables CSS -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap5.min.css">
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/select/1.7.0/css/select.bootstrap5.min.css">
{% endblock %}

{% block nav_items %}
<li class="nav-item">
    <a class="nav-link" href="{% url 'users:dashboard' %}">
        <i class="bi bi-house"></i>
        用户中心
    </a>
</li>
{% if user.profile.is_admin %}
<li class="nav-item">
    <a class="nav-link" href="{% url 'users:user_list' %}">
        <i class="bi bi-people"></i>
        用户管理
    </a>
</li>
<li class="nav-item">
    <a class="nav-link active" href="{% url 'users:department_list' %}">
        <i class="bi bi-building"></i>
        科室管理
    </a>
</li>
{% endif %}
<li class="nav-item dropdown">
    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
        <i class="bi bi-person-circle"></i>
        {{ user.get_full_name|default:user.username }}
    </a>
    <ul class="dropdown-menu">
        <li><a class="dropdown-item" href="#"><i class="bi bi-gear me-2"></i>个人设置</a></li>
        <li><hr class="dropdown-divider"></li>
        <li><a class="dropdown-item" href="{% url 'users:logout' %}"><i class="bi bi-box-arrow-right me-2"></i>退出登录</a></li>
    </ul>
</li>
{% endblock %}

{% block content %}
<div class="department-management-container">
    <!-- 页面标题 -->
    <div class="page-header mb-4">
        <div class="row align-items-center">
            <div class="col">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'users:dashboard' %}">用户中心</a></li>
                        <li class="breadcrumb-item active">科室管理</li>
                    </ol>
                </nav>
                <h2 class="page-title">
                    <i class="bi bi-building me-2"></i>
                    科室管理
                </h2>
                <p class="page-subtitle text-muted">管理医院科室信息和用户分配</p>
            </div>
            <div class="col-auto">
                <div class="btn-group" role="group">
                    <a href="{% url 'users:department_create' %}" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>
                        新建科室
                    </a>
                    <button type="button" class="btn btn-outline-success" id="importBtn">
                        <i class="bi bi-upload me-2"></i>
                        导入Excel
                    </button>
                    <a href="{% url 'users:department_export_excel' %}" class="btn btn-outline-secondary">
                        <i class="bi bi-download me-2"></i>
                        导出Excel
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">总科室数</h6>
                            <h3 class="mb-0">{{ total_count|default:0 }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-building fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">启用科室</h6>
                            <h3 class="mb-0">{{ departments.paginator.object_list|length }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-check-circle fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">总用户数</h6>
                            <h3 class="mb-0">
                                {% for dept in departments %}
                                    {{ dept.total_users|add:0 }}
                                {% empty %}
                                    0
                                {% endfor %}
                            </h3>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-people fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">活跃用户</h6>
                            <h3 class="mb-0">
                                {% for dept in departments %}
                                    {{ dept.active_users|add:0 }}
                                {% empty %}
                                    0
                                {% endfor %}
                            </h3>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-person-check fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 筛选面板 -->
    <div class="filter-panel card mb-4">
        <div class="card-header">
            <h6 class="card-title mb-0">
                <i class="bi bi-funnel me-2"></i>
                筛选条件
            </h6>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3" id="filterForm">
                <div class="col-md-4">
                    <label for="statusFilter" class="form-label">状态</label>
                    <select class="form-select" id="statusFilter" name="is_active">
                        <option value="">全部状态</option>
                        <option value="true" {% if is_active == True %}selected{% endif %}>启用</option>
                        <option value="false" {% if is_active == False %}selected{% endif %}>禁用</option>
                    </select>
                </div>
                <div class="col-md-6">
                    <label for="searchInput" class="form-label">搜索</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="searchInput" name="search" 
                               value="{{ search }}" placeholder="科室代码、科室名称">
                        <button class="btn btn-outline-primary" type="submit">
                            <i class="bi bi-search"></i>
                        </button>
                        <button class="btn btn-outline-secondary" type="button" id="clearSearch">
                            <i class="bi bi-x"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="button" class="btn btn-outline-secondary" id="refreshBtn">
                            <i class="bi bi-arrow-clockwise me-1"></i>
                            刷新
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 科室列表 -->
    <div class="department-list-panel card">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-list me-2"></i>
                        科室列表
                    </h6>
                </div>
                <div class="col-auto">
                    <small class="text-muted">
                        共 {{ total_count }} 个科室
                    </small>
                </div>
            </div>
        </div>
        <div class="card-body">
            <!-- 数据表格 -->
            <div class="table-responsive">
                <table id="departmentTable" class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>科室代码</th>
                            <th>科室名称</th>
                            <th>状态</th>
                            <th>总用户数</th>
                            <th>活跃用户</th>
                            <th>管理员</th>
                            <th>科室人员</th>
                            <th>创建时间</th>
                            <th width="150">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for department in departments %}
                        <tr data-dept-id="{{ department.id }}">
                            <td>
                                <strong class="text-primary">{{ department.code }}</strong>
                            </td>
                            <td>{{ department.name }}</td>
                            <td>
                                <div class="form-check form-switch">
                                    <input class="form-check-input status-toggle" type="checkbox" 
                                           data-dept-id="{{ department.id }}"
                                           {% if department.is_active %}checked{% endif %}>
                                    <label class="form-check-label">
                                        {% if department.is_active %}
                                            <span class="badge bg-success">启用</span>
                                        {% else %}
                                            <span class="badge bg-secondary">禁用</span>
                                        {% endif %}
                                    </label>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ department.total_users|default:0 }}</span>
                            </td>
                            <td>
                                <span class="badge bg-success">{{ department.active_users|default:0 }}</span>
                            </td>
                            <td>
                                <span class="badge bg-warning">{{ department.admin_users|default:0 }}</span>
                            </td>
                            <td>
                                <span class="badge bg-primary">{{ department.staff_users|default:0 }}</span>
                            </td>
                            <td>
                                <small class="text-muted">
                                    {{ department.created_at|date:"Y-m-d H:i" }}
                                </small>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <a href="{% url 'users:department_detail' department.id %}" 
                                       class="btn btn-outline-info" title="查看详情">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="{% url 'users:department_edit' department.id %}" 
                                       class="btn btn-outline-primary" title="编辑">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <button type="button" class="btn btn-outline-danger delete-btn" 
                                            data-dept-id="{{ department.id }}" 
                                            data-dept-name="{{ department.name }}" title="删除">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="9" class="text-center text-muted py-4">
                                <i class="bi bi-inbox fs-1 d-block mb-2"></i>
                                暂无科室数据
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            {% if departments.has_other_pages %}
            <nav aria-label="科室列表分页">
                <ul class="pagination justify-content-center">
                    {% if departments.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ departments.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if is_active != None %}&is_active={{ is_active }}{% endif %}">
                                <i class="bi bi-chevron-left"></i>
                            </a>
                        </li>
                    {% endif %}
                    
                    {% for num in departments.paginator.page_range %}
                        {% if departments.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                        {% elif num > departments.number|add:'-3' and num < departments.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}{% if search %}&search={{ search }}{% endif %}{% if is_active != None %}&is_active={{ is_active }}{% endif %}">{{ num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if departments.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ departments.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if is_active != None %}&is_active={{ is_active }}{% endif %}">
                                <i class="bi bi-chevron-right"></i>
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>

<!-- Excel导入模态框 -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-upload me-2"></i>
                    导入科室Excel
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="importForm" method="post" enctype="multipart/form-data" action="{% url 'users:department_import' %}">
                <div class="modal-body">
                    {% csrf_token %}
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <label for="excel_file" class="form-label mb-0">选择Excel文件</label>
                            <a href="{% url 'users:department_template' %}" class="btn btn-outline-info btn-sm">
                                <i class="bi bi-download me-1"></i>
                                下载模板
                            </a>
                        </div>
                        <input type="file" class="form-control" id="excel_file" name="excel_file"
                               accept=".xlsx,.xls" required>
                        <div class="form-text">
                            <i class="bi bi-info-circle me-1"></i>
                            支持.xlsx和.xls格式，文件应包含：科室代码、科室名称、状态等列
                        </div>
                    </div>
                    <div class="alert alert-info">
                        <h6><i class="bi bi-lightbulb me-2"></i>导入说明：</h6>
                        <ul class="mb-0">
                            <li>Excel文件第一行为标题行，将被跳过</li>
                            <li>科室代码和名称为必填项</li>
                            <li>状态列：启用、1、True、true、是 表示启用</li>
                            <li>如果科室代码已存在，将更新现有科室信息</li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" id="importSubmitBtn" class="btn btn-primary">
                        <i class="bi bi-upload me-2"></i>
                        开始导入
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 确认删除模态框 -->
<div class="modal fade" id="confirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-exclamation-triangle text-warning me-2"></i>
                    确认删除
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p id="confirmMessage">确定要删除科室 <strong id="deptName"></strong> 吗？</p>
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    删除科室将影响该科室下的所有用户，请谨慎操作！
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">确定删除</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- DataTables JavaScript -->
<script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap5.min.js"></script>

<!-- 科室管理JavaScript -->
<script>
$(document).ready(function() {
    console.log('🔧 科室管理功能已加载');

    // 导入按钮事件
    $('#importBtn').on('click', function() {
        console.log('导入按钮被点击');
        $('#importModal').modal('show');
    });

    // 导入表单提交事件
    $('#importForm').on('submit', function(e) {
        e.preventDefault();

        const form = this;
        const fileInput = $('#excel_file')[0];
        const submitBtn = $('#importSubmitBtn');

        // 验证文件
        if (!fileInput.files || fileInput.files.length === 0) {
            alert('请选择要导入的Excel文件');
            return;
        }

        const file = fileInput.files[0];
        if (!file.name.match(/\.(xlsx|xls)$/i)) {
            alert('请选择Excel格式文件（.xlsx或.xls）');
            return;
        }

        // 显示加载状态
        const originalText = submitBtn.html();
        submitBtn.html('<i class="spinner-border spinner-border-sm me-2"></i>处理中...');
        submitBtn.prop('disabled', true);

        // 创建FormData
        const formData = new FormData(form);

        // 发送AJAX请求
        $.ajax({
            url: '/departments/import/',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'X-CSRFToken': $('[name=csrfmiddlewaretoken]').val()
            },
            success: function(response) {
                $('#importModal').modal('hide');
                alert('科室数据导入成功');
                window.location.reload();
            },
            error: function(xhr, status, error) {
                console.error('导入失败:', error);
                let errorMsg = '导入失败，请重试';

                if (xhr.responseJSON && xhr.responseJSON.error) {
                    errorMsg = xhr.responseJSON.error;
                }

                alert(errorMsg);
            },
            complete: function() {
                submitBtn.html(originalText);
                submitBtn.prop('disabled', false);
            }
        });
    });

    // 移除测试按钮（如果存在）
    $('#testBtn').remove();
});
</script>

<!-- 科室管理JavaScript -->
<script src="{% static 'users/js/department_list.js' %}"></script>
{% endblock %}
