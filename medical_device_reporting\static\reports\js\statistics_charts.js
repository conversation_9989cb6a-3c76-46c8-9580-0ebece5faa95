/**
 * 统计图表JavaScript模块
 * Statistics Charts JavaScript Module
 */

const StatisticsCharts = {
    // 图表实例存储
    charts: {},
    
    // 默认图表配置
    defaultOptions: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    padding: 20,
                    usePointStyle: true
                }
            },
            tooltip: {
                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                titleColor: '#fff',
                bodyColor: '#fff',
                borderColor: '#0d6efd',
                borderWidth: 1,
                cornerRadius: 8,
                displayColors: true
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                grid: {
                    color: 'rgba(0, 0, 0, 0.1)'
                },
                ticks: {
                    color: '#6c757d'
                }
            },
            x: {
                grid: {
                    color: 'rgba(0, 0, 0, 0.1)'
                },
                ticks: {
                    color: '#6c757d'
                }
            }
        }
    },

    // 颜色配置
    colors: {
        primary: '#0d6efd',
        success: '#198754',
        danger: '#dc3545',
        warning: '#ffc107',
        info: '#0dcaf0',
        secondary: '#6c757d',
        light: '#f8f9fa',
        dark: '#212529'
    },

    // 初始化统计仪表板
    init: function() {
        console.log('初始化统计图表...');

        // 检查数据是否存在
        if (typeof window.chartData === 'undefined') {
            console.warn('图表数据未找到');
            this.showError('数据加载失败');
            return;
        }

        // 初始化各个图表
        this.initTimeSeriesChart();
        this.initCrossDimensionChart();
        this.initDeviceStatsChart();

        if (window.isAdmin) {
            this.initDepartmentStatsChart();
        }

        // 绑定交互事件
        this.bindChartEvents();

        // 初始化筛选器
        this.initFilters();
    },

    // 绑定图表交互事件
    bindChartEvents: function() {
        // 时间粒度切换按钮
        const granularityButtons = document.querySelectorAll('[data-chart="time_series"][data-granularity]');
        granularityButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const granularity = button.getAttribute('data-granularity');
                this.updateTimeSeriesGranularity(granularity);

                // 更新按钮状态
                granularityButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');
            });
        });

        // 图表类型切换
        const chartTypeSelect = document.getElementById('chart_type');
        if (chartTypeSelect) {
            chartTypeSelect.addEventListener('change', () => {
                this.handleChartTypeChange();
            });
        }

        // 维度选择器变化
        const dimension1Select = document.getElementById('dimension1Select');
        const dimension2Select = document.getElementById('dimension2Select');

        if (dimension1Select && dimension2Select) {
            [dimension1Select, dimension2Select].forEach(select => {
                select.addEventListener('change', () => {
                    this.updateCrossDimensionChart();
                });
            });
        }
    },

    // 初始化筛选器
    initFilters: function() {
        // 日期范围选择器
        const startDateInput = document.getElementById('start_date');
        const endDateInput = document.getElementById('end_date');

        if (startDateInput && endDateInput) {
            [startDateInput, endDateInput].forEach(input => {
                input.addEventListener('change', () => {
                    this.handleDateRangeChange();
                });
            });
        }

        // 自动刷新定时器
        this.setupAutoRefresh();
    },

    // 设置自动刷新
    setupAutoRefresh: function() {
        // 每5分钟自动刷新一次数据
        this.refreshInterval = setInterval(() => {
            this.refreshAllCharts();
        }, 5 * 60 * 1000);
    },

    // 初始化详细分析
    initDetailAnalysis: function() {
        console.log('初始化详细分析图表...');
        
        if (typeof window.analysisData === 'undefined') {
            console.warn('分析数据未找到');
            this.showError('分析数据加载失败');
            return;
        }

        const analysisType = window.analysisType;
        
        switch (analysisType) {
            case 'time_series':
                this.initTimeSeriesDetailChart();
                this.populateTimeSeriesTable();
                break;
            case 'cross_dimension':
                this.initCrossDimensionDetailChart();
                this.populateDimensionStats();
                break;
            case 'device_stats':
                this.initDeviceDetailCharts();
                break;
            case 'department_stats':
                if (window.isAdmin) {
                    this.initDepartmentDetailChart();
                    this.populateDepartmentEfficiency();
                }
                break;
            case 'trend_analysis':
                this.initTrendAnalysisChart();
                this.populateTrendMetrics();
                break;
        }
        
        this.populateDetailTable();
    },

    // 时间序列图表
    initTimeSeriesChart: function() {
        const ctx = document.getElementById('timeSeriesChart');
        if (!ctx) return;

        const data = window.chartData.time_series || [];
        
        if (data.length === 0) {
            this.showChartMessage(ctx, '暂无时间序列数据');
            return;
        }

        const chartData = {
            labels: data.map(item => item.period),
            datasets: [{
                label: '报告数量',
                data: data.map(item => item.count),
                borderColor: this.colors.primary,
                backgroundColor: this.colors.primary + '20',
                fill: true,
                tension: 0.4
            }]
        };

        this.charts.timeSeries = new Chart(ctx, {
            type: 'line',
            data: chartData,
            options: {
                ...this.defaultOptions,
                plugins: {
                    ...this.defaultOptions.plugins,
                    title: {
                        display: true,
                        text: '时间趋势分析'
                    }
                }
            }
        });
    },

    // 交叉维度图表
    initCrossDimensionChart: function() {
        const ctx = document.getElementById('crossDimensionChart');
        if (!ctx) return;

        const data = window.chartData.cross_dimension || {};
        const crossData = data.cross_data || [];
        
        if (crossData.length === 0) {
            this.showChartMessage(ctx, '暂无交叉维度数据');
            return;
        }

        // 创建简单的柱状图显示交叉数据
        const chartData = {
            labels: crossData.map(item => `${item.dimension1_value} - ${item.dimension2_value}`),
            datasets: [{
                label: '报告数量',
                data: crossData.map(item => item.count),
                backgroundColor: this.generateColors(crossData.length)
            }]
        };

        this.charts.crossDimension = new Chart(ctx, {
            type: 'bar',
            data: chartData,
            options: {
                ...this.defaultOptions,
                plugins: {
                    ...this.defaultOptions.plugins,
                    title: {
                        display: true,
                        text: '交叉维度分析'
                    }
                },
                scales: {
                    ...this.defaultOptions.scales,
                    x: {
                        ...this.defaultOptions.scales.x,
                        ticks: {
                            ...this.defaultOptions.scales.x.ticks,
                            maxRotation: 45
                        }
                    }
                }
            }
        });
    },

    // 器械统计图表
    initDeviceStatsChart: function() {
        const ctx = document.getElementById('deviceStatsChart');
        if (!ctx) return;

        const data = window.chartData.device_stats || [];
        
        if (data.length === 0) {
            this.showChartMessage(ctx, '暂无器械统计数据');
            return;
        }

        const chartData = {
            labels: data.slice(0, 10).map(item => item.device_name),
            datasets: [{
                label: '报告数量',
                data: data.slice(0, 10).map(item => item.count),
                backgroundColor: this.generateColors(Math.min(data.length, 10))
            }]
        };

        this.charts.deviceStats = new Chart(ctx, {
            type: 'doughnut',
            data: chartData,
            options: {
                ...this.defaultOptions,
                plugins: {
                    ...this.defaultOptions.plugins,
                    title: {
                        display: true,
                        text: '热门器械统计'
                    }
                }
            }
        });
    },

    // 科室统计图表
    initDepartmentStatsChart: function() {
        const ctx = document.getElementById('departmentStatsChart');
        if (!ctx) return;

        const data = window.chartData.department_stats || [];
        
        if (data.length === 0) {
            this.showChartMessage(ctx, '暂无科室统计数据');
            return;
        }

        const chartData = {
            labels: data.map(item => item.department_name),
            datasets: [{
                label: '报告数量',
                data: data.map(item => item.count),
                backgroundColor: this.colors.info,
                borderColor: this.colors.primary,
                borderWidth: 1
            }]
        };

        this.charts.departmentStats = new Chart(ctx, {
            type: 'bar',
            data: chartData,
            options: {
                ...this.defaultOptions,
                plugins: {
                    ...this.defaultOptions.plugins,
                    title: {
                        display: true,
                        text: '科室统计分析'
                    }
                }
            }
        });
    },

    // 显示图表错误信息
    showError: function(message) {
        const containers = document.querySelectorAll('.chart-container');
        containers.forEach(container => {
            this.showChartMessage(container, message, 'error');
        });
    },

    // 显示图表消息
    showChartMessage: function(container, message, type = 'info') {
        const iconClass = type === 'error' ? 'bi-exclamation-triangle' : 'bi-info-circle';
        const textClass = type === 'error' ? 'text-danger' : 'text-muted';
        
        container.innerHTML = `
            <div class="chart-${type} ${textClass}">
                <i class="bi ${iconClass} display-4 mb-3"></i>
                <p>${message}</p>
            </div>
        `;
    },

    // 生成颜色数组
    generateColors: function(count) {
        const baseColors = [
            this.colors.primary,
            this.colors.success,
            this.colors.danger,
            this.colors.warning,
            this.colors.info,
            this.colors.secondary
        ];
        
        const colors = [];
        for (let i = 0; i < count; i++) {
            colors.push(baseColors[i % baseColors.length]);
        }
        return colors;
    },

    // 动态数据更新方法
    updateTimeSeriesGranularity: function(granularity) {
        this.showChartLoading('timeSeriesChart');

        const params = new URLSearchParams(window.location.search);
        params.set('granularity', granularity);

        this.fetchChartData('time_series', params)
            .then(data => {
                this.updateTimeSeriesChartData(data);
            })
            .catch(error => {
                this.showChartError('timeSeriesChart', '更新时间序列数据失败');
                console.error('更新时间序列数据失败:', error);
            });
    },

    updateCrossDimensionChart: function() {
        const dimension1 = document.getElementById('dimension1Select').value;
        const dimension2 = document.getElementById('dimension2Select').value;

        this.showChartLoading('crossDimensionChart');

        const params = new URLSearchParams(window.location.search);
        params.set('chart_type', 'cross_dimension');
        params.set('dimension1', dimension1);
        params.set('dimension2', dimension2);

        this.fetchChartData('cross_dimension', params)
            .then(data => {
                this.updateCrossDimensionChartData(data);
            })
            .catch(error => {
                this.showChartError('crossDimensionChart', '更新交叉维度数据失败');
                console.error('更新交叉维度数据失败:', error);
            });
    },

    handleChartTypeChange: function() {
        const chartType = document.getElementById('chart_type').value;

        // 根据图表类型显示/隐藏相关控件
        this.toggleChartControls(chartType);

        // 如果是自动提交的表单，直接提交
        const form = document.getElementById('filterForm');
        if (form && form.hasAttribute('data-auto-submit')) {
            form.submit();
        }
    },

    handleDateRangeChange: function() {
        const startDate = document.getElementById('start_date').value;
        const endDate = document.getElementById('end_date').value;

        if (startDate && endDate && startDate > endDate) {
            showWarningMessage('开始日期不能晚于结束日期');
            return;
        }

        // 延迟更新，避免频繁请求
        clearTimeout(this.dateChangeTimeout);
        this.dateChangeTimeout = setTimeout(() => {
            this.refreshAllCharts();
        }, 1000);
    },

    toggleChartControls: function(chartType) {
        // 隐藏所有特定控件
        const controls = {
            'granularity': document.querySelector('[data-control="granularity"]'),
            'dimensions': document.querySelector('[data-control="dimensions"]'),
            'metrics': document.querySelector('[data-control="metrics"]')
        };

        Object.values(controls).forEach(control => {
            if (control) control.style.display = 'none';
        });

        // 根据图表类型显示相应控件
        switch (chartType) {
            case 'time_series':
            case 'trend_analysis':
                if (controls.granularity) controls.granularity.style.display = 'block';
                if (controls.metrics && chartType === 'trend_analysis') {
                    controls.metrics.style.display = 'block';
                }
                break;
            case 'cross_dimension':
                if (controls.dimensions) controls.dimensions.style.display = 'block';
                break;
        }
    },

    // AJAX数据获取
    fetchChartData: function(chartType, params = new URLSearchParams()) {
        const url = `${ReportsConfig.apiBaseUrl}/reports/statistics/?${params.toString()}`;

        return ReportsUtils.ajax(url, {
            method: 'GET'
        }).then(response => {
            if (response.chart_type && response.chart_type !== chartType) {
                console.warn(`期望图表类型 ${chartType}，但收到 ${response.chart_type}`);
            }
            return response;
        });
    },

    refreshAllCharts: function() {
        console.log('刷新所有图表数据...');

        // 显示全局加载状态
        this.showGlobalLoading(true);

        const params = new URLSearchParams(window.location.search);

        Promise.all([
            this.fetchChartData('time_series', new URLSearchParams(params)),
            this.fetchChartData('cross_dimension', new URLSearchParams(params)),
            this.fetchChartData('device_stats', new URLSearchParams(params)),
            window.isAdmin ? this.fetchChartData('department_stats', new URLSearchParams(params)) : Promise.resolve(null)
        ]).then(([timeSeriesData, crossDimensionData, deviceStatsData, departmentStatsData]) => {
            // 更新图表数据
            if (timeSeriesData && timeSeriesData.data) {
                this.updateTimeSeriesChartData(timeSeriesData.data);
            }
            if (crossDimensionData && crossDimensionData.data) {
                this.updateCrossDimensionChartData(crossDimensionData.data);
            }
            if (deviceStatsData && deviceStatsData.data) {
                this.updateDeviceStatsChartData(deviceStatsData.data);
            }
            if (departmentStatsData && departmentStatsData.data && window.isAdmin) {
                this.updateDepartmentStatsChartData(departmentStatsData.data);
            }

            showSuccessMessage('图表数据已更新');
        }).catch(error => {
            console.error('刷新图表数据失败:', error);
            showErrorMessage('刷新图表数据失败，请稍后重试');
        }).finally(() => {
            this.showGlobalLoading(false);
        });
    },

    // 图表数据更新方法
    updateTimeSeriesChartData: function(data) {
        const chart = this.charts.timeSeries;
        if (!chart || !data || data.length === 0) {
            this.showChartMessage(document.getElementById('timeSeriesChart'), '暂无时间序列数据');
            return;
        }

        chart.data.labels = data.map(item => item.period);
        chart.data.datasets[0].data = data.map(item => item.count);
        chart.update('active');

        this.hideChartLoading('timeSeriesChart');
    },

    updateCrossDimensionChartData: function(data) {
        const chart = this.charts.crossDimension;
        if (!chart || !data || !data.cross_data || data.cross_data.length === 0) {
            this.showChartMessage(document.getElementById('crossDimensionChart'), '暂无交叉维度数据');
            return;
        }

        const crossData = data.cross_data;
        chart.data.labels = crossData.map(item => `${item.dimension1_value} - ${item.dimension2_value}`);
        chart.data.datasets[0].data = crossData.map(item => item.count);
        chart.data.datasets[0].backgroundColor = this.generateColors(crossData.length);
        chart.update('active');

        this.hideChartLoading('crossDimensionChart');
    },

    updateDeviceStatsChartData: function(data) {
        const chart = this.charts.deviceStats;
        if (!chart || !data || data.length === 0) {
            this.showChartMessage(document.getElementById('deviceStatsChart'), '暂无器械统计数据');
            return;
        }

        const topDevices = data.slice(0, 10);
        chart.data.labels = topDevices.map(item => item.device_name);
        chart.data.datasets[0].data = topDevices.map(item => item.count);
        chart.data.datasets[0].backgroundColor = this.generateColors(topDevices.length);
        chart.update('active');

        this.hideChartLoading('deviceStatsChart');
    },

    updateDepartmentStatsChartData: function(data) {
        const chart = this.charts.departmentStats;
        if (!chart || !data || data.length === 0) {
            this.showChartMessage(document.getElementById('departmentStatsChart'), '暂无科室统计数据');
            return;
        }

        chart.data.labels = data.map(item => item.department_name);
        chart.data.datasets[0].data = data.map(item => item.count);
        chart.update('active');

        this.hideChartLoading('departmentStatsChart');
    },

    // 加载状态管理
    showChartLoading: function(chartId) {
        const container = document.getElementById(chartId);
        if (!container) return;

        container.innerHTML = `
            <div class="chart-loading">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2">正在更新图表数据...</p>
            </div>
        `;
    },

    hideChartLoading: function(chartId) {
        // 加载状态会在图表更新时自动隐藏
        // 这里主要用于错误处理后的清理
    },

    showChartError: function(chartId, message) {
        const container = document.getElementById(chartId);
        if (!container) return;

        this.showChartMessage(container, message, 'error');
    },

    showGlobalLoading: function(show) {
        const loadingOverlay = document.getElementById('globalLoadingOverlay');

        if (show) {
            if (!loadingOverlay) {
                const overlay = document.createElement('div');
                overlay.id = 'globalLoadingOverlay';
                overlay.className = 'position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center';
                overlay.style.backgroundColor = 'rgba(255, 255, 255, 0.8)';
                overlay.style.zIndex = '9999';
                overlay.innerHTML = `
                    <div class="text-center">
                        <div class="spinner-border text-primary mb-3" style="width: 3rem; height: 3rem;" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="h5">正在更新数据...</p>
                    </div>
                `;
                document.body.appendChild(overlay);
            } else {
                loadingOverlay.style.display = 'flex';
            }
        } else {
            if (loadingOverlay) {
                loadingOverlay.style.display = 'none';
            }
        }
    },

    // 详细分析图表实现
    initTimeSeriesDetailChart: function() {
        const ctx = document.getElementById('timeSeriesDetailChart');
        if (!ctx) return;

        const data = window.analysisData || [];

        if (data.length === 0) {
            this.showChartMessage(ctx, '暂无时间序列详细数据');
            return;
        }

        const chartData = {
            labels: data.map(item => item.period),
            datasets: [
                {
                    label: '总报告数',
                    data: data.map(item => item.count),
                    borderColor: this.colors.primary,
                    backgroundColor: this.colors.primary + '20',
                    fill: true,
                    tension: 0.4,
                    yAxisID: 'y'
                },
                {
                    label: '严重事件数',
                    data: data.map(item => item.serious_count || 0),
                    borderColor: this.colors.danger,
                    backgroundColor: this.colors.danger + '20',
                    fill: false,
                    tension: 0.4,
                    yAxisID: 'y'
                }
            ]
        };

        this.charts.timeSeriesDetail = new Chart(ctx, {
            type: 'line',
            data: chartData,
            options: {
                ...this.defaultOptions,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                plugins: {
                    ...this.defaultOptions.plugins,
                    title: {
                        display: true,
                        text: '时间序列详细分析'
                    }
                },
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: '时间'
                        }
                    },
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: '报告数量'
                        }
                    }
                }
            }
        });

        // 填充对应的数据表格
        this.populateTimeSeriesTable(data);
    },

    // 数据表格填充方法
    populateTimeSeriesTable: function(data = null) {
        const tableBody = document.getElementById('timeSeriesTableBody');
        if (!tableBody) return;

        const timeSeriesData = data || window.analysisData || [];

        if (timeSeriesData.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="4" class="text-center text-muted">暂无数据</td></tr>';
            return;
        }

        const rows = timeSeriesData.map(item => {
            const total = item.count || 0;
            const serious = item.serious_count || 0;
            const ratio = total > 0 ? ((serious / total) * 100).toFixed(1) : '0.0';

            return `
                <tr>
                    <td>${item.period}</td>
                    <td>${total}</td>
                    <td>${serious}</td>
                    <td>${ratio}%</td>
                </tr>
            `;
        }).join('');

        tableBody.innerHTML = rows;
    },

    initCrossDimensionDetailChart: function() {
        const ctx = document.getElementById('crossDimensionDetailChart');
        if (!ctx) return;

        const data = window.analysisData || {};
        const crossData = data.cross_data || [];

        if (crossData.length === 0) {
            this.showChartMessage(ctx, '暂无交叉维度详细数据');
            return;
        }

        // 创建热力图数据
        const chartData = {
            labels: crossData.map(item => `${item.dimension1_value} - ${item.dimension2_value}`),
            datasets: [{
                label: '报告数量',
                data: crossData.map(item => item.count),
                backgroundColor: this.generateColors(crossData.length),
                borderColor: '#fff',
                borderWidth: 2
            }]
        };

        this.charts.crossDimensionDetail = new Chart(ctx, {
            type: 'bar',
            data: chartData,
            options: {
                ...this.defaultOptions,
                indexAxis: 'y', // 水平柱状图
                plugins: {
                    ...this.defaultOptions.plugins,
                    title: {
                        display: true,
                        text: '交叉维度详细分析'
                    }
                },
                scales: {
                    x: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '报告数量'
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: '维度组合'
                        }
                    }
                }
            }
        });

        // 填充维度统计
        this.populateDimensionStats(data);
    },

    populateDimensionStats: function(data = null) {
        const dimension1Stats = document.getElementById('dimension1Stats');
        const dimension2Stats = document.getElementById('dimension2Stats');

        if (!dimension1Stats || !dimension2Stats) return;

        const analysisData = data || window.analysisData || {};
        const dim1Totals = analysisData.dimension1_totals || [];
        const dim2Totals = analysisData.dimension2_totals || [];

        // 填充维度1统计
        if (dim1Totals.length > 0) {
            const dim1Html = dim1Totals.map(item => `
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="text-truncate">${item.value}</span>
                    <span class="badge bg-primary">${item.count}</span>
                </div>
            `).join('');
            dimension1Stats.innerHTML = dim1Html;
        } else {
            dimension1Stats.innerHTML = '<p class="text-muted">暂无数据</p>';
        }

        // 填充维度2统计
        if (dim2Totals.length > 0) {
            const dim2Html = dim2Totals.map(item => `
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="text-truncate">${item.value}</span>
                    <span class="badge bg-secondary">${item.count}</span>
                </div>
            `).join('');
            dimension2Stats.innerHTML = dim2Html;
        } else {
            dimension2Stats.innerHTML = '<p class="text-muted">暂无数据</p>';
        }
    },

    initDeviceDetailCharts: function() {
        // 器械数量图表
        const countCtx = document.getElementById('deviceCountChart');
        if (countCtx) {
            this.initDeviceCountChart(countCtx);
        }

        // 器械风险图表
        const riskCtx = document.getElementById('deviceRiskChart');
        if (riskCtx) {
            this.initDeviceRiskChart(riskCtx);
        }
    },

    initDeviceCountChart: function(ctx) {
        const data = window.analysisData || [];

        if (data.length === 0) {
            this.showChartMessage(ctx, '暂无器械统计数据');
            return;
        }

        const chartData = {
            labels: data.slice(0, 15).map(item => item.device_name),
            datasets: [{
                label: '报告数量',
                data: data.slice(0, 15).map(item => item.count),
                backgroundColor: this.colors.primary,
                borderColor: this.colors.primary,
                borderWidth: 1
            }]
        };

        this.charts.deviceCount = new Chart(ctx, {
            type: 'bar',
            data: chartData,
            options: {
                ...this.defaultOptions,
                plugins: {
                    ...this.defaultOptions.plugins,
                    title: {
                        display: true,
                        text: '器械报告数量排行'
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            maxRotation: 45
                        }
                    },
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '报告数量'
                        }
                    }
                }
            }
        });
    },

    initDeviceRiskChart: function(ctx) {
        const data = window.analysisData || [];

        if (data.length === 0) {
            this.showChartMessage(ctx, '暂无器械风险数据');
            return;
        }

        // 计算风险比例
        const riskData = data.slice(0, 10).map(item => ({
            device_name: item.device_name,
            risk_ratio: item.risk_ratio || 0
        }));

        const chartData = {
            labels: riskData.map(item => item.device_name),
            datasets: [{
                label: '风险比例 (%)',
                data: riskData.map(item => item.risk_ratio),
                backgroundColor: riskData.map(item => {
                    if (item.risk_ratio > 50) return this.colors.danger;
                    if (item.risk_ratio > 20) return this.colors.warning;
                    return this.colors.success;
                }),
                borderWidth: 1
            }]
        };

        this.charts.deviceRisk = new Chart(ctx, {
            type: 'doughnut',
            data: chartData,
            options: {
                ...this.defaultOptions,
                plugins: {
                    ...this.defaultOptions.plugins,
                    title: {
                        display: true,
                        text: '器械风险分析'
                    }
                }
            }
        });
    },

    initDepartmentDetailChart: function() {
        const ctx = document.getElementById('departmentDetailChart');
        if (!ctx) return;

        const data = window.analysisData || [];

        if (data.length === 0) {
            this.showChartMessage(ctx, '暂无科室统计详细数据');
            return;
        }

        const chartData = {
            labels: data.map(item => item.department_name),
            datasets: [
                {
                    label: '总报告数',
                    data: data.map(item => item.count),
                    backgroundColor: this.colors.primary + '80',
                    borderColor: this.colors.primary,
                    borderWidth: 1,
                    yAxisID: 'y'
                },
                {
                    label: '严重事件数',
                    data: data.map(item => item.serious_count || 0),
                    backgroundColor: this.colors.danger + '80',
                    borderColor: this.colors.danger,
                    borderWidth: 1,
                    yAxisID: 'y'
                }
            ]
        };

        this.charts.departmentDetail = new Chart(ctx, {
            type: 'bar',
            data: chartData,
            options: {
                ...this.defaultOptions,
                plugins: {
                    ...this.defaultOptions.plugins,
                    title: {
                        display: true,
                        text: '科室详细统计分析'
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            maxRotation: 45
                        }
                    },
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '报告数量'
                        }
                    }
                }
            }
        });

        // 填充效率分析
        this.populateDepartmentEfficiency(data);
    },

    populateDepartmentEfficiency: function(data = null) {
        const efficiencyDiv = document.getElementById('departmentEfficiency');
        if (!efficiencyDiv) return;

        const departmentData = data || window.analysisData || [];

        if (departmentData.length === 0) {
            efficiencyDiv.innerHTML = '<p class="text-muted">暂无效率数据</p>';
            return;
        }

        const efficiencyHtml = departmentData.slice(0, 5).map(item => {
            const avgProcessTime = item.avg_process_time || 0;
            const approvalRate = item.approval_rate || 0;

            return `
                <div class="mb-3 p-3 border rounded">
                    <h6 class="mb-2">${item.department_name}</h6>
                    <div class="row">
                        <div class="col-6">
                            <small class="text-muted">平均处理时间</small>
                            <div class="fw-bold">${avgProcessTime.toFixed(1)} 天</div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">批准率</small>
                            <div class="fw-bold">${approvalRate.toFixed(1)}%</div>
                        </div>
                    </div>
                </div>
            `;
        }).join('');

        efficiencyDiv.innerHTML = efficiencyHtml;
    },

    initTrendAnalysisChart: function() {
        const ctx = document.getElementById('trendAnalysisChart');
        if (!ctx) return;

        const data = window.analysisData || {};
        const trendData = data.trend_data || [];

        if (trendData.length === 0) {
            this.showChartMessage(ctx, '暂无趋势分析数据');
            return;
        }

        const chartData = {
            labels: trendData.map(item => item.period),
            datasets: [{
                label: '趋势值',
                data: trendData.map(item => item.value),
                borderColor: this.colors.primary,
                backgroundColor: this.colors.primary + '20',
                fill: true,
                tension: 0.4
            }]
        };

        this.charts.trendAnalysis = new Chart(ctx, {
            type: 'line',
            data: chartData,
            options: {
                ...this.defaultOptions,
                plugins: {
                    ...this.defaultOptions.plugins,
                    title: {
                        display: true,
                        text: '趋势分析图表'
                    }
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: '时间'
                        }
                    },
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '数值'
                        }
                    }
                }
            }
        });

        // 填充趋势指标
        this.populateTrendMetrics(data);
    },

    populateTrendMetrics: function(data = null) {
        const metricsDiv = document.getElementById('trendMetrics');
        if (!metricsDiv) return;

        const trendData = data || window.analysisData || {};
        const growthRate = trendData.growth_rate || 0;
        const trendDirection = trendData.trend_direction || 'stable';
        const analysis = trendData.analysis || '暂无分析';

        const directionIcon = {
            'increasing': 'bi-arrow-up text-success',
            'decreasing': 'bi-arrow-down text-danger',
            'stable': 'bi-arrow-right text-secondary'
        }[trendDirection] || 'bi-arrow-right text-secondary';

        const directionText = {
            'increasing': '上升趋势',
            'decreasing': '下降趋势',
            'stable': '稳定趋势'
        }[trendDirection] || '稳定趋势';

        metricsDiv.innerHTML = `
            <div class="text-center mb-3">
                <div class="metric-value">${growthRate.toFixed(1)}%</div>
                <div class="metric-label">增长率</div>
            </div>
            <div class="text-center mb-3">
                <i class="bi ${directionIcon} fs-4"></i>
                <div class="mt-1">${directionText}</div>
            </div>
            <div class="mt-3">
                <h6>分析结果</h6>
                <p class="small text-muted">${analysis}</p>
            </div>
        `;
    },

    populateDetailTable: function() {
        const tableHead = document.getElementById('detailTableHead');
        const tableBody = document.getElementById('detailTableBody');

        if (!tableHead || !tableBody) return;

        const analysisType = window.analysisType;
        const data = window.analysisData;

        if (!data) {
            tableBody.innerHTML = '<tr><td colspan="100%" class="text-center text-muted">暂无详细数据</td></tr>';
            return;
        }

        // 根据分析类型生成不同的表格
        switch (analysisType) {
            case 'time_series':
                this.generateTimeSeriesTable(tableHead, tableBody, data);
                break;
            case 'cross_dimension':
                this.generateCrossDimensionTable(tableHead, tableBody, data);
                break;
            case 'device_stats':
                this.generateDeviceStatsTable(tableHead, tableBody, data);
                break;
            case 'department_stats':
                this.generateDepartmentStatsTable(tableHead, tableBody, data);
                break;
            case 'trend_analysis':
                this.generateTrendAnalysisTable(tableHead, tableBody, data);
                break;
            default:
                tableBody.innerHTML = '<tr><td colspan="100%" class="text-center text-muted">未知的分析类型</td></tr>';
        }
    },

    generateTimeSeriesTable: function(tableHead, tableBody, data) {
        tableHead.innerHTML = `
            <tr>
                <th>时间</th>
                <th>总报告数</th>
                <th>严重事件数</th>
                <th>死亡事件数</th>
                <th>严重事件比例</th>
            </tr>
        `;

        if (!Array.isArray(data) || data.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="5" class="text-center text-muted">暂无数据</td></tr>';
            return;
        }

        const rows = data.map(item => {
            const total = item.count || 0;
            const serious = item.serious_count || 0;
            const death = item.death_count || 0;
            const ratio = total > 0 ? ((serious / total) * 100).toFixed(1) : '0.0';

            return `
                <tr>
                    <td>${item.period}</td>
                    <td>${total}</td>
                    <td>${serious}</td>
                    <td>${death}</td>
                    <td>${ratio}%</td>
                </tr>
            `;
        }).join('');

        tableBody.innerHTML = rows;
    },

    generateCrossDimensionTable: function(tableHead, tableBody, data) {
        tableHead.innerHTML = `
            <tr>
                <th>维度1值</th>
                <th>维度2值</th>
                <th>报告数量</th>
                <th>占比</th>
            </tr>
        `;

        const crossData = data.cross_data || [];
        const totalRecords = data.total_records || 0;

        if (crossData.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="4" class="text-center text-muted">暂无数据</td></tr>';
            return;
        }

        const rows = crossData.map(item => {
            const percentage = totalRecords > 0 ? ((item.count / totalRecords) * 100).toFixed(1) : '0.0';

            return `
                <tr>
                    <td>${item.dimension1_value}</td>
                    <td>${item.dimension2_value}</td>
                    <td>${item.count}</td>
                    <td>${percentage}%</td>
                </tr>
            `;
        }).join('');

        tableBody.innerHTML = rows;
    },

    generateDeviceStatsTable: function(tableHead, tableBody, data) {
        tableHead.innerHTML = `
            <tr>
                <th>器械名称</th>
                <th>制造商</th>
                <th>报告数量</th>
                <th>风险比例</th>
                <th>最近报告时间</th>
            </tr>
        `;

        if (!Array.isArray(data) || data.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="5" class="text-center text-muted">暂无数据</td></tr>';
            return;
        }

        const rows = data.map(item => {
            const riskRatio = (item.risk_ratio || 0).toFixed(1);
            const lastReportDate = item.last_report_date ?
                ReportsUtils.formatDate(item.last_report_date, 'YYYY-MM-DD') : '未知';

            return `
                <tr>
                    <td>${item.device_name}</td>
                    <td>${item.manufacturer || '未知'}</td>
                    <td>${item.count}</td>
                    <td>${riskRatio}%</td>
                    <td>${lastReportDate}</td>
                </tr>
            `;
        }).join('');

        tableBody.innerHTML = rows;
    },

    generateDepartmentStatsTable: function(tableHead, tableBody, data) {
        tableHead.innerHTML = `
            <tr>
                <th>科室名称</th>
                <th>报告数量</th>
                <th>严重事件数</th>
                <th>平均处理时间</th>
                <th>批准率</th>
            </tr>
        `;

        if (!Array.isArray(data) || data.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="5" class="text-center text-muted">暂无数据</td></tr>';
            return;
        }

        const rows = data.map(item => {
            const avgProcessTime = (item.avg_process_time || 0).toFixed(1);
            const approvalRate = (item.approval_rate || 0).toFixed(1);

            return `
                <tr>
                    <td>${item.department_name}</td>
                    <td>${item.count}</td>
                    <td>${item.serious_count || 0}</td>
                    <td>${avgProcessTime} 天</td>
                    <td>${approvalRate}%</td>
                </tr>
            `;
        }).join('');

        tableBody.innerHTML = rows;
    },

    generateTrendAnalysisTable: function(tableHead, tableBody, data) {
        tableHead.innerHTML = `
            <tr>
                <th>时间</th>
                <th>数值</th>
                <th>变化量</th>
                <th>变化率</th>
            </tr>
        `;

        const trendData = data.trend_data || [];

        if (trendData.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="4" class="text-center text-muted">暂无数据</td></tr>';
            return;
        }

        const rows = trendData.map((item, index) => {
            const change = index > 0 ? item.value - trendData[index - 1].value : 0;
            const changeRate = index > 0 && trendData[index - 1].value > 0 ?
                ((change / trendData[index - 1].value) * 100).toFixed(1) : '0.0';

            const changeClass = change > 0 ? 'text-success' : (change < 0 ? 'text-danger' : 'text-muted');
            const changeIcon = change > 0 ? '↑' : (change < 0 ? '↓' : '→');

            return `
                <tr>
                    <td>${item.period}</td>
                    <td>${item.value}</td>
                    <td class="${changeClass}">${changeIcon} ${Math.abs(change)}</td>
                    <td class="${changeClass}">${changeRate}%</td>
                </tr>
            `;
        }).join('');

        tableBody.innerHTML = rows;
    },

    // 销毁所有图表
    destroy: function() {
        // 清理定时器
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }
        if (this.dateChangeTimeout) {
            clearTimeout(this.dateChangeTimeout);
        }

        // 销毁所有图表实例
        Object.values(this.charts).forEach(chart => {
            if (chart && typeof chart.destroy === 'function') {
                chart.destroy();
            }
        });
        this.charts = {};

        // 清理全局加载覆盖层
        const loadingOverlay = document.getElementById('globalLoadingOverlay');
        if (loadingOverlay) {
            loadingOverlay.remove();
        }
    }
};

// 页面卸载时清理图表
window.addEventListener('beforeunload', function() {
    StatisticsCharts.destroy();
});

// 导出功能
const StatisticsExport = {
    // 导出统计数据
    exportStatistics: function(format = 'excel', analysisType = 'summary') {
        const params = new URLSearchParams(window.location.search);
        params.set('format', format);
        params.set('analysis_type', analysisType);

        // 添加当前筛选条件
        const currentFilters = this.getCurrentFilters();
        Object.entries(currentFilters).forEach(([key, value]) => {
            if (value) {
                params.set(key, value);
            }
        });

        const exportUrl = `${ReportsConfig.apiBaseUrl}/reports/export/?${params.toString()}`;

        // 显示导出进度
        this.showExportProgress(true);

        // 创建隐藏的下载链接
        const link = document.createElement('a');
        link.href = exportUrl;
        link.style.display = 'none';
        document.body.appendChild(link);

        // 监听下载完成
        const checkDownload = setInterval(() => {
            // 简单的下载完成检测（实际项目中可能需要更复杂的逻辑）
            setTimeout(() => {
                this.showExportProgress(false);
                showSuccessMessage(`${format.toUpperCase()}文件导出成功`);
                clearInterval(checkDownload);
            }, 2000);
        }, 100);

        // 触发下载
        link.click();
        document.body.removeChild(link);
    },

    // 导出详细数据
    exportDetailData: function(format = 'excel') {
        const analysisType = window.analysisType || 'summary';
        this.exportStatistics(format, analysisType);
    },

    // 获取当前筛选条件
    getCurrentFilters: function() {
        const filters = {};

        // 从URL参数获取
        const urlParams = new URLSearchParams(window.location.search);
        const filterKeys = ['chart_type', 'granularity', 'start_date', 'end_date', 'dimension1', 'dimension2', 'metric', 'limit'];

        filterKeys.forEach(key => {
            const value = urlParams.get(key);
            if (value) {
                filters[key] = value;
            }
        });

        // 从表单元素获取
        const formElements = [
            'chart_type', 'granularity', 'start_date', 'end_date',
            'dimension1', 'dimension2', 'metric', 'analysis_type'
        ];

        formElements.forEach(id => {
            const element = document.getElementById(id);
            if (element && element.value) {
                filters[id] = element.value;
            }
        });

        return filters;
    },

    // 显示导出进度
    showExportProgress: function(show) {
        let progressModal = document.getElementById('exportProgressModal');

        if (show) {
            if (!progressModal) {
                progressModal = document.createElement('div');
                progressModal.id = 'exportProgressModal';
                progressModal.className = 'modal fade';
                progressModal.innerHTML = `
                    <div class="modal-dialog modal-sm">
                        <div class="modal-content">
                            <div class="modal-body text-center">
                                <div class="spinner-border text-primary mb-3" role="status">
                                    <span class="visually-hidden">导出中...</span>
                                </div>
                                <p>正在导出数据，请稍候...</p>
                            </div>
                        </div>
                    </div>
                `;
                document.body.appendChild(progressModal);
            }

            // 显示模态框
            if (typeof bootstrap !== 'undefined') {
                const modal = new bootstrap.Modal(progressModal);
                modal.show();
            } else {
                progressModal.style.display = 'block';
                progressModal.classList.add('show');
            }
        } else {
            if (progressModal) {
                if (typeof bootstrap !== 'undefined') {
                    const modal = bootstrap.Modal.getInstance(progressModal);
                    if (modal) {
                        modal.hide();
                    }
                } else {
                    progressModal.style.display = 'none';
                    progressModal.classList.remove('show');
                }
            }
        }
    },

    // 批量导出
    exportBatch: function(formats = ['excel', 'pdf']) {
        const analysisType = window.analysisType || 'summary';

        showInfoMessage(`开始批量导出 ${formats.join(', ').toUpperCase()} 格式文件...`);

        formats.forEach((format, index) => {
            setTimeout(() => {
                this.exportStatistics(format, analysisType);
            }, index * 1000); // 间隔1秒导出，避免并发问题
        });
    }
};

// 全局导出函数（供模板调用）
function exportStatistics(format = 'excel') {
    StatisticsExport.exportStatistics(format);
}

function exportDetailData(format = 'excel') {
    StatisticsExport.exportDetailData(format);
}

function exportBatch() {
    StatisticsExport.exportBatch(['excel', 'pdf']);
}
