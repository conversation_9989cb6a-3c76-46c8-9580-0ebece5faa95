# Redis缓存配置指导
# Redis Cache Configuration Guide

## 概述

本文档提供医疗器械不良事件报告系统的Redis缓存配置指导，确保缓存系统在各种环境下稳定运行。

## Redis安装和配置

### 1. Redis安装

#### Ubuntu/Debian
```bash
# 更新包列表
sudo apt update

# 安装Redis
sudo apt install redis-server

# 启动Redis服务
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

#### CentOS/RHEL
```bash
# 安装EPEL仓库
sudo yum install epel-release

# 安装Redis
sudo yum install redis

# 启动Redis服务
sudo systemctl start redis
sudo systemctl enable redis
```

#### Windows
```bash
# 使用Chocolatey安装
choco install redis-64

# 或下载Windows版本
# https://github.com/microsoftarchive/redis/releases
```

#### Docker
```bash
# 运行Redis容器
docker run -d --name redis-cache \
  -p 6379:6379 \
  -v redis-data:/data \
  redis:7-alpine redis-server --appendonly yes

# 检查Redis状态
docker exec redis-cache redis-cli ping
```

### 2. Redis基本配置

#### 配置文件位置
- **Linux**: `/etc/redis/redis.conf`
- **Windows**: `C:\Program Files\Redis\redis.windows.conf`
- **Docker**: 通过环境变量或挂载配置文件

#### 推荐配置
```conf
# 基本设置
bind 127.0.0.1
port 6379
timeout 300

# 内存设置
maxmemory 256mb
maxmemory-policy allkeys-lru

# 持久化设置
save 900 1
save 300 10
save 60 10000

# 日志设置
loglevel notice
logfile /var/log/redis/redis-server.log

# 安全设置
requirepass your_secure_password_here

# 性能优化
tcp-keepalive 300
tcp-backlog 511
```

### 3. Django Redis配置

#### settings.py配置
```python
# Redis缓存配置
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            'PASSWORD': 'your_secure_password_here',
            'CONNECTION_POOL_KWARGS': {
                'max_connections': 50,
                'retry_on_timeout': True,
            },
            'SERIALIZER': 'django_redis.serializers.json.JSONSerializer',
            'COMPRESSOR': 'django_redis.compressors.zlib.ZlibCompressor',
        },
        'KEY_PREFIX': 'medical_device_reports',
        'VERSION': 1,
        'TIMEOUT': 300,  # 5分钟默认超时
    }
}

# 会话存储（可选）
SESSION_ENGINE = 'django.contrib.sessions.backends.cache'
SESSION_CACHE_ALIAS = 'default'
```

#### 生产环境配置
```python
# 生产环境Redis配置
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': [
            'redis://redis-master:6379/1',
            'redis://redis-slave1:6379/1',
            'redis://redis-slave2:6379/1',
        ],
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.ShardClient',
            'PASSWORD': os.environ.get('REDIS_PASSWORD'),
            'CONNECTION_POOL_KWARGS': {
                'max_connections': 100,
                'retry_on_timeout': True,
                'socket_keepalive': True,
                'socket_keepalive_options': {},
            },
            'SERIALIZER': 'django_redis.serializers.json.JSONSerializer',
            'COMPRESSOR': 'django_redis.compressors.zlib.ZlibCompressor',
        },
        'KEY_PREFIX': 'medical_device_reports',
        'VERSION': 1,
        'TIMEOUT': 3600,  # 1小时默认超时
    }
}
```

## 缓存策略配置

### 1. 统计缓存配置

```python
# apps/common/cache_utils.py
CACHE_TIMEOUTS = {
    'report_statistics': 1800,      # 30分钟
    'get_time_series_statistics': 3600,  # 1小时
    'get_device_statistics': 3600,      # 1小时
    'get_department_statistics': 1800,   # 30分钟
    'get_cross_dimension_statistics': 2700,  # 45分钟
    'get_trend_analysis': 7200,          # 2小时
}
```

### 2. 缓存键命名规范

```python
# 缓存键格式
stats:{function_name}:{level}:{hash}

# 示例
stats:report_statistics:global:a1b2c3d4
stats:get_time_series_statistics:dept:e5f6g7h8
stats:get_device_statistics:user:i9j0k1l2
```

## 性能优化

### 1. Redis性能调优

```conf
# redis.conf 性能优化
# 禁用持久化（如果可以接受数据丢失）
save ""

# 启用压缩
rdbcompression yes

# 优化内存使用
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# 网络优化
tcp-nodelay yes
```

### 2. Django缓存优化

```python
# 缓存预热
def warm_up_cache():
    """预热常用缓存"""
    from apps.reports.selectors import (
        report_statistics,
        get_time_series_statistics
    )
    
    # 预热全局统计
    report_statistics()
    
    # 预热时间序列
    get_time_series_statistics(granularity='month')
```

## 监控和维护

### 1. Redis监控

```bash
# 连接Redis CLI
redis-cli

# 查看Redis信息
INFO

# 查看内存使用
INFO memory

# 查看连接数
INFO clients

# 查看缓存命中率
INFO stats
```

### 2. 缓存监控脚本

```python
# scripts/monitor_cache.py
import redis
from django.core.cache import cache

def check_redis_health():
    """检查Redis健康状态"""
    try:
        # 测试连接
        cache.set('health_check', 'ok', 60)
        result = cache.get('health_check')
        
        if result == 'ok':
            print("✅ Redis连接正常")
            return True
        else:
            print("❌ Redis连接异常")
            return False
            
    except Exception as e:
        print(f"❌ Redis连接失败: {e}")
        return False

def get_cache_stats():
    """获取缓存统计信息"""
    from apps.common.cache_utils import StatisticsCacheManager
    return StatisticsCacheManager.get_cache_stats()
```

## 故障排除

### 1. 常见问题

#### 连接失败
```bash
# 检查Redis服务状态
sudo systemctl status redis

# 检查端口是否开放
netstat -tlnp | grep 6379

# 检查防火墙
sudo ufw status
```

#### 内存不足
```bash
# 查看内存使用
redis-cli INFO memory

# 清理过期键
redis-cli --scan --pattern "*" | xargs redis-cli DEL

# 调整内存策略
redis-cli CONFIG SET maxmemory-policy allkeys-lru
```

#### 性能问题
```bash
# 查看慢查询
redis-cli SLOWLOG GET 10

# 监控实时命令
redis-cli MONITOR

# 检查键空间
redis-cli INFO keyspace
```

### 2. 配置验证

```python
# 验证Redis配置
def verify_redis_config():
    """验证Redis配置"""
    try:
        from django.core.cache import cache
        
        # 测试基本操作
        cache.set('test_key', 'test_value', 60)
        value = cache.get('test_key')
        cache.delete('test_key')
        
        if value == 'test_value':
            print("✅ Redis基本操作正常")
        else:
            print("❌ Redis基本操作失败")
            
        # 测试缓存失效
        from apps.common.cache_utils import StatisticsCacheManager
        StatisticsCacheManager.invalidate_cache('test_function')
        print("✅ 缓存失效机制正常")
        
    except Exception as e:
        print(f"❌ Redis配置验证失败: {e}")
```

## 部署建议

### 1. 开发环境
- 使用本地Redis实例
- 启用调试日志
- 较短的缓存超时时间

### 2. 测试环境
- 独立的Redis实例
- 模拟生产环境配置
- 启用监控

### 3. 生产环境
- Redis集群或主从配置
- 启用持久化
- 配置监控和告警
- 定期备份

### 4. 安全建议
- 设置强密码
- 限制网络访问
- 定期更新Redis版本
- 监控异常访问

## 相关链接

- [Redis官方文档](https://redis.io/documentation)
- [Django Redis文档](https://django-redis.readthedocs.io/)
- [Redis性能优化指南](https://redis.io/topics/memory-optimization)
- [Redis安全指南](https://redis.io/topics/security)
