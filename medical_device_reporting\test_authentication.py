#!/usr/bin/env python
"""
认证系统测试脚本
Authentication System Test Script for Medical Device Reporting Platform
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from django.contrib.auth.models import User
from django.contrib.auth import authenticate
from apps.users.models import UserProfile, Department
from apps.users.authentication import AccountNumberBackend

def test_authentication_system():
    """测试认证系统"""
    
    print("=== 认证系统测试 ===\n")
    
    # 1. 创建测试科室
    print("1. 创建测试科室...")
    department, created = Department.objects.get_or_create(
        code='TEST',
        defaults={
            'name': '测试科室',
            'is_active': True
        }
    )
    if created:
        print(f"   ✅ 科室创建成功: {department}")
    else:
        print(f"   ℹ️  科室已存在: {department}")
    
    # 2. 创建测试用户
    print("\n2. 创建测试用户...")
    user, created = User.objects.get_or_create(
        username='testuser',
        defaults={
            'first_name': '测试',
            'last_name': '用户',
            'email': '<EMAIL>',
            'is_active': True
        }
    )
    if created:
        print(f"   ✅ Django用户创建成功: {user}")
    else:
        print(f"   ℹ️  Django用户已存在: {user}")
    
    # 3. 创建用户配置文件
    print("\n3. 创建用户配置文件...")
    user_profile, created = UserProfile.objects.get_or_create(
        account_number='1234',
        defaults={
            'user': user,
            'department': department,
            'role': 'staff',
            'is_active': True
        }
    )
    if created:
        print(f"   ✅ 用户配置文件创建成功: {user_profile}")
    else:
        print(f"   ℹ️  用户配置文件已存在: {user_profile}")
    
    # 4. 测试认证后端
    print("\n4. 测试认证后端...")
    
    # 测试正确的账号
    print("   测试正确的账号 '1234'...")
    auth_user = authenticate(account_number='1234')
    if auth_user:
        print(f"   ✅ 认证成功: {auth_user.username}")
    else:
        print("   ❌ 认证失败")
    
    # 测试错误的账号
    print("   测试错误的账号 '9999'...")
    auth_user = authenticate(account_number='9999')
    if auth_user:
        print(f"   ❌ 认证不应该成功: {auth_user.username}")
    else:
        print("   ✅ 认证正确失败")
    
    # 测试格式错误的账号
    print("   测试格式错误的账号 'abc1'...")
    auth_user = authenticate(account_number='abc1')
    if auth_user:
        print(f"   ❌ 认证不应该成功: {auth_user.username}")
    else:
        print("   ✅ 认证正确失败")
    
    # 5. 测试用户状态
    print("\n5. 测试用户状态...")
    
    # 禁用用户
    print("   禁用用户配置文件...")
    user_profile.is_active = False
    user_profile.save()
    
    auth_user = authenticate(account_number='1234')
    if auth_user:
        print("   ❌ 禁用用户不应该认证成功")
    else:
        print("   ✅ 禁用用户认证正确失败")
    
    # 重新启用用户
    print("   重新启用用户配置文件...")
    user_profile.is_active = True
    user_profile.save()
    
    auth_user = authenticate(account_number='1234')
    if auth_user:
        print(f"   ✅ 启用用户认证成功: {auth_user.username}")
    else:
        print("   ❌ 启用用户认证失败")
    
    # 6. 测试直接使用认证后端
    print("\n6. 测试直接使用认证后端...")
    backend = AccountNumberBackend()
    
    # 模拟请求对象
    class MockRequest:
        META = {'REMOTE_ADDR': '127.0.0.1'}
    
    mock_request = MockRequest()
    auth_user = backend.authenticate(mock_request, account_number='1234')
    if auth_user:
        print(f"   ✅ 直接认证成功: {auth_user.username}")
    else:
        print("   ❌ 直接认证失败")
    
    # 测试get_user方法
    retrieved_user = backend.get_user(user.id)
    if retrieved_user and retrieved_user.id == user.id:
        print(f"   ✅ get_user方法正常: {retrieved_user.username}")
    else:
        print("   ❌ get_user方法失败")
    
    print("\n=== 认证系统测试完成 ===")
    
    return True

if __name__ == '__main__':
    try:
        test_authentication_system()
        print("\n🎉 所有测试通过！")
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
