#!/usr/bin/env python
"""
Redis缓存失效机制优化测试
Redis Cache Invalidation Optimization Test

测试改进后的缓存失效机制在不同配置下的兼容性和性能。
"""

import os
import sys
import django
from datetime import datetime, timedelta

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from django.core.cache import cache
from django.utils import timezone
from django.contrib.auth.models import User
from apps.users.models import UserProfile, Department
from apps.reports.models import AdverseEventReport
from apps.common.cache_utils import StatisticsCacheManager, invalidate_statistics_cache
from apps.reports.selectors import get_time_series_statistics, report_statistics

def check_cache_backend():
    """检查缓存后端配置"""
    print("=== 检查缓存后端配置 ===")
    
    try:
        cache_info = {
            'backend': str(cache.__class__),
            'location': getattr(cache, '_cache', {}).get('_server', 'Unknown'),
        }
        
        print(f"缓存后端: {cache_info['backend']}")
        print(f"缓存位置: {cache_info['location']}")
        
        # 检查是否是Redis后端
        is_redis = 'redis' in cache_info['backend'].lower()
        print(f"是否为Redis: {'是' if is_redis else '否'}")
        
        # 测试基本缓存操作
        test_key = 'cache_test_key'
        test_value = 'cache_test_value'
        
        cache.set(test_key, test_value, 60)
        retrieved_value = cache.get(test_key)
        
        if retrieved_value == test_value:
            print("✅ 基本缓存操作正常")
        else:
            print("❌ 基本缓存操作失败")
        
        cache.delete(test_key)
        return is_redis
        
    except Exception as e:
        print(f"❌ 缓存后端检查失败: {e}")
        return False

def test_cache_key_generation():
    """测试缓存键生成"""
    print("\n=== 测试缓存键生成 ===")
    
    try:
        # 创建测试用户
        user = User.objects.first()
        user_profile = UserProfile.objects.filter(user=user).first()
        
        # 测试不同的缓存键生成
        test_cases = [
            ('get_time_series_statistics', 'global', {}),
            ('report_statistics', 'dept', {'start_date': timezone.now()}),
            ('get_device_statistics', 'user', {'limit': 10}),
        ]
        
        for func_name, level, kwargs in test_cases:
            cache_key = StatisticsCacheManager.generate_cache_key(
                function_name=func_name,
                user_profile=user_profile,
                level=level,
                **kwargs
            )
            print(f"✅ {func_name} ({level}): {cache_key}")
        
        return True
        
    except Exception as e:
        print(f"❌ 缓存键生成测试失败: {e}")
        return False

def test_cache_pattern_generation():
    """测试缓存模式生成"""
    print("\n=== 测试缓存模式生成 ===")
    
    try:
        test_patterns = [
            ('get_time_series_statistics', 'global'),
            ('report_statistics', 'dept'),
            ('get_device_statistics', None),
        ]
        
        for func_name, level in test_patterns:
            pattern = StatisticsCacheManager.get_cache_pattern(func_name, level)
            print(f"✅ {func_name} ({level}): {pattern}")
        
        return True
        
    except Exception as e:
        print(f"❌ 缓存模式生成测试失败: {e}")
        return False

def test_cache_invalidation_methods():
    """测试缓存失效方法"""
    print("\n=== 测试缓存失效方法 ===")
    
    try:
        # 创建一些测试缓存
        test_keys = [
            'stats:get_time_series_statistics:global:test1',
            'stats:get_time_series_statistics:global:test2',
            'stats:report_statistics:dept:test1',
            'stats:get_device_statistics:user:test1',
        ]
        
        # 设置测试缓存
        for key in test_keys:
            cache.set(key, {'test': 'data'}, 300)
        
        print(f"设置了{len(test_keys)}个测试缓存键")
        
        # 测试模式删除
        pattern = 'stats:get_time_series_statistics:*'
        deleted_count = StatisticsCacheManager._delete_cache_pattern_with_count(pattern)
        print(f"✅ 模式删除测试: {pattern}, 删除了{deleted_count}个键")
        
        # 测试单个函数失效
        StatisticsCacheManager.invalidate_cache('report_statistics')
        print("✅ 单个函数缓存失效测试完成")
        
        # 测试批量失效
        StatisticsCacheManager.invalidate_cache(None)
        print("✅ 批量缓存失效测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 缓存失效方法测试失败: {e}")
        return False

def test_cache_with_real_data():
    """使用真实数据测试缓存"""
    print("\n=== 使用真实数据测试缓存 ===")
    
    try:
        # 获取用户配置文件
        user_profile = UserProfile.objects.first()
        if not user_profile:
            print("❌ 未找到用户配置文件")
            return False
        
        print(f"使用用户: {user_profile}")
        
        # 测试统计函数缓存
        print("测试基础统计缓存...")
        stats1 = report_statistics(user_profile=user_profile)
        stats2 = report_statistics(user_profile=user_profile)  # 应该从缓存获取
        
        print("✅ 基础统计缓存测试完成")
        
        # 测试时间序列缓存
        print("测试时间序列缓存...")
        ts1 = get_time_series_statistics(user_profile=user_profile, granularity='month')
        ts2 = get_time_series_statistics(user_profile=user_profile, granularity='month')  # 应该从缓存获取
        
        print("✅ 时间序列缓存测试完成")
        
        # 测试缓存失效
        print("测试缓存失效...")
        invalidate_statistics_cache(['report_statistics', 'get_time_series_statistics'])
        
        # 重新获取数据（应该重新计算）
        stats3 = report_statistics(user_profile=user_profile)
        ts3 = get_time_series_statistics(user_profile=user_profile, granularity='month')
        
        print("✅ 缓存失效测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 真实数据缓存测试失败: {e}")
        return False

def test_cache_stats():
    """测试缓存统计信息"""
    print("\n=== 测试缓存统计信息 ===")
    
    try:
        stats = StatisticsCacheManager.get_cache_stats()
        
        print("缓存统计信息:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        if 'error' not in stats:
            print("✅ 缓存统计信息获取成功")
            return True
        else:
            print("⚠️ 缓存统计信息获取有错误")
            return False
        
    except Exception as e:
        print(f"❌ 缓存统计信息测试失败: {e}")
        return False

def performance_test():
    """性能测试"""
    print("\n=== 缓存性能测试 ===")
    
    try:
        import time
        
        # 创建大量测试缓存键
        test_keys = []
        for i in range(100):
            key = f'stats:performance_test:global:key_{i:03d}'
            test_keys.append(key)
            cache.set(key, {'data': f'test_data_{i}'}, 300)
        
        print(f"创建了{len(test_keys)}个测试缓存键")
        
        # 测试模式删除性能
        start_time = time.time()
        pattern = 'stats:performance_test:*'
        deleted_count = StatisticsCacheManager._delete_cache_pattern_with_count(pattern)
        end_time = time.time()
        
        duration = end_time - start_time
        print(f"✅ 模式删除性能: 删除{deleted_count}个键，耗时{duration:.3f}秒")
        
        if duration < 1.0:  # 应该在1秒内完成
            print("✅ 性能测试通过")
            return True
        else:
            print("⚠️ 性能可能需要优化")
            return False
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始Redis缓存失效机制优化测试...")
    
    test_results = []
    
    # 执行各项测试
    test_functions = [
        ("缓存后端配置检查", check_cache_backend),
        ("缓存键生成测试", test_cache_key_generation),
        ("缓存模式生成测试", test_cache_pattern_generation),
        ("缓存失效方法测试", test_cache_invalidation_methods),
        ("真实数据缓存测试", test_cache_with_real_data),
        ("缓存统计信息测试", test_cache_stats),
        ("缓存性能测试", performance_test),
    ]
    
    for test_name, test_func in test_functions:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}执行失败: {e}")
            test_results.append((test_name, False))
    
    # 总结测试结果
    print("\n=== 测试结果总结 ===")
    
    passed_tests = 0
    total_tests = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status}: {test_name}")
        if result:
            passed_tests += 1
    
    print(f"\n📊 测试统计: {passed_tests}/{total_tests} 通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！缓存失效机制优化成功！")
    elif passed_tests >= total_tests * 0.8:
        print("✅ 大部分测试通过，缓存失效机制基本正常")
    else:
        print("⚠️ 多个测试失败，需要进一步检查缓存配置")
    
    print("\n💡 优化效果:")
    print("✅ 使用SCAN命令替代KEYS命令，避免Redis阻塞")
    print("✅ 添加了多层降级策略，提高兼容性")
    print("✅ 减少了不必要的警告日志")
    print("✅ 改进了错误处理和性能监控")
    
    print("\n🎯 Redis缓存失效机制优化测试完成！")

if __name__ == '__main__':
    main()
