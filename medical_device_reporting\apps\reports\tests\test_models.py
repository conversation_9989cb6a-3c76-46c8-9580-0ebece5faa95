"""
报告管理模型测试
Reports Management Models Tests
"""

from django.test import TestCase
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.utils import timezone
from datetime import date, timedelta

from apps.users.models import UserProfile, Department
from apps.reports.models import AdverseEventReport


class AdverseEventReportModelTest(TestCase):
    """不良事件报告模型测试"""
    
    def setUp(self):
        """测试前准备"""
        # 创建管理员用户
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>'
        )
        
        # 创建测试用户
        self.test_user = User.objects.create_user(
            username='testuser',
            first_name='测试',
            last_name='用户',
            email='<EMAIL>'
        )
        
        # 创建科室
        self.department = Department.objects.create(
            code='TEST',
            name='测试科室',
            created_by=self.admin_user
        )
        
        # 创建用户配置文件
        self.user_profile = UserProfile.objects.create(
            user=self.test_user,
            account_number='1001',
            department=self.department,
            role='staff',
            created_by=self.admin_user
        )
    
    def test_report_creation(self):
        """测试报告创建"""
        report = AdverseEventReport.objects.create(
            # 上报人信息
            reporter=self.test_user,
            reporter_name='测试用户',
            reporter_phone='***********',
            department=self.department,
            
            # 患者信息
            patient_name='张三',
            patient_age=45,
            patient_gender='male',
            patient_contact='***********',
            
            # 事件信息
            event_date=date.today(),
            device_malfunction=True,
            injury_level='moderate',
            event_description='测试事件描述',
            injury_description='测试伤害描述',
            
            # 器械信息
            device_name='测试器械',
            registration_number='TEST123456',
            manufacturer='测试厂商',
            specification='测试规格',
            model='TEST-001',
            batch_number='BATCH001',
            production_date=date.today() - timedelta(days=30),
            expiry_date=date.today() + timedelta(days=365),
            
            # 分析和处理
            initial_cause_analysis='初步原因分析',
            initial_treatment='初步处理措施'
        )
        
        self.assertEqual(report.reporter, self.test_user)
        self.assertEqual(report.patient_name, '张三')
        self.assertEqual(report.device_name, '测试器械')
        self.assertEqual(report.status, 'draft')  # 默认状态
        self.assertIsNotNone(report.report_number)  # 自动生成编号
    
    def test_report_str_representation(self):
        """测试报告字符串表示"""
        report = AdverseEventReport.objects.create(
            reporter=self.test_user,
            reporter_name='测试用户',
            patient_name='张三',
            device_name='测试器械',
            event_date=date.today(),
            injury_level='moderate',
            event_description='测试事件描述'
        )
        
        expected = f'{report.report_number} - 测试器械'
        self.assertEqual(str(report), expected)
    
    def test_report_number_generation(self):
        """测试报告编号生成"""
        report1 = AdverseEventReport.objects.create(
            reporter=self.test_user,
            reporter_name='测试用户',
            patient_name='张三',
            device_name='测试器械1',
            event_date=date.today(),
            injury_level='moderate',
            event_description='测试事件描述1'
        )
        
        report2 = AdverseEventReport.objects.create(
            reporter=self.test_user,
            reporter_name='测试用户',
            patient_name='李四',
            device_name='测试器械2',
            event_date=date.today(),
            injury_level='moderate',
            event_description='测试事件描述2'
        )
        
        # 编号应该不同
        self.assertNotEqual(report1.report_number, report2.report_number)
        
        # 编号格式应该正确（YYYYMMDD-XXXX）
        today_str = date.today().strftime('%Y%m%d')
        self.assertTrue(report1.report_number.startswith(today_str))
        self.assertTrue(report2.report_number.startswith(today_str))
    
    def test_required_fields_validation(self):
        """测试必填字段验证"""
        # 测试缺少必填字段
        report = AdverseEventReport(
            reporter=self.test_user,
            # 缺少 reporter_name
            patient_name='张三',
            device_name='测试器械',
            event_date=date.today(),
            injury_level='moderate',
            event_description='测试事件描述'
        )
        
        with self.assertRaises(ValidationError):
            report.full_clean()
    
    def test_patient_age_validation(self):
        """测试患者年龄验证"""
        # 测试负数年龄
        report = AdverseEventReport(
            reporter=self.test_user,
            reporter_name='测试用户',
            patient_name='张三',
            patient_age=-1,  # 负数年龄
            device_name='测试器械',
            event_date=date.today(),
            injury_level='moderate',
            event_description='测试事件描述'
        )
        
        with self.assertRaises(ValidationError):
            report.full_clean()
        
        # 测试过大年龄
        report.patient_age = 200  # 过大年龄
        with self.assertRaises(ValidationError):
            report.full_clean()
    
    def test_phone_number_validation(self):
        """测试电话号码验证"""
        report = AdverseEventReport(
            reporter=self.test_user,
            reporter_name='测试用户',
            reporter_phone='invalid_phone',  # 无效电话
            patient_name='张三',
            device_name='测试器械',
            event_date=date.today(),
            injury_level='moderate',
            event_description='测试事件描述'
        )
        
        with self.assertRaises(ValidationError):
            report.full_clean()
    
    def test_event_date_validation(self):
        """测试事件日期验证"""
        # 测试未来日期
        future_date = date.today() + timedelta(days=1)
        report = AdverseEventReport(
            reporter=self.test_user,
            reporter_name='测试用户',
            patient_name='张三',
            device_name='测试器械',
            event_date=future_date,  # 未来日期
            injury_level='moderate',
            event_description='测试事件描述'
        )
        
        with self.assertRaises(ValidationError):
            report.full_clean()
    
    def test_status_choices(self):
        """测试状态选择"""
        report = AdverseEventReport.objects.create(
            reporter=self.test_user,
            reporter_name='测试用户',
            patient_name='张三',
            device_name='测试器械',
            event_date=date.today(),
            injury_level='moderate',
            event_description='测试事件描述'
        )
        
        # 测试所有有效状态
        valid_statuses = ['draft', 'submitted', 'under_review', 'approved', 'rejected']
        for status in valid_statuses:
            report.status = status
            report.full_clean()  # 应该不抛出异常
    
    def test_injury_level_choices(self):
        """测试伤害程度选择"""
        report = AdverseEventReport.objects.create(
            reporter=self.test_user,
            reporter_name='测试用户',
            patient_name='张三',
            device_name='测试器械',
            event_date=date.today(),
            injury_level='moderate',
            event_description='测试事件描述'
        )
        
        # 测试所有有效伤害程度
        valid_levels = ['none', 'mild', 'moderate', 'severe', 'death']
        for level in valid_levels:
            report.injury_level = level
            report.full_clean()  # 应该不抛出异常
    
    def test_can_edit_property(self):
        """测试可编辑属性"""
        report = AdverseEventReport.objects.create(
            reporter=self.test_user,
            reporter_name='测试用户',
            patient_name='张三',
            device_name='测试器械',
            event_date=date.today(),
            injury_level='moderate',
            event_description='测试事件描述'
        )
        
        # 草稿状态可以编辑
        self.assertTrue(report.can_edit)
        
        # 已提交状态不能编辑
        report.status = 'submitted'
        self.assertFalse(report.can_edit)
        
        # 审核中状态不能编辑
        report.status = 'under_review'
        self.assertFalse(report.can_edit)
        
        # 已批准状态不能编辑
        report.status = 'approved'
        self.assertFalse(report.can_edit)
        
        # 被拒绝状态可以编辑
        report.status = 'rejected'
        self.assertTrue(report.can_edit)
    
    def test_can_submit_property(self):
        """测试可提交属性"""
        report = AdverseEventReport.objects.create(
            reporter=self.test_user,
            reporter_name='测试用户',
            patient_name='张三',
            device_name='测试器械',
            event_date=date.today(),
            injury_level='moderate',
            event_description='测试事件描述'
        )
        
        # 草稿状态可以提交
        self.assertTrue(report.can_submit)
        
        # 被拒绝状态可以提交
        report.status = 'rejected'
        self.assertTrue(report.can_submit)
        
        # 其他状态不能提交
        for status in ['submitted', 'under_review', 'approved']:
            report.status = status
            self.assertFalse(report.can_submit)
    
    def test_can_review_property(self):
        """测试可审核属性"""
        report = AdverseEventReport.objects.create(
            reporter=self.test_user,
            reporter_name='测试用户',
            patient_name='张三',
            device_name='测试器械',
            event_date=date.today(),
            injury_level='moderate',
            event_description='测试事件描述'
        )
        
        # 只有已提交和审核中状态可以审核
        report.status = 'submitted'
        self.assertTrue(report.can_review)
        
        report.status = 'under_review'
        self.assertTrue(report.can_review)
        
        # 其他状态不能审核
        for status in ['draft', 'approved', 'rejected']:
            report.status = status
            self.assertFalse(report.can_review)
    
    def test_is_serious_property(self):
        """测试严重事件属性"""
        report = AdverseEventReport.objects.create(
            reporter=self.test_user,
            reporter_name='测试用户',
            patient_name='张三',
            device_name='测试器械',
            event_date=date.today(),
            injury_level='moderate',
            event_description='测试事件描述'
        )
        
        # 严重伤害和死亡是严重事件
        report.injury_level = 'severe'
        self.assertTrue(report.is_serious)
        
        report.injury_level = 'death'
        self.assertTrue(report.is_serious)
        
        # 其他程度不是严重事件
        for level in ['none', 'mild', 'moderate']:
            report.injury_level = level
            self.assertFalse(report.is_serious)
