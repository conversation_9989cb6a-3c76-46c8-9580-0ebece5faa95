"""
不良事件上报管理后台配置
Adverse Event Reports Management Admin Configuration for Medical Device Reporting Platform
"""

from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from django.utils import timezone

from .models import AdverseEventReport


@admin.register(AdverseEventReport)
class AdverseEventReportAdmin(admin.ModelAdmin):
    """
    不良事件上报管理后台配置
    """

    list_display = [
        'report_number',
        'device_name',
        'patient_name',
        'reporter_info_display',
        'department',
        'status_display',
        'injury_level_display',
        'event_date',
        'created_at',
    ]

    list_filter = [
        'status',
        'injury_level',
        'department',
        'event_date',
        'created_at',
        'patient_gender',
    ]

    search_fields = [
        'report_number',
        'device_name',
        'patient_name',
        'manufacturer',
        'registration_number',
        'reporter__user__first_name',
        'reporter__user__last_name',
        'reporter__account_number',
    ]

    readonly_fields = [
        'report_number',
        'created_at',
        'updated_at',
        'created_by',
        'updated_by',
        'submitted_at',
        'reviewed_at',
    ]

    fieldsets = (
        ('基础信息', {
            'fields': (
                'report_number',
                'status',
                'reporter',
                'department',
                'reporter_phone',
            )
        }),
        ('患者信息', {
            'fields': (
                'patient_name',
                'patient_age',
                'patient_gender',
                'patient_contact',
            )
        }),
        ('事件详情', {
            'fields': (
                'device_malfunction',
                'event_date',
                'injury_level',
                'injury_description',
                'event_description',
                'initial_cause_analysis',
                'initial_treatment',
            )
        }),
        ('医疗器械信息', {
            'fields': (
                'device_name',
                'registration_number',
                'manufacturer',
                'specification',
                'model',
                'product_number',
                'batch_number',
                'production_date',
                'expiry_date',
            )
        }),
        ('审核信息', {
            'fields': (
                'reviewed_by',
                'reviewed_at',
                'review_comments',
                'submitted_at',
            )
        }),
        ('系统信息', {
            'fields': (
                'created_at',
                'updated_at',
                'created_by',
                'updated_by',
            ),
            'classes': ('collapse',)
        }),
    )

    ordering = ['-created_at', '-report_number']

    def reporter_info_display(self, obj):
        """显示上报人信息"""
        if obj.reporter:
            return f"{obj.reporter.display_name} ({obj.reporter.account_number})"
        return "未知"
    reporter_info_display.short_description = "上报人"

    def status_display(self, obj):
        """显示状态（带颜色）"""
        colors = {
            'draft': 'gray',
            'submitted': 'blue',
            'under_review': 'orange',
            'approved': 'green',
            'rejected': 'red',
        }
        color = colors.get(obj.status, 'gray')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_status_display()
        )
    status_display.short_description = "状态"

    def injury_level_display(self, obj):
        """显示伤害程度（带颜色）"""
        colors = {
            'death': 'red',
            'serious_injury': 'orange',
            'other': 'green',
        }
        color = colors.get(obj.injury_level, 'gray')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_injury_level_display()
        )
    injury_level_display.short_description = "伤害程度"

    def get_queryset(self, request):
        """优化查询性能"""
        return super().get_queryset(request).select_related(
            'reporter',
            'reporter__user',
            'department',
            'reviewed_by',
            'reviewed_by__user'
        )

    def has_delete_permission(self, request, obj=None):
        """限制删除权限"""
        if obj and obj.status in ['approved', 'under_review']:
            return False
        return super().has_delete_permission(request, obj)

    def has_change_permission(self, request, obj=None):
        """限制修改权限"""
        if obj and obj.status == 'approved':
            return False
        return super().has_change_permission(request, obj)
