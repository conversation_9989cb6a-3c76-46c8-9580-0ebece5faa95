/**
 * 科室列表页面JavaScript
 * Department List Page JavaScript
 */

$(document).ready(function() {
    console.log('🔧 科室管理JS已加载 - 版本: 2024-06-20-20:45');

    // 调试页面元素
    console.log('页面标题:', document.title);
    console.log('页面URL:', window.location.href);
    console.log('页面元素检查:');
    console.log('- 导入按钮 #importBtn:', $('#importBtn').length);
    console.log('- 导入模态框 #importModal:', $('#importModal').length);
    console.log('- 科室表格 #departmentTable:', $('#departmentTable').length);
    console.log('- 筛选表单 #filterForm:', $('#filterForm').length);

    // 如果是登录页面，不初始化科室管理功能
    if (document.title.includes('登录') || window.location.pathname.includes('login')) {
        console.log('检测到登录页面，跳过科室管理功能初始化');
        return;
    }

    // 初始化DataTables
    initializeDepartmentTable();

    // 初始化事件监听器
    initializeEventListeners();

    // 初始化筛选功能
    initializeFilters();
});

/**
 * 初始化科室列表DataTables
 */
function initializeDepartmentTable() {
    // 检查表格是否存在
    if ($('#departmentTable').length === 0) {
        console.log('科室表格不存在，跳过DataTables初始化');
        return;
    }

    if ($.fn.DataTable.isDataTable('#departmentTable')) {
        $('#departmentTable').DataTable().destroy();
    }

    $('#departmentTable').DataTable({
        processing: false,
        serverSide: false,  // 使用客户端处理
        paging: false,      // 禁用分页，使用服务器端分页
        searching: false,   // 禁用搜索，使用服务器端搜索
        info: false,        // 禁用信息显示
        order: [[0, 'asc']],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/zh.json'
        },
        responsive: true,
        dom: '<"table-responsive"t>',  // 显示表格和表头
        drawCallback: function(settings) {
            // 重新初始化工具提示
            initializeTooltips();
        }
    });
}

/**
 * 初始化事件监听器
 */
function initializeEventListeners() {
    console.log('初始化事件监听器');

    // 检查导入按钮是否存在
    const importBtn = $('#importBtn');
    console.log('导入按钮元素:', importBtn.length > 0 ? '找到' : '未找到');

    // 检查导入模态框是否存在
    const importModal = $('#importModal');
    console.log('导入模态框:', importModal.length > 0 ? '找到' : '未找到');
    // 清除搜索按钮
    $('#clearSearch').on('click', function() {
        $('#searchInput').val('');
        $('#filterForm').submit();
    });

    // 刷新按钮
    $('#refreshBtn').on('click', function() {
        window.location.reload();
    });

    // 状态切换按钮 (使用传统表单提交)
    $(document).on('change', '.status-toggle', function() {
        const checkbox = $(this);
        const deptId = checkbox.data('dept-id');
        const newStatus = checkbox.is(':checked');
        const actionText = newStatus ? '启用' : '禁用';

        if (confirm(`确定要${actionText}这个科室吗？`)) {
            // 创建表单并提交
            const form = $('<form>', {
                method: 'POST',
                action: `/departments/${deptId}/toggle-status/`
            });
            form.append($('<input>', {
                type: 'hidden',
                name: 'csrfmiddlewaretoken',
                value: getCsrfToken()
            }));
            $('body').append(form);
            form.submit();
        } else {
            // 恢复原状态
            checkbox.prop('checked', !newStatus);
        }
    });

    // 删除按钮
    $(document).on('click', '.delete-btn', function() {
        const btn = $(this);
        const deptId = btn.data('dept-id');
        const deptName = btn.data('dept-name');

        if (confirm(`确定要删除科室 "${deptName}" 吗？\n\n删除科室将影响该科室下的所有用户，请谨慎操作！`)) {
            // 创建表单并提交
            const form = $('<form>', {
                method: 'POST',
                action: `/departments/${deptId}/delete/`
            });
            form.append($('<input>', {
                type: 'hidden',
                name: 'csrfmiddlewaretoken',
                value: getCsrfToken()
            }));
            $('body').append(form);
            form.submit();
        }
    });

    // 导入按钮
    $('#importBtn').on('click', function() {
        console.log('导入按钮被点击');
        alert('导入按钮点击测试 - JavaScript正常工作');
        $('#importModal').modal('show');
    });

    // 导入表单提交
    $('#importForm').on('submit', handleImportSubmit);

    // 添加测试按钮事件（如果存在）
    $('#testBtn').on('click', function() {
        alert('测试按钮工作正常！');
    });
}

/**
 * 初始化筛选功能
 */
function initializeFilters() {
    // 筛选功能已在模板中实现，这里不需要额外处理
    console.log('筛选功能已初始化');
}

/**
 * 处理导入表单提交
 */
function handleImportSubmit(e) {
    e.preventDefault();

    const form = $('#importForm')[0];
    const fileInput = $('#excel_file')[0];
    const submitBtn = $('#importSubmitBtn');

    // 验证文件
    if (!fileInput.files || fileInput.files.length === 0) {
        showErrorMessage('请选择要导入的Excel文件');
        return;
    }

    const file = fileInput.files[0];
    const allowedTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel'
    ];

    if (!allowedTypes.includes(file.type) && !file.name.match(/\.(xlsx|xls)$/i)) {
        showErrorMessage('请选择Excel格式文件（.xlsx或.xls）');
        return;
    }

    // 显示加载状态
    LoadingIndicator.show(submitBtn[0]);
    submitBtn.prop('disabled', true);

    // 创建FormData
    const formData = new FormData(form);

    // 发送AJAX请求
    $.ajax({
        url: '/departments/import/',
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        headers: {
            'X-CSRFToken': getCsrfToken()
        },
        success: function(response) {
            $('#importModal').modal('hide');
            showSuccessMessage('科室数据导入成功');
            $('#departmentTable').DataTable().ajax.reload();

            // 重置表单
            form.reset();
        },
        error: function(xhr, status, error) {
            console.error('导入失败:', error);
            let errorMsg = '导入失败，请重试';

            if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMsg = xhr.responseJSON.error;
            } else if (xhr.responseText) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    errorMsg = response.error || errorMsg;
                } catch (e) {
                    // 如果不是JSON响应，使用默认错误消息
                }
            }

            showErrorMessage(errorMsg);
        },
        complete: function() {
            LoadingIndicator.hide(submitBtn[0]);
            submitBtn.prop('disabled', false);
        }
    });
}



/**
 * 防抖函数
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * 获取CSRF Token
 */
function getCsrfToken() {
    // 尝试多种方式获取CSRF token
    let token = '';

    // 方式1: 从meta标签获取
    const metaToken = document.querySelector('meta[name=csrf-token]')?.getAttribute('content');
    if (metaToken) {
        token = metaToken;
    }

    // 方式2: 从表单字段获取
    if (!token) {
        const inputToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value;
        if (inputToken) {
            token = inputToken;
        }
    }

    // 方式3: 从cookie获取
    if (!token) {
        const cookieToken = getCookieValue('csrftoken');
        if (cookieToken) {
            token = cookieToken;
        }
    }

    return token;
}

/**
 * 从cookie获取值
 */
function getCookieValue(name) {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop().split(';').shift();
    return null;
}

/**
 * 格式化日期
 */
function formatDate(date, format) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return format
        .replace('YYYY', year)
        .replace('MM', month)
        .replace('DD', day)
        .replace('HH', hours)
        .replace('mm', minutes)
        .replace('ss', seconds);
}

/**
 * 显示成功消息
 */
function showSuccessMessage(message) {
    // 使用Bootstrap的Toast或者简单的alert
    if (typeof toastr !== 'undefined') {
        toastr.success(message);
    } else {
        // 创建简单的成功提示
        showMessage(message, 'success');
    }
}

/**
 * 显示错误消息
 */
function showErrorMessage(message) {
    if (typeof toastr !== 'undefined') {
        toastr.error(message);
    } else {
        showMessage(message, 'danger');
    }
}

/**
 * 显示警告消息
 */
function showWarningMessage(message) {
    if (typeof toastr !== 'undefined') {
        toastr.warning(message);
    } else {
        showMessage(message, 'warning');
    }
}

/**
 * 显示信息消息
 */
function showInfoMessage(message) {
    if (typeof toastr !== 'undefined') {
        toastr.info(message);
    } else {
        showMessage(message, 'info');
    }
}

/**
 * 通用消息显示函数
 */
function showMessage(message, type) {
    // 创建Bootstrap alert
    const alertDiv = $(`
        <div class="alert alert-${type} alert-dismissible fade show position-fixed"
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);

    $('body').append(alertDiv);

    // 3秒后自动消失
    setTimeout(() => {
        alertDiv.alert('close');
    }, 3000);
}

/**
 * 加载指示器
 */
const LoadingIndicator = {
    show: function(element) {
        const $el = $(element);
        const originalText = $el.html();
        $el.data('original-text', originalText);
        $el.html('<i class="spinner-border spinner-border-sm me-2"></i>处理中...');
        $el.prop('disabled', true);
    },

    hide: function(element) {
        const $el = $(element);
        const originalText = $el.data('original-text');
        if (originalText) {
            $el.html(originalText);
        }
        $el.prop('disabled', false);
    }
};

/**
 * 初始化工具提示
 */
function initializeTooltips() {
    // 初始化Bootstrap工具提示
    if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
}
