"""
用户管理应用配置
User Management App Configuration for Medical Device Reporting Platform
"""

from django.apps import AppConfig


class UsersConfig(AppConfig):
    """
    用户管理应用配置类
    
    配置用户管理应用的基本信息和初始化逻辑
    """
    
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.users'
    verbose_name = '用户管理'
    verbose_name_plural = '用户管理'
    
    def ready(self):
        """
        应用准备就绪时的初始化逻辑
        
        在Django启动时执行一次，用于注册信号处理器、
        初始化权限组、注册自定义检查等
        """
        
        # 导入信号处理器
        try:
            from . import signals
        except ImportError:
            pass
        
        # 初始化用户权限组
        self._setup_user_groups()
        
        # 初始化日志
        self._setup_logging()
    
    def _setup_user_groups(self):
        """
        设置用户权限组
        """
        # 延迟导入避免应用启动时的循环导入
        from django.contrib.auth.models import Group, Permission
        from django.contrib.contenttypes.models import ContentType
        
        try:
            # 创建管理员组
            admin_group, created = Group.objects.get_or_create(name='管理员')
            if created:
                # 为管理员组分配所有用户管理权限
                user_permissions = Permission.objects.filter(
                    content_type__app_label='users'
                )
                admin_group.permissions.set(user_permissions)
            
            # 创建科室人员组
            staff_group, created = Group.objects.get_or_create(name='科室人员')
            if created:
                # 为科室人员组分配基本权限
                basic_permissions = Permission.objects.filter(
                    content_type__app_label='users',
                    codename__in=['view_userprofile']
                )
                staff_group.permissions.set(basic_permissions)
                
        except Exception:
            # 在迁移过程中可能会出现表不存在的情况，忽略错误
            pass
    
    def _setup_logging(self):
        """
        设置应用特定的日志配置
        """
        import logging
        
        logger = logging.getLogger('apps.users')
        logger.info('用户管理应用已初始化')
