"""
报告管理集成测试
Reports Management Integration Tests
"""

from django.test import TestCase, TransactionTestCase
from django.contrib.auth.models import User
from django.urls import reverse
from django.test import Client
from datetime import date, timedelta

from apps.users.models import UserProfile, Department
from apps.reports.models import AdverseEventReport
from apps.reports.services import report_create, report_submit, report_review


class ReportWorkflowIntegrationTest(TransactionTestCase):
    """报告工作流集成测试"""
    
    def setUp(self):
        """测试前准备"""
        self.client = Client()
        
        # 创建管理员用户
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # 创建普通用户
        self.staff_user = User.objects.create_user(
            username='staff',
            first_name='测试',
            last_name='用户',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # 创建科室
        self.department = Department.objects.create(
            code='TEST',
            name='测试科室',
            created_by=self.admin_user
        )
        
        # 创建用户配置文件
        self.admin_profile = UserProfile.objects.create(
            user=self.admin_user,
            account_number='0001',
            role='admin',
            created_by=self.admin_user
        )
        
        self.staff_profile = UserProfile.objects.create(
            user=self.staff_user,
            account_number='1001',
            department=self.department,
            role='staff',
            created_by=self.admin_user
        )
    
    def test_complete_report_workflow(self):
        """测试完整的报告工作流"""
        # 1. 用户创建报告
        report_data = {
            'reporter_name': '测试用户',
            'reporter_phone': '***********',
            'patient_name': '张三',
            'patient_age': 45,
            'patient_gender': 'male',
            'event_date': date.today(),
            'injury_level': 'moderate',
            'event_description': '测试事件描述',
            'device_name': '测试器械',
            'registration_number': 'TEST123456',
            'manufacturer': '测试厂商'
        }
        
        report = report_create(user=self.staff_user, data=report_data)
        self.assertEqual(report.status, 'draft')
        
        # 2. 用户提交报告
        submitted_report = report_submit(report_id=report.id, user=self.staff_user)
        self.assertEqual(submitted_report.status, 'submitted')
        self.assertIsNotNone(submitted_report.submitted_at)
        
        # 3. 管理员审核报告
        reviewed_report = report_review(
            report_id=report.id,
            user=self.admin_user,
            action='approve',
            comments='审核通过'
        )
        self.assertEqual(reviewed_report.status, 'approved')
        self.assertEqual(reviewed_report.reviewed_by, self.admin_user)
        self.assertIsNotNone(reviewed_report.reviewed_at)
    
    def test_report_rejection_workflow(self):
        """测试报告拒绝工作流"""
        # 1. 创建并提交报告
        report_data = {
            'reporter_name': '测试用户',
            'patient_name': '张三',
            'event_date': date.today(),
            'injury_level': 'moderate',
            'event_description': '不完整的事件描述',
            'device_name': '测试器械'
        }
        
        report = report_create(user=self.staff_user, data=report_data)
        submitted_report = report_submit(report_id=report.id, user=self.staff_user)
        
        # 2. 管理员拒绝报告
        rejected_report = report_review(
            report_id=report.id,
            user=self.admin_user,
            action='reject',
            comments='信息不完整，请补充详细描述'
        )
        self.assertEqual(rejected_report.status, 'rejected')
        
        # 3. 用户修改并重新提交
        from apps.reports.services import report_update
        
        update_data = {
            'event_description': '详细的事件描述，包含所有必要信息',
            'injury_description': '详细的伤害描述'
        }
        
        updated_report = report_update(
            report_id=report.id,
            user=self.staff_user,
            data=update_data
        )
        
        # 4. 重新提交
        resubmitted_report = report_submit(
            report_id=report.id,
            user=self.staff_user
        )
        self.assertEqual(resubmitted_report.status, 'submitted')
        
        # 5. 管理员重新审核并批准
        final_report = report_review(
            report_id=report.id,
            user=self.admin_user,
            action='approve',
            comments='修改后信息完整，审核通过'
        )
        self.assertEqual(final_report.status, 'approved')
    
    def test_serious_event_workflow(self):
        """测试严重事件工作流"""
        # 创建严重事件报告
        serious_data = {
            'reporter_name': '测试用户',
            'patient_name': '严重患者',
            'event_date': date.today(),
            'injury_level': 'death',  # 死亡事件
            'event_description': '严重不良事件描述',
            'injury_description': '患者死亡',
            'device_name': '严重器械',
            'device_malfunction': True
        }
        
        report = report_create(user=self.staff_user, data=serious_data)
        
        # 验证严重事件属性
        self.assertTrue(report.is_serious)
        
        # 提交严重事件
        submitted_report = report_submit(report_id=report.id, user=self.staff_user)
        
        # 管理员应该能在严重事件列表中看到
        self.client.login(username='admin', password='testpass123')
        url = reverse('reports:serious_events')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, report.report_number)
        self.assertContains(response, '死亡')
    
    def test_multi_user_report_access(self):
        """测试多用户报告访问控制"""
        # 创建多个用户和报告
        user1 = User.objects.create_user(username='user1', password='testpass123')
        user2 = User.objects.create_user(username='user2', password='testpass123')
        
        profile1 = UserProfile.objects.create(
            user=user1,
            account_number='1001',
            department=self.department,
            role='staff',
            created_by=self.admin_user
        )
        
        profile2 = UserProfile.objects.create(
            user=user2,
            account_number='1002',
            department=self.department,
            role='staff',
            created_by=self.admin_user
        )
        
        # 用户1创建报告
        report1 = AdverseEventReport.objects.create(
            reporter=user1,
            reporter_name='用户1',
            patient_name='患者1',
            event_date=date.today(),
            injury_level='moderate',
            event_description='用户1的报告',
            device_name='器械1'
        )
        
        # 用户2创建报告
        report2 = AdverseEventReport.objects.create(
            reporter=user2,
            reporter_name='用户2',
            patient_name='患者2',
            event_date=date.today(),
            injury_level='moderate',
            event_description='用户2的报告',
            device_name='器械2'
        )
        
        # 用户1只能看到自己的报告
        self.client.login(username='user1', password='testpass123')
        url = reverse('reports:report_detail', kwargs={'report_id': report1.id})
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        
        # 用户1不能看到用户2的报告
        url = reverse('reports:report_detail', kwargs={'report_id': report2.id})
        response = self.client.get(url)
        self.assertEqual(response.status_code, 403)
        
        # 管理员可以看到所有报告
        self.client.login(username='admin', password='testpass123')
        
        url = reverse('reports:report_detail', kwargs={'report_id': report1.id})
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        
        url = reverse('reports:report_detail', kwargs={'report_id': report2.id})
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
    
    def test_step_form_workflow(self):
        """测试分步表单工作流"""
        self.client.login(username='staff', password='testpass123')
        
        # 步骤1：上报人信息
        url = reverse('reports:step_form', kwargs={'step': 1})
        step1_data = {
            'reporter_name': '测试用户',
            'reporter_phone': '***********',
            'next_step': '2'
        }
        response = self.client.post(url, step1_data)
        self.assertEqual(response.status_code, 302)  # 重定向到步骤2
        
        # 步骤2：患者信息
        url = reverse('reports:step_form', kwargs={'step': 2})
        step2_data = {
            'patient_name': '张三',
            'patient_age': 45,
            'patient_gender': 'male',
            'next_step': '3'
        }
        response = self.client.post(url, step2_data)
        self.assertEqual(response.status_code, 302)  # 重定向到步骤3
        
        # 步骤3：事件信息
        url = reverse('reports:step_form', kwargs={'step': 3})
        step3_data = {
            'event_date': date.today().strftime('%Y-%m-%d'),
            'injury_level': 'moderate',
            'event_description': '测试事件描述',
            'next_step': '4'
        }
        response = self.client.post(url, step3_data)
        self.assertEqual(response.status_code, 302)  # 重定向到步骤4
        
        # 步骤4：器械信息
        url = reverse('reports:step_form', kwargs={'step': 4})
        step4_data = {
            'device_name': '测试器械',
            'registration_number': 'TEST123456',
            'manufacturer': '测试厂商',
            'action': 'save'
        }
        response = self.client.post(url, step4_data)
        self.assertEqual(response.status_code, 302)  # 重定向到报告详情
        
        # 验证报告创建成功
        report = AdverseEventReport.objects.filter(
            reporter=self.staff_user,
            device_name='测试器械'
        ).first()
        self.assertIsNotNone(report)
        self.assertEqual(report.patient_name, '张三')
    
    def test_dashboard_integration(self):
        """测试仪表板集成"""
        # 创建不同状态的报告
        reports_data = [
            {'status': 'draft', 'injury_level': 'mild'},
            {'status': 'submitted', 'injury_level': 'moderate'},
            {'status': 'approved', 'injury_level': 'severe'},
            {'status': 'submitted', 'injury_level': 'death'},
        ]
        
        for i, data in enumerate(reports_data):
            report = AdverseEventReport.objects.create(
                reporter=self.staff_user,
                reporter_name=f'用户{i}',
                patient_name=f'患者{i}',
                event_date=date.today(),
                injury_level=data['injury_level'],
                event_description=f'事件描述{i}',
                device_name=f'器械{i}',
                status=data['status']
            )
        
        # 测试管理员仪表板
        self.client.login(username='admin', password='testpass123')
        url = reverse('reports:dashboard')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '报告管理')
        
        # 测试普通用户仪表板
        self.client.login(username='staff', password='testpass123')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '报告管理')
