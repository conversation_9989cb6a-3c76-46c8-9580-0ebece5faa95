"""
不良事件上报管理数据模型
Adverse Event Reports Management Models for Medical Device Reporting Platform
"""

import uuid
from datetime import datetime, date
from django.db import models
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.urls import reverse

from apps.common.models import BaseModel, AuditableModel
from apps.common.utils import ValidationUtils
from apps.users.models import UserProfile, Department


class AdverseEventReport(AuditableModel):
    """
    不良事件上报主模型

    管理医疗器械不良事件上报的核心数据
    """

    # 状态选择
    STATUS_CHOICES = [
        ('draft', '草稿'),
        ('submitted', '已提交'),
        ('under_review', '审核中'),
        ('approved', '已批准'),
        ('rejected', '已拒绝'),
    ]

    # 患者性别选择
    GENDER_CHOICES = [
        ('male', '男'),
        ('female', '女'),
        ('unknown', '未知'),
    ]

    # 伤害程度选择
    INJURY_LEVEL_CHOICES = [
        ('death', '死亡'),
        ('serious_injury', '严重伤害'),
        ('other', '其他'),
    ]

    # 基础信息
    report_number = models.CharField(
        max_length=20,
        unique=True,
        verbose_name='报告编号',
        help_text='系统自动生成的唯一报告编号'
    )

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='draft',
        verbose_name='报告状态',
        help_text='报告当前的处理状态'
    )

    # 上报人信息（关联现有用户系统）
    reporter = models.ForeignKey(
        UserProfile,
        on_delete=models.PROTECT,
        related_name='reported_events',
        verbose_name='上报人',
        help_text='上报此事件的用户'
    )

    department = models.ForeignKey(
        Department,
        on_delete=models.PROTECT,
        related_name='department_reports',
        verbose_name='上报科室',
        help_text='上报人所属科室'
    )

    reporter_phone = models.CharField(
        max_length=20,
        verbose_name='上报人联系电话',
        help_text='上报人的联系电话'
    )

    # 患者信息
    patient_name = models.CharField(
        max_length=100,
        verbose_name='患者姓名',
        help_text='发生不良事件的患者姓名'
    )

    patient_age = models.PositiveIntegerField(
        verbose_name='患者年龄',
        help_text='患者年龄（岁）'
    )

    patient_gender = models.CharField(
        max_length=10,
        choices=GENDER_CHOICES,
        verbose_name='患者性别',
        help_text='患者的性别'
    )

    patient_contact = models.CharField(
        max_length=200,
        blank=True,
        verbose_name='患者或其家属联系方式',
        help_text='患者或其家属的联系方式'
    )

    # 不良事件情况
    device_malfunction = models.TextField(
        verbose_name='器械故障表现',
        help_text='详细描述医疗器械的故障表现'
    )

    event_date = models.DateTimeField(
        verbose_name='事件发生日期',
        help_text='不良事件发生的具体日期和时间'
    )

    injury_level = models.CharField(
        max_length=20,
        choices=INJURY_LEVEL_CHOICES,
        verbose_name='伤害程度',
        help_text='不良事件造成的伤害程度'
    )

    injury_description = models.TextField(
        blank=True,
        verbose_name='伤害表现',
        help_text='如造成伤害需填写具体的伤害表现'
    )

    event_description = models.TextField(
        verbose_name='事件陈述',
        help_text='详细陈述事件情况，包括器械使用时间、使用目的、使用依据、使用情况、出现的不良事件情况、对受害者影响、采取的治疗措施、器械联合使用情况'
    )

    initial_cause_analysis = models.TextField(
        blank=True,
        verbose_name='事件发生初步原因分析',
        help_text='对事件发生原因的初步分析'
    )

    initial_treatment = models.TextField(
        blank=True,
        verbose_name='事件初步处理情况',
        help_text='对事件的初步处理措施和情况'
    )

    # 医疗器械信息
    device_name = models.CharField(
        max_length=200,
        verbose_name='医疗器械名称',
        help_text='发生不良事件的医疗器械名称'
    )

    registration_number = models.CharField(
        max_length=50,
        verbose_name='注册证号',
        help_text='医疗器械注册证号'
    )

    manufacturer = models.CharField(
        max_length=200,
        verbose_name='生产企业名称',
        help_text='医疗器械生产企业名称'
    )

    specification = models.CharField(
        max_length=100,
        blank=True,
        verbose_name='规格',
        help_text='医疗器械规格'
    )

    model = models.CharField(
        max_length=100,
        blank=True,
        verbose_name='型号',
        help_text='医疗器械型号'
    )

    product_number = models.CharField(
        max_length=100,
        blank=True,
        verbose_name='产品编号',
        help_text='医疗器械产品编号'
    )

    batch_number = models.CharField(
        max_length=100,
        blank=True,
        verbose_name='产品批号',
        help_text='医疗器械产品批号'
    )

    production_date = models.DateField(
        null=True,
        blank=True,
        verbose_name='生产日期',
        help_text='医疗器械生产日期'
    )

    expiry_date = models.DateField(
        null=True,
        blank=True,
        verbose_name='有效期至',
        help_text='医疗器械有效期截止日期'
    )

    # 审核相关字段
    reviewed_by = models.ForeignKey(
        UserProfile,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='reviewed_reports',
        verbose_name='审核人',
        help_text='审核此报告的用户'
    )

    reviewed_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='审核时间',
        help_text='报告审核的时间'
    )

    review_comments = models.TextField(
        blank=True,
        verbose_name='审核意见',
        help_text='审核人的审核意见和建议'
    )

    # 提交时间
    submitted_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='提交时间',
        help_text='报告提交的时间'
    )

    class Meta:
        db_table = 'reports_adverse_event'
        verbose_name = '不良事件上报'
        verbose_name_plural = '不良事件上报'
        ordering = ['-created_at', '-report_number']
        indexes = [
            models.Index(fields=['report_number']),
            models.Index(fields=['status']),
            models.Index(fields=['reporter']),
            models.Index(fields=['department']),
            models.Index(fields=['event_date']),
            models.Index(fields=['device_name']),
            models.Index(fields=['registration_number']),
            models.Index(fields=['created_at']),
            models.Index(fields=['submitted_at']),
        ]
        permissions = [
            ('can_submit_report', '可以提交报告'),
            ('can_review_report', '可以审核报告'),
            ('can_view_all_reports', '可以查看所有报告'),
            ('can_export_reports', '可以导出报告'),
        ]

    def __str__(self):
        return f"{self.report_number} - {self.device_name} ({self.get_status_display()})"

    def clean(self):
        """
        数据验证
        """
        super().clean()

        # 验证上报人联系电话格式
        if self.reporter_phone:
            if not ValidationUtils.validate_phone(self.reporter_phone):
                raise ValidationError({
                    'reporter_phone': '联系电话格式不正确'
                })

        # 验证患者年龄范围
        if self.patient_age is not None:
            if self.patient_age < 0 or self.patient_age > 150:
                raise ValidationError({
                    'patient_age': '患者年龄必须在0-150岁之间'
                })

        # 验证事件发生日期不能是未来时间
        if self.event_date:
            if self.event_date > timezone.now():
                raise ValidationError({
                    'event_date': '事件发生日期不能是未来时间'
                })

        # 验证生产日期和有效期的逻辑关系
        if self.production_date and self.expiry_date:
            if self.production_date >= self.expiry_date:
                raise ValidationError({
                    'expiry_date': '有效期必须晚于生产日期'
                })

        # 验证伤害程度为死亡或严重伤害时必须填写伤害表现
        if self.injury_level in ['death', 'serious_injury'] and not self.injury_description:
            raise ValidationError({
                'injury_description': '当伤害程度为死亡或严重伤害时，必须填写伤害表现'
            })

        # 验证上报人和科室的关联关系
        if self.reporter and self.department:
            if self.reporter.department != self.department:
                raise ValidationError({
                    'department': '上报科室必须与上报人所属科室一致'
                })

        # 验证状态转换的合法性
        if self.pk:  # 更新现有记录时
            try:
                original = AdverseEventReport.objects.get(pk=self.pk)
                if not self._is_valid_status_transition(original.status, self.status):
                    raise ValidationError({
                        'status': f'不能从{original.get_status_display()}状态转换为{self.get_status_display()}状态'
                    })
            except AdverseEventReport.DoesNotExist:
                pass

    def save(self, *args, **kwargs):
        """
        保存时的额外处理
        """
        # 生成报告编号（如果是新记录）
        if not self.report_number:
            self.report_number = self._generate_report_number()

        # 标准化数据
        if self.reporter_phone:
            self.reporter_phone = self.reporter_phone.strip()
        if self.patient_name:
            self.patient_name = self.patient_name.strip()
        if self.device_name:
            self.device_name = self.device_name.strip()
        if self.manufacturer:
            self.manufacturer = self.manufacturer.strip()

        # 自动设置科室（如果未设置）
        if self.reporter and not self.department:
            self.department = self.reporter.department

        # 调用clean方法进行验证
        self.clean()

        # 处理状态变更
        if self.pk:
            try:
                original = AdverseEventReport.objects.get(pk=self.pk)
                if original.status != self.status:
                    self._handle_status_change(original.status, self.status)
            except AdverseEventReport.DoesNotExist:
                pass

        super().save(*args, **kwargs)

    def _generate_report_number(self):
        """
        生成唯一的报告编号
        格式：AER + 年月日 + 4位序号，如：AER20241220001
        """
        today = timezone.now().date()
        date_str = today.strftime('%Y%m%d')
        prefix = f'AER{date_str}'

        # 查找当天最大的序号
        latest_report = AdverseEventReport.objects.filter(
            report_number__startswith=prefix
        ).order_by('-report_number').first()

        if latest_report:
            # 提取序号并加1
            last_number = int(latest_report.report_number[-3:])
            next_number = last_number + 1
        else:
            next_number = 1

        return f'{prefix}{next_number:03d}'

    def _is_valid_status_transition(self, from_status, to_status):
        """
        验证状态转换是否合法
        """
        # 如果状态没有变化，允许保存
        if from_status == to_status:
            return True

        valid_transitions = {
            'draft': ['submitted'],
            'submitted': ['under_review', 'draft'],
            'under_review': ['approved', 'rejected', 'submitted'],
            'approved': [],  # 已批准的报告不能再变更状态
            'rejected': ['draft'],  # 被拒绝的报告可以重新编辑
        }

        return to_status in valid_transitions.get(from_status, [])

    def _handle_status_change(self, old_status, new_status):
        """
        处理状态变更时的额外逻辑
        """
        now = timezone.now()

        if new_status == 'submitted' and old_status == 'draft':
            self.submitted_at = now
        elif new_status in ['approved', 'rejected'] and old_status == 'under_review':
            self.reviewed_at = now

    # 属性方法
    @property
    def is_draft(self):
        """是否为草稿状态"""
        return self.status == 'draft'

    @property
    def is_submitted(self):
        """是否已提交"""
        return self.status == 'submitted'

    @property
    def is_under_review(self):
        """是否在审核中"""
        return self.status == 'under_review'

    @property
    def is_approved(self):
        """是否已批准"""
        return self.status == 'approved'

    @property
    def is_rejected(self):
        """是否已拒绝"""
        return self.status == 'rejected'

    @property
    def can_edit(self):
        """是否可以编辑"""
        return self.status in ['draft', 'rejected']

    @property
    def can_submit(self):
        """是否可以提交"""
        return self.status == 'draft'

    @property
    def can_review(self):
        """是否可以审核"""
        return self.status in ['submitted', 'under_review']

    @property
    def reporter_info(self):
        """上报人完整信息"""
        if self.reporter:
            return f"{self.reporter.display_name} ({self.reporter.account_number})"
        return "未知"

    @property
    def patient_info(self):
        """患者基本信息"""
        return f"{self.patient_name} ({self.get_patient_gender_display()}, {self.patient_age}岁)"

    @property
    def device_info(self):
        """器械基本信息"""
        info = self.device_name
        if self.manufacturer:
            info += f" - {self.manufacturer}"
        if self.registration_number:
            info += f" ({self.registration_number})"
        return info

    @property
    def is_serious_event(self):
        """是否为严重事件"""
        return self.injury_level in ['death', 'serious_injury']

    # 业务方法
    def submit(self, user=None):
        """
        提交报告
        """
        if not self.can_submit:
            raise ValidationError('当前状态不允许提交')

        self.status = 'submitted'
        self.submitted_at = timezone.now()
        self.save(user=user)

    def start_review(self, reviewer, user=None):
        """
        开始审核
        """
        if not self.can_review:
            raise ValidationError('当前状态不允许审核')

        self.status = 'under_review'
        self.reviewed_by = reviewer
        self.save(user=user)

    def approve(self, reviewer, comments='', user=None):
        """
        批准报告
        """
        if self.status != 'under_review':
            raise ValidationError('只有审核中的报告才能批准')

        self.status = 'approved'
        self.reviewed_by = reviewer
        self.reviewed_at = timezone.now()
        self.review_comments = comments
        self.save(user=user)

    def reject(self, reviewer, comments, user=None):
        """
        拒绝报告
        """
        if self.status != 'under_review':
            raise ValidationError('只有审核中的报告才能拒绝')

        if not comments:
            raise ValidationError('拒绝报告时必须填写审核意见')

        self.status = 'rejected'
        self.reviewed_by = reviewer
        self.reviewed_at = timezone.now()
        self.review_comments = comments
        self.save(user=user)

    def get_absolute_url(self):
        """获取报告详情页面URL"""
        return reverse('reports:report_detail', kwargs={'pk': self.pk})

    def get_edit_url(self):
        """获取报告编辑页面URL"""
        return reverse('reports:report_edit', kwargs={'pk': self.pk})

    @classmethod
    def get_reports_by_department(cls, department):
        """获取指定科室的报告"""
        return cls.objects.filter(department=department)

    @classmethod
    def get_reports_by_status(cls, status):
        """获取指定状态的报告"""
        return cls.objects.filter(status=status)

    @classmethod
    def get_serious_events(cls):
        """获取严重事件报告"""
        return cls.objects.filter(injury_level__in=['death', 'serious_injury'])
