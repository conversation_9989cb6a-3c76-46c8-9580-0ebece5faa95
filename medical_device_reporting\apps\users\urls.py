"""
用户管理URL配置
User Management URLs for Medical Device Reporting Platform
"""

from django.urls import path
from django.shortcuts import redirect
from . import views, apis

app_name = 'users'

def root_redirect(request):
    """根路径重定向"""
    if request.user.is_authenticated:
        return redirect('users:dashboard')
    else:
        return redirect('users:login')

# 页面视图URL
urlpatterns = [
    # 根路径重定向
    path('', root_redirect, name='root'),

    # 认证相关
    path('login/', views.login_view, name='login'),
    path('logout/', views.logout_view, name='logout'),

    # 用户中心
    path('dashboard/', views.dashboard_view, name='dashboard'),

    # 用户管理
    path('users/', views.user_list_view, name='user_list'),
    path('users/create/', views.user_create_view, name='user_create'),
    path('users/<int:user_id>/edit/', views.user_edit_view, name='user_edit'),
    path('users/<int:user_id>/toggle-status/', views.user_toggle_status, name='user_toggle_status'),
    path('users/<int:user_id>/', views.user_profile_view, name='user_profile'),
    path('debug-permissions/', views.user_debug_permissions, name='debug_permissions'),

    # 科室管理
    path('departments/', views.department_list_view, name='department_list'),
    path('departments/create/', views.department_create_view, name='department_create'),
    path('departments/<int:dept_id>/edit/', views.department_edit_view, name='department_edit'),
    path('departments/<int:dept_id>/toggle-status/', views.department_toggle_status, name='department_toggle_status'),
    path('departments/<int:dept_id>/', views.department_detail_view, name='department_detail'),
    path('departments/<int:dept_id>/delete/', views.department_delete_view, name='department_delete'),
    path('departments/import/', views.department_import_excel, name='department_import'),
    path('departments/export/', views.department_export_excel, name='department_export_excel'),
    path('departments/template/', views.department_download_template, name='department_template'),

    # 个人设置
    path('profile/', views.profile_view, name='profile'),
    path('profile/edit/', views.profile_edit_view, name='profile_edit'),
    path('settings/', views.user_settings_view, name='user_settings'),
    path('change-password/', views.change_password_view, name='change_password'),
]

# API接口URL
api_urlpatterns = [
    # 用户API
    path('api/users/', apis.UserListApi.as_view(), name='api_user_list'),
    path('api/users/create/', apis.UserCreateApi.as_view(), name='api_user_create'),
    path('api/users/<int:pk>/', apis.UserDetailApi.as_view(), name='api_user_detail'),
    path('api/users/<int:pk>/activate/', apis.UserActivateApi.as_view(), name='api_user_activate'),
    path('api/users/<int:pk>/deactivate/', apis.UserDeactivateApi.as_view(), name='api_user_deactivate'),
    path('api/users/<int:pk>/change-role/', apis.UserRoleChangeApi.as_view(), name='api_user_change_role'),
    path('api/users/<int:pk>/change-department/', apis.UserDepartmentChangeApi.as_view(), name='api_user_change_department'),
    path('api/users/bulk-action/', apis.UserBulkActionApi.as_view(), name='api_user_bulk_action'),
    path('api/users/statistics/', apis.UserStatisticsApi.as_view(), name='api_user_statistics'),
    path('api/users/search/', apis.UserSearchApi.as_view(), name='api_user_search'),
    path('api/users/debug-permissions/', apis.UserPermissionDebugApi.as_view(), name='api_user_debug_permissions'),

    # 科室API
    path('api/departments/', apis.DepartmentListApi.as_view(), name='api_department_list'),
    path('api/departments/<int:pk>/', apis.DepartmentDetailApi.as_view(), name='api_department_detail'),
    path('api/departments/export/', views.department_export_excel, name='api_department_export'),
]

# 合并URL模式
urlpatterns += api_urlpatterns
