"""
测试环境配置
Test Environment Settings for Medical Device Reporting Platform
"""

from .base import *

# 测试环境标识
TESTING = True

# 数据库配置 - 使用内存数据库加速测试
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': ':memory:',
    }
}

# 密码验证器 - 测试环境简化
AUTH_PASSWORD_VALIDATORS = []

# 缓存配置 - 使用本地内存缓存
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
    }
}

# 邮件配置 - 使用控制台后端
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# 静态文件配置
STATIC_URL = '/static/'
STATICFILES_STORAGE = 'django.contrib.staticfiles.storage.StaticFilesStorage'

# 媒体文件配置
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'test_media'

# 日志配置 - 测试环境简化
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'WARNING',
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': 'WARNING',
            'propagate': False,
        },
        'apps': {
            'handlers': ['console'],
            'level': 'WARNING',
            'propagate': False,
        },
    },
}

# 安全配置 - 测试环境放宽
SECRET_KEY = 'test-secret-key-for-testing-only-not-for-production'
DEBUG = True
ALLOWED_HOSTS = ['*']

# 禁用系统检查中的某些警告
SILENCED_SYSTEM_CHECKS = [
    'common.E003',  # 生产环境SECRET_KEY检查
    'common.W005',  # 生产环境数据库密码检查
]

# 会话配置
SESSION_ENGINE = 'django.contrib.sessions.backends.cache'
SESSION_CACHE_ALIAS = 'default'

# 测试专用设置
TEST_RUNNER = 'django.test.runner.DiscoverRunner'

# 禁用迁移以加速测试
class DisableMigrations:
    def __contains__(self, item):
        return True
    
    def __getitem__(self, item):
        return None

# 在测试中禁用迁移（可选）
# MIGRATION_MODULES = DisableMigrations()

# 时区设置
USE_TZ = True
TIME_ZONE = 'Asia/Shanghai'

# 国际化设置
USE_I18N = True
USE_L10N = True

# 文件上传设置
FILE_UPLOAD_MAX_MEMORY_SIZE = 2621440  # 2.5MB
DATA_UPLOAD_MAX_MEMORY_SIZE = 2621440  # 2.5MB

# 测试数据库设置
TEST_DATABASE_PREFIX = 'test_'

# 禁用某些中间件以加速测试
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'apps.users.middleware.UserProfileMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
]

# 测试时禁用某些应用
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    
    # 第三方应用
    'rest_framework',
    'corsheaders',
    
    # 本地应用
    'apps.common',
    'apps.users',
]

# REST Framework配置
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework.authentication.SessionAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'DEFAULT_RENDERER_CLASSES': [
        'rest_framework.renderers.JSONRenderer',
    ],
    'DEFAULT_PARSER_CLASSES': [
        'rest_framework.parsers.JSONParser',
    ],
    'TEST_REQUEST_DEFAULT_FORMAT': 'json',
}

# 用户管理配置
USER_MANAGEMENT = {
    'ACCOUNT_NUMBER_LENGTH': 4,
    'DEFAULT_ADMIN_ACCOUNT': '0001',
    'SESSION_TIMEOUT': 3600,  # 1小时
    'MAX_LOGIN_ATTEMPTS': 5,
    'LOGIN_ATTEMPT_TIMEOUT': 300,  # 5分钟
}

print("测试环境配置已加载")
