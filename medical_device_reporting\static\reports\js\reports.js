/**
 * 报告管理模块JavaScript文件
 * Reports Management Module JavaScript
 */

// 全局配置
const ReportsConfig = {
    // API基础URL
    apiBaseUrl: '/reports/api',
    
    // 默认分页大小
    defaultPageSize: 20,
    
    // 自动保存间隔（毫秒）
    autoSaveInterval: 30000,
    
    // Toast显示时间（毫秒）
    toastDuration: 5000,
    
    // 动画持续时间（毫秒）
    animationDuration: 300
};

// 工具函数
const ReportsUtils = {
    /**
     * 显示Toast消息
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型 (success, error, warning, info)
     * @param {number} duration - 显示时间
     */
    showToast: function(message, type = 'info', duration = ReportsConfig.toastDuration) {
        // 确保Toast容器存在
        if (!document.querySelector('.toast-container')) {
            const container = document.createElement('div');
            container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
            document.body.appendChild(container);
        }
        
        const toastId = 'toast-' + Date.now();
        const toastHtml = `
            <div id="${toastId}" class="toast align-items-center text-white bg-${type} border-0" role="alert">
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="bi bi-${this.getToastIcon(type)} me-2"></i>
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `;
        
        const container = document.querySelector('.toast-container');
        container.insertAdjacentHTML('beforeend', toastHtml);
        
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, {
            autohide: true,
            delay: duration
        });
        
        toast.show();
        
        // 自动清理
        toastElement.addEventListener('hidden.bs.toast', function() {
            this.remove();
        });
    },
    
    /**
     * 获取Toast图标
     * @param {string} type - 消息类型
     * @returns {string} 图标类名
     */
    getToastIcon: function(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-triangle',
            warning: 'exclamation-circle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    },
    
    /**
     * 格式化日期
     * @param {Date|string} date - 日期对象或字符串
     * @param {string} format - 格式类型
     * @returns {string} 格式化后的日期
     */
    formatDate: function(date, format = 'YYYY-MM-DD') {
        if (!date) return '';
        
        const d = new Date(date);
        if (isNaN(d.getTime())) return '';
        
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        
        switch (format) {
            case 'YYYY-MM-DD':
                return `${year}-${month}-${day}`;
            case 'YYYY-MM-DD HH:mm':
                return `${year}-${month}-${day} ${hours}:${minutes}`;
            case 'MM-DD':
                return `${month}-${day}`;
            case 'MM-DD HH:mm':
                return `${month}-${day} ${hours}:${minutes}`;
            default:
                return d.toLocaleDateString();
        }
    },
    
    /**
     * 防抖函数
     * @param {Function} func - 要防抖的函数
     * @param {number} wait - 等待时间
     * @returns {Function} 防抖后的函数
     */
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    /**
     * 节流函数
     * @param {Function} func - 要节流的函数
     * @param {number} limit - 限制时间
     * @returns {Function} 节流后的函数
     */
    throttle: function(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },
    
    /**
     * 获取CSRF Token
     * @returns {string} CSRF Token
     */
    getCSRFToken: function() {
        const token = document.querySelector('[name=csrfmiddlewaretoken]');
        return token ? token.value : '';
    },
    
    /**
     * 发送AJAX请求
     * @param {string} url - 请求URL
     * @param {Object} options - 请求选项
     * @returns {Promise} Promise对象
     */
    ajax: function(url, options = {}) {
        const defaults = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': this.getCSRFToken()
            }
        };
        
        const config = Object.assign({}, defaults, options);
        
        // 如果是FormData，移除Content-Type让浏览器自动设置
        if (config.body instanceof FormData) {
            delete config.headers['Content-Type'];
        }
        
        return fetch(url, config)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .catch(error => {
                console.error('AJAX请求失败:', error);
                throw error;
            });
    }
};

// 表单验证器
const ReportsValidator = {
    /**
     * 验证手机号码
     * @param {string} phone - 手机号码
     * @returns {boolean} 是否有效
     */
    validatePhone: function(phone) {
        const phoneRegex = /^1[3-9]\d{9}$/;
        return phoneRegex.test(phone);
    },
    
    /**
     * 验证年龄
     * @param {number} age - 年龄
     * @returns {boolean} 是否有效
     */
    validateAge: function(age) {
        return age >= 0 && age <= 150;
    },
    
    /**
     * 验证日期
     * @param {string} date - 日期字符串
     * @param {boolean} allowFuture - 是否允许未来日期
     * @returns {boolean} 是否有效
     */
    validateDate: function(date, allowFuture = false) {
        if (!date) return false;
        
        const inputDate = new Date(date);
        const today = new Date();
        
        if (isNaN(inputDate.getTime())) return false;
        
        if (!allowFuture && inputDate > today) return false;
        
        return true;
    },
    
    /**
     * 验证必填字段
     * @param {string} value - 字段值
     * @returns {boolean} 是否有效
     */
    validateRequired: function(value) {
        return value && value.trim().length > 0;
    }
};

// 报告管理器
const ReportsManager = {
    /**
     * 初始化
     */
    init: function() {
        this.bindEvents();
        this.initComponents();
    },
    
    /**
     * 绑定事件
     */
    bindEvents: function() {
        // 表单验证
        this.bindFormValidation();
        
        // 搜索功能
        this.bindSearchEvents();
        
        // 批量操作
        this.bindBatchActions();
        
        // 快捷键
        this.bindKeyboardShortcuts();
    },
    
    /**
     * 初始化组件
     */
    initComponents: function() {
        // 初始化工具提示
        this.initTooltips();
        
        // 初始化日期选择器
        this.initDatePickers();
        
        // 初始化数据表格
        this.initDataTables();
    },
    
    /**
     * 绑定表单验证
     */
    bindFormValidation: function() {
        const forms = document.querySelectorAll('form[novalidate]');
        
        forms.forEach(form => {
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            });
            
            // 实时验证
            const inputs = form.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                input.addEventListener('blur', function() {
                    ReportsManager.validateField(this);
                });
            });
        });
    },
    
    /**
     * 验证单个字段
     * @param {HTMLElement} field - 字段元素
     */
    validateField: function(field) {
        const value = field.value.trim();
        const isRequired = field.hasAttribute('required');
        
        // 清除之前的验证状态
        field.classList.remove('is-valid', 'is-invalid');
        
        // 必填验证
        if (isRequired && !ReportsValidator.validateRequired(value)) {
            field.classList.add('is-invalid');
            return false;
        }
        
        // 特定字段验证
        switch (field.name) {
            case 'patient_age':
                if (value && !ReportsValidator.validateAge(parseInt(value))) {
                    field.classList.add('is-invalid');
                    return false;
                }
                break;
                
            case 'reporter_phone':
            case 'patient_contact':
                if (value && !ReportsValidator.validatePhone(value)) {
                    field.classList.add('is-invalid');
                    return false;
                }
                break;
                
            case 'event_date':
                if (value && !ReportsValidator.validateDate(value, false)) {
                    field.classList.add('is-invalid');
                    return false;
                }
                break;
        }
        
        // 验证通过
        if (value || !isRequired) {
            field.classList.add('is-valid');
        }
        
        return true;
    },
    
    /**
     * 绑定搜索事件
     */
    bindSearchEvents: function() {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            const debouncedSearch = ReportsUtils.debounce(function() {
                ReportsManager.performSearch();
            }, 500);
            
            searchInput.addEventListener('input', debouncedSearch);
        }
    },
    
    /**
     * 执行搜索
     */
    performSearch: function() {
        const searchForm = document.getElementById('filterForm');
        if (searchForm) {
            const formData = new FormData(searchForm);
            const params = new URLSearchParams(formData);
            window.location.href = '?' + params.toString();
        }
    },
    
    /**
     * 绑定批量操作
     */
    bindBatchActions: function() {
        // 全选/取消全选
        const selectAllCheckbox = document.getElementById('selectAll');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                const checkboxes = document.querySelectorAll('.row-checkbox');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
                ReportsManager.updateBatchActionButtons();
            });
        }
        
        // 单个复选框
        const rowCheckboxes = document.querySelectorAll('.row-checkbox');
        rowCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                ReportsManager.updateSelectAllState();
                ReportsManager.updateBatchActionButtons();
            });
        });
    },
    
    /**
     * 更新全选状态
     */
    updateSelectAllState: function() {
        const selectAllCheckbox = document.getElementById('selectAll');
        const rowCheckboxes = document.querySelectorAll('.row-checkbox');
        
        if (selectAllCheckbox && rowCheckboxes.length > 0) {
            const checkedCount = document.querySelectorAll('.row-checkbox:checked').length;
            const totalCount = rowCheckboxes.length;
            
            selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < totalCount;
            selectAllCheckbox.checked = checkedCount === totalCount;
        }
    },
    
    /**
     * 更新批量操作按钮状态
     */
    updateBatchActionButtons: function() {
        const checkedCount = document.querySelectorAll('.row-checkbox:checked').length;
        const batchButtons = document.querySelectorAll('[data-batch-action]');
        
        batchButtons.forEach(button => {
            button.disabled = checkedCount === 0;
        });
        
        // 更新选中计数
        const countElements = document.querySelectorAll('[data-selected-count]');
        countElements.forEach(element => {
            element.textContent = checkedCount;
        });
    },
    
    /**
     * 绑定键盘快捷键
     */
    bindKeyboardShortcuts: function() {
        document.addEventListener('keydown', function(event) {
            // Ctrl + S: 保存
            if (event.ctrlKey && event.key === 's') {
                event.preventDefault();
                const saveButton = document.querySelector('button[type="submit"]');
                if (saveButton) {
                    saveButton.click();
                }
            }
            
            // Ctrl + Enter: 提交
            if (event.ctrlKey && event.key === 'Enter') {
                event.preventDefault();
                const submitButton = document.querySelector('button[name="action"][value="save_and_submit"]');
                if (submitButton) {
                    submitButton.click();
                }
            }
            
            // Escape: 关闭模态框
            if (event.key === 'Escape') {
                const openModal = document.querySelector('.modal.show');
                if (openModal) {
                    const modal = bootstrap.Modal.getInstance(openModal);
                    if (modal) {
                        modal.hide();
                    }
                }
            }
        });
    },
    
    /**
     * 初始化工具提示
     */
    initTooltips: function() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    },
    
    /**
     * 初始化日期选择器
     */
    initDatePickers: function() {
        const dateInputs = document.querySelectorAll('input[type="date"], input[type="datetime-local"]');
        const today = new Date().toISOString().split('T')[0];

        dateInputs.forEach(input => {
            // 事件日期不能是未来
            if (input.name === 'event_date') {
                input.setAttribute('max', today);
            }

            // 添加点击事件，让整个输入框都可以点击打开日期选择器
            input.addEventListener('click', function() {
                // 对于支持showPicker的浏览器，直接调用
                if (this.showPicker) {
                    this.showPicker();
                } else {
                    // 对于不支持的浏览器，聚焦到输入框
                    this.focus();
                }
            });

            // 添加键盘事件支持
            input.addEventListener('keydown', function(e) {
                // 按下空格键或回车键时打开日期选择器
                if (e.key === ' ' || e.key === 'Enter') {
                    e.preventDefault();
                    if (this.showPicker) {
                        this.showPicker();
                    }
                }
            });

            // 改善视觉反馈
            input.style.cursor = 'pointer';
        });
    },
    
    /**
     * 初始化数据表格
     */
    initDataTables: function() {
        // 如果页面有DataTables，进行初始化
        if (typeof $.fn.DataTable !== 'undefined') {
            const tables = document.querySelectorAll('table[data-datatable]');
            tables.forEach(table => {
                $(table).DataTable({
                    language: {
                        url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/zh.json'
                    },
                    pageLength: ReportsConfig.defaultPageSize,
                    responsive: true,
                    order: [[0, 'desc']]
                });
            });
        }
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    ReportsManager.init();
});

// API调用函数
const ReportsAPI = {
    /**
     * 提交报告
     * @param {string} reportId - 报告ID
     * @returns {Promise} Promise对象
     */
    submitReport: function(reportId) {
        return ReportsUtils.ajax(`${ReportsConfig.apiBaseUrl}/reports/${reportId}/submit/`, {
            method: 'POST'
        });
    },

    /**
     * 审核报告
     * @param {string} reportId - 报告ID
     * @param {Object} data - 审核数据
     * @returns {Promise} Promise对象
     */
    reviewReport: function(reportId, data) {
        return ReportsUtils.ajax(`${ReportsConfig.apiBaseUrl}/reports/${reportId}/review/`, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    },

    /**
     * 获取报告详情
     * @param {string} reportId - 报告ID
     * @returns {Promise} Promise对象
     */
    getReportDetail: function(reportId) {
        return ReportsUtils.ajax(`${ReportsConfig.apiBaseUrl}/reports/${reportId}/`);
    },

    /**
     * 获取仪表板数据
     * @returns {Promise} Promise对象
     */
    getDashboardData: function() {
        return ReportsUtils.ajax(`${ReportsConfig.apiBaseUrl}/dashboard/`);
    },

    /**
     * 导出报告
     * @param {Object} filters - 筛选条件
     * @returns {Promise} Promise对象
     */
    exportReports: function(filters = {}) {
        const params = new URLSearchParams(filters);
        return ReportsUtils.ajax(`${ReportsConfig.apiBaseUrl}/reports/export/?${params}`);
    }
};

// 通用消息显示函数
function showMessage(message, type = 'info', duration = 5000) {
    ReportsUtils.showToast(message, type, duration);
}

// 通用成功消息
function showSuccessMessage(message) {
    showMessage(message, 'success', 3000);
}

// 通用错误消息
function showErrorMessage(message) {
    showMessage(message, 'error', 5000);
}

// 通用信息消息
function showInfoMessage(message) {
    showMessage(message, 'info', 3000);
}

// 通用警告消息
function showWarningMessage(message) {
    showMessage(message, 'warning', 4000);
}

// 导出到全局
window.ReportsConfig = ReportsConfig;
window.ReportsUtils = ReportsUtils;
window.ReportsValidator = ReportsValidator;
window.ReportsManager = ReportsManager;
window.ReportsAPI = ReportsAPI;
window.showMessage = showMessage;
window.showSuccessMessage = showSuccessMessage;
window.showErrorMessage = showErrorMessage;
window.showInfoMessage = showInfoMessage;
window.showWarningMessage = showWarningMessage;
