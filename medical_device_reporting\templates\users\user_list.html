{% extends 'base.html' %}
{% load static %}

{% block title %}用户管理 - 医疗器械不良事件上报平台{% endblock %}

{% block extra_head %}
<meta name="csrf-token" content="{{ csrf_token }}">
<!-- 调试信息 -->
<script>
console.log('当前用户权限调试信息:');
console.log('用户名:', '{{ user.username }}');
console.log('账号:', '{{ user.profile.account_number }}');
console.log('角色:', '{{ user.profile.role }}');
console.log('是否管理员:', {{ user.profile.is_admin|yesno:"true,false" }});
console.log('是否有change_userprofile权限:', {{ perms.users.change_userprofile|yesno:"true,false" }});
</script>
{% endblock %}

{% block extra_css %}
<link href="{% static 'users/css/user_management.css' %}" rel="stylesheet">
<!-- DataTables CSS -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap5.min.css">
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/select/1.7.0/css/select.bootstrap5.min.css">
{% endblock %}

{% block nav_items %}
<li class="nav-item">
    <a class="nav-link" href="{% url 'users:dashboard' %}">
        <i class="bi bi-house"></i>
        用户中心
    </a>
</li>
{% if user.profile.is_admin %}
<li class="nav-item">
    <a class="nav-link active" href="{% url 'users:user_list' %}">
        <i class="bi bi-people"></i>
        用户管理
    </a>
</li>
{% endif %}
<li class="nav-item dropdown">
    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
        <i class="bi bi-person-circle"></i>
        {{ user.get_full_name|default:user.username }}
    </a>
    <ul class="dropdown-menu">
        <li><a class="dropdown-item" href="#"><i class="bi bi-gear me-2"></i>个人设置</a></li>
        <li><hr class="dropdown-divider"></li>
        <li><a class="dropdown-item" href="{% url 'users:logout' %}"><i class="bi bi-box-arrow-right me-2"></i>退出登录</a></li>
    </ul>
</li>
{% endblock %}

{% block content %}
<div class="user-management-container">
    <!-- 页面标题 -->
    <div class="page-header mb-4">
        <div class="row align-items-center">
            <div class="col">
                <h2 class="page-title">
                    <i class="bi bi-people me-2"></i>
                    用户管理
                </h2>
                <p class="page-subtitle text-muted">管理系统用户账号和权限</p>
            </div>
            <div class="col-auto">
                {% if perms.users.add_userprofile %}
                <a href="{% url 'users:user_create' %}" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>
                    新建用户
                </a>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- 筛选面板 -->
    <div class="filter-panel card mb-4">
        <div class="card-header">
            <h6 class="card-title mb-0">
                <i class="bi bi-funnel me-2"></i>
                筛选条件
            </h6>
        </div>
        <div class="card-body">
            <form id="filterForm" class="row g-3">
                <div class="col-md-3">
                    <label for="departmentFilter" class="form-label">科室</label>
                    <select class="form-select" id="departmentFilter" name="department">
                        <option value="">全部科室</option>
                        {% for department in departments %}
                        <option value="{{ department.id }}">{{ department.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="roleFilter" class="form-label">角色</label>
                    <select class="form-select" id="roleFilter" name="role">
                        <option value="">全部角色</option>
                        {% for role_value, role_label in role_choices %}
                        <option value="{{ role_value }}">{{ role_label }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="statusFilter" class="form-label">状态</label>
                    <select class="form-select" id="statusFilter" name="is_active">
                        <option value="">全部状态</option>
                        <option value="true">正常</option>
                        <option value="false">禁用</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="searchInput" class="form-label">搜索</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="searchInput" name="search" placeholder="账号、姓名、邮箱">
                        <button class="btn btn-outline-secondary" type="button" id="clearSearch">
                            <i class="bi bi-x"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 用户列表 -->
    <div class="user-list-panel card">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-list me-2"></i>
                        用户列表
                    </h6>
                </div>
                <div class="col-auto">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary btn-sm" id="refreshBtn">
                            <i class="bi bi-arrow-clockwise me-1"></i>
                            刷新
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" id="exportBtn">
                            <i class="bi bi-download me-1"></i>
                            导出
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <!-- 批量操作工具栏 -->
            <div class="bulk-actions-toolbar mb-3" style="display: none;">
                <div class="row align-items-center">
                    <div class="col">
                        <span class="selected-count">已选择 <strong>0</strong> 个用户</span>
                    </div>
                    <div class="col-auto">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-success btn-sm" id="bulkActivateBtn">
                                <i class="bi bi-check-circle me-1"></i>
                                批量启用
                            </button>
                            <button type="button" class="btn btn-warning btn-sm" id="bulkDeactivateBtn">
                                <i class="bi bi-x-circle me-1"></i>
                                批量禁用
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据表格 -->
            <div class="table-responsive">
                <table id="userTable" class="table table-striped table-hover">
                    <thead class="table-dark" style="background-color: #000000 !important;">
                        <tr>
                            <th width="40" style="background-color: #000000 !important; color: #ffffff !important; font-weight: bold; text-align: center; padding: 12px 8px;">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAll">
                                </div>
                            </th>
                            <th style="background-color: #000000 !important; color: #ffffff !important; font-weight: bold; text-align: center; padding: 12px 8px;">账号</th>
                            <th style="background-color: #000000 !important; color: #ffffff !important; font-weight: bold; text-align: center; padding: 12px 8px;">姓名</th>
                            <th style="background-color: #000000 !important; color: #ffffff !important; font-weight: bold; text-align: center; padding: 12px 8px;">邮箱</th>
                            <th style="background-color: #000000 !important; color: #ffffff !important; font-weight: bold; text-align: center; padding: 12px 8px;">科室</th>
                            <th style="background-color: #000000 !important; color: #ffffff !important; font-weight: bold; text-align: center; padding: 12px 8px;">角色</th>
                            <th style="background-color: #000000 !important; color: #ffffff !important; font-weight: bold; text-align: center; padding: 12px 8px;">状态</th>
                            <th style="background-color: #000000 !important; color: #ffffff !important; font-weight: bold; text-align: center; padding: 12px 8px;">最后登录</th>
                            <th width="120" style="background-color: #000000 !important; color: #ffffff !important; font-weight: bold; text-align: center; padding: 12px 8px;">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- DataTables will populate this -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- 用户详情模态框 -->
<div class="modal fade" id="userDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-person-badge me-2"></i>
                    用户详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="userDetailContent">
                    <!-- 用户详情内容将在这里动态加载 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="editUserBtn">编辑用户</button>
            </div>
        </div>
    </div>
</div>

<!-- 确认删除模态框 -->
<div class="modal fade" id="confirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-exclamation-triangle text-warning me-2"></i>
                    确认操作
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p id="confirmMessage">确定要执行此操作吗？</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmBtn">确定</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- DataTables JavaScript -->
<script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap5.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/select/1.7.0/js/dataTables.select.min.js"></script>

<!-- 用户管理JavaScript -->
<script src="{% static 'users/js/user_management.js' %}?v={{ timestamp }}"></script>
<script>
// 强制刷新缓存的时间戳
console.log('JavaScript文件版本:', '{{ timestamp }}');
</script>
{% endblock %}
