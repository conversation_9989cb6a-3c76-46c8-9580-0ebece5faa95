#!/usr/bin/env python
"""
快速设置脚本 - 一键初始化系统
Quick Setup Script for Medical Device Reporting Platform
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

def quick_setup():
    """快速设置系统"""
    print("🚀 医疗器械不良事件上报平台 - 快速设置")
    print("=" * 50)
    
    try:
        # 1. 运行数据库迁移
        print("1. 运行数据库迁移...")
        os.system('python manage.py migrate --settings=config.settings.development')
        print("✅ 数据库迁移完成")
        
        # 2. 创建用户
        print("\n2. 创建初始用户...")
        os.system('python create_superuser.py')
        print("✅ 用户创建完成")
        
        # 3. 收集静态文件
        print("\n3. 收集静态文件...")
        os.system('python manage.py collectstatic --noinput --settings=config.settings.development')
        print("✅ 静态文件收集完成")
        
        print("\n" + "=" * 50)
        print("🎉 快速设置完成！")
        print("\n📋 下一步操作：")
        print("1. 启动开发服务器：")
        print("   python manage.py runserver --settings=config.settings.development")
        print("\n2. 访问系统：")
        print("   - 业务系统：http://127.0.0.1:8000/")
        print("   - 管理后台：http://127.0.0.1:8000/admin/")
        print("\n3. 登录信息：")
        print("   - 管理员账号：0001（无需密码）")
        print("   - 测试人员账号：1001（无需密码）")
        print("   - Django管理员：admin / admin123456")
        
        return True
        
    except Exception as e:
        print(f"❌ 快速设置失败: {e}")
        return False

if __name__ == '__main__':
    try:
        success = quick_setup()
        if success:
            print("\n✨ 系统已准备就绪，开始使用吧！")
        else:
            print("\n💥 设置过程中出现问题，请检查错误信息")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断操作")
        sys.exit(1)
