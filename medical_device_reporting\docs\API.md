# API 接口文档

## 📋 目录
- [认证说明](#认证说明)
- [用户管理API](#用户管理api)
- [科室管理API](#科室管理api)
- [个人设置API](#个人设置api)
- [错误处理](#错误处理)
- [状态码说明](#状态码说明)

## 🔐 认证说明

### 认证方式
系统使用Django的会话认证机制，所有API请求需要用户登录状态。

### CSRF保护
所有POST、PUT、DELETE请求需要包含CSRF令牌：
```javascript
headers: {
    'X-CSRFToken': getCsrfToken(),
    'Content-Type': 'application/json'
}
```

### 权限要求
- **管理员权限**：用户管理、科室管理相关API
- **用户权限**：个人设置相关API
- **对象权限**：用户只能操作自己的数据

## 👥 用户管理API

### 获取用户列表
```http
GET /api/users/
```

**权限要求**：管理员

**查询参数**：
- `page` (int): 页码，默认1
- `page_size` (int): 每页数量，默认20
- `search` (string): 搜索关键词
- `role` (string): 角色筛选 (admin/staff)
- `department` (int): 科室ID筛选
- `is_active` (boolean): 状态筛选

**响应示例**：
```json
{
    "count": 100,
    "next": "http://example.com/api/users/?page=2",
    "previous": null,
    "results": [
        {
            "id": 1,
            "account_number": "0001",
            "username": "admin",
            "first_name": "管理",
            "last_name": "员",
            "email": "<EMAIL>",
            "role": "admin",
            "department": {
                "id": 1,
                "code": "ICU",
                "name": "重症监护科"
            },
            "is_active": true,
            "last_login": "2024-01-15T10:30:00Z",
            "created_at": "2024-01-01T00:00:00Z"
        }
    ]
}
```

### 创建用户
```http
POST /api/users/
```

**权限要求**：管理员

**请求体**：
```json
{
    "username": "newuser",
    "first_name": "新",
    "last_name": "用户",
    "email": "<EMAIL>",
    "role": "staff",
    "department": 1,
    "phone": "***********"
}
```

**响应示例**：
```json
{
    "id": 2,
    "account_number": "1001",
    "username": "newuser",
    "first_name": "新",
    "last_name": "用户",
    "email": "<EMAIL>",
    "role": "staff",
    "department": {
        "id": 1,
        "code": "ICU",
        "name": "重症监护科"
    },
    "is_active": true,
    "created_at": "2024-01-15T10:30:00Z"
}
```

### 获取用户详情
```http
GET /api/users/{id}/
```

**权限要求**：管理员或用户本人

**响应示例**：
```json
{
    "id": 1,
    "account_number": "0001",
    "username": "admin",
    "first_name": "管理",
    "last_name": "员",
    "email": "<EMAIL>",
    "role": "admin",
    "department": {
        "id": 1,
        "code": "ICU",
        "name": "重症监护科"
    },
    "is_active": true,
    "last_login": "2024-01-15T10:30:00Z",
    "last_login_ip": "*************",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-15T10:30:00Z"
}
```

### 更新用户
```http
PUT /api/users/{id}/
```

**权限要求**：管理员

**请求体**：
```json
{
    "first_name": "更新的名",
    "last_name": "更新的姓",
    "email": "<EMAIL>",
    "role": "staff",
    "department": 2,
    "is_active": true
}
```

### 切换用户状态
```http
POST /api/users/{id}/toggle-status/
```

**权限要求**：管理员

**响应示例**：
```json
{
    "success": true,
    "message": "用户状态已更新",
    "is_active": false
}
```

## 🏥 科室管理API

### 获取科室列表
```http
GET /api/departments/
```

**权限要求**：管理员

**查询参数**：
- `page` (int): 页码，默认1
- `page_size` (int): 每页数量，默认20
- `search` (string): 搜索关键词
- `is_active` (boolean): 状态筛选

**响应示例**：
```json
{
    "count": 10,
    "next": null,
    "previous": null,
    "results": [
        {
            "id": 1,
            "code": "ICU",
            "name": "重症监护科",
            "is_active": true,
            "total_users": 5,
            "active_users": 4,
            "created_at": "2024-01-01T00:00:00Z",
            "created_by": {
                "id": 1,
                "username": "admin"
            }
        }
    ]
}
```

### 创建科室
```http
POST /api/departments/
```

**权限要求**：管理员

**请求体**：
```json
{
    "code": "ER",
    "name": "急诊科",
    "is_active": true
}
```

**响应示例**：
```json
{
    "id": 2,
    "code": "ER",
    "name": "急诊科",
    "is_active": true,
    "total_users": 0,
    "active_users": 0,
    "created_at": "2024-01-15T10:30:00Z",
    "created_by": {
        "id": 1,
        "username": "admin"
    }
}
```

### 获取科室详情
```http
GET /api/departments/{id}/
```

**权限要求**：管理员

**响应示例**：
```json
{
    "id": 1,
    "code": "ICU",
    "name": "重症监护科",
    "is_active": true,
    "total_users": 5,
    "active_users": 4,
    "users": [
        {
            "id": 2,
            "account_number": "1001",
            "username": "staff1",
            "full_name": "科室 人员",
            "email": "<EMAIL>",
            "role": "staff",
            "is_active": true,
            "last_login": "2024-01-15T09:00:00Z"
        }
    ],
    "created_at": "2024-01-01T00:00:00Z",
    "created_by": {
        "id": 1,
        "username": "admin"
    },
    "updated_at": "2024-01-15T10:30:00Z",
    "updated_by": {
        "id": 1,
        "username": "admin"
    }
}
```

### 更新科室
```http
PUT /api/departments/{id}/
```

**权限要求**：管理员

**请求体**：
```json
{
    "name": "重症监护室",
    "is_active": false
}
```

### 删除科室
```http
DELETE /api/departments/{id}/
```

**权限要求**：管理员

**注意**：只能删除没有用户的科室

**响应示例**：
```json
{
    "success": true,
    "message": "科室删除成功"
}
```

### 检查科室代码
```http
GET /api/departments/check-code/?code=ICU
```

**权限要求**：管理员

**响应示例**：
```json
{
    "exists": true,
    "message": "科室代码已存在"
}
```

### Excel导入
```http
POST /api/departments/import/
```

**权限要求**：管理员

**请求体**：multipart/form-data
- `excel_file`: Excel文件
- `skip_errors`: 是否跳过错误行
- `update_existing`: 是否更新已存在的科室

**响应示例**：
```json
{
    "success": true,
    "message": "导入完成",
    "success_count": 5,
    "failed_count": 1,
    "errors": [
        "第3行：科室代码已存在"
    ]
}
```

### Excel导出
```http
GET /api/departments/export/?format=excel
```

**权限要求**：管理员

**查询参数**：
- `format` (string): 导出格式，默认excel
- `search` (string): 搜索条件
- `is_active` (boolean): 状态筛选

**响应**：Excel文件下载

### 下载模板
```http
GET /api/departments/template/
```

**权限要求**：管理员

**响应**：Excel模板文件下载

## 👤 个人设置API

### 获取个人信息
```http
GET /profile/
```

**权限要求**：已登录用户

**响应示例**：
```json
{
    "user": {
        "id": 2,
        "username": "staff1",
        "first_name": "科室",
        "last_name": "人员",
        "email": "<EMAIL>",
        "last_login": "2024-01-15T09:00:00Z",
        "date_joined": "2024-01-01T00:00:00Z"
    },
    "profile": {
        "account_number": "1001",
        "role": "staff",
        "department": {
            "id": 1,
            "code": "ICU",
            "name": "重症监护科"
        },
        "is_active": true,
        "last_login_ip": "*************"
    }
}
```

### 更新个人信息
```http
POST /profile/edit/
```

**权限要求**：已登录用户

**请求体**：
```json
{
    "first_name": "更新的名",
    "last_name": "更新的姓",
    "email": "<EMAIL>"
}
```

**响应示例**：
```json
{
    "success": true,
    "message": "个人信息更新成功"
}
```

### 保存用户设置
```http
POST /settings/
```

**权限要求**：已登录用户

**请求体**：
```json
{
    "type": "preferences",
    "value": {
        "theme": "dark",
        "language": "zh-cn",
        "timezone": "Asia/Shanghai",
        "page_size": "20"
    }
}
```

**响应示例**：
```json
{
    "success": true,
    "message": "设置已保存"
}
```

### 修改密码
```http
POST /change-password/
```

**权限要求**：已登录用户

**请求体**：
```json
{
    "current_password": "oldpassword123",
    "new_password": "newpassword123",
    "confirm_password": "newpassword123"
}
```

**响应示例**：
```json
{
    "success": true,
    "message": "密码修改成功，请重新登录"
}
```

## ❌ 错误处理

### 错误响应格式
```json
{
    "error": "错误消息",
    "details": {
        "field": ["字段错误详情"]
    },
    "code": "ERROR_CODE"
}
```

### 常见错误
- **认证错误**：用户未登录或会话过期
- **权限错误**：用户无权限访问资源
- **验证错误**：请求数据验证失败
- **业务错误**：业务逻辑约束违反
- **系统错误**：服务器内部错误

## 📊 状态码说明

| 状态码 | 说明 | 示例场景 |
|--------|------|----------|
| 200 | 成功 | 获取数据成功 |
| 201 | 创建成功 | 用户/科室创建成功 |
| 400 | 请求错误 | 表单验证失败 |
| 401 | 未认证 | 用户未登录 |
| 403 | 权限不足 | 科室人员访问管理功能 |
| 404 | 资源不存在 | 用户/科室不存在 |
| 409 | 冲突 | 科室代码重复 |
| 500 | 服务器错误 | 系统内部错误 |

## 🔧 使用示例

### JavaScript示例
```javascript
// 获取科室列表
async function getDepartments() {
    try {
        const response = await fetch('/api/departments/', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        
        if (!response.ok) {
            throw new Error('获取科室列表失败');
        }
        
        const data = await response.json();
        return data.results;
    } catch (error) {
        console.error('Error:', error);
        throw error;
    }
}

// 创建科室
async function createDepartment(departmentData) {
    try {
        const response = await fetch('/api/departments/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken(),
            },
            body: JSON.stringify(departmentData)
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || '创建科室失败');
        }
        
        return await response.json();
    } catch (error) {
        console.error('Error:', error);
        throw error;
    }
}

// 修改密码
async function changePassword(passwordData) {
    try {
        const response = await fetch('/change-password/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': getCsrfToken(),
            },
            body: new URLSearchParams(passwordData)
        });
        
        const result = await response.json();
        
        if (!result.success) {
            throw new Error(result.error || '密码修改失败');
        }
        
        return result;
    } catch (error) {
        console.error('Error:', error);
        throw error;
    }
}
```

### Python示例
```python
import requests

# 配置
BASE_URL = 'http://localhost:8000'
session = requests.Session()

# 登录
def login(account_number):
    response = session.post(f'{BASE_URL}/login/', {
        'account_number': account_number
    })
    return response.status_code == 200

# 获取科室列表
def get_departments():
    response = session.get(f'{BASE_URL}/api/departments/')
    if response.status_code == 200:
        return response.json()['results']
    else:
        raise Exception(f'获取科室列表失败: {response.status_code}')

# 创建科室
def create_department(code, name, is_active=True):
    csrf_token = session.cookies.get('csrftoken')
    response = session.post(f'{BASE_URL}/api/departments/', 
        json={
            'code': code,
            'name': name,
            'is_active': is_active
        },
        headers={
            'X-CSRFToken': csrf_token,
            'Content-Type': 'application/json'
        }
    )
    
    if response.status_code == 201:
        return response.json()
    else:
        raise Exception(f'创建科室失败: {response.json()}')
```
