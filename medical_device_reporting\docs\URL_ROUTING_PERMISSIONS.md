# URL路由和权限配置文档

## 概述

本文档描述了医疗器械不良事件上报平台的URL路由结构和权限控制机制。

## URL路由结构

### 主要URL模式

```
/                           # 根路径，重定向到用户中心或登录页面
/login/                     # 用户登录页面
/logout/                    # 用户登出
/dashboard/                 # 用户中心（需要科室成员或管理员权限）
/users/                     # 用户管理列表（需要管理员权限）
/users/create/              # 用户创建页面（需要管理员权限）
/users/{id}/edit/           # 用户编辑页面（需要管理员权限）
/users/{id}/toggle-status/  # 用户状态切换（需要管理员权限）
```

### API路由结构

```
/api/users/                     # 用户列表API（需要管理员权限）
/api/users/create/              # 用户创建API（需要管理员权限）
/api/users/{id}/                # 用户详情API（需要管理员权限）
/api/users/{id}/activate/       # 用户激活API（需要管理员权限）
/api/users/{id}/deactivate/     # 用户禁用API（需要管理员权限）
/api/users/{id}/change-role/    # 角色变更API（需要管理员权限）
/api/users/{id}/change-department/ # 科室变更API（需要管理员权限）
/api/users/bulk-action/         # 批量操作API（需要管理员权限）
/api/users/statistics/          # 统计API（科室成员或管理员）
/api/users/search/              # 搜索API（科室成员或管理员）
/api/departments/               # 科室列表API（GET：科室成员或管理员，POST/PUT/DELETE：管理员）
/api/departments/{id}/          # 科室详情API（GET：科室成员或管理员，PUT/DELETE：管理员）
```

## 权限控制机制

### 用户角色

1. **管理员 (admin)**
   - 可以访问所有功能
   - 用户管理：创建、编辑、删除、状态管理
   - 科室管理：创建、编辑、删除科室
   - 系统统计：查看所有统计数据
   - 可以没有所属科室

2. **科室人员 (staff)**
   - 只能访问基础功能
   - 用户中心：查看个人信息和基础统计
   - 不能管理其他用户
   - 不能管理科室
   - 必须有所属科室

### 权限装饰器

#### 函数装饰器

```python
@admin_required
def admin_only_view(request):
    """只有管理员可以访问的视图"""
    pass

@department_member_or_admin_required
def staff_or_admin_view(request):
    """科室成员或管理员可以访问的视图"""
    pass

@owner_or_admin_required(get_object_func=get_user_object)
def owner_or_admin_view(request, user_id):
    """对象所有者或管理员可以访问的视图"""
    pass
```

#### 类视图Mixin

```python
class AdminOnlyView(AdminRequiredMixin, View):
    """只有管理员可以访问的类视图"""
    pass

class StaffOrAdminView(DepartmentMemberOrAdminMixin, View):
    """科室成员或管理员可以访问的类视图"""
    pass

class OwnerOrAdminView(OwnerOrAdminMixin, DetailView):
    """对象所有者或管理员可以访问的类视图"""
    pass
```

#### DRF权限类

```python
class AdminOnlyAPI(APIView):
    permission_classes = [IsAdminUser]

class StaffOrAdminAPI(APIView):
    permission_classes = [IsDepartmentMemberOrAdmin]

class OwnerOrAdminAPI(APIView):
    permission_classes = [IsOwnerOrAdmin]
```

### 中间件权限控制

#### LoginRequiredMiddleware
- 确保用户已登录才能访问受保护的页面
- 豁免URL：`/login/`, `/admin/login/`, `/static/`, `/media/`, `/__debug__/`

#### PermissionControlMiddleware
- 基于角色的访问控制
- 自动检查URL模式并验证用户权限
- 支持AJAX请求的JSON错误响应

### 权限检查流程

1. **用户认证检查**
   - 检查用户是否已登录
   - 检查用户是否有有效的profile

2. **用户状态检查**
   - 检查用户是否被禁用
   - 检查科室人员是否有有效的科室

3. **权限级别检查**
   - 管理员：可以访问所有管理功能
   - 科室人员：只能访问基础功能和统计查询
   - 禁用用户：拒绝所有访问

4. **对象级权限检查**
   - 用户只能访问自己的数据
   - 管理员可以访问所有数据
   - 科室成员可以访问同科室的数据（如果适用）

## 安全特性

### CSRF保护
- 所有POST/PUT/DELETE请求都需要CSRF令牌
- AJAX请求自动包含CSRF令牌

### 会话安全
- 会话超时控制
- 登录IP地址验证
- 并发登录限制

### 输入验证
- 所有用户输入都经过验证
- API参数类型检查
- SQL注入防护

### 错误处理
- 统一的错误响应格式
- 敏感信息不暴露
- 详细的日志记录

## 配置示例

### settings.py配置

```python
MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'apps.users.middleware.UserProfileMiddleware',
    'apps.users.middleware.SessionSecurityMiddleware',
    'apps.users.middleware.LoginRequiredMiddleware',
    'apps.users.middleware.PermissionControlMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

# 登录相关设置
LOGIN_URL = '/login/'
LOGIN_REDIRECT_URL = '/dashboard/'
LOGOUT_REDIRECT_URL = '/login/'
```

### URL配置

```python
# config/urls.py
urlpatterns = [
    path('admin/', admin.site.urls),
    path('', include('apps.users.urls')),
]

# apps/users/urls.py
urlpatterns = [
    path('', root_redirect, name='root'),
    path('login/', views.login_view, name='login'),
    path('logout/', views.logout_view, name='logout'),
    path('dashboard/', views.dashboard_view, name='dashboard'),
    path('users/', views.user_list_view, name='user_list'),
    # ... 其他URL模式
] + api_urlpatterns
```

## 测试

### 权限测试脚本

运行权限测试：
```bash
python test_permissions.py
```

测试覆盖：
- 未登录用户访问控制
- 管理员权限验证
- 科室人员权限限制
- 禁用用户访问拒绝
- API权限控制
- AJAX请求处理

### 手动测试检查点

1. **登录流程**
   - 4位数账号登录
   - 登录状态保持
   - 自动重定向

2. **权限边界**
   - 管理员可以访问所有功能
   - 科室人员只能访问允许的功能
   - 禁用用户被正确拒绝

3. **安全性**
   - CSRF保护生效
   - 会话安全正常
   - 错误信息不泄露敏感数据

## 故障排除

### 常见问题

1. **权限拒绝错误**
   - 检查用户角色配置
   - 验证中间件顺序
   - 确认权限装饰器使用正确

2. **重定向循环**
   - 检查LOGIN_URL配置
   - 验证豁免URL列表
   - 确认根路径重定向逻辑

3. **API权限问题**
   - 检查DRF权限类配置
   - 验证用户profile状态
   - 确认API URL模式匹配

### 调试技巧

1. **启用详细日志**
   ```python
   LOGGING = {
       'loggers': {
           'apps.users.permissions': {
               'level': 'DEBUG',
           },
           'apps.users.middleware': {
               'level': 'DEBUG',
           },
       }
   }
   ```

2. **检查用户权限**
   ```python
   from apps.users.permissions import get_user_role_permissions
   permissions = get_user_role_permissions(user)
   print(permissions)
   ```

3. **测试权限装饰器**
   ```python
   from apps.users.permissions import check_user_permissions
   has_perm = check_user_permissions(user, ['users.view_userprofile'])
   print(has_perm)
   ```
