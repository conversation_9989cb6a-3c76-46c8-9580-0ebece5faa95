{% extends 'reports/base.html' %}
{% load static %}

{% block page_title %}报告管理仪表板{% endblock %}
{% block page_heading %}报告管理仪表板{% endblock %}
{% block page_description %}查看报告统计信息和快速操作{% endblock %}

{% block page_actions %}
<a href="{% url 'reports:step_create' %}" class="btn btn-primary">
    <i class="bi bi-plus-circle me-2"></i>
    新建报告
</a>
{% endblock %}

{% block reports_content %}
<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="stat-card card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="stat-icon bg-primary">
                    <i class="bi bi-file-text"></i>
                </div>
                <h4 class="stat-number">{{ stats.total_count|default:0 }}</h4>
                <p class="stat-label">总报告数</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="stat-card card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="stat-icon bg-warning">
                    <i class="bi bi-clock"></i>
                </div>
                <h4 class="stat-number">{{ stats.draft_count|default:0 }}</h4>
                <p class="stat-label">草稿</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="stat-card card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="stat-icon bg-info">
                    <i class="bi bi-send"></i>
                </div>
                <h4 class="stat-number">{{ stats.submitted_count|default:0 }}</h4>
                <p class="stat-label">已提交</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="stat-card card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="stat-icon bg-success">
                    <i class="bi bi-check-circle"></i>
                </div>
                <h4 class="stat-number">{{ stats.approved_count|default:0 }}</h4>
                <p class="stat-label">已批准</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 左侧功能区域 -->
    <div class="col-lg-8 mb-4">
        <!-- 快速操作 -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white border-bottom">
                <h5 class="card-title mb-0">
                    <i class="bi bi-lightning me-2"></i>
                    快速操作
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 col-lg-4 mb-3">
                        <a href="{% url 'reports:step_create' %}" class="feature-link">
                            <div class="feature-card">
                                <div class="feature-icon bg-primary">
                                    <i class="bi bi-plus-circle"></i>
                                </div>
                                <h6 class="feature-title">新建报告</h6>
                                <p class="feature-desc">创建新的不良事件报告</p>
                            </div>
                        </a>
                    </div>
                    
                    <div class="col-md-6 col-lg-4 mb-3">
                        <a href="{% url 'reports:report_create' %}" class="feature-link">
                            <div class="feature-card">
                                <div class="feature-icon bg-success">
                                    <i class="bi bi-file-text"></i>
                                </div>
                                <h6 class="feature-title">完整表单</h6>
                                <p class="feature-desc">使用完整表单创建报告</p>
                            </div>
                        </a>
                    </div>
                    
                    <div class="col-md-6 col-lg-4 mb-3">
                        <a href="{% url 'reports:report_list' %}" class="feature-link">
                            <div class="feature-card">
                                <div class="feature-icon bg-info">
                                    <i class="bi bi-list"></i>
                                </div>
                                <h6 class="feature-title">报告列表</h6>
                                <p class="feature-desc">查看所有报告</p>
                            </div>
                        </a>
                    </div>
                    
                    {% if user.profile.is_admin %}
                    <div class="col-md-6 col-lg-4 mb-3">
                        <a href="{% url 'reports:pending_review' %}" class="feature-link">
                            <div class="feature-card">
                                <div class="feature-icon bg-warning">
                                    <i class="bi bi-eye"></i>
                                </div>
                                <h6 class="feature-title">待审核</h6>
                                <p class="feature-desc">审核待处理报告</p>
                            </div>
                        </a>
                    </div>
                    
                    <div class="col-md-6 col-lg-4 mb-3">
                        <a href="{% url 'reports:serious_events' %}" class="feature-link">
                            <div class="feature-card">
                                <div class="feature-icon bg-danger">
                                    <i class="bi bi-exclamation-triangle"></i>
                                </div>
                                <h6 class="feature-title">严重事件</h6>
                                <p class="feature-desc">查看严重不良事件</p>
                            </div>
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- 最近报告 -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-clock-history me-2"></i>
                            最近报告
                        </h5>
                    </div>
                    <div class="col-auto">
                        <a href="{% url 'reports:report_list' %}" class="btn btn-outline-primary btn-sm">
                            查看全部
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                {% if recent_reports %}
                <div class="list-group list-group-flush">
                    {% for report in recent_reports %}
                    <div class="list-group-item border-0 px-0">
                        <div class="row align-items-center">
                            <div class="col">
                                <h6 class="mb-1">
                                    <a href="{% url 'reports:report_detail' report_id=report.id %}" class="text-decoration-none">
                                        {{ report.report_number }}
                                    </a>
                                </h6>
                                <p class="mb-1 text-muted small">{{ report.device_name|truncatechars:50 }}</p>
                                <small class="text-muted">{{ report.created_at|date:"Y-m-d H:i" }}</small>
                            </div>
                            <div class="col-auto">
                                <span class="badge bg-{{ report.status|default:'secondary' }}">
                                    {{ report.get_status_display }}
                                </span>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="bi bi-inbox display-4 text-muted"></i>
                    <p class="text-muted mt-2">暂无报告</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- 右侧信息面板 -->
    <div class="col-lg-4 mb-4">
        {% if user.profile.is_admin and pending_reports %}
        <!-- 待审核报告 -->
        <div class="card border-0 shadow-sm mb-3">
            <div class="card-header bg-white border-bottom">
                <h6 class="card-title mb-0">
                    <i class="bi bi-eye me-2"></i>
                    待审核报告
                </h6>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    {% for report in pending_reports %}
                    <div class="list-group-item border-0 px-0">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="mb-1">
                                    <a href="{% url 'reports:report_detail' report_id=report.id %}" class="text-decoration-none">
                                        {{ report.report_number }}
                                    </a>
                                </h6>
                                <small class="text-muted">{{ report.submitted_at|date:"m-d H:i" }}</small>
                            </div>
                            <span class="badge bg-warning">待审核</span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                <div class="text-center mt-3">
                    <a href="{% url 'reports:pending_review' %}" class="btn btn-outline-warning btn-sm">
                        查看全部待审核
                    </a>
                </div>
            </div>
        </div>
        {% endif %}

        {% if user.profile.is_admin and serious_events %}
        <!-- 严重事件 -->
        <div class="card border-0 shadow-sm mb-3">
            <div class="card-header bg-white border-bottom">
                <h6 class="card-title mb-0">
                    <i class="bi bi-exclamation-triangle text-danger me-2"></i>
                    严重事件
                </h6>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    {% for report in serious_events %}
                    <div class="list-group-item border-0 px-0">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="mb-1">
                                    <a href="{% url 'reports:report_detail' report_id=report.id %}" class="text-decoration-none">
                                        {{ report.report_number }}
                                    </a>
                                </h6>
                                <small class="text-muted">{{ report.event_date|date:"m-d" }}</small>
                            </div>
                            <span class="badge bg-danger">{{ report.get_injury_level_display }}</span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                <div class="text-center mt-3">
                    <a href="{% url 'reports:serious_events' %}" class="btn btn-outline-danger btn-sm">
                        查看全部严重事件
                    </a>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- 个人统计 -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <h6 class="card-title mb-0">
                    <i class="bi bi-person-badge me-2"></i>
                    个人统计
                </h6>
            </div>
            <div class="card-body">
                <div class="user-stats">
                    <div class="stat-item">
                        <label>我的报告：</label>
                        <span class="badge bg-primary">{{ stats.my_reports_count|default:0 }}</span>
                    </div>
                    <div class="stat-item">
                        <label>本月新增：</label>
                        <span class="badge bg-success">{{ stats.this_month_count|default:0 }}</span>
                    </div>
                    <div class="stat-item">
                        <label>待提交：</label>
                        <span class="badge bg-warning">{{ stats.my_draft_count|default:0 }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block reports_extra_css %}
<style>
.stat-card {
    transition: transform 0.2s;
}
.stat-card:hover {
    transform: translateY(-2px);
}
.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    color: white;
    font-size: 24px;
}
.stat-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}
.stat-label {
    color: #6c757d;
    margin-bottom: 0;
}
.feature-link {
    text-decoration: none;
    color: inherit;
}
.feature-card {
    text-align: center;
    padding: 20px;
    border-radius: 8px;
    transition: all 0.2s;
    border: 1px solid #e9ecef;
}
.feature-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}
.feature-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    color: white;
    font-size: 20px;
}
.feature-title {
    font-weight: 600;
    margin-bottom: 8px;
}
.feature-desc {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 0;
}
.user-stats .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f8f9fa;
}
.user-stats .stat-item:last-child {
    border-bottom: none;
}
</style>
{% endblock %}
