"""
缓存管理API
Cache Management APIs

提供缓存状态监控、手动失效和预热等管理功能。
"""

import logging
from typing import Dict, Any

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes

from apps.common.cache_utils import (
    StatisticsCacheManager,
    invalidate_statistics_cache,
    warm_up_cache
)
from apps.users.models import UserProfile
from .permissions import CanViewAllReports

logger = logging.getLogger(__name__)


class CacheStatsAPIView(APIView):
    """缓存统计信息API"""
    permission_classes = [permissions.IsAuthenticated, CanViewAllReports]
    
    def get(self, request):
        """获取缓存统计信息"""
        try:
            # 获取用户配置文件
            try:
                user_profile = UserProfile.objects.get(user=request.user)
            except UserProfile.DoesNotExist:
                return Response(
                    {'error': '用户配置文件不存在'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # 只有管理员可以查看缓存统计
            if not user_profile.is_admin:
                return Response(
                    {'error': '权限不足，只有管理员可以查看缓存统计'},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            # 获取缓存统计信息
            cache_stats = StatisticsCacheManager.get_cache_stats()
            
            # 添加缓存配置信息
            cache_config = {
                'cache_timeouts': StatisticsCacheManager.CACHE_TIMEOUTS,
                'default_timeout': StatisticsCacheManager.DEFAULT_TIMEOUT,
                'cache_levels': StatisticsCacheManager.CACHE_LEVELS,
            }
            
            data = {
                'cache_stats': cache_stats,
                'cache_config': cache_config,
                'status': 'success'
            }
            
            return Response(data, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f'获取缓存统计失败: {str(e)}')
            return Response(
                {'error': f'获取缓存统计失败: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class CacheInvalidateAPIView(APIView):
    """缓存失效API"""
    permission_classes = [permissions.IsAuthenticated, CanViewAllReports]
    
    def post(self, request):
        """手动失效缓存"""
        try:
            # 获取用户配置文件
            try:
                user_profile = UserProfile.objects.get(user=request.user)
            except UserProfile.DoesNotExist:
                return Response(
                    {'error': '用户配置文件不存在'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # 只有管理员可以手动失效缓存
            if not user_profile.is_admin:
                return Response(
                    {'error': '权限不足，只有管理员可以手动失效缓存'},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            # 获取请求参数
            function_names = request.data.get('function_names')  # 可选，特定函数名列表
            level = request.data.get('level')  # 可选，缓存层级
            scope = request.data.get('scope', 'all')  # 失效范围：all, global, department, user
            
            # 执行缓存失效
            if scope == 'all':
                # 失效所有统计缓存
                invalidate_statistics_cache()
                message = '所有统计缓存已失效'
                
            elif scope == 'global':
                # 失效全局缓存
                invalidate_statistics_cache(
                    function_names=function_names,
                    level=StatisticsCacheManager.CACHE_LEVELS['global']
                )
                message = '全局统计缓存已失效'
                
            elif scope == 'department':
                # 失效科室级缓存
                invalidate_statistics_cache(
                    function_names=function_names,
                    level=StatisticsCacheManager.CACHE_LEVELS['department']
                )
                message = '科室级统计缓存已失效'
                
            elif scope == 'user':
                # 失效用户级缓存
                invalidate_statistics_cache(
                    function_names=function_names,
                    user_profile=user_profile
                )
                message = '用户级统计缓存已失效'
                
            else:
                return Response(
                    {'error': f'无效的失效范围: {scope}'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            logger.info(
                f'手动缓存失效: {message}',
                extra={
                    'user_id': request.user.id,
                    'scope': scope,
                    'function_names': function_names,
                    'level': level
                }
            )
            
            return Response({
                'message': message,
                'scope': scope,
                'function_names': function_names,
                'status': 'success'
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f'手动缓存失效失败: {str(e)}')
            return Response(
                {'error': f'缓存失效失败: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class CacheWarmupAPIView(APIView):
    """缓存预热API"""
    permission_classes = [permissions.IsAuthenticated, CanViewAllReports]
    
    def post(self, request):
        """预热缓存"""
        try:
            # 获取用户配置文件
            try:
                user_profile = UserProfile.objects.get(user=request.user)
            except UserProfile.DoesNotExist:
                return Response(
                    {'error': '用户配置文件不存在'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # 只有管理员可以预热缓存
            if not user_profile.is_admin:
                return Response(
                    {'error': '权限不足，只有管理员可以预热缓存'},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            # 获取请求参数
            scope = request.data.get('scope', 'global')  # 预热范围：global, department
            
            if scope == 'global':
                # 全局预热
                warm_up_cache()
                message = '全局缓存预热完成'
                
            elif scope == 'department':
                # 科室级预热
                warm_up_cache(user_profile=user_profile)
                message = f'科室 {user_profile.department.name} 缓存预热完成'
                
            else:
                return Response(
                    {'error': f'无效的预热范围: {scope}'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            logger.info(
                f'缓存预热完成: {message}',
                extra={
                    'user_id': request.user.id,
                    'scope': scope
                }
            )
            
            return Response({
                'message': message,
                'scope': scope,
                'status': 'success'
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f'缓存预热失败: {str(e)}')
            return Response(
                {'error': f'缓存预热失败: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def cache_health_check(request):
    """缓存健康检查"""
    try:
        # 获取用户配置文件
        try:
            user_profile = UserProfile.objects.get(user=request.user)
        except UserProfile.DoesNotExist:
            return Response(
                {'error': '用户配置文件不存在'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 执行简单的缓存测试
        from django.core.cache import cache
        
        test_key = 'cache_health_test'
        test_value = 'test_value'
        
        # 测试缓存写入
        cache.set(test_key, test_value, 60)
        
        # 测试缓存读取
        cached_value = cache.get(test_key)
        
        # 清理测试数据
        cache.delete(test_key)
        
        # 检查结果
        if cached_value == test_value:
            health_status = 'healthy'
            message = '缓存系统正常'
        else:
            health_status = 'unhealthy'
            message = '缓存系统异常'
        
        # 获取基础统计信息
        cache_stats = StatisticsCacheManager.get_cache_stats()
        
        return Response({
            'health_status': health_status,
            'message': message,
            'cache_backend': str(cache.__class__),
            'basic_stats': cache_stats,
            'timestamp': cache_stats.get('timestamp')
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f'缓存健康检查失败: {str(e)}')
        return Response({
            'health_status': 'error',
            'message': f'缓存健康检查失败: {str(e)}',
            'timestamp': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
