{% extends 'base.html' %}
{% load static %}

{% block title %}编辑个人信息 - 医疗器械不良事件上报平台{% endblock %}

{% block extra_css %}
<link href="{% static 'users/css/user_management.css' %}" rel="stylesheet">
{% endblock %}

{% block nav_items %}
<li class="nav-item">
    <a class="nav-link" href="{% url 'users:dashboard' %}">
        <i class="bi bi-house"></i>
        用户中心
    </a>
</li>
{% if user.profile.is_admin %}
<li class="nav-item">
    <a class="nav-link" href="{% url 'users:user_list' %}">
        <i class="bi bi-people"></i>
        用户管理
    </a>
</li>
<li class="nav-item">
    <a class="nav-link" href="{% url 'users:department_list' %}">
        <i class="bi bi-building"></i>
        科室管理
    </a>
</li>
{% endif %}
<li class="nav-item dropdown">
    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
        <i class="bi bi-person-circle"></i>
        {{ user.get_full_name|default:user.username }}
    </a>
    <ul class="dropdown-menu">
        <li><a class="dropdown-item" href="{% url 'users:profile' %}"><i class="bi bi-person me-2"></i>个人信息</a></li>
        <li><a class="dropdown-item" href="{% url 'users:user_settings' %}"><i class="bi bi-gear me-2"></i>个人设置</a></li>
        <li><hr class="dropdown-divider"></li>
        <li><a class="dropdown-item" href="{% url 'users:logout' %}"><i class="bi bi-box-arrow-right me-2"></i>退出登录</a></li>
    </ul>
</li>
{% endblock %}

{% block content %}
<div class="profile-edit-container">
    <!-- 页面标题 -->
    <div class="page-header mb-4">
        <div class="row align-items-center">
            <div class="col">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'users:dashboard' %}">用户中心</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'users:profile' %}">个人信息</a></li>
                        <li class="breadcrumb-item active">编辑信息</li>
                    </ol>
                </nav>
                <h2 class="page-title">
                    <i class="bi bi-person-gear me-2"></i>
                    编辑个人信息
                </h2>
                <p class="page-subtitle text-muted">修改您的基本信息，部分敏感信息需要联系管理员修改</p>
            </div>
            <div class="col-auto">
                <a href="{% url 'users:profile' %}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left me-2"></i>
                    返回查看
                </a>
            </div>
        </div>
    </div>

    <!-- 编辑表单 -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-person-lines-fill me-2"></i>
                        个人信息编辑
                    </h6>
                </div>
                <div class="card-body">
                    <!-- 错误消息显示 -->
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {% if message.tags == 'error' %}
                                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                {% elif message.tags == 'success' %}
                                    <i class="bi bi-check-circle-fill me-2"></i>
                                {% elif message.tags == 'warning' %}
                                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                {% else %}
                                    <i class="bi bi-info-circle-fill me-2"></i>
                                {% endif %}
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <form method="post" class="needs-validation" novalidate id="profileEditForm">
                        {% csrf_token %}
                        
                        <!-- 不可编辑信息提示 -->
                        <div class="alert alert-info">
                            <h6><i class="bi bi-info-circle me-2"></i>编辑说明：</h6>
                            <ul class="mb-0">
                                <li>您只能修改姓名和邮箱信息</li>
                                <li>账号、角色、科室等信息需要联系管理员修改</li>
                                <li>修改后的信息将立即生效</li>
                            </ul>
                        </div>

                        <div class="row">
                            <!-- 不可编辑字段 -->
                            <div class="col-md-6">
                                <h6 class="form-section-title">账户信息（不可编辑）</h6>
                                
                                <!-- 账号 -->
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-hash me-1"></i>
                                        账号
                                    </label>
                                    <input type="text" class="form-control" value="{{ user_profile.account_number }}" readonly>
                                    <div class="form-text">
                                        <i class="bi bi-lock me-1"></i>
                                        账号信息不可修改
                                    </div>
                                </div>

                                <!-- 用户名 -->
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-person me-1"></i>
                                        用户名
                                    </label>
                                    <input type="text" class="form-control" value="{{ user.username }}" readonly>
                                    <div class="form-text">
                                        <i class="bi bi-lock me-1"></i>
                                        用户名不可修改
                                    </div>
                                </div>

                                <!-- 角色 -->
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-shield-check me-1"></i>
                                        角色
                                    </label>
                                    <input type="text" class="form-control" value="{{ user_profile.get_role_display }}" readonly>
                                    <div class="form-text">
                                        <i class="bi bi-lock me-1"></i>
                                        角色信息需要管理员修改
                                    </div>
                                </div>

                                <!-- 科室 -->
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-building me-1"></i>
                                        所属科室
                                    </label>
                                    <input type="text" class="form-control" 
                                           value="{% if user_profile.department %}{{ user_profile.department.name }}{% else %}未分配{% endif %}" readonly>
                                    <div class="form-text">
                                        <i class="bi bi-lock me-1"></i>
                                        科室信息需要管理员修改
                                    </div>
                                </div>
                            </div>

                            <!-- 可编辑字段 -->
                            <div class="col-md-6">
                                <h6 class="form-section-title">个人信息（可编辑）</h6>
                                
                                <!-- 姓 -->
                                <div class="mb-3">
                                    <label for="first_name" class="form-label">
                                        <i class="bi bi-person-badge me-1"></i>
                                        姓
                                    </label>
                                    <input 
                                        type="text" 
                                        class="form-control" 
                                        id="first_name" 
                                        name="first_name" 
                                        value="{{ user.first_name }}"
                                        maxlength="30"
                                        placeholder="请输入您的姓"
                                    >
                                    <div class="form-text">
                                        <i class="bi bi-pencil me-1"></i>
                                        可以修改
                                    </div>
                                </div>

                                <!-- 名 -->
                                <div class="mb-3">
                                    <label for="last_name" class="form-label">
                                        <i class="bi bi-person-badge me-1"></i>
                                        名
                                    </label>
                                    <input 
                                        type="text" 
                                        class="form-control" 
                                        id="last_name" 
                                        name="last_name" 
                                        value="{{ user.last_name }}"
                                        maxlength="30"
                                        placeholder="请输入您的名"
                                    >
                                    <div class="form-text">
                                        <i class="bi bi-pencil me-1"></i>
                                        可以修改
                                    </div>
                                </div>

                                <!-- 邮箱 -->
                                <div class="mb-3">
                                    <label for="email" class="form-label">
                                        <i class="bi bi-envelope me-1"></i>
                                        邮箱地址
                                    </label>
                                    <input 
                                        type="email" 
                                        class="form-control" 
                                        id="email" 
                                        name="email" 
                                        value="{{ user.email }}"
                                        placeholder="请输入邮箱地址"
                                    >
                                    <div class="invalid-feedback">
                                        请输入有效的邮箱地址
                                    </div>
                                    <div class="form-text">
                                        <i class="bi bi-pencil me-1"></i>
                                        可以修改，用于接收系统通知
                                    </div>
                                </div>

                                <!-- 状态显示 -->
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-toggle-on me-1"></i>
                                        账户状态
                                    </label>
                                    <div class="form-control-plaintext">
                                        {% if user_profile.is_active %}
                                            <span class="badge bg-success">
                                                <i class="bi bi-check-circle me-1"></i>正常
                                            </span>
                                        {% else %}
                                            <span class="badge bg-danger">
                                                <i class="bi bi-x-circle me-1"></i>禁用
                                            </span>
                                        {% endif %}
                                    </div>
                                    <div class="form-text">
                                        <i class="bi bi-lock me-1"></i>
                                        账户状态由管理员控制
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 表单按钮 -->
                        <div class="form-actions mt-4 pt-3 border-top">
                            <div class="row">
                                <div class="col">
                                    <a href="{% url 'users:profile' %}" class="btn btn-outline-secondary">
                                        <i class="bi bi-x-circle me-2"></i>
                                        取消
                                    </a>
                                </div>
                                <div class="col-auto">
                                    <button type="submit" class="btn btn-primary" id="submitBtn">
                                        <span class="btn-text">
                                            <i class="bi bi-check-circle me-2"></i>
                                            保存修改
                                        </span>
                                        <span class="btn-loading d-none">
                                            <span class="spinner-border spinner-border-sm me-2"></span>
                                            保存中...
                                        </span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 其他操作 -->
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-white border-bottom">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-gear me-2"></i>
                        其他操作
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="mb-2">修改密码</h6>
                            <p class="text-muted mb-3">定期修改密码可以提高账户安全性</p>
                            <button type="button" class="btn btn-outline-warning" data-bs-toggle="modal" data-bs-target="#changePasswordModal">
                                <i class="bi bi-key me-2"></i>
                                修改密码
                            </button>
                        </div>
                        <div class="col-md-6">
                            <h6 class="mb-2">个人设置</h6>
                            <p class="text-muted mb-3">配置个人偏好和系统设置</p>
                            <a href="{% url 'users:user_settings' %}" class="btn btn-outline-secondary">
                                <i class="bi bi-sliders me-2"></i>
                                个人设置
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 修改密码模态框 -->
<div class="modal fade" id="changePasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-key me-2"></i>
                    修改密码
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="changePasswordForm">
                <div class="modal-body">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="current_password" class="form-label">当前密码</label>
                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                        <div class="invalid-feedback">请输入当前密码</div>
                    </div>
                    <div class="mb-3">
                        <label for="new_password" class="form-label">新密码</label>
                        <input type="password" class="form-control" id="new_password" name="new_password" 
                               minlength="6" required>
                        <div class="form-text">密码长度至少6位</div>
                        <div class="invalid-feedback">请输入新密码（至少6位）</div>
                    </div>
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">确认新密码</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                               minlength="6" required>
                        <div class="invalid-feedback">请确认新密码</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-circle me-2"></i>
                        确认修改
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'users/js/profile_edit.js' %}"></script>
{% endblock %}
