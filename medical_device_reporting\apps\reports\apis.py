"""
不良事件上报管理API视图
Adverse Event Reports Management API Views for Medical Device Reporting Platform
"""

from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from django.shortcuts import get_object_or_404
from django.db.models import Q

from apps.users.permissions import IsDepartmentMemberOrAdmin, IsAdminUser
from .permissions import (
    IsReportOwnerOrAdmin,
    CanSubmitReport,
    CanReviewReport,
    CanViewAllReports,
    CanExportReports
)
from apps.common.exceptions import (
    BusinessLogicError,
    DataValidationError,
    ResourceNotFoundError,
    ReportSubmissionError
)

from .models import AdverseEventReport
from .serializers import (
    AdverseEventReportListSerializer,
    AdverseEventReportDetailSerializer,
    AdverseEventReportCreateSerializer,
    AdverseEventReportUpdateSerializer,
    AdverseEventReportReviewSerializer,
    ReportStatisticsSerializer,
    ReportSearchSerializer,
    ReportSummarySerializer,
    TimeSeriesDataSerializer,
    CrossDimensionDataSerializer,
    DeviceStatisticsSerializer,
    DepartmentStatisticsSerializer,
    TrendAnalysisSerializer
)
from .services import (
    report_create,
    report_update,
    report_submit,
    report_review
)
from .selectors import (
    report_list,
    report_detail,
    report_statistics,
    report_list_paginated
)

import logging

logger = logging.getLogger('apps.reports')


class ReportListAPIView(generics.ListAPIView):
    """
    报告列表API视图
    """
    serializer_class = AdverseEventReportListSerializer
    permission_classes = [CanViewAllReports]

    def get_queryset(self):
        """获取查询集"""
        return report_list(user_profile=self.request.user.profile)

    def list(self, request, *args, **kwargs):
        """列表视图"""
        try:
            # 获取搜索参数
            search_serializer = ReportSearchSerializer(data=request.query_params)
            if not search_serializer.is_valid():
                return Response(
                    {'error': '搜索参数无效', 'details': search_serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST
                )

            search_params = search_serializer.validated_data

            # 获取分页数据
            paginated_data = report_list_paginated(
                user_profile=request.user.profile,
                **search_params
            )

            # 序列化数据
            serializer = self.get_serializer(paginated_data['reports'], many=True)

            return Response({
                'results': serializer.data,
                'count': paginated_data['total_count'],
                'page': paginated_data['page'],
                'total_pages': paginated_data['total_pages'],
                'has_next': paginated_data['has_next'],
                'has_previous': paginated_data['has_previous'],
            })

        except Exception as e:
            logger.error(f'获取报告列表失败: {str(e)}')
            return Response(
                {'error': '获取报告列表失败'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ReportDetailAPIView(generics.RetrieveAPIView):
    """
    报告详情API视图
    """
    serializer_class = AdverseEventReportDetailSerializer
    permission_classes = [CanViewAllReports]

    def get_object(self):
        """获取对象"""
        report_id = self.kwargs.get('pk')
        report = report_detail(report_id, user_profile=self.request.user.profile)
        if not report:
            raise ResourceNotFoundError('报告不存在或您没有权限查看')
        return report


class ReportCreateAPIView(generics.CreateAPIView):
    """
    报告创建API视图
    """
    serializer_class = AdverseEventReportCreateSerializer
    permission_classes = [CanSubmitReport]

    def create(self, request, *args, **kwargs):
        """创建报告"""
        serializer = self.get_serializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        try:
            # 使用服务层创建报告
            report = report_create(
                **serializer.validated_data,
                created_by=request.user
            )

            # 返回创建的报告
            response_serializer = AdverseEventReportDetailSerializer(report)
            return Response(
                response_serializer.data,
                status=status.HTTP_201_CREATED
            )

        except (DataValidationError, BusinessLogicError) as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f'创建报告失败: {str(e)}')
            return Response(
                {'error': '创建报告失败'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ReportUpdateAPIView(generics.UpdateAPIView):
    """
    报告更新API视图
    """
    serializer_class = AdverseEventReportUpdateSerializer
    permission_classes = [IsReportOwnerOrAdmin]

    def get_object(self):
        """获取对象"""
        report_id = self.kwargs.get('pk')
        report = report_detail(report_id, user_profile=self.request.user.profile)
        if not report:
            raise ResourceNotFoundError('报告不存在或您没有权限编辑')

        # 检查编辑权限
        if not report.can_edit:
            raise BusinessLogicError('报告当前状态不允许编辑')

        # 检查用户权限
        if not (self.request.user.profile.is_admin or report.reporter == self.request.user.profile):
            raise BusinessLogicError('您没有权限编辑此报告')

        return report

    def update(self, request, *args, **kwargs):
        """更新报告"""
        report = self.get_object()
        serializer = self.get_serializer(data=request.data, partial=True)

        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        try:
            # 使用服务层更新报告
            updated_report = report_update(
                report_id=report.id,
                **serializer.validated_data,
                updated_by=request.user
            )

            # 返回更新后的报告
            response_serializer = AdverseEventReportDetailSerializer(updated_report)
            return Response(response_serializer.data)

        except (DataValidationError, BusinessLogicError) as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f'更新报告失败: {str(e)}')
            return Response(
                {'error': '更新报告失败'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ReportSubmitAPIView(APIView):
    """
    报告提交API视图
    """
    permission_classes = [CanSubmitReport]

    def post(self, request, pk):
        """提交报告"""
        try:
            report = report_detail(pk, user_profile=request.user.profile)
            if not report:
                return Response(
                    {'error': '报告不存在或您没有权限操作'},
                    status=status.HTTP_404_NOT_FOUND
                )

            # 检查提交权限
            if not report.can_submit:
                return Response(
                    {'error': '报告当前状态不允许提交'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # 检查用户权限
            if not (request.user.profile.is_admin or report.reporter == request.user.profile):
                return Response(
                    {'error': '您没有权限提交此报告'},
                    status=status.HTTP_403_FORBIDDEN
                )

            # 提交报告
            submitted_report = report_submit(
                report_id=report.id,
                submitted_by=request.user
            )

            # 返回提交后的报告
            serializer = AdverseEventReportDetailSerializer(submitted_report)
            return Response({
                'message': f'报告 {submitted_report.report_number} 提交成功',
                'report': serializer.data
            })

        except ReportSubmissionError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f'提交报告失败: {str(e)}')
            return Response(
                {'error': '提交报告失败'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ReportReviewAPIView(APIView):
    """
    报告审核API视图
    """
    permission_classes = [CanReviewReport]

    def post(self, request, pk):
        """审核报告"""
        serializer = AdverseEventReportReviewSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        try:
            # 使用服务层审核报告
            reviewed_report = report_review(
                report_id=pk,
                **serializer.validated_data,
                reviewed_by=request.user
            )

            action_text = {
                'approve': '批准',
                'reject': '拒绝'
            }.get(serializer.validated_data['action'], '审核')

            # 返回审核后的报告
            response_serializer = AdverseEventReportDetailSerializer(reviewed_report)
            return Response({
                'message': f'报告 {reviewed_report.report_number} {action_text}成功',
                'report': response_serializer.data
            })

        except (DataValidationError, BusinessLogicError) as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f'审核报告失败: {str(e)}')
            return Response(
                {'error': '审核报告失败'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ReportStatisticsAPIView(APIView):
    """
    报告统计API视图（扩展版）
    """
    permission_classes = [CanViewAllReports]

    def get(self, request):
        """获取统计信息"""
        try:
            # 获取图表类型参数
            chart_type = request.query_params.get('chart_type', 'summary')

            # 根据图表类型调用不同的方法
            if chart_type == 'time_series':
                return self.get_time_series_data(request)
            elif chart_type == 'cross_dimension':
                return self.get_cross_dimension_data(request)
            elif chart_type == 'device_stats':
                return self.get_device_statistics_data(request)
            elif chart_type == 'department_stats':
                return self.get_department_statistics_data(request)
            elif chart_type == 'trend_analysis':
                return self.get_trend_analysis_data(request)
            else:
                return self.get_summary_statistics(request)

        except Exception as e:
            logger.error(f'获取统计信息失败: {str(e)}')
            return Response(
                {'error': '获取统计信息失败'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def get_summary_statistics(self, request):
        """获取基础统计信息（原有功能）"""
        # 获取筛选参数
        department_id = request.query_params.get('department_id')
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')

        # 获取统计数据
        stats = report_statistics(
            user_profile=request.user.profile,
            department_id=int(department_id) if department_id else None,
            start_date=start_date,
            end_date=end_date
        )

        serializer = ReportStatisticsSerializer(stats)
        return Response(serializer.data)

    def get_time_series_data(self, request):
        """获取时间序列统计数据"""
        from .selectors import get_time_series_statistics
        from .serializers import TimeSeriesDataSerializer

        # 获取参数
        granularity = request.query_params.get('granularity', 'month')
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')
        date_field = request.query_params.get('date_field', 'created_at')

        # 日期解析
        parsed_start_date = None
        parsed_end_date = None
        if start_date:
            try:
                from django.utils.dateparse import parse_datetime
                parsed_start_date = parse_datetime(start_date)
            except:
                pass
        if end_date:
            try:
                from django.utils.dateparse import parse_datetime
                parsed_end_date = parse_datetime(end_date)
            except:
                pass

        # 获取时间序列数据
        time_series_data = get_time_series_statistics(
            user_profile=request.user.profile,
            start_date=parsed_start_date,
            end_date=parsed_end_date,
            granularity=granularity,
            date_field=date_field
        )

        # 序列化数据
        serializer = TimeSeriesDataSerializer(time_series_data, many=True)
        return Response({
            'chart_type': 'time_series',
            'granularity': granularity,
            'date_field': date_field,
            'data': serializer.data
        })

    def get_cross_dimension_data(self, request):
        """获取交叉维度分析数据"""
        from .selectors import get_cross_dimension_statistics
        from .serializers import CrossDimensionDataSerializer

        # 获取参数
        dimension1 = request.query_params.get('dimension1', 'department')
        dimension2 = request.query_params.get('dimension2', 'injury_level')
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')
        limit = int(request.query_params.get('limit', 50))

        # 日期解析
        parsed_start_date = None
        parsed_end_date = None
        if start_date:
            try:
                from django.utils.dateparse import parse_datetime
                parsed_start_date = parse_datetime(start_date)
            except:
                pass
        if end_date:
            try:
                from django.utils.dateparse import parse_datetime
                parsed_end_date = parse_datetime(end_date)
            except:
                pass

        # 获取交叉维度数据
        cross_data = get_cross_dimension_statistics(
            user_profile=request.user.profile,
            dimension1=dimension1,
            dimension2=dimension2,
            start_date=parsed_start_date,
            end_date=parsed_end_date,
            limit=limit
        )

        # 序列化数据
        serializer = CrossDimensionDataSerializer(cross_data)
        return Response({
            'chart_type': 'cross_dimension',
            'dimension1': dimension1,
            'dimension2': dimension2,
            'data': serializer.data
        })

    def get_device_statistics_data(self, request):
        """获取器械统计数据"""
        from .selectors import get_device_statistics
        from .serializers import DeviceStatisticsSerializer

        # 获取参数
        limit = int(request.query_params.get('limit', 20))
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')
        include_manufacturer = request.query_params.get('include_manufacturer', 'false').lower() == 'true'

        # 日期解析
        parsed_start_date = None
        parsed_end_date = None
        if start_date:
            try:
                from django.utils.dateparse import parse_datetime
                parsed_start_date = parse_datetime(start_date)
            except:
                pass
        if end_date:
            try:
                from django.utils.dateparse import parse_datetime
                parsed_end_date = parse_datetime(end_date)
            except:
                pass

        # 获取器械统计数据
        device_data = get_device_statistics(
            user_profile=request.user.profile,
            limit=limit,
            start_date=parsed_start_date,
            end_date=parsed_end_date,
            include_manufacturer=include_manufacturer
        )

        # 处理数据格式
        if isinstance(device_data, dict) and 'device_stats' in device_data:
            # 包含制造商统计的情况
            device_serializer = DeviceStatisticsSerializer(device_data['device_stats'], many=True)
            manufacturer_serializer = DeviceStatisticsSerializer(device_data['manufacturer_stats'], many=True)
            return Response({
                'chart_type': 'device_stats',
                'include_manufacturer': include_manufacturer,
                'device_stats': device_serializer.data,
                'manufacturer_stats': manufacturer_serializer.data
            })
        else:
            # 只有器械统计的情况
            serializer = DeviceStatisticsSerializer(device_data, many=True)
            return Response({
                'chart_type': 'device_stats',
                'include_manufacturer': include_manufacturer,
                'data': serializer.data
            })

    def get_department_statistics_data(self, request):
        """获取科室统计数据"""
        from .selectors import get_department_statistics
        from .serializers import DepartmentStatisticsSerializer

        # 获取参数
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')
        include_efficiency = request.query_params.get('include_efficiency', 'true').lower() == 'true'

        # 日期解析
        parsed_start_date = None
        parsed_end_date = None
        if start_date:
            try:
                from django.utils.dateparse import parse_datetime
                parsed_start_date = parse_datetime(start_date)
            except:
                pass
        if end_date:
            try:
                from django.utils.dateparse import parse_datetime
                parsed_end_date = parse_datetime(end_date)
            except:
                pass

        # 获取科室统计数据
        department_data = get_department_statistics(
            user_profile=request.user.profile,
            start_date=parsed_start_date,
            end_date=parsed_end_date,
            include_efficiency=include_efficiency
        )

        # 序列化数据
        serializer = DepartmentStatisticsSerializer(department_data, many=True)
        return Response({
            'chart_type': 'department_stats',
            'include_efficiency': include_efficiency,
            'data': serializer.data
        })

    def get_trend_analysis_data(self, request):
        """获取趋势分析数据"""
        from .selectors import get_trend_analysis
        from .serializers import TrendAnalysisSerializer

        # 获取参数
        metric = request.query_params.get('metric', 'total_count')
        granularity = request.query_params.get('granularity', 'month')
        periods = int(request.query_params.get('periods', 12))

        # 获取趋势分析数据
        trend_data = get_trend_analysis(
            user_profile=request.user.profile,
            metric=metric,
            granularity=granularity,
            periods=periods
        )

        # 序列化数据
        serializer = TrendAnalysisSerializer(trend_data)
        return Response({
            'chart_type': 'trend_analysis',
            'metric': metric,
            'granularity': granularity,
            'periods': periods,
            'data': serializer.data
        })


@api_view(['GET'])
@permission_classes([CanViewAllReports])
def report_search_suggestions(request):
    """
    报告搜索建议API
    """
    try:
        query = request.query_params.get('q', '')
        limit = int(request.query_params.get('limit', 10))

        if not query or len(query) < 2:
            return Response({'suggestions': []})

        from .selectors import report_search_suggestions as get_suggestions
        suggestions = get_suggestions(
            query=query,
            user_profile=request.user.profile,
            limit=limit
        )

        return Response({'suggestions': suggestions})

    except Exception as e:
        logger.error(f'获取搜索建议失败: {str(e)}')
        return Response(
            {'error': '获取搜索建议失败'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([CanViewAllReports])
def report_dashboard_data(request):
    """
    仪表板数据API（扩展版）
    """
    try:
        # 获取是否包含图表数据的参数
        include_charts = request.query_params.get('include_charts', 'false').lower() == 'true'

        # 获取统计信息
        stats = report_statistics(user_profile=request.user.profile)

        # 获取最近的报告
        from .selectors import user_reports, report_list_pending_review, report_list_serious_events
        recent_reports = user_reports(
            user_profile=request.user.profile,
            ordering='-created_at'
        )[:10]

        # 获取待审核报告（仅管理员）
        pending_reports = []
        if request.user.profile.is_admin:
            pending_reports = report_list_pending_review(
                user_profile=request.user.profile
            )[:5]

        # 获取严重事件（仅管理员）
        serious_events = []
        if request.user.profile.is_admin:
            serious_events = report_list_serious_events(
                user_profile=request.user.profile,
                days=30
            )[:5]

        # 序列化数据
        recent_reports_data = ReportSummarySerializer(recent_reports, many=True).data
        pending_reports_data = ReportSummarySerializer(pending_reports, many=True).data
        serious_events_data = ReportSummarySerializer(serious_events, many=True).data

        response_data = {
            'stats': stats,
            'recent_reports': recent_reports_data,
            'pending_reports': pending_reports_data,
            'serious_events': serious_events_data,
            'is_admin': request.user.profile.is_admin,
        }

        # 如果需要图表数据，添加图表数据
        if include_charts:
            chart_data = get_dashboard_chart_data(request.user.profile)
            response_data['chart_data'] = chart_data

        return Response(response_data)

    except Exception as e:
        logger.error(f'获取仪表板数据失败: {str(e)}')
        return Response(
            {'error': '获取仪表板数据失败'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


def get_dashboard_chart_data(user_profile):
    """
    获取仪表板图表数据
    """
    from .selectors import (
        get_time_series_statistics,
        get_device_statistics,
        get_department_statistics
    )
    from .serializers import (
        TimeSeriesDataSerializer,
        DeviceStatisticsSerializer,
        DepartmentStatisticsSerializer
    )

    try:
        # 获取最近6个月的时间序列数据
        time_series_data = get_time_series_statistics(
            user_profile=user_profile,
            granularity='month'
        )

        # 获取前10个器械统计
        device_data = get_device_statistics(
            user_profile=user_profile,
            limit=10
        )

        # 获取科室统计（如果是管理员）
        department_data = []
        if user_profile.is_admin:
            department_data = get_department_statistics(
                user_profile=user_profile,
                include_efficiency=False
            )[:10]  # 只取前10个科室

        # 序列化数据
        time_series_serializer = TimeSeriesDataSerializer(time_series_data, many=True)
        device_serializer = DeviceStatisticsSerializer(device_data, many=True)
        department_serializer = DepartmentStatisticsSerializer(department_data, many=True)

        return {
            'time_series': time_series_serializer.data,
            'top_devices': device_serializer.data,
            'department_stats': department_serializer.data if user_profile.is_admin else []
        }

    except Exception as e:
        logger.error(f'获取仪表板图表数据失败: {str(e)}')
        return {
            'time_series': [],
            'top_devices': [],
            'department_stats': []
        }


class ReportUpdateAPIView(generics.UpdateAPIView):
    """
    报告更新API视图
    """
    serializer_class = AdverseEventReportUpdateSerializer
    permission_classes = [IsDepartmentMemberOrAdmin]

    def get_object(self):
        """获取对象"""
        report_id = self.kwargs.get('pk')
        report = report_detail(report_id, user_profile=self.request.user.profile)
        if not report:
            raise ResourceNotFoundError('报告不存在或您没有权限编辑')

        # 检查编辑权限
        if not report.can_edit:
            raise BusinessLogicError('报告当前状态不允许编辑')

        # 检查用户权限
        if not (self.request.user.profile.is_admin or report.reporter == self.request.user.profile):
            raise BusinessLogicError('您没有权限编辑此报告')

        return report

    def update(self, request, *args, **kwargs):
        """更新报告"""
        report = self.get_object()
        serializer = self.get_serializer(data=request.data, partial=True)

        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        try:
            # 使用服务层更新报告
            updated_report = report_update(
                report_id=report.id,
                **serializer.validated_data,
                updated_by=request.user
            )

            # 返回更新后的报告
            response_serializer = AdverseEventReportDetailSerializer(updated_report)
            return Response(response_serializer.data)

        except (DataValidationError, BusinessLogicError) as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f'更新报告失败: {str(e)}')
            return Response(
                {'error': '更新报告失败'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ReportSubmitAPIView(APIView):
    """
    报告提交API视图
    """
    permission_classes = [IsDepartmentMemberOrAdmin]

    def post(self, request, pk):
        """提交报告"""
        try:
            report = report_detail(pk, user_profile=request.user.profile)
            if not report:
                return Response(
                    {'error': '报告不存在或您没有权限操作'},
                    status=status.HTTP_404_NOT_FOUND
                )

            # 检查提交权限
            if not report.can_submit:
                return Response(
                    {'error': '报告当前状态不允许提交'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # 检查用户权限
            if not (request.user.profile.is_admin or report.reporter == request.user.profile):
                return Response(
                    {'error': '您没有权限提交此报告'},
                    status=status.HTTP_403_FORBIDDEN
                )

            # 提交报告
            submitted_report = report_submit(
                report_id=report.id,
                submitted_by=request.user
            )

            # 返回提交后的报告
            serializer = AdverseEventReportDetailSerializer(submitted_report)
            return Response({
                'message': f'报告 {submitted_report.report_number} 提交成功',
                'report': serializer.data
            })

        except ReportSubmissionError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f'提交报告失败: {str(e)}')
            return Response(
                {'error': '提交报告失败'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ReportReviewAPIView(APIView):
    """
    报告审核API视图
    """
    permission_classes = [IsAdminUser]

    def post(self, request, pk):
        """审核报告"""
        serializer = AdverseEventReportReviewSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        try:
            # 使用服务层审核报告
            reviewed_report = report_review(
                report_id=pk,
                **serializer.validated_data,
                reviewed_by=request.user
            )

            action_text = {
                'approve': '批准',
                'reject': '拒绝'
            }.get(serializer.validated_data['action'], '审核')

            # 返回审核后的报告
            response_serializer = AdverseEventReportDetailSerializer(reviewed_report)
            return Response({
                'message': f'报告 {reviewed_report.report_number} {action_text}成功',
                'report': response_serializer.data
            })

        except (DataValidationError, BusinessLogicError) as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f'审核报告失败: {str(e)}')
            return Response(
                {'error': '审核报告失败'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
