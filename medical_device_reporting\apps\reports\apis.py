"""
不良事件上报管理API视图
Adverse Event Reports Management API Views for Medical Device Reporting Platform
"""

from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from django.shortcuts import get_object_or_404
from django.db.models import Q

from apps.users.permissions import IsDepartmentMemberOrAdmin, IsAdminUser
from .permissions import (
    IsReportOwnerOrAdmin,
    CanSubmitReport,
    CanReviewReport,
    CanViewAllReports,
    CanExportReports
)
from apps.common.exceptions import (
    BusinessLogicError,
    DataValidationError,
    ResourceNotFoundError,
    ReportSubmissionError
)

from .models import AdverseEventReport
from .serializers import (
    AdverseEventReportListSerializer,
    AdverseEventReportDetailSerializer,
    AdverseEventReportCreateSerializer,
    AdverseEventReportUpdateSerializer,
    AdverseEventReportReviewSerializer,
    ReportStatisticsSerializer,
    ReportSearchSerializer,
    ReportSummarySerializer
)
from .services import (
    report_create,
    report_update,
    report_submit,
    report_review
)
from .selectors import (
    report_list,
    report_detail,
    report_statistics,
    report_list_paginated
)

import logging

logger = logging.getLogger('apps.reports')


class ReportListAPIView(generics.ListAPIView):
    """
    报告列表API视图
    """
    serializer_class = AdverseEventReportListSerializer
    permission_classes = [CanViewAllReports]

    def get_queryset(self):
        """获取查询集"""
        return report_list(user_profile=self.request.user.profile)

    def list(self, request, *args, **kwargs):
        """列表视图"""
        try:
            # 获取搜索参数
            search_serializer = ReportSearchSerializer(data=request.query_params)
            if not search_serializer.is_valid():
                return Response(
                    {'error': '搜索参数无效', 'details': search_serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST
                )

            search_params = search_serializer.validated_data

            # 获取分页数据
            paginated_data = report_list_paginated(
                user_profile=request.user.profile,
                **search_params
            )

            # 序列化数据
            serializer = self.get_serializer(paginated_data['reports'], many=True)

            return Response({
                'results': serializer.data,
                'count': paginated_data['total_count'],
                'page': paginated_data['page'],
                'total_pages': paginated_data['total_pages'],
                'has_next': paginated_data['has_next'],
                'has_previous': paginated_data['has_previous'],
            })

        except Exception as e:
            logger.error(f'获取报告列表失败: {str(e)}')
            return Response(
                {'error': '获取报告列表失败'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ReportDetailAPIView(generics.RetrieveAPIView):
    """
    报告详情API视图
    """
    serializer_class = AdverseEventReportDetailSerializer
    permission_classes = [CanViewAllReports]

    def get_object(self):
        """获取对象"""
        report_id = self.kwargs.get('pk')
        report = report_detail(report_id, user_profile=self.request.user.profile)
        if not report:
            raise ResourceNotFoundError('报告不存在或您没有权限查看')
        return report


class ReportCreateAPIView(generics.CreateAPIView):
    """
    报告创建API视图
    """
    serializer_class = AdverseEventReportCreateSerializer
    permission_classes = [CanSubmitReport]

    def create(self, request, *args, **kwargs):
        """创建报告"""
        serializer = self.get_serializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        try:
            # 使用服务层创建报告
            report = report_create(
                **serializer.validated_data,
                created_by=request.user
            )

            # 返回创建的报告
            response_serializer = AdverseEventReportDetailSerializer(report)
            return Response(
                response_serializer.data,
                status=status.HTTP_201_CREATED
            )

        except (DataValidationError, BusinessLogicError) as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f'创建报告失败: {str(e)}')
            return Response(
                {'error': '创建报告失败'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ReportUpdateAPIView(generics.UpdateAPIView):
    """
    报告更新API视图
    """
    serializer_class = AdverseEventReportUpdateSerializer
    permission_classes = [IsReportOwnerOrAdmin]

    def get_object(self):
        """获取对象"""
        report_id = self.kwargs.get('pk')
        report = report_detail(report_id, user_profile=self.request.user.profile)
        if not report:
            raise ResourceNotFoundError('报告不存在或您没有权限编辑')

        # 检查编辑权限
        if not report.can_edit:
            raise BusinessLogicError('报告当前状态不允许编辑')

        # 检查用户权限
        if not (self.request.user.profile.is_admin or report.reporter == self.request.user.profile):
            raise BusinessLogicError('您没有权限编辑此报告')

        return report

    def update(self, request, *args, **kwargs):
        """更新报告"""
        report = self.get_object()
        serializer = self.get_serializer(data=request.data, partial=True)

        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        try:
            # 使用服务层更新报告
            updated_report = report_update(
                report_id=report.id,
                **serializer.validated_data,
                updated_by=request.user
            )

            # 返回更新后的报告
            response_serializer = AdverseEventReportDetailSerializer(updated_report)
            return Response(response_serializer.data)

        except (DataValidationError, BusinessLogicError) as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f'更新报告失败: {str(e)}')
            return Response(
                {'error': '更新报告失败'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ReportSubmitAPIView(APIView):
    """
    报告提交API视图
    """
    permission_classes = [CanSubmitReport]

    def post(self, request, pk):
        """提交报告"""
        try:
            report = report_detail(pk, user_profile=request.user.profile)
            if not report:
                return Response(
                    {'error': '报告不存在或您没有权限操作'},
                    status=status.HTTP_404_NOT_FOUND
                )

            # 检查提交权限
            if not report.can_submit:
                return Response(
                    {'error': '报告当前状态不允许提交'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # 检查用户权限
            if not (request.user.profile.is_admin or report.reporter == request.user.profile):
                return Response(
                    {'error': '您没有权限提交此报告'},
                    status=status.HTTP_403_FORBIDDEN
                )

            # 提交报告
            submitted_report = report_submit(
                report_id=report.id,
                submitted_by=request.user
            )

            # 返回提交后的报告
            serializer = AdverseEventReportDetailSerializer(submitted_report)
            return Response({
                'message': f'报告 {submitted_report.report_number} 提交成功',
                'report': serializer.data
            })

        except ReportSubmissionError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f'提交报告失败: {str(e)}')
            return Response(
                {'error': '提交报告失败'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ReportReviewAPIView(APIView):
    """
    报告审核API视图
    """
    permission_classes = [CanReviewReport]

    def post(self, request, pk):
        """审核报告"""
        serializer = AdverseEventReportReviewSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        try:
            # 使用服务层审核报告
            reviewed_report = report_review(
                report_id=pk,
                **serializer.validated_data,
                reviewed_by=request.user
            )

            action_text = {
                'start_review': '开始审核',
                'approve': '批准',
                'reject': '拒绝'
            }.get(serializer.validated_data['action'], '审核')

            # 返回审核后的报告
            response_serializer = AdverseEventReportDetailSerializer(reviewed_report)
            return Response({
                'message': f'报告 {reviewed_report.report_number} {action_text}成功',
                'report': response_serializer.data
            })

        except (DataValidationError, BusinessLogicError) as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f'审核报告失败: {str(e)}')
            return Response(
                {'error': '审核报告失败'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ReportStatisticsAPIView(APIView):
    """
    报告统计API视图
    """
    permission_classes = [CanViewAllReports]

    def get(self, request):
        """获取统计信息"""
        try:
            # 获取筛选参数
            department_id = request.query_params.get('department_id')
            start_date = request.query_params.get('start_date')
            end_date = request.query_params.get('end_date')

            # 获取统计数据
            stats = report_statistics(
                user_profile=request.user.profile,
                department_id=int(department_id) if department_id else None,
                start_date=start_date,
                end_date=end_date
            )

            serializer = ReportStatisticsSerializer(stats)
            return Response(serializer.data)

        except Exception as e:
            logger.error(f'获取统计信息失败: {str(e)}')
            return Response(
                {'error': '获取统计信息失败'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


@api_view(['GET'])
@permission_classes([CanViewAllReports])
def report_search_suggestions(request):
    """
    报告搜索建议API
    """
    try:
        query = request.query_params.get('q', '')
        limit = int(request.query_params.get('limit', 10))

        if not query or len(query) < 2:
            return Response({'suggestions': []})

        from .selectors import report_search_suggestions as get_suggestions
        suggestions = get_suggestions(
            query=query,
            user_profile=request.user.profile,
            limit=limit
        )

        return Response({'suggestions': suggestions})

    except Exception as e:
        logger.error(f'获取搜索建议失败: {str(e)}')
        return Response(
            {'error': '获取搜索建议失败'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([CanViewAllReports])
def report_dashboard_data(request):
    """
    仪表板数据API
    """
    try:
        # 获取统计信息
        stats = report_statistics(user_profile=request.user.profile)

        # 获取最近的报告
        from .selectors import user_reports, report_list_pending_review, report_list_serious_events
        recent_reports = user_reports(
            user_profile=request.user.profile,
            ordering='-created_at'
        )[:10]

        # 获取待审核报告（仅管理员）
        pending_reports = []
        if request.user.profile.is_admin:
            pending_reports = report_list_pending_review(
                user_profile=request.user.profile
            )[:5]

        # 获取严重事件（仅管理员）
        serious_events = []
        if request.user.profile.is_admin:
            serious_events = report_list_serious_events(
                user_profile=request.user.profile,
                days=30
            )[:5]

        # 序列化数据
        recent_reports_data = ReportSummarySerializer(recent_reports, many=True).data
        pending_reports_data = ReportSummarySerializer(pending_reports, many=True).data
        serious_events_data = ReportSummarySerializer(serious_events, many=True).data

        return Response({
            'stats': stats,
            'recent_reports': recent_reports_data,
            'pending_reports': pending_reports_data,
            'serious_events': serious_events_data,
            'is_admin': request.user.profile.is_admin,
        })

    except Exception as e:
        logger.error(f'获取仪表板数据失败: {str(e)}')
        return Response(
            {'error': '获取仪表板数据失败'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


class ReportUpdateAPIView(generics.UpdateAPIView):
    """
    报告更新API视图
    """
    serializer_class = AdverseEventReportUpdateSerializer
    permission_classes = [IsDepartmentMemberOrAdmin]

    def get_object(self):
        """获取对象"""
        report_id = self.kwargs.get('pk')
        report = report_detail(report_id, user_profile=self.request.user.profile)
        if not report:
            raise ResourceNotFoundError('报告不存在或您没有权限编辑')

        # 检查编辑权限
        if not report.can_edit:
            raise BusinessLogicError('报告当前状态不允许编辑')

        # 检查用户权限
        if not (self.request.user.profile.is_admin or report.reporter == self.request.user.profile):
            raise BusinessLogicError('您没有权限编辑此报告')

        return report

    def update(self, request, *args, **kwargs):
        """更新报告"""
        report = self.get_object()
        serializer = self.get_serializer(data=request.data, partial=True)

        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        try:
            # 使用服务层更新报告
            updated_report = report_update(
                report_id=report.id,
                **serializer.validated_data,
                updated_by=request.user
            )

            # 返回更新后的报告
            response_serializer = AdverseEventReportDetailSerializer(updated_report)
            return Response(response_serializer.data)

        except (DataValidationError, BusinessLogicError) as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f'更新报告失败: {str(e)}')
            return Response(
                {'error': '更新报告失败'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ReportSubmitAPIView(APIView):
    """
    报告提交API视图
    """
    permission_classes = [IsDepartmentMemberOrAdmin]

    def post(self, request, pk):
        """提交报告"""
        try:
            report = report_detail(pk, user_profile=request.user.profile)
            if not report:
                return Response(
                    {'error': '报告不存在或您没有权限操作'},
                    status=status.HTTP_404_NOT_FOUND
                )

            # 检查提交权限
            if not report.can_submit:
                return Response(
                    {'error': '报告当前状态不允许提交'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # 检查用户权限
            if not (request.user.profile.is_admin or report.reporter == request.user.profile):
                return Response(
                    {'error': '您没有权限提交此报告'},
                    status=status.HTTP_403_FORBIDDEN
                )

            # 提交报告
            submitted_report = report_submit(
                report_id=report.id,
                submitted_by=request.user
            )

            # 返回提交后的报告
            serializer = AdverseEventReportDetailSerializer(submitted_report)
            return Response({
                'message': f'报告 {submitted_report.report_number} 提交成功',
                'report': serializer.data
            })

        except ReportSubmissionError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f'提交报告失败: {str(e)}')
            return Response(
                {'error': '提交报告失败'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ReportReviewAPIView(APIView):
    """
    报告审核API视图
    """
    permission_classes = [IsAdminUser]

    def post(self, request, pk):
        """审核报告"""
        serializer = AdverseEventReportReviewSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        try:
            # 使用服务层审核报告
            reviewed_report = report_review(
                report_id=pk,
                **serializer.validated_data,
                reviewed_by=request.user
            )

            action_text = {
                'start_review': '开始审核',
                'approve': '批准',
                'reject': '拒绝'
            }.get(serializer.validated_data['action'], '审核')

            # 返回审核后的报告
            response_serializer = AdverseEventReportDetailSerializer(reviewed_report)
            return Response({
                'message': f'报告 {reviewed_report.report_number} {action_text}成功',
                'report': response_serializer.data
            })

        except (DataValidationError, BusinessLogicError) as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f'审核报告失败: {str(e)}')
            return Response(
                {'error': '审核报告失败'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
