{% extends 'reports/base.html' %}
{% load static %}

{% block page_title %}审核报告{% endblock %}
{% block page_heading %}审核报告{% endblock %}
{% block page_description %}{{ report.report_number }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'reports:report_list' %}">报告列表</a></li>
<li class="breadcrumb-item"><a href="{% url 'reports:report_detail' report_id=report.id %}">{{ report.report_number }}</a></li>
<li class="breadcrumb-item active">审核</li>
{% endblock %}

{% block page_actions %}
<div class="btn-group" role="group">
    <a href="{% url 'reports:report_detail' report_id=report.id %}" class="btn btn-outline-primary">
        <i class="bi bi-eye me-2"></i>
        查看详情
    </a>
    <a href="{% url 'reports:pending_review' %}" class="btn btn-outline-secondary">
        <i class="bi bi-list me-2"></i>
        待审核列表
    </a>
</div>
{% endblock %}

{% block reports_content %}
<div class="row">
    <!-- 左侧：报告信息 -->
    <div class="col-lg-8">
        <!-- 报告基本信息 -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="bi bi-file-text me-2"></i>
                    报告信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="info-item">
                            <label>报告编号：</label>
                            <span class="fw-bold">{{ report.report_number }}</span>
                        </div>
                        <div class="info-item">
                            <label>上报人：</label>
                            <span>{{ report.reporter.username }}</span>
                        </div>
                        <div class="info-item">
                            <label>科室：</label>
                            <span>{{ report.department.name|default:"未指定" }}</span>
                        </div>
                        <div class="info-item">
                            <label>器械名称：</label>
                            <span class="fw-bold">{{ report.device_name }}</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-item">
                            <label>事件日期：</label>
                            <span>{{ report.event_date|date:"Y-m-d" }}</span>
                        </div>
                        <div class="info-item">
                            <label>伤害程度：</label>
                            <span class="badge bg-{% if report.injury_level == 'death' %}danger{% elif report.injury_level == 'severe' %}warning{% elif report.injury_level == 'moderate' %}info{% else %}secondary{% endif %} fs-6">
                                {{ report.get_injury_level_display }}
                            </span>
                        </div>
                        <div class="info-item">
                            <label>提交时间：</label>
                            <span>{{ report.submitted_at|date:"Y-m-d H:i:s" }}</span>
                        </div>
                        <div class="info-item">
                            <label>当前状态：</label>
                            <span class="badge bg-{% if report.status == 'submitted' %}info{% elif report.status == 'under_review' %}warning{% else %}secondary{% endif %} fs-6">
                                {{ report.get_status_display }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 事件描述 -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-warning text-dark">
                <h5 class="card-title mb-0">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    事件描述
                </h5>
            </div>
            <div class="card-body">
                <div class="event-description">
                    {{ report.event_description|linebreaks }}
                </div>
                
                {% if report.injury_description %}
                <div class="mt-3">
                    <h6 class="text-muted">伤害描述：</h6>
                    <div class="injury-description">
                        {{ report.injury_description|linebreaks }}
                    </div>
                </div>
                {% endif %}

                {% if report.initial_cause_analysis %}
                <div class="mt-3">
                    <h6 class="text-muted">初步原因分析：</h6>
                    <div class="cause-analysis">
                        {{ report.initial_cause_analysis|linebreaks }}
                    </div>
                </div>
                {% endif %}

                {% if report.initial_treatment %}
                <div class="mt-3">
                    <h6 class="text-muted">初步处理措施：</h6>
                    <div class="treatment">
                        {{ report.initial_treatment|linebreaks }}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- 器械信息 -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="bi bi-gear me-2"></i>
                    器械信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="info-item">
                            <label>器械名称：</label>
                            <span>{{ report.device_name }}</span>
                        </div>
                        <div class="info-item">
                            <label>注册证号：</label>
                            <span>{{ report.registration_number }}</span>
                        </div>
                        <div class="info-item">
                            <label>生产企业：</label>
                            <span>{{ report.manufacturer }}</span>
                        </div>
                        <div class="info-item">
                            <label>规格型号：</label>
                            <span>{{ report.specification|default:"未填写" }}</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-item">
                            <label>产品型号：</label>
                            <span>{{ report.model|default:"未填写" }}</span>
                        </div>
                        <div class="info-item">
                            <label>批号：</label>
                            <span>{{ report.batch_number|default:"未填写" }}</span>
                        </div>
                        <div class="info-item">
                            <label>生产日期：</label>
                            <span>{{ report.production_date|date:"Y-m-d"|default:"未填写" }}</span>
                        </div>
                        <div class="info-item">
                            <label>有效期至：</label>
                            <span>{{ report.expiry_date|date:"Y-m-d"|default:"未填写" }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 右侧：审核操作 -->
    <div class="col-lg-4">
        <!-- 审核表单 -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="bi bi-eye-fill me-2"></i>
                    审核操作
                </h5>
            </div>
            <div class="card-body">
                <form method="post" id="reviewForm">
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="{{ form.action.id_for_label }}" class="form-label">审核决定 <span class="text-danger">*</span></label>
                        {{ form.action }}
                        {% if form.action.errors %}
                        <div class="invalid-feedback d-block">
                            {{ form.action.errors.0 }}
                        </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.comments.id_for_label }}" class="form-label">审核意见</label>
                        {{ form.comments }}
                        {% if form.comments.errors %}
                        <div class="invalid-feedback d-block">
                            {{ form.comments.errors.0 }}
                        </div>
                        {% endif %}
                        <div class="form-text">请详细说明审核意见和建议</div>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="bi bi-check-circle me-2"></i>
                            提交审核
                        </button>
                        <a href="{% url 'reports:report_detail' report_id=report.id %}" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle me-2"></i>
                            取消
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- 审核指南 -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-light">
                <h6 class="card-title mb-0">
                    <i class="bi bi-info-circle me-2"></i>
                    审核指南
                </h6>
            </div>
            <div class="card-body">
                <div class="review-guide">
                    <div class="guide-item">
                        <h6 class="text-success">批准</h6>
                        <p class="small text-muted">确认报告内容准确、完整，符合上报要求。报告将被标记为已批准状态。</p>
                    </div>

                    <div class="guide-item">
                        <h6 class="text-danger">拒绝</h6>
                        <p class="small text-muted">报告内容不完整、不准确或不符合要求，需要重新填写。必须填写详细的拒绝原因。</p>
                    </div>

                    <div class="guide-item">
                        <h6 class="text-info">审核要点</h6>
                        <ul class="small text-muted mb-0">
                            <li>检查报告信息的完整性和准确性</li>
                            <li>验证器械故障表现描述是否详细</li>
                            <li>确认事件描述是否清晰完整</li>
                            <li>核实医疗器械信息是否正确</li>
                        </ul>
                    </div>
                </div>
                
                <div class="mt-3">
                    <h6 class="text-muted">审核要点：</h6>
                    <ul class="small text-muted">
                        <li>事件描述是否详细、准确</li>
                        <li>器械信息是否完整</li>
                        <li>伤害程度评估是否合理</li>
                        <li>初步分析是否有依据</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 患者信息 -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-secondary text-white">
                <h6 class="card-title mb-0">
                    <i class="bi bi-person me-2"></i>
                    患者信息
                </h6>
            </div>
            <div class="card-body">
                <div class="info-item">
                    <label>患者姓名：</label>
                    <span>{{ report.patient_name }}</span>
                </div>
                <div class="info-item">
                    <label>年龄：</label>
                    <span>{{ report.patient_age }}岁</span>
                </div>
                <div class="info-item">
                    <label>性别：</label>
                    <span>{{ report.get_patient_gender_display }}</span>
                </div>
                {% if report.patient_contact %}
                <div class="info-item">
                    <label>联系方式：</label>
                    <span>{{ report.patient_contact }}</span>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 确认模态框 -->
<div class="modal fade" id="confirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    确认审核
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p id="confirmMessage">确定要执行此审核操作吗？</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmSubmitBtn">确认</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block reports_extra_css %}
<style>
.info-item {
    display: flex;
    margin-bottom: 12px;
    align-items: flex-start;
}
.info-item label {
    min-width: 100px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 0;
}
.info-item span {
    flex: 1;
}

.event-description, .injury-description, .cause-analysis, .treatment {
    background-color: #f8f9fa;
    border-left: 4px solid #0d6efd;
    padding: 15px;
    border-radius: 0 4px 4px 0;
    line-height: 1.6;
}

.guide-item {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #f1f3f4;
}

.guide-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.guide-item h6 {
    margin-bottom: 5px;
}

.review-guide ul {
    margin-bottom: 0;
    padding-left: 20px;
}

.card-header {
    position: relative;
    overflow: hidden;
}

.card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
    pointer-events: none;
}
</style>
{% endblock %}

{% block reports_extra_js %}
<script>
$(document).ready(function() {
    // 表单提交确认
    $('#reviewForm').on('submit', function(e) {
        e.preventDefault();
        
        const action = $('#id_action').val();
        const actionText = $('#id_action option:selected').text();
        
        $('#confirmMessage').text(`确定要${actionText}此报告吗？此操作不可撤销。`);
        $('#confirmModal').modal('show');
    });

    // 确认提交
    $('#confirmSubmitBtn').on('click', function() {
        $('#confirmModal').modal('hide');
        $('#reviewForm')[0].submit();
    });

    // 审核决定变化时的提示
    $('#id_action').on('change', function() {
        const action = $(this).val();
        const $comments = $('#id_comments');

        if (action === 'reject') {
            $comments.prop('required', true);
            $comments.attr('placeholder', '请详细说明拒绝原因和需要修改的内容...');
            $comments.closest('.mb-3').find('label').html('审核意见 <span class="text-danger">*</span>');
        } else if (action === 'approve') {
            $comments.prop('required', false);
            $comments.attr('placeholder', '请填写审核意见和建议（可选）...');
            $comments.closest('.mb-3').find('label').text('审核意见');
        }
    });

    // 触发初始状态
    $('#id_action').trigger('change');
});
</script>
{% endblock %}
