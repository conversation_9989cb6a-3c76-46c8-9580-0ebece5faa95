{% extends 'base.html' %}
{% load static %}

{% block title %}导入科室Excel - 医疗器械不良事件上报平台{% endblock %}

{% block extra_css %}
<link href="{% static 'users/css/user_management.css' %}" rel="stylesheet">
{% endblock %}

{% block nav_items %}
<li class="nav-item">
    <a class="nav-link" href="{% url 'users:dashboard' %}">
        <i class="bi bi-house"></i>
        用户中心
    </a>
</li>
{% if user.profile.is_admin %}
<li class="nav-item">
    <a class="nav-link" href="{% url 'users:user_list' %}">
        <i class="bi bi-people"></i>
        用户管理
    </a>
</li>
<li class="nav-item">
    <a class="nav-link" href="{% url 'users:department_list' %}">
        <i class="bi bi-building"></i>
        科室管理
    </a>
</li>
{% endif %}
<li class="nav-item dropdown">
    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
        <i class="bi bi-person-circle"></i>
        {{ user.get_full_name|default:user.username }}
    </a>
    <ul class="dropdown-menu">
        <li><a class="dropdown-item" href="#"><i class="bi bi-gear me-2"></i>个人设置</a></li>
        <li><hr class="dropdown-divider"></li>
        <li><a class="dropdown-item" href="{% url 'users:logout' %}"><i class="bi bi-box-arrow-right me-2"></i>退出登录</a></li>
    </ul>
</li>
{% endblock %}

{% block content %}
<div class="import-container">
    <!-- 页面标题 -->
    <div class="page-header mb-4">
        <div class="row align-items-center">
            <div class="col">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'users:dashboard' %}">用户中心</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'users:department_list' %}">科室管理</a></li>
                        <li class="breadcrumb-item active">导入Excel</li>
                    </ol>
                </nav>
                <h2 class="page-title">
                    <i class="bi bi-upload me-2"></i>
                    导入科室Excel
                </h2>
                <p class="page-subtitle text-muted">批量导入科室信息，支持创建新科室和更新现有科室</p>
            </div>
            <div class="col-auto">
                <a href="{% url 'users:department_list' %}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left me-2"></i>
                    返回列表
                </a>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- 导入说明 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-info-circle me-2"></i>
                        导入说明
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>文件格式要求：</h6>
                            <ul class="list-unstyled">
                                <li><i class="bi bi-check-circle text-success me-2"></i>支持 .xlsx 和 .xls 格式</li>
                                <li><i class="bi bi-check-circle text-success me-2"></i>第一行为标题行（将被跳过）</li>
                                <li><i class="bi bi-check-circle text-success me-2"></i>文件大小不超过 10MB</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>数据列要求：</h6>
                            <ul class="list-unstyled">
                                <li><i class="bi bi-asterisk text-danger me-2"></i><strong>科室代码</strong>（必填）</li>
                                <li><i class="bi bi-asterisk text-danger me-2"></i><strong>科室名称</strong>（必填）</li>
                                <li><i class="bi bi-circle text-muted me-2"></i>状态（可选，默认启用）</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="alert alert-info mt-3">
                        <h6><i class="bi bi-lightbulb me-2"></i>导入规则：</h6>
                        <ul class="mb-0">
                            <li>如果科室代码已存在，将更新现有科室信息</li>
                            <li>状态列：<code>启用</code>、<code>1</code>、<code>True</code>、<code>true</code>、<code>是</code> 表示启用</li>
                            <li>其他值或空值表示禁用</li>
                            <li>空行将被自动跳过</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Excel模板下载 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-download me-2"></i>
                        模板下载
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h6 class="mb-1">Excel导入模板</h6>
                            <p class="text-muted mb-0">
                                下载标准模板，按照模板格式填写科室信息，确保导入成功。
                            </p>
                        </div>
                        <div class="col-auto">
                            <button type="button" class="btn btn-outline-primary" id="downloadTemplateBtn">
                                <i class="bi bi-download me-2"></i>
                                下载模板
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 文件上传 -->
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-cloud-upload me-2"></i>
                        上传Excel文件
                    </h6>
                </div>
                <div class="card-body">
                    <!-- 错误消息显示 -->
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {% if message.tags == 'error' %}
                                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                {% elif message.tags == 'success' %}
                                    <i class="bi bi-check-circle-fill me-2"></i>
                                {% elif message.tags == 'warning' %}
                                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                {% else %}
                                    <i class="bi bi-info-circle-fill me-2"></i>
                                {% endif %}
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <form method="post" enctype="multipart/form-data" id="importForm" class="needs-validation" novalidate>
                        {% csrf_token %}
                        
                        <!-- 文件选择区域 -->
                        <div class="upload-area border border-2 border-dashed rounded p-4 text-center mb-4" id="uploadArea">
                            <div class="upload-content">
                                <i class="bi bi-cloud-upload fs-1 text-muted d-block mb-3"></i>
                                <h5 class="mb-2">选择Excel文件</h5>
                                <p class="text-muted mb-3">
                                    拖拽文件到此处或点击选择文件<br>
                                    <small>支持 .xlsx 和 .xls 格式，最大 10MB</small>
                                </p>
                                <input type="file" class="form-control d-none" id="excel_file" name="excel_file" 
                                       accept=".xlsx,.xls" required>
                                <button type="button" class="btn btn-outline-primary" id="selectFileBtn">
                                    <i class="bi bi-folder2-open me-2"></i>
                                    选择文件
                                </button>
                            </div>
                            
                            <!-- 文件信息显示 -->
                            <div class="file-info d-none" id="fileInfo">
                                <div class="row align-items-center">
                                    <div class="col">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-file-earmark-excel text-success fs-3 me-3"></i>
                                            <div>
                                                <h6 class="mb-1" id="fileName">文件名</h6>
                                                <small class="text-muted" id="fileSize">文件大小</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <button type="button" class="btn btn-outline-secondary btn-sm" id="removeFileBtn">
                                            <i class="bi bi-x"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 导入选项 -->
                        <div class="import-options mb-4">
                            <h6 class="mb-3">导入选项</h6>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="skipErrors" name="skip_errors" checked>
                                <label class="form-check-label" for="skipErrors">
                                    跳过错误行，继续导入其他数据
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="updateExisting" name="update_existing" checked>
                                <label class="form-check-label" for="updateExisting">
                                    更新已存在的科室信息
                                </label>
                            </div>
                        </div>

                        <!-- 提交按钮 -->
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'users:department_list' %}" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle me-2"></i>
                                取消
                            </a>
                            <button type="submit" class="btn btn-primary" id="submitBtn" disabled>
                                <span class="btn-text">
                                    <i class="bi bi-upload me-2"></i>
                                    开始导入
                                </span>
                                <span class="btn-loading d-none">
                                    <span class="spinner-border spinner-border-sm me-2"></span>
                                    导入中...
                                </span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 导入历史 -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-clock-history me-2"></i>
                        最近导入记录
                    </h6>
                </div>
                <div class="card-body">
                    <div class="text-center text-muted py-3">
                        <i class="bi bi-inbox fs-3 d-block mb-2"></i>
                        <p class="mb-0">暂无导入记录</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 导入进度模态框 -->
<div class="modal fade" id="importProgressModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-upload me-2"></i>
                    正在导入
                </h5>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">导入中...</span>
                    </div>
                    <h6>正在处理Excel文件...</h6>
                    <p class="text-muted mb-0">请稍候，不要关闭页面</p>
                </div>
                
                <div class="progress mt-3" style="height: 6px;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                         role="progressbar" style="width: 0%" id="importProgress"></div>
                </div>
                
                <div class="mt-3">
                    <small class="text-muted" id="importStatus">准备导入...</small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'users/js/department_import.js' %}"></script>
{% endblock %}
