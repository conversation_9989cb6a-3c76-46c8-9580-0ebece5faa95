/**
 * Report List JavaScript for Medical Device Reporting Platform
 * 医疗器械不良事件上报平台报告列表脚本
 */

document.addEventListener('DOMContentLoaded', function() {
    // 初始化报告列表
    initializeReportList();
    
    /**
     * 初始化报告列表
     */
    function initializeReportList() {
        // 绑定事件监听器
        bindEventListeners();
        
        // 初始化数据表格
        initializeDataTable();
        
        // 初始化批量操作
        initializeBatchActions();
        
        // 初始化筛选功能
        initializeFilters();
        
        console.log('报告列表初始化完成');
    }
    
    /**
     * 绑定事件监听器
     */
    function bindEventListeners() {
        // 刷新按钮
        const refreshBtn = document.getElementById('refreshBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', function() {
                location.reload();
            });
        }
        
        // 导出按钮
        const exportBtn = document.getElementById('exportBtn');
        if (exportBtn) {
            exportBtn.addEventListener('click', handleExport);
        }
        
        // 全选复选框
        const selectAllCheckbox = document.getElementById('selectAll');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', handleSelectAll);
        }
        
        // 行复选框
        const rowCheckboxes = document.querySelectorAll('.row-checkbox');
        rowCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', handleRowSelect);
        });
        
        // 提交按钮
        const submitButtons = document.querySelectorAll('.submit-btn');
        submitButtons.forEach(button => {
            button.addEventListener('click', handleSubmitReport);
        });
        
        // 搜索输入框
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', debounce(handleSearch, 500));
        }
        
        // 清除搜索按钮
        const clearSearchBtn = document.getElementById('clearSearch');
        if (clearSearchBtn) {
            clearSearchBtn.addEventListener('click', handleClearSearch);
        }
        
        // 筛选表单
        const filterForm = document.getElementById('filterForm');
        if (filterForm) {
            const filterInputs = filterForm.querySelectorAll('select, input');
            filterInputs.forEach(input => {
                input.addEventListener('change', handleFilterChange);
            });
        }
    }
    
    /**
     * 初始化数据表格
     */
    function initializeDataTable() {
        const table = document.getElementById('reportTable');
        if (table && typeof $.fn.DataTable !== 'undefined') {
            $(table).DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/zh.json'
                },
                pageLength: 20,
                order: [[7, 'desc']], // 按创建时间降序
                columnDefs: [
                    { orderable: false, targets: [0, 8] }, // 复选框和操作列不可排序
                    { searchable: false, targets: [0, 8] }
                ],
                responsive: true,
                dom: 'rtip', // 只显示表格、信息和分页
                searching: false, // 禁用内置搜索，使用自定义搜索
                lengthChange: false // 禁用每页显示数量选择
            });
        }
    }
    
    /**
     * 初始化批量操作
     */
    function initializeBatchActions() {
        updateBatchActionButtons();
    }
    
    /**
     * 初始化筛选功能
     */
    function initializeFilters() {
        // 从URL参数恢复筛选状态
        const urlParams = new URLSearchParams(window.location.search);
        
        urlParams.forEach((value, key) => {
            const input = document.querySelector(`[name="${key}"]`);
            if (input) {
                input.value = value;
            }
        });
    }
    
    /**
     * 处理全选
     */
    function handleSelectAll(event) {
        const isChecked = event.target.checked;
        const rowCheckboxes = document.querySelectorAll('.row-checkbox');
        
        rowCheckboxes.forEach(checkbox => {
            checkbox.checked = isChecked;
        });
        
        updateBatchActionButtons();
    }
    
    /**
     * 处理行选择
     */
    function handleRowSelect() {
        updateSelectAllState();
        updateBatchActionButtons();
    }
    
    /**
     * 更新全选状态
     */
    function updateSelectAllState() {
        const selectAllCheckbox = document.getElementById('selectAll');
        const rowCheckboxes = document.querySelectorAll('.row-checkbox');
        const checkedCount = document.querySelectorAll('.row-checkbox:checked').length;
        const totalCount = rowCheckboxes.length;
        
        if (selectAllCheckbox) {
            selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < totalCount;
            selectAllCheckbox.checked = checkedCount === totalCount && totalCount > 0;
        }
    }
    
    /**
     * 更新批量操作按钮状态
     */
    function updateBatchActionButtons() {
        const checkedCount = document.querySelectorAll('.row-checkbox:checked').length;
        const batchButtons = document.querySelectorAll('[data-batch-action]');
        
        batchButtons.forEach(button => {
            button.disabled = checkedCount === 0;
        });
        
        // 更新选中计数显示
        const countElements = document.querySelectorAll('[data-selected-count]');
        countElements.forEach(element => {
            element.textContent = checkedCount;
        });
    }
    
    /**
     * 处理报告提交
     */
    function handleSubmitReport(event) {
        const button = event.target.closest('.submit-btn');
        const reportId = button.getAttribute('data-report-id');
        
        if (!reportId) return;
        
        // 显示确认对话框
        const modal = document.getElementById('submitModal');
        if (modal) {
            const confirmBtn = document.getElementById('confirmSubmitBtn');
            if (confirmBtn) {
                confirmBtn.setAttribute('data-report-id', reportId);
            }
            
            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();
        }
    }
    
    /**
     * 确认提交报告
     */
    function confirmSubmitReport(reportId) {
        if (!reportId) return;
        
        const url = `/reports/api/reports/${reportId}/submit/`;
        
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken()
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            showSuccessMessage('报告提交成功');
            setTimeout(() => {
                location.reload();
            }, 1000);
        })
        .catch(error => {
            console.error('提交报告失败:', error);
            showErrorMessage('提交报告失败，请稍后重试');
        });
    }
    
    /**
     * 处理搜索
     */
    function handleSearch(event) {
        const searchTerm = event.target.value.trim();
        
        // 更新URL参数
        const url = new URL(window.location);
        if (searchTerm) {
            url.searchParams.set('search', searchTerm);
        } else {
            url.searchParams.delete('search');
        }
        url.searchParams.delete('page'); // 重置页码
        
        // 跳转到新URL
        window.location.href = url.toString();
    }
    
    /**
     * 处理清除搜索
     */
    function handleClearSearch() {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.value = '';
            
            // 清除URL中的搜索参数
            const url = new URL(window.location);
            url.searchParams.delete('search');
            url.searchParams.delete('page');
            
            window.location.href = url.toString();
        }
    }
    
    /**
     * 处理筛选变化
     */
    function handleFilterChange() {
        const filterForm = document.getElementById('filterForm');
        if (!filterForm) return;
        
        const formData = new FormData(filterForm);
        const url = new URL(window.location);
        
        // 清除现有参数
        url.searchParams.delete('status');
        url.searchParams.delete('injury_level');
        url.searchParams.delete('date_range');
        url.searchParams.delete('search');
        url.searchParams.delete('page');
        
        // 添加新参数
        for (const [key, value] of formData.entries()) {
            if (value) {
                url.searchParams.set(key, value);
            }
        }
        
        // 跳转到新URL
        window.location.href = url.toString();
    }
    
    /**
     * 处理导出
     */
    function handleExport() {
        // 获取当前筛选条件
        const url = new URL(window.location);
        const params = url.searchParams;
        
        // 构建导出URL
        const exportUrl = `/reports/api/reports/export/?${params.toString()}`;
        
        // 创建隐藏的下载链接
        const link = document.createElement('a');
        link.href = exportUrl;
        link.download = `reports_${new Date().toISOString().split('T')[0]}.xlsx`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        showInfoMessage('导出任务已开始，请稍候...');
    }
    
    /**
     * 防抖函数
     */
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    /**
     * 获取CSRF Token
     */
    function getCsrfToken() {
        const token = document.querySelector('[name=csrfmiddlewaretoken]');
        return token ? token.value : '';
    }
    
    // 绑定确认提交按钮事件
    const confirmSubmitBtn = document.getElementById('confirmSubmitBtn');
    if (confirmSubmitBtn) {
        confirmSubmitBtn.addEventListener('click', function() {
            const reportId = this.getAttribute('data-report-id');
            if (reportId) {
                confirmSubmitReport(reportId);
                
                // 关闭模态框
                const modal = document.getElementById('submitModal');
                if (modal) {
                    const bootstrapModal = bootstrap.Modal.getInstance(modal);
                    if (bootstrapModal) {
                        bootstrapModal.hide();
                    }
                }
            }
        });
    }
});
