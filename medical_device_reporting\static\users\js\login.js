/**
 * Login Page JavaScript for Medical Device Reporting Platform
 * 医疗器械不良事件上报平台登录页面脚本
 */

document.addEventListener('DOMContentLoaded', function() {
    // 获取表单元素
    const loginForm = document.getElementById('loginForm');
    const accountNumberInput = document.getElementById('account_number');
    const loginBtn = document.getElementById('loginBtn');
    const btnText = loginBtn.querySelector('.btn-text');
    const btnLoading = loginBtn.querySelector('.btn-loading');
    
    // 初始化
    initializeLogin();
    
    /**
     * 初始化登录页面
     */
    function initializeLogin() {
        // 设置输入焦点
        if (accountNumberInput) {
            accountNumberInput.focus();
        }
        
        // 绑定事件监听器
        bindEventListeners();
        
        // 初始化表单验证
        initializeValidation();
        
        // 检查是否有保存的账号
        loadSavedAccount();
        
        console.log('登录页面初始化完成');
    }
    
    /**
     * 绑定事件监听器
     */
    function bindEventListeners() {
        // 表单提交事件
        if (loginForm) {
            loginForm.addEventListener('submit', handleFormSubmit);
        }
        
        // 账号输入事件
        if (accountNumberInput) {
            accountNumberInput.addEventListener('input', handleAccountInput);
            accountNumberInput.addEventListener('keypress', handleKeyPress);
            accountNumberInput.addEventListener('paste', handlePaste);
        }
        
        // 记住登录状态复选框
        const rememberCheckbox = document.getElementById('remember_me');
        if (rememberCheckbox) {
            rememberCheckbox.addEventListener('change', handleRememberChange);
        }
    }
    
    /**
     * 处理表单提交
     */
    function handleFormSubmit(event) {
        event.preventDefault();
        
        // 验证表单
        if (!validateForm()) {
            return false;
        }
        
        // 显示加载状态
        showLoading();
        
        // 保存账号（如果选择记住）
        saveAccountIfRemember();
        
        // 提交表单
        submitForm();
    }
    
    /**
     * 处理账号输入
     */
    function handleAccountInput(event) {
        const input = event.target;
        let value = input.value;
        
        // 只允许数字输入
        value = value.replace(/[^0-9]/g, '');
        
        // 限制长度为4位
        if (value.length > 4) {
            value = value.substring(0, 4);
        }
        
        input.value = value;
        
        // 实时验证
        validateAccountNumber(input);
        
        // 如果输入4位数字，自动提交
        if (value.length === 4) {
            setTimeout(() => {
                if (validateForm()) {
                    loginForm.dispatchEvent(new Event('submit'));
                }
            }, 500);
        }
    }
    
    /**
     * 处理按键事件
     */
    function handleKeyPress(event) {
        const char = String.fromCharCode(event.which);
        
        // 只允许数字
        if (!/[0-9]/.test(char)) {
            event.preventDefault();
            return false;
        }
        
        // Enter键提交表单
        if (event.key === 'Enter') {
            event.preventDefault();
            loginForm.dispatchEvent(new Event('submit'));
        }
    }
    
    /**
     * 处理粘贴事件
     */
    function handlePaste(event) {
        event.preventDefault();
        
        // 获取粘贴的文本
        const paste = (event.clipboardData || window.clipboardData).getData('text');
        
        // 只保留数字，限制4位
        const numbers = paste.replace(/[^0-9]/g, '').substring(0, 4);
        
        // 设置值并触发验证
        accountNumberInput.value = numbers;
        validateAccountNumber(accountNumberInput);
    }
    
    /**
     * 处理记住登录状态变化
     */
    function handleRememberChange(event) {
        const isChecked = event.target.checked;
        
        if (!isChecked) {
            // 如果取消记住，清除保存的账号
            localStorage.removeItem('saved_account_number');
        }
    }
    
    /**
     * 验证表单
     */
    function validateForm() {
        let isValid = true;
        
        // 验证账号
        if (!validateAccountNumber(accountNumberInput)) {
            isValid = false;
        }
        
        // 添加Bootstrap验证类
        loginForm.classList.add('was-validated');
        
        return isValid;
    }
    
    /**
     * 验证账号格式
     */
    function validateAccountNumber(input) {
        const value = input.value.trim();
        const isValid = /^[0-9]{4}$/.test(value);
        
        // 更新验证状态
        if (isValid) {
            input.classList.remove('is-invalid');
            input.classList.add('is-valid');
        } else {
            input.classList.remove('is-valid');
            input.classList.add('is-invalid');
        }
        
        return isValid;
    }
    
    /**
     * 初始化表单验证
     */
    function initializeValidation() {
        // Bootstrap 5 表单验证
        const forms = document.querySelectorAll('.needs-validation');
        
        Array.from(forms).forEach(form => {
            form.addEventListener('submit', event => {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            });
        });
    }
    
    /**
     * 显示加载状态
     */
    function showLoading() {
        if (btnText && btnLoading) {
            btnText.classList.add('d-none');
            btnLoading.classList.remove('d-none');
        }
        
        if (loginBtn) {
            loginBtn.disabled = true;
        }
    }
    
    /**
     * 隐藏加载状态
     */
    function hideLoading() {
        if (btnText && btnLoading) {
            btnText.classList.remove('d-none');
            btnLoading.classList.add('d-none');
        }
        
        if (loginBtn) {
            loginBtn.disabled = false;
        }
    }
    
    /**
     * 保存账号（如果选择记住）
     */
    function saveAccountIfRemember() {
        const rememberCheckbox = document.getElementById('remember_me');
        
        if (rememberCheckbox && rememberCheckbox.checked) {
            const accountNumber = accountNumberInput.value;
            localStorage.setItem('saved_account_number', accountNumber);
        }
    }
    
    /**
     * 加载保存的账号
     */
    function loadSavedAccount() {
        const savedAccount = localStorage.getItem('saved_account_number');
        
        if (savedAccount && accountNumberInput) {
            accountNumberInput.value = savedAccount;
            validateAccountNumber(accountNumberInput);
            
            // 勾选记住登录状态
            const rememberCheckbox = document.getElementById('remember_me');
            if (rememberCheckbox) {
                rememberCheckbox.checked = true;
            }
        }
    }
    
    /**
     * 提交表单
     */
    function submitForm() {
        // 创建FormData对象
        const formData = new FormData(loginForm);
        
        // 使用fetch API提交表单
        fetch(loginForm.action || window.location.href, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
            }
        })
        .then(response => {
            if (response.redirected) {
                // 登录成功，重定向
                window.location.href = response.url;
            } else {
                return response.text();
            }
        })
        .then(html => {
            if (html) {
                // 登录失败，更新页面内容
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                const newContent = doc.querySelector('.login-container');
                
                if (newContent) {
                    document.querySelector('.login-container').innerHTML = newContent.innerHTML;
                    // 重新初始化
                    initializeLogin();
                }
            }
        })
        .catch(error => {
            console.error('登录请求失败:', error);
            showError('登录请求失败，请稍后重试');
        })
        .finally(() => {
            hideLoading();
        });
    }
    
    /**
     * 显示错误消息
     */
    function showError(message) {
        const alertHtml = `
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        
        const cardBody = document.querySelector('.login-card .card-body');
        if (cardBody) {
            cardBody.insertAdjacentHTML('afterbegin', alertHtml);
        }
    }
    
    /**
     * 工具函数：防抖
     */
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    // 导出函数供外部使用
    window.LoginPage = {
        validateAccountNumber,
        showError,
        hideLoading
    };
});
