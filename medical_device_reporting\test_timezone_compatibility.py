#!/usr/bin/env python
"""
时区兼容性测试脚本
Timezone Compatibility Test Script

专门测试MySQL时区配置修复的兼容性，验证在不同时区配置下的功能稳定性。
"""

import os
import sys
import django
import time
from datetime import datetime, timedelta

# 设置Django环境
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from django.utils import timezone
from django.db import connection, models
from django.contrib.auth.models import User
from apps.users.models import UserProfile, Department
from apps.reports.models import AdverseEventReport
from apps.reports.selectors import (
    get_time_series_statistics,
    get_trend_analysis,
    _get_time_series_statistics_fallback
)

class TimezoneCompatibilityTester:
    """时区兼容性测试器"""
    
    def __init__(self):
        self.test_results = {}
        self.timezone_status = None
        
    def print_header(self, title):
        """打印测试标题"""
        print(f"\n{'='*70}")
        print(f" {title}")
        print(f"{'='*70}")
    
    def print_test_result(self, test_name, success, message="", duration=None):
        """打印测试结果"""
        status = "✅ 通过" if success else "❌ 失败"
        duration_str = f" ({duration:.3f}s)" if duration else ""
        print(f"{status}: {test_name}{duration_str}")
        if message:
            print(f"   {message}")
        
        self.test_results[test_name] = success
    
    def check_mysql_timezone_status(self):
        """检查MySQL时区配置状态"""
        self.print_header("MySQL时区配置状态检查")
        
        try:
            with connection.cursor() as cursor:
                # 检查时区表
                cursor.execute("SELECT COUNT(*) FROM mysql.time_zone")
                time_zone_count = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM mysql.time_zone_name")
                time_zone_name_count = cursor.fetchone()[0]
                
                # 检查当前时区设置
                cursor.execute("SELECT @@global.time_zone, @@session.time_zone")
                global_tz, session_tz = cursor.fetchone()
            
            timezone_tables_installed = time_zone_count > 0 and time_zone_name_count > 0
            
            print(f"时区表状态:")
            print(f"  time_zone表记录数: {time_zone_count}")
            print(f"  time_zone_name表记录数: {time_zone_name_count}")
            print(f"  全局时区: {global_tz}")
            print(f"  会话时区: {session_tz}")
            print(f"  时区表已安装: {'是' if timezone_tables_installed else '否'}")
            
            self.timezone_status = {
                'tables_installed': timezone_tables_installed,
                'time_zone_count': time_zone_count,
                'time_zone_name_count': time_zone_name_count,
                'global_timezone': global_tz,
                'session_timezone': session_tz
            }
            
            return timezone_tables_installed
            
        except Exception as e:
            print(f"❌ 时区状态检查失败: {e}")
            return False
    
    def test_django_timezone_functions(self):
        """测试Django时区函数"""
        self.print_header("Django时区函数测试")
        
        from django.db.models.functions import TruncYear, TruncMonth, TruncWeek, TruncDay
        
        test_functions = [
            ('TruncYear', TruncYear, 'year'),
            ('TruncMonth', TruncMonth, 'month'),
            ('TruncWeek', TruncWeek, 'week'),
            ('TruncDay', TruncDay, 'day'),
        ]
        
        for func_name, func_class, granularity in test_functions:
            try:
                start_time = time.time()
                
                # 尝试使用Django时区函数
                test_query = AdverseEventReport.objects.annotate(
                    period=func_class('created_at')
                ).values('period').annotate(
                    count=models.Count('id')
                )[:5]
                
                result = list(test_query)
                duration = time.time() - start_time
                
                self.print_test_result(
                    f"Django {func_name}",
                    True,
                    f"成功执行，返回{len(result)}条记录",
                    duration
                )
                
            except Exception as e:
                # 预期的错误，测试降级处理
                error_msg = str(e)
                is_timezone_error = "time zone definitions" in error_msg.lower()
                
                if is_timezone_error:
                    self.print_test_result(
                        f"Django {func_name}",
                        True,  # 这是预期的，有降级处理
                        f"时区错误（预期）: {error_msg[:50]}..."
                    )
                else:
                    self.print_test_result(
                        f"Django {func_name}",
                        False,
                        f"意外错误: {error_msg[:50]}..."
                    )
    
    def test_fallback_mechanism(self):
        """测试降级处理机制"""
        self.print_header("降级处理机制测试")
        
        try:
            user_profile = UserProfile.objects.first()
            if not user_profile:
                self.print_test_result("降级处理测试", False, "未找到用户配置文件")
                return
            
            # 获取查询集
            queryset = AdverseEventReport.objects.filter(
                is_deleted=False
            )
            
            granularities = ['year', 'month', 'week', 'day']
            
            for granularity in granularities:
                try:
                    start_time = time.time()
                    
                    # 直接测试降级处理函数
                    result = _get_time_series_statistics_fallback(
                        queryset=queryset,
                        date_field='created_at',
                        granularity=granularity,
                        start_date=timezone.now() - timedelta(days=90),
                        end_date=timezone.now()
                    )
                    
                    duration = time.time() - start_time
                    
                    # 验证结果格式
                    is_valid = (
                        isinstance(result, list) and
                        all(isinstance(item, dict) for item in result) and
                        all('total_count' in item for item in result)
                    )
                    
                    self.print_test_result(
                        f"降级处理 ({granularity})",
                        is_valid,
                        f"返回{len(result)}条记录，格式正确",
                        duration
                    )
                    
                    # 验证数据结构
                    if result:
                        sample = result[0]
                        required_fields = ['period', 'label', 'total_count', 'serious_count']
                        has_required_fields = all(field in sample for field in required_fields)
                        
                        self.print_test_result(
                            f"数据结构验证 ({granularity})",
                            has_required_fields,
                            f"包含必需字段: {list(sample.keys())}"
                        )
                    
                except Exception as e:
                    self.print_test_result(
                        f"降级处理 ({granularity})",
                        False,
                        f"执行失败: {e}"
                    )
                    
        except Exception as e:
            self.print_test_result("降级处理测试", False, f"测试失败: {e}")
    
    def test_integrated_statistics_functions(self):
        """测试集成的统计函数"""
        self.print_header("集成统计函数测试")
        
        try:
            user_profile = UserProfile.objects.first()
            if not user_profile:
                self.print_test_result("集成统计测试", False, "未找到用户配置文件")
                return
            
            # 测试时间序列统计
            granularities = ['year', 'month', 'week', 'day']
            
            for granularity in granularities:
                try:
                    start_time = time.time()
                    
                    result = get_time_series_statistics(
                        user_profile=user_profile,
                        granularity=granularity,
                        start_date=timezone.now() - timedelta(days=90),
                        end_date=timezone.now()
                    )
                    
                    duration = time.time() - start_time
                    
                    # 验证结果
                    is_valid = isinstance(result, list)
                    
                    self.print_test_result(
                        f"时间序列统计 ({granularity})",
                        is_valid,
                        f"返回{len(result)}条记录",
                        duration
                    )
                    
                except Exception as e:
                    self.print_test_result(
                        f"时间序列统计 ({granularity})",
                        False,
                        f"执行失败: {e}"
                    )
            
            # 测试趋势分析
            try:
                start_time = time.time()
                
                trend_result = get_trend_analysis(
                    user_profile=user_profile,
                    metric='total_count',
                    granularity='month',
                    periods=6
                )
                
                duration = time.time() - start_time
                
                is_valid = (
                    isinstance(trend_result, dict) and
                    'trend_direction' in trend_result and
                    'growth_rate' in trend_result
                )
                
                self.print_test_result(
                    "趋势分析",
                    is_valid,
                    f"趋势方向: {trend_result.get('trend_direction', 'N/A')}",
                    duration
                )
                
            except Exception as e:
                self.print_test_result("趋势分析", False, f"执行失败: {e}")
                
        except Exception as e:
            self.print_test_result("集成统计测试", False, f"测试失败: {e}")
    
    def test_data_consistency(self):
        """测试数据一致性"""
        self.print_header("数据一致性测试")
        
        try:
            user_profile = UserProfile.objects.first()
            if not user_profile:
                self.print_test_result("数据一致性测试", False, "未找到用户配置文件")
                return
            
            # 比较Django函数和降级处理的结果
            queryset = AdverseEventReport.objects.filter(is_deleted=False)
            
            # 测试月度统计的一致性
            try:
                # 使用降级处理
                fallback_result = _get_time_series_statistics_fallback(
                    queryset=queryset,
                    date_field='created_at',
                    granularity='month',
                    start_date=timezone.now() - timedelta(days=90),
                    end_date=timezone.now()
                )
                
                # 使用集成函数（会自动选择降级处理）
                integrated_result = get_time_series_statistics(
                    user_profile=user_profile,
                    granularity='month',
                    start_date=timezone.now() - timedelta(days=90),
                    end_date=timezone.now()
                )
                
                # 比较结果数量
                count_consistent = len(fallback_result) == len(integrated_result)
                
                # 比较总计数
                fallback_total = sum(item.get('total_count', 0) for item in fallback_result)
                integrated_total = sum(item.get('total_count', 0) for item in integrated_result)
                total_consistent = fallback_total == integrated_total
                
                self.print_test_result(
                    "数据一致性检查",
                    count_consistent and total_consistent,
                    f"降级处理: {len(fallback_result)}条/{fallback_total}总数, "
                    f"集成函数: {len(integrated_result)}条/{integrated_total}总数"
                )
                
            except Exception as e:
                self.print_test_result("数据一致性检查", False, f"检查失败: {e}")
                
        except Exception as e:
            self.print_test_result("数据一致性测试", False, f"测试失败: {e}")
    
    def test_performance_comparison(self):
        """测试性能对比"""
        self.print_header("性能对比测试")
        
        try:
            user_profile = UserProfile.objects.first()
            if not user_profile:
                self.print_test_result("性能对比测试", False, "未找到用户配置文件")
                return
            
            queryset = AdverseEventReport.objects.filter(is_deleted=False)
            iterations = 10
            
            # 测试降级处理性能
            fallback_times = []
            for i in range(iterations):
                start_time = time.time()
                _get_time_series_statistics_fallback(
                    queryset=queryset,
                    date_field='created_at',
                    granularity='month',
                    start_date=timezone.now() - timedelta(days=90),
                    end_date=timezone.now()
                )
                fallback_times.append(time.time() - start_time)
            
            # 测试集成函数性能
            integrated_times = []
            for i in range(iterations):
                start_time = time.time()
                get_time_series_statistics(
                    user_profile=user_profile,
                    granularity='month',
                    start_date=timezone.now() - timedelta(days=90),
                    end_date=timezone.now()
                )
                integrated_times.append(time.time() - start_time)
            
            avg_fallback_time = sum(fallback_times) / len(fallback_times)
            avg_integrated_time = sum(integrated_times) / len(integrated_times)
            
            # 性能应该相近（因为集成函数会使用降级处理）
            performance_acceptable = abs(avg_fallback_time - avg_integrated_time) < 0.1
            
            self.print_test_result(
                "性能对比",
                performance_acceptable,
                f"降级处理: {avg_fallback_time:.3f}s, 集成函数: {avg_integrated_time:.3f}s"
            )
            
        except Exception as e:
            self.print_test_result("性能对比测试", False, f"测试失败: {e}")
    
    def generate_compatibility_report(self):
        """生成兼容性测试报告"""
        self.print_header("时区兼容性测试报告")
        
        total_tests = len(self.test_results)
        passed_tests = sum(self.test_results.values())
        failed_tests = total_tests - passed_tests
        
        print(f"测试总数: {total_tests}")
        print(f"通过测试: {passed_tests}")
        print(f"失败测试: {failed_tests}")
        print(f"通过率: {(passed_tests / total_tests * 100):.1f}%")
        
        if self.timezone_status:
            print(f"\n时区配置状态:")
            print(f"  时区表已安装: {'是' if self.timezone_status['tables_installed'] else '否'}")
            print(f"  time_zone表记录: {self.timezone_status['time_zone_count']}")
            print(f"  time_zone_name表记录: {self.timezone_status['time_zone_name_count']}")
        
        # 评估兼容性
        if passed_tests == total_tests:
            print(f"\n🎉 时区兼容性测试全部通过！")
            compatibility_level = "完全兼容"
        elif passed_tests >= total_tests * 0.9:
            print(f"\n✅ 时区兼容性良好。")
            compatibility_level = "高度兼容"
        elif passed_tests >= total_tests * 0.8:
            print(f"\n⚠️ 时区兼容性基本正常，有少数问题。")
            compatibility_level = "基本兼容"
        else:
            print(f"\n❌ 时区兼容性存在问题。")
            compatibility_level = "兼容性差"
        
        print(f"\n📊 兼容性评级: {compatibility_level}")
        
        return {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': failed_tests,
            'pass_rate': (passed_tests / total_tests * 100),
            'compatibility_level': compatibility_level,
            'timezone_status': self.timezone_status
        }
    
    def run_all_tests(self):
        """运行所有兼容性测试"""
        print("🚀 开始时区兼容性测试...")
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 检查时区状态
        self.check_mysql_timezone_status()
        
        # 执行各项测试
        self.test_django_timezone_functions()
        self.test_fallback_mechanism()
        self.test_integrated_statistics_functions()
        self.test_data_consistency()
        self.test_performance_comparison()
        
        # 生成报告
        report = self.generate_compatibility_report()
        
        print(f"\n🎯 时区兼容性测试完成！")
        
        return report

def main():
    """主测试函数"""
    tester = TimezoneCompatibilityTester()
    report = tester.run_all_tests()
    
    # 返回适当的退出码
    return 0 if report['pass_rate'] >= 80 else 1

if __name__ == '__main__':
    sys.exit(main())
