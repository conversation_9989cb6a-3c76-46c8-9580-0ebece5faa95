# Base requirements for Medical Device Reporting Platform
# 医疗器械不良事件上报平台基础依赖

# Django framework
Django>=4.2,<5.0

# MySQL database client
mysqlclient>=2.1.0

# Environment variables management
django-environ>=0.10.0

# Image processing
Pillow>=9.0.0

# Django REST framework for API development
djangorestframework>=3.14.0

# CORS headers for API access
django-cors-headers>=4.0.0

# Timezone support
pytz>=2023.3

# Excel file processing
openpyxl>=3.1.0

# PDF generation
reportlab>=4.0.0

# Chart image generation
matplotlib>=3.7.0

# Data manipulation for exports
pandas>=2.0.0
