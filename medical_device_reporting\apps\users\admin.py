"""
用户管理Django管理后台配置
User Management Django Admin Configuration
"""

from django.contrib import admin
from django.contrib.auth.models import User
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from django.db.models import Count
from django.contrib.admin import SimpleListFilter

from .models import UserProfile, Department


class ActiveUsersFilter(SimpleListFilter):
    """活跃用户过滤器"""
    title = '用户状态'
    parameter_name = 'user_status'

    def lookups(self, request, model_admin):
        return (
            ('active', '活跃用户'),
            ('inactive', '禁用用户'),
            ('no_department', '无科室用户'),
        )

    def queryset(self, request, queryset):
        if self.value() == 'active':
            return queryset.filter(is_active=True, is_deleted=False)
        elif self.value() == 'inactive':
            return queryset.filter(is_active=False)
        elif self.value() == 'no_department':
            return queryset.filter(department__isnull=True)
        return queryset


class DepartmentUsersFilter(SimpleListFilter):
    """科室用户数量过滤器"""
    title = '科室规模'
    parameter_name = 'department_size'

    def lookups(self, request, model_admin):
        return (
            ('empty', '无用户'),
            ('small', '1-5人'),
            ('medium', '6-20人'),
            ('large', '20人以上'),
        )

    def queryset(self, request, queryset):
        if self.value() == 'empty':
            return queryset.annotate(user_count=Count('users')).filter(user_count=0)
        elif self.value() == 'small':
            return queryset.annotate(user_count=Count('users')).filter(user_count__range=(1, 5))
        elif self.value() == 'medium':
            return queryset.annotate(user_count=Count('users')).filter(user_count__range=(6, 20))
        elif self.value() == 'large':
            return queryset.annotate(user_count=Count('users')).filter(user_count__gt=20)
        return queryset


@admin.register(Department)
class DepartmentAdmin(admin.ModelAdmin):
    """
    科室管理后台
    """
    
    list_display = [
        'code', 'name', 'user_count',
        'is_active', 'created_at', 'updated_at'
    ]
    list_filter = ['is_active', 'created_at', DepartmentUsersFilter]
    search_fields = ['name', 'code']
    ordering = ['code', 'name']
    list_per_page = 20
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'code')
        }),
        ('状态', {
            'fields': ('is_active',)
        }),
        ('审计信息', {
            'fields': ('created_at', 'updated_at', 'created_by', 'updated_by'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ['created_at', 'updated_at', 'created_by', 'updated_by']
    
    def user_count(self, obj):
        """
        显示科室用户数量
        """
        count = obj.users.filter(is_deleted=False, is_active=True).count()
        if count > 0:
            url = reverse('admin:users_userprofile_changelist')
            return format_html(
                '<a href="{}?department__id__exact={}">{} 人</a>',
                url, obj.id, count
            )
        return '0 人'
    
    user_count.short_description = '用户数量'
    user_count.admin_order_field = 'users__count'
    
    def save_model(self, request, obj, form, change):
        """
        保存时设置创建者/更新者
        """
        if not change:
            obj.created_by = request.user
        obj.updated_by = request.user
        super().save_model(request, obj, form, change)

    actions = ['activate_departments', 'deactivate_departments']

    def activate_departments(self, request, queryset):
        """批量激活科室"""
        updated = queryset.update(is_active=True)
        self.message_user(request, f'成功激活 {updated} 个科室')
    activate_departments.short_description = '激活选中的科室'

    def deactivate_departments(self, request, queryset):
        """批量禁用科室"""
        updated = queryset.update(is_active=False)
        self.message_user(request, f'成功禁用 {updated} 个科室')
    deactivate_departments.short_description = '禁用选中的科室'


class UserProfileInline(admin.StackedInline):
    """
    用户配置文件内联编辑
    """

    model = UserProfile
    fk_name = 'user'
    can_delete = False
    verbose_name = '用户配置文件'
    verbose_name_plural = '用户配置文件'
    
    fieldsets = (
        ('账号信息', {
            'fields': ('account_number', 'department', 'role')
        }),

        ('状态', {
            'fields': ('is_active',)
        }),
        ('登录信息', {
            'fields': ('last_login_ip',),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ['last_login_ip']


class CustomUserAdmin(BaseUserAdmin):
    """
    自定义用户管理后台
    """
    
    inlines = (UserProfileInline,)
    
    list_display = [
        'username', 'get_account_number', 'get_display_name',
        'get_department', 'get_role', 'is_active', 'date_joined'
    ]
    list_filter = [
        'is_active', 'is_staff', 'is_superuser', 'date_joined',
        'profile__role', 'profile__department'
    ]
    search_fields = [
        'username', 'first_name', 'last_name', 'email',
        'profile__account_number', 'profile__phone'
    ]
    
    def get_account_number(self, obj):
        """
        获取账号
        """
        if hasattr(obj, 'profile'):
            return obj.profile.account_number
        return '-'
    
    get_account_number.short_description = '账号'
    get_account_number.admin_order_field = 'profile__account_number'
    
    def get_display_name(self, obj):
        """
        获取显示名称
        """
        return obj.get_full_name() or obj.username
    
    get_display_name.short_description = '姓名'
    
    def get_department(self, obj):
        """
        获取科室
        """
        if hasattr(obj, 'profile') and obj.profile.department:
            return obj.profile.department.name
        return '-'
    
    get_department.short_description = '科室'
    get_department.admin_order_field = 'profile__department__name'
    
    def get_role(self, obj):
        """
        获取角色
        """
        if hasattr(obj, 'profile'):
            return obj.profile.get_role_display()
        return '-'
    
    get_role.short_description = '角色'
    get_role.admin_order_field = 'profile__role'


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    """
    用户配置文件管理后台
    """
    
    list_display = [
        'account_number', 'get_username', 'get_display_name',
        'department', 'role', 'is_active', 'created_at'
    ]
    list_filter = [
        'role', 'is_active', 'department', 'created_at', ActiveUsersFilter
    ]
    search_fields = [
        'account_number', 'user__username', 'user__first_name',
        'user__last_name', 'user__email'
    ]
    ordering = ['account_number']
    list_per_page = 20
    
    fieldsets = (
        ('用户关联', {
            'fields': ('user',)
        }),
        ('账号信息', {
            'fields': ('account_number', 'department', 'role')
        }),
        ('联系信息', {
            'fields': ('phone',)
        }),
        ('状态', {
            'fields': ('is_active',)
        }),
        ('登录信息', {
            'fields': ('last_login_ip',),
            'classes': ('collapse',)
        }),
        ('审计信息', {
            'fields': ('created_at', 'updated_at', 'created_by', 'updated_by'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = [
        'created_at', 'updated_at', 'created_by', 'updated_by', 'last_login_ip'
    ]
    
    def get_username(self, obj):
        """
        获取用户名
        """
        return obj.user.username
    
    get_username.short_description = '用户名'
    get_username.admin_order_field = 'user__username'
    
    def get_display_name(self, obj):
        """
        获取显示名称
        """
        return obj.display_name
    
    get_display_name.short_description = '姓名'
    
    def save_model(self, request, obj, form, change):
        """
        保存时设置创建者/更新者
        """
        if not change:
            obj.created_by = request.user
        obj.updated_by = request.user
        super().save_model(request, obj, form, change)

    actions = ['activate_users', 'deactivate_users', 'reset_login_info']

    def activate_users(self, request, queryset):
        """批量激活用户"""
        updated = 0
        for profile in queryset:
            if not profile.is_active:
                profile.is_active = True
                profile.user.is_active = True
                profile.save()
                profile.user.save()
                updated += 1
        self.message_user(request, f'成功激活 {updated} 个用户')
    activate_users.short_description = '激活选中的用户'

    def deactivate_users(self, request, queryset):
        """批量禁用用户"""
        updated = 0
        for profile in queryset:
            if profile.is_active:
                profile.is_active = False
                profile.user.is_active = False
                profile.save()
                profile.user.save()
                updated += 1
        self.message_user(request, f'成功禁用 {updated} 个用户')
    deactivate_users.short_description = '禁用选中的用户'

    def reset_login_info(self, request, queryset):
        """重置登录信息"""
        updated = queryset.update(last_login_ip=None)
        self.message_user(request, f'成功重置 {updated} 个用户的登录信息')
    reset_login_info.short_description = '重置登录信息'


# 重新注册User模型
admin.site.unregister(User)
admin.site.register(User, CustomUserAdmin)
