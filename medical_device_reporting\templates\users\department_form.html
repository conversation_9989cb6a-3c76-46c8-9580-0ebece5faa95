{% extends 'base.html' %}
{% load static %}

{% block title %}
{% if is_edit %}编辑科室{% else %}新建科室{% endif %} - 医疗器械不良事件上报平台
{% endblock %}

{% block extra_css %}
<link href="{% static 'users/css/user_management.css' %}" rel="stylesheet">
{% endblock %}

{% block nav_items %}
<li class="nav-item">
    <a class="nav-link" href="{% url 'users:dashboard' %}">
        <i class="bi bi-house"></i>
        用户中心
    </a>
</li>
{% if user.profile.is_admin %}
<li class="nav-item">
    <a class="nav-link" href="{% url 'users:user_list' %}">
        <i class="bi bi-people"></i>
        用户管理
    </a>
</li>
<li class="nav-item">
    <a class="nav-link" href="{% url 'users:department_list' %}">
        <i class="bi bi-building"></i>
        科室管理
    </a>
</li>
{% endif %}
<li class="nav-item dropdown">
    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
        <i class="bi bi-person-circle"></i>
        {{ user.get_full_name|default:user.username }}
    </a>
    <ul class="dropdown-menu">
        <li><a class="dropdown-item" href="#"><i class="bi bi-gear me-2"></i>个人设置</a></li>
        <li><hr class="dropdown-divider"></li>
        <li><a class="dropdown-item" href="{% url 'users:logout' %}"><i class="bi bi-box-arrow-right me-2"></i>退出登录</a></li>
    </ul>
</li>
{% endblock %}

{% block content %}
<div class="department-form-container">
    <!-- 页面标题 -->
    <div class="page-header mb-4">
        <div class="row align-items-center">
            <div class="col">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'users:dashboard' %}">用户中心</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'users:department_list' %}">科室管理</a></li>
                        <li class="breadcrumb-item active">
                            {% if is_edit %}编辑科室{% else %}新建科室{% endif %}
                        </li>
                    </ol>
                </nav>
                <h2 class="page-title">
                    {% if is_edit %}
                        <i class="bi bi-building-gear me-2"></i>
                        编辑科室
                    {% else %}
                        <i class="bi bi-building-add me-2"></i>
                        新建科室
                    {% endif %}
                </h2>
                {% if is_edit %}
                <p class="page-subtitle text-muted">编辑科室 {{ department.code }} - {{ department.name }} 的信息</p>
                {% else %}
                <p class="page-subtitle text-muted">创建新的医院科室</p>
                {% endif %}
            </div>
            <div class="col-auto">
                <a href="{% url 'users:department_list' %}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left me-2"></i>
                    返回列表
                </a>
            </div>
        </div>
    </div>

    <!-- 科室表单 -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-building-check me-2"></i>
                        科室信息
                    </h6>
                </div>
                <div class="card-body">
                    <!-- 错误消息显示 -->
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {% if message.tags == 'error' %}
                                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                {% elif message.tags == 'success' %}
                                    <i class="bi bi-check-circle-fill me-2"></i>
                                {% elif message.tags == 'warning' %}
                                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                {% else %}
                                    <i class="bi bi-info-circle-fill me-2"></i>
                                {% endif %}
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <form method="post" class="needs-validation" novalidate id="departmentForm">
                        {% csrf_token %}
                        
                        <div class="row">
                            <!-- 基本信息 -->
                            <div class="col-md-6">
                                <h6 class="form-section-title">基本信息</h6>
                                
                                <!-- 科室代码 -->
                                <div class="mb-3">
                                    <label for="code" class="form-label required">
                                        <i class="bi bi-hash me-1"></i>
                                        科室代码
                                    </label>
                                    <input 
                                        type="text" 
                                        class="form-control" 
                                        id="code" 
                                        name="code" 
                                        value="{% if is_edit %}{{ department.code }}{% endif %}"
                                        maxlength="20" 
                                        required
                                        {% if is_edit %}readonly{% endif %}
                                        placeholder="请输入科室代码（如：ICU、ER等）"
                                    >
                                    <div class="invalid-feedback">
                                        请输入科室代码
                                    </div>
                                    {% if is_edit %}
                                    <div class="form-text">
                                        <i class="bi bi-info-circle me-1"></i>
                                        科室代码创建后不可修改
                                    </div>
                                    {% else %}
                                    <div class="form-text">
                                        <i class="bi bi-info-circle me-1"></i>
                                        科室代码应简洁明了，便于识别
                                    </div>
                                    {% endif %}
                                </div>

                                <!-- 科室名称 -->
                                <div class="mb-3">
                                    <label for="name" class="form-label required">
                                        <i class="bi bi-building me-1"></i>
                                        科室名称
                                    </label>
                                    <input 
                                        type="text" 
                                        class="form-control" 
                                        id="name" 
                                        name="name" 
                                        value="{% if is_edit %}{{ department.name }}{% endif %}"
                                        maxlength="100"
                                        required
                                        placeholder="请输入科室全称"
                                    >
                                    <div class="invalid-feedback">
                                        请输入科室名称
                                    </div>
                                    <div class="form-text">
                                        <i class="bi bi-info-circle me-1"></i>
                                        请输入科室的完整名称
                                    </div>
                                </div>
                            </div>

                            <!-- 状态设置 -->
                            <div class="col-md-6">
                                <h6 class="form-section-title">状态设置</h6>
                                
                                <!-- 启用状态 -->
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-toggle-on me-1"></i>
                                        科室状态
                                    </label>
                                    <div class="form-check form-switch">
                                        <input 
                                            class="form-check-input" 
                                            type="checkbox" 
                                            id="is_active" 
                                            name="is_active"
                                            {% if not is_edit or department.is_active %}checked{% endif %}
                                        >
                                        <label class="form-check-label" for="is_active">
                                            启用科室
                                        </label>
                                    </div>
                                    <div class="form-text">
                                        <i class="bi bi-info-circle me-1"></i>
                                        禁用的科室无法分配新用户
                                    </div>
                                </div>

                                {% if is_edit %}
                                <!-- 统计信息 -->
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-bar-chart me-1"></i>
                                        科室统计
                                    </label>
                                    <div class="row g-2">
                                        <div class="col-6">
                                            <div class="card bg-light">
                                                <div class="card-body text-center py-2">
                                                    <h6 class="card-title mb-1">总用户数</h6>
                                                    <h4 class="text-primary mb-0">{{ department.total_users|default:0 }}</h4>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="card bg-light">
                                                <div class="card-body text-center py-2">
                                                    <h6 class="card-title mb-1">活跃用户</h6>
                                                    <h4 class="text-success mb-0">{{ department.active_users|default:0 }}</h4>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 创建信息 -->
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-clock-history me-1"></i>
                                        创建信息
                                    </label>
                                    <div class="form-control-plaintext">
                                        <small class="text-muted">
                                            创建时间：{{ department.created_at|date:"Y-m-d H:i" }}<br>
                                            {% if department.created_by %}
                                            创建人：{{ department.created_by.get_full_name|default:department.created_by.username }}<br>
                                            {% endif %}
                                            {% if department.updated_at %}
                                            更新时间：{{ department.updated_at|date:"Y-m-d H:i" }}<br>
                                            {% endif %}
                                            {% if department.updated_by %}
                                            更新人：{{ department.updated_by.get_full_name|default:department.updated_by.username }}
                                            {% endif %}
                                        </small>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- 表单按钮 -->
                        <div class="form-actions mt-4 pt-3 border-top">
                            <div class="row">
                                <div class="col">
                                    <a href="{% url 'users:department_list' %}" class="btn btn-outline-secondary">
                                        <i class="bi bi-x-circle me-2"></i>
                                        取消
                                    </a>
                                    {% if is_edit %}
                                    <a href="{% url 'users:department_detail' department.id %}" class="btn btn-outline-info ms-2">
                                        <i class="bi bi-eye me-2"></i>
                                        查看详情
                                    </a>
                                    {% endif %}
                                </div>
                                <div class="col-auto">
                                    <button type="submit" class="btn btn-primary" id="submitBtn">
                                        <span class="btn-text">
                                            <i class="bi bi-check-circle me-2"></i>
                                            {% if is_edit %}更新科室{% else %}创建科室{% endif %}
                                        </span>
                                        <span class="btn-loading d-none">
                                            <span class="spinner-border spinner-border-sm me-2"></span>
                                            {% if is_edit %}更新中...{% else %}创建中...{% endif %}
                                        </span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            {% if is_edit %}
            <!-- 危险操作区域 -->
            <div class="card mt-4 border-danger">
                <div class="card-header bg-danger text-white">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        危险操作
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h6 class="mb-1">删除科室</h6>
                            <p class="text-muted mb-0">
                                删除科室将影响该科室下的所有用户，此操作不可恢复。
                            </p>
                        </div>
                        <div class="col-auto">
                            <button type="button" class="btn btn-outline-danger" id="deleteDeptBtn" 
                                    data-dept-id="{{ department.id }}" 
                                    data-dept-name="{{ department.name }}">
                                <i class="bi bi-trash me-2"></i>
                                删除科室
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- 确认删除模态框 -->
<div class="modal fade" id="confirmDeleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-exclamation-triangle text-danger me-2"></i>
                    确认删除科室
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <strong>警告：</strong>此操作不可恢复！
                </div>
                <p>确定要删除科室 <strong id="deleteDeptName"></strong> 吗？</p>
                <p class="text-muted">删除科室将影响该科室下的所有用户，请确保已妥善处理相关用户。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="bi bi-trash me-2"></i>
                    确定删除
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'users/js/department_form.js' %}"></script>
{% endblock %}
