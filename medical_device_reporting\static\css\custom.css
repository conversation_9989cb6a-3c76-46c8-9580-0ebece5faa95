/* 
 * Custom CSS for Medical Device Reporting Platform
 * 医疗器械不良事件上报平台自定义样式
 */

/* 全局样式 */
body {
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    background-color: #f8f9fa;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: 600;
    font-size: 1.2rem;
}

.navbar-brand i {
    margin-right: 0.5rem;
}

/* 主要内容区域 */
main {
    min-height: calc(100vh - 200px);
    padding-top: 2rem;
    padding-bottom: 2rem;
}

/* 卡片样式 */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
}

.card-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    border-radius: 0.5rem 0.5rem 0 0 !important;
}

/* 按钮样式 */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

/* 表单样式 */
.form-control {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
}

.form-control:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* 表格样式 */
.table {
    border-radius: 0.5rem;
    overflow: hidden;
}

.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
}

/* 页脚样式 */
footer {
    border-top: 1px solid #dee2e6;
    margin-top: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1rem;
    }
    
    main {
        padding-top: 1rem;
        padding-bottom: 1rem;
    }
    
    .card {
        margin: 0.5rem;
    }
}

/* 工具类 */
.text-primary-custom {
    color: #0d6efd !important;
}

.bg-primary-custom {
    background-color: #0d6efd !important;
}

.border-primary-custom {
    border-color: #0d6efd !important;
}

/* 加载动画 */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* 消息提示样式 */
.alert {
    border-radius: 0.5rem;
    border: none;
}

.alert-success {
    background-color: #d1e7dd;
    color: #0f5132;
}

.alert-danger {
    background-color: #f8d7da;
    color: #842029;
}

.alert-warning {
    background-color: #fff3cd;
    color: #664d03;
}

.alert-info {
    background-color: #d1ecf1;
    color: #055160;
}
