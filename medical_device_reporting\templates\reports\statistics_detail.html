{% extends 'reports/base.html' %}
{% load static %}

{% block page_title %}{{ analysis_title }}{% endblock %}
{% block page_heading %}{{ analysis_title }}{% endblock %}
{% block page_description %}详细的数据分析和可视化展示{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'reports:statistics_dashboard' %}">统计分析</a></li>
<li class="breadcrumb-item active">{{ analysis_title }}</li>
{% endblock %}

{% block page_actions %}
<div class="btn-group" role="group">
    <a href="{% url 'reports:statistics_dashboard' %}" class="btn btn-outline-secondary btn-sm">
        <i class="bi bi-arrow-left me-1"></i>
        返回仪表板
    </a>
    <div class="btn-group" role="group">
        <button type="button" class="btn btn-outline-primary btn-sm dropdown-toggle" data-bs-toggle="dropdown">
            <i class="bi bi-download me-1"></i>
            导出详细数据
        </button>
        <ul class="dropdown-menu">
            <li><a class="dropdown-item" href="#" onclick="exportDetailData('excel')">
                <i class="bi bi-file-earmark-excel me-2"></i>导出Excel
            </a></li>
            <li><a class="dropdown-item" href="#" onclick="exportDetailData('pdf')">
                <i class="bi bi-file-earmark-pdf me-2"></i>导出PDF
            </a></li>
        </ul>
    </div>
    <button type="button" class="btn btn-outline-info btn-sm" onclick="refreshAnalysis()">
        <i class="bi bi-arrow-clockwise me-1"></i>
        刷新分析
    </button>
</div>
{% endblock %}

{% block reports_content %}
<!-- 分析类型和筛选控件 -->
<div class="filter-controls">
    <form method="get" id="analysisForm">
        <div class="row">
            <div class="col-md-3 mb-3">
                <label for="analysis_type" class="form-label">分析类型</label>
                <select class="form-select" id="analysis_type" name="type">
                    {% for value, label in filter_options.analysis_types %}
                    <option value="{{ value }}" {% if current_filters.type == value %}selected{% endif %}>
                        {{ label }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            
            {% if current_filters.type == 'time_series' or current_filters.type == 'trend_analysis' %}
            <div class="col-md-2 mb-3">
                <label for="granularity" class="form-label">时间粒度</label>
                <select class="form-select" id="granularity" name="granularity">
                    {% for value, label in filter_options.granularities %}
                    <option value="{{ value }}" {% if current_filters.granularity == value %}selected{% endif %}>
                        {{ label }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            {% endif %}
            
            {% if current_filters.type == 'cross_dimension' %}
            <div class="col-md-2 mb-3">
                <label for="dimension1" class="form-label">维度1</label>
                <select class="form-select" id="dimension1" name="dimension1">
                    {% for value, label in filter_options.dimensions %}
                    <option value="{{ value }}" {% if current_filters.dimension1 == value %}selected{% endif %}>
                        {{ label }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2 mb-3">
                <label for="dimension2" class="form-label">维度2</label>
                <select class="form-select" id="dimension2" name="dimension2">
                    {% for value, label in filter_options.dimensions %}
                    <option value="{{ value }}" {% if current_filters.dimension2 == value %}selected{% endif %}>
                        {{ label }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            {% endif %}
            
            {% if current_filters.type == 'trend_analysis' %}
            <div class="col-md-2 mb-3">
                <label for="metric" class="form-label">分析指标</label>
                <select class="form-select" id="metric" name="metric">
                    {% for value, label in filter_options.metrics %}
                    <option value="{{ value }}" {% if current_filters.metric == value %}selected{% endif %}>
                        {{ label }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            {% endif %}
            
            <div class="col-md-2 mb-3">
                <label for="start_date" class="form-label">开始日期</label>
                <input type="date" class="form-control" id="start_date" name="start_date" 
                       value="{{ current_filters.start_date|default:'' }}">
            </div>
            <div class="col-md-2 mb-3">
                <label for="end_date" class="form-label">结束日期</label>
                <input type="date" class="form-control" id="end_date" name="end_date" 
                       value="{{ current_filters.end_date|default:'' }}">
            </div>
            <div class="col-md-1 mb-3">
                <label class="form-label">&nbsp;</label>
                <button type="submit" class="btn btn-primary w-100">
                    <i class="bi bi-search"></i>
                </button>
            </div>
        </div>
    </form>
</div>

<!-- 分析结果展示 -->
{% if current_filters.type == 'time_series' %}
    <!-- 时间序列分析 -->
    <div class="row">
        <div class="col-lg-8 mb-4">
            <div class="statistics-card">
                <h5 class="card-title">
                    <i class="bi bi-graph-up"></i>
                    时间序列图表
                </h5>
                <div class="chart-container large">
                    <canvas id="timeSeriesDetailChart" style="display: none;"></canvas>
                    <div class="chart-loading">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载图表数据...</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4 mb-4">
            <div class="statistics-card">
                <h5 class="card-title">
                    <i class="bi bi-table"></i>
                    数据详情
                </h5>
                <div class="data-table-container">
                    <table class="table data-table">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>总数</th>
                                <th>严重事件</th>
                                <th>比例</th>
                            </tr>
                        </thead>
                        <tbody id="timeSeriesTableBody">
                            <!-- 数据将通过JavaScript填充 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

{% elif current_filters.type == 'cross_dimension' %}
    <!-- 交叉维度分析 -->
    <div class="row">
        <div class="col-lg-8 mb-4">
            <div class="statistics-card">
                <h5 class="card-title">
                    <i class="bi bi-diagram-3"></i>
                    交叉维度热力图
                </h5>
                <div class="chart-container large">
                    <canvas id="crossDimensionDetailChart" style="display: none;"></canvas>
                    <div class="chart-loading">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载图表数据...</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4 mb-4">
            <div class="statistics-card">
                <h5 class="card-title">
                    <i class="bi bi-bar-chart"></i>
                    维度统计
                </h5>
                <div class="row">
                    <div class="col-12 mb-3">
                        <h6>{{ current_filters.dimension1_name }}统计</h6>
                        <div id="dimension1Stats">
                            <!-- 数据将通过JavaScript填充 -->
                        </div>
                    </div>
                    <div class="col-12">
                        <h6>{{ current_filters.dimension2_name }}统计</h6>
                        <div id="dimension2Stats">
                            <!-- 数据将通过JavaScript填充 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

{% elif current_filters.type == 'device_stats' %}
    <!-- 器械统计分析 -->
    <div class="row">
        <div class="col-lg-6 mb-4">
            <div class="statistics-card">
                <h5 class="card-title">
                    <i class="bi bi-gear"></i>
                    器械报告数量
                </h5>
                <div class="chart-container">
                    <canvas id="deviceCountChart" style="display: none;"></canvas>
                    <div class="chart-loading">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载图表数据...</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-6 mb-4">
            <div class="statistics-card">
                <h5 class="card-title">
                    <i class="bi bi-exclamation-triangle"></i>
                    器械风险分析
                </h5>
                <div class="chart-container">
                    <canvas id="deviceRiskChart" style="display: none;"></canvas>
                    <div class="chart-loading">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载图表数据...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

{% elif current_filters.type == 'department_stats' and is_admin %}
    <!-- 科室统计分析 -->
    <div class="row">
        <div class="col-lg-8 mb-4">
            <div class="statistics-card">
                <h5 class="card-title">
                    <i class="bi bi-building"></i>
                    科室报告统计
                </h5>
                <div class="chart-container large">
                    <canvas id="departmentDetailChart" style="display: none;"></canvas>
                    <div class="chart-loading">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载图表数据...</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4 mb-4">
            <div class="statistics-card">
                <h5 class="card-title">
                    <i class="bi bi-speedometer2"></i>
                    效率分析
                </h5>
                <div id="departmentEfficiency">
                    <!-- 数据将通过JavaScript填充 -->
                </div>
            </div>
        </div>
    </div>

{% elif current_filters.type == 'trend_analysis' %}
    <!-- 趋势分析 -->
    <div class="row">
        <div class="col-lg-9 mb-4">
            <div class="statistics-card">
                <h5 class="card-title">
                    <i class="bi bi-trending-up"></i>
                    趋势分析图表
                </h5>
                <div class="chart-container large">
                    <canvas id="trendAnalysisChart" style="display: none;"></canvas>
                    <div class="chart-loading">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载图表数据...</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 mb-4">
            <div class="statistics-card">
                <h5 class="card-title">
                    <i class="bi bi-info-circle"></i>
                    趋势指标
                </h5>
                <div id="trendMetrics">
                    <!-- 数据将通过JavaScript填充 -->
                </div>
            </div>
        </div>
    </div>
{% endif %}

<!-- 详细数据表格 -->
<div class="row">
    <div class="col-12">
        <div class="statistics-card">
            <h5 class="card-title">
                <i class="bi bi-table"></i>
                详细数据表格
            </h5>
            <div class="data-table-container" style="max-height: 500px;">
                <table class="table data-table" id="detailDataTable">
                    <thead id="detailTableHead">
                        <!-- 表头将通过JavaScript生成 -->
                    </thead>
                    <tbody id="detailTableBody">
                        <!-- 数据将通过JavaScript填充 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block reports_extra_js %}
<!-- Chart.js CDN -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="{% static 'reports/js/statistics_charts.js' %}"></script>

<script>
// 页面加载完成后初始化分析
document.addEventListener('DOMContentLoaded', function() {
    console.log('=== 详细分析页面JavaScript开始执行 ===');

    // 传递分析数据到JavaScript
    try {
        window.analysisData = {{ analysis_data_json|safe }};
        window.analysisType = '{{ current_filters.type }}';
        window.isAdmin = {{ is_admin|yesno:"true,false" }};

        console.log('分析数据已设置:', window.analysisData);
        console.log('分析类型:', window.analysisType);
        console.log('管理员权限:', window.isAdmin);
    } catch (error) {
        console.error('设置分析数据时发生错误:', error);
        console.log('原始analysis_data:', '{{ analysis_data|escapejs }}');
    }

    // 初始化详细分析
    if (typeof StatisticsCharts !== 'undefined') {
        console.log('开始初始化详细分析...');
        StatisticsCharts.initDetailAnalysis();
    } else {
        console.error('StatisticsCharts未定义');
    }
});

// 分析类型改变时自动提交
document.getElementById('analysis_type').addEventListener('change', function() {
    document.getElementById('analysisForm').submit();
});

// 刷新分析
function refreshAnalysis() {
    location.reload();
}

// 导出详细数据
function exportDetailData() {
    // TODO: 实现详细数据导出功能
    alert('详细数据导出功能开发中...');
}
</script>
{% endblock %}
