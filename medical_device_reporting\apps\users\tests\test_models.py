"""
用户管理模型测试
User Management Models Tests
"""

from django.test import TestCase
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.db import IntegrityError

from apps.users.models import UserProfile, Department


class DepartmentModelTest(TestCase):
    """科室模型测试"""
    
    def setUp(self):
        """测试前准备"""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>'
        )
    
    def test_department_creation(self):
        """测试科室创建"""
        department = Department.objects.create(
            code='TEST',
            name='测试科室',
            is_active=True,
            created_by=self.user
        )

        self.assertEqual(department.code, 'TEST')
        self.assertEqual(department.name, '测试科室')
        self.assertTrue(department.is_active)
        self.assertEqual(department.created_by, self.user)
    
    def test_department_str_representation(self):
        """测试科室字符串表示"""
        department = Department.objects.create(
            code='TEST',
            name='测试科室',
            created_by=self.user
        )
        
        self.assertEqual(str(department), 'TEST - 测试科室')
    
    def test_department_code_unique(self):
        """测试科室代码唯一性"""
        Department.objects.create(
            code='TEST',
            name='测试科室1',
            created_by=self.user
        )

        with self.assertRaises(ValidationError):
            Department.objects.create(
                code='TEST',
                name='测试科室2',
                created_by=self.user
            )
    
    def test_department_code_validation(self):
        """测试科室代码验证"""
        # 测试空代码
        department = Department(
            code='',
            name='测试科室',
            created_by=self.user
        )
        
        with self.assertRaises(ValidationError):
            department.full_clean()
        
        # 测试过长代码
        department = Department(
            code='A' * 21,  # 超过20字符
            name='测试科室',
            created_by=self.user
        )
        
        with self.assertRaises(ValidationError):
            department.full_clean()


class UserProfileModelTest(TestCase):
    """用户配置文件模型测试"""
    
    def setUp(self):
        """测试前准备"""
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>'
        )
        
        self.test_user = User.objects.create_user(
            username='testuser',
            first_name='测试',
            last_name='用户',
            email='<EMAIL>'
        )
        
        self.department = Department.objects.create(
            code='TEST',
            name='测试科室',
            created_by=self.admin_user
        )
    
    def test_user_profile_creation(self):
        """测试用户配置文件创建"""
        profile = UserProfile.objects.create(
            user=self.test_user,
            account_number='1001',
            department=self.department,
            role='staff',
            is_active=True,
            created_by=self.admin_user
        )
        
        self.assertEqual(profile.user, self.test_user)
        self.assertEqual(profile.account_number, '1001')
        self.assertEqual(profile.department, self.department)
        self.assertEqual(profile.role, 'staff')
        self.assertTrue(profile.is_active)
    
    def test_user_profile_str_representation(self):
        """测试用户配置文件字符串表示"""
        profile = UserProfile.objects.create(
            user=self.test_user,
            account_number='1001',
            department=self.department,
            role='staff',
            created_by=self.admin_user
        )
        
        expected = f'1001 - {self.test_user.get_full_name()}'
        self.assertEqual(str(profile), expected)
    
    def test_account_number_unique(self):
        """测试账号唯一性"""
        UserProfile.objects.create(
            user=self.test_user,
            account_number='1001',
            department=self.department,
            role='staff',
            created_by=self.admin_user
        )
        
        # 创建另一个用户
        another_user = User.objects.create_user(
            username='another',
            email='<EMAIL>'
        )
        
        with self.assertRaises(ValidationError):
            UserProfile.objects.create(
                user=another_user,
                account_number='1001',  # 重复账号
                department=self.department,
                role='staff',
                created_by=self.admin_user
            )
    
    def test_account_number_validation(self):
        """测试账号验证"""
        # 测试空账号
        profile = UserProfile(
            user=self.test_user,
            account_number='',
            department=self.department,
            role='staff',
            created_by=self.admin_user
        )
        
        with self.assertRaises(ValidationError):
            profile.full_clean()
        
        # 测试非4位数账号
        profile = UserProfile(
            user=self.test_user,
            account_number='12345',  # 5位数
            department=self.department,
            role='staff',
            created_by=self.admin_user
        )
        
        with self.assertRaises(ValidationError):
            profile.full_clean()
    
    def test_display_name_property(self):
        """测试显示名称属性"""
        profile = UserProfile.objects.create(
            user=self.test_user,
            account_number='1001',
            department=self.department,
            role='staff',
            created_by=self.admin_user
        )
        
        # 有姓名时返回姓名
        expected = self.test_user.get_full_name()
        self.assertEqual(profile.display_name, expected)
        
        # 无姓名时返回用户名
        self.test_user.first_name = ''
        self.test_user.last_name = ''
        self.test_user.save()
        
        profile.refresh_from_db()
        self.assertEqual(profile.display_name, self.test_user.username)
    
    def test_is_admin_property(self):
        """测试管理员属性"""
        # 测试管理员角色
        admin_profile = UserProfile.objects.create(
            user=self.test_user,
            account_number='0001',
            role='admin',
            created_by=self.admin_user
        )
        
        self.assertTrue(admin_profile.is_admin)
        
        # 测试非管理员角色
        staff_profile = UserProfile.objects.create(
            user=User.objects.create_user(username='staff'),
            account_number='1001',
            department=self.department,
            role='staff',
            created_by=self.admin_user
        )
        
        self.assertFalse(staff_profile.is_admin)
    
    def test_role_choices(self):
        """测试角色选择"""
        # 测试管理员角色（不需要科室）
        admin_profile = UserProfile(
            user=self.test_user,
            account_number='0001',
            role='admin',
            created_by=self.admin_user
        )

        # 应该不抛出异常
        admin_profile.full_clean()

        # 测试科室人员角色（需要科室）
        staff_profile = UserProfile(
            user=User.objects.create_user(username='staff_test'),
            account_number='1001',
            department=self.department,
            role='staff',
            created_by=self.admin_user
        )

        # 应该不抛出异常
        staff_profile.full_clean()
    
    def test_staff_requires_department(self):
        """测试科室人员必须有科室"""
        # 科室人员没有科室应该抛出验证错误
        with self.assertRaises(ValidationError):
            UserProfile.objects.create(
                user=self.test_user,
                account_number='1001',
                department=None,  # 无科室
                role='staff',
                created_by=self.admin_user
            )
    
    def test_soft_delete(self):
        """测试软删除"""
        profile = UserProfile.objects.create(
            user=self.test_user,
            account_number='1001',
            department=self.department,
            role='staff',
            created_by=self.admin_user
        )
        
        # 软删除
        profile.is_deleted = True
        profile.save()
        
        # 仍然存在于数据库中
        self.assertTrue(UserProfile.objects.filter(id=profile.id).exists())
        
        # 但被标记为已删除
        profile.refresh_from_db()
        self.assertTrue(profile.is_deleted)
