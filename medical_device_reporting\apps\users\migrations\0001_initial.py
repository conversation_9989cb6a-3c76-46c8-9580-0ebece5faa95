# Generated by Django 4.2.23 on 2025-06-20 01:33

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("auth", "0012_alter_user_first_name_max_length"),
    ]

    operations = [
        migrations.CreateModel(
            name="Department",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True,
                        help_text="记录创建的时间",
                        verbose_name="创建时间",
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True,
                        help_text="记录最后更新的时间",
                        verbose_name="更新时间",
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(
                        default=False,
                        help_text="软删除标记，True表示已删除",
                        verbose_name="是否删除",
                    ),
                ),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="记录删除的时间",
                        null=True,
                        verbose_name="删除时间",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="科室的完整名称",
                        max_length=100,
                        unique=True,
                        verbose_name="科室名称",
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        help_text="科室的唯一标识代码",
                        max_length=20,
                        unique=True,
                        verbose_name="科室代码",
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        help_text="科室的详细描述信息",
                        verbose_name="科室描述",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="科室是否处于启用状态",
                        verbose_name="是否启用",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="创建者",
                    ),
                ),
                (
                    "parent",
                    models.ForeignKey(
                        blank=True,
                        help_text="上级科室，支持科室层级结构",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="children",
                        to="users.department",
                        verbose_name="上级科室",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="最后修改者",
                    ),
                ),
            ],
            options={
                "verbose_name": "科室",
                "verbose_name_plural": "科室",
                "db_table": "users_department",
                "ordering": ["code", "name"],
            },
        ),
        migrations.CreateModel(
            name="UserRole",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True,
                        help_text="记录创建的时间",
                        verbose_name="创建时间",
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True,
                        help_text="记录最后更新的时间",
                        verbose_name="更新时间",
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(
                        default=False,
                        help_text="软删除标记，True表示已删除",
                        verbose_name="是否删除",
                    ),
                ),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="记录删除的时间",
                        null=True,
                        verbose_name="删除时间",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="角色的名称",
                        max_length=50,
                        unique=True,
                        verbose_name="角色名称",
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        help_text="角色的唯一标识代码",
                        max_length=20,
                        unique=True,
                        verbose_name="角色代码",
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True, help_text="角色的详细描述", verbose_name="角色描述"
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="角色是否处于启用状态",
                        verbose_name="是否启用",
                    ),
                ),
                (
                    "is_system_role",
                    models.BooleanField(
                        default=False,
                        help_text="系统内置角色不可删除",
                        verbose_name="是否系统角色",
                    ),
                ),
                (
                    "permissions",
                    models.ManyToManyField(
                        blank=True,
                        help_text="角色拥有的权限",
                        to="auth.permission",
                        verbose_name="权限",
                    ),
                ),
            ],
            options={
                "verbose_name": "用户角色",
                "verbose_name_plural": "用户角色",
                "db_table": "users_userrole",
                "ordering": ["code", "name"],
                "indexes": [
                    models.Index(fields=["code"], name="users_userr_code_1672f8_idx"),
                    models.Index(fields=["name"], name="users_userr_name_1e79c2_idx"),
                    models.Index(
                        fields=["is_active"], name="users_userr_is_acti_e1ec1a_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="UserProfile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True,
                        help_text="记录创建的时间",
                        verbose_name="创建时间",
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True,
                        help_text="记录最后更新的时间",
                        verbose_name="更新时间",
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(
                        default=False,
                        help_text="软删除标记，True表示已删除",
                        verbose_name="是否删除",
                    ),
                ),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="记录删除的时间",
                        null=True,
                        verbose_name="删除时间",
                    ),
                ),
                (
                    "account_number",
                    models.CharField(
                        help_text="4位数登录账号",
                        max_length=4,
                        unique=True,
                        verbose_name="账号",
                    ),
                ),
                (
                    "role",
                    models.CharField(
                        choices=[("admin", "管理员"), ("staff", "科室人员")],
                        default="staff",
                        help_text="用户在系统中的角色",
                        max_length=20,
                        verbose_name="用户角色",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="用户账号是否处于启用状态",
                        verbose_name="是否启用",
                    ),
                ),
                (
                    "phone",
                    models.CharField(
                        blank=True,
                        help_text="用户的联系电话",
                        max_length=20,
                        verbose_name="联系电话",
                    ),
                ),
                (
                    "last_login_ip",
                    models.GenericIPAddressField(
                        blank=True,
                        help_text="用户最后一次登录的IP地址",
                        null=True,
                        verbose_name="最后登录IP",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="创建者",
                    ),
                ),
                (
                    "department",
                    models.ForeignKey(
                        blank=True,
                        help_text="用户所属的科室",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="users",
                        to="users.department",
                        verbose_name="所属科室",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="最后修改者",
                    ),
                ),
                (
                    "user",
                    models.OneToOneField(
                        help_text="关联的Django用户对象",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="profile",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "用户配置文件",
                "verbose_name_plural": "用户配置文件",
                "db_table": "users_userprofile",
                "ordering": ["account_number"],
                "permissions": [
                    ("can_manage_users", "可以管理用户"),
                    ("can_view_all_users", "可以查看所有用户"),
                    ("can_assign_roles", "可以分配角色"),
                ],
                "indexes": [
                    models.Index(
                        fields=["account_number"], name="users_userp_account_a16dc9_idx"
                    ),
                    models.Index(fields=["role"], name="users_userp_role_c31a7e_idx"),
                    models.Index(
                        fields=["is_active"], name="users_userp_is_acti_32460c_idx"
                    ),
                    models.Index(
                        fields=["department"], name="users_userp_departm_f3b8be_idx"
                    ),
                ],
            },
        ),
        migrations.AddIndex(
            model_name="department",
            index=models.Index(fields=["code"], name="users_depar_code_872430_idx"),
        ),
        migrations.AddIndex(
            model_name="department",
            index=models.Index(fields=["name"], name="users_depar_name_30ebb2_idx"),
        ),
        migrations.AddIndex(
            model_name="department",
            index=models.Index(
                fields=["is_active"], name="users_depar_is_acti_1b6176_idx"
            ),
        ),
    ]
