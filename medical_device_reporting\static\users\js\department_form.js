/**
 * 科室表单页面JavaScript
 * Department Form Page JavaScript
 */

$(document).ready(function() {
    // 初始化表单验证
    initializeFormValidation();
    
    // 初始化事件监听器
    initializeEventListeners();
    
    // 初始化删除功能（编辑模式）
    if ($('#deleteDeptBtn').length > 0) {
        initializeDeleteFunction();
    }
});

/**
 * 初始化表单验证
 */
function initializeFormValidation() {
    const form = $('#departmentForm')[0];
    
    if (!form) return;
    
    // 自定义验证规则
    addCustomValidationRules();
    
    // 表单提交事件
    $(form).on('submit', function(event) {
        event.preventDefault();
        
        if (form.checkValidity()) {
            handleFormSubmit();
        } else {
            // 显示验证错误
            form.classList.add('was-validated');
            
            // 滚动到第一个错误字段
            const firstInvalidField = form.querySelector(':invalid');
            if (firstInvalidField) {
                firstInvalidField.scrollIntoView({ behavior: 'smooth', block: 'center' });
                firstInvalidField.focus();
            }
        }
    });
    
    // 实时验证
    $('#code, #name').on('input', function() {
        validateField(this);
    });
}

/**
 * 添加自定义验证规则
 */
function addCustomValidationRules() {
    // 科室代码验证
    const codeInput = $('#code')[0];
    if (codeInput) {
        codeInput.addEventListener('input', function() {
            const value = this.value.trim();
            
            // 检查格式（只允许字母、数字和下划线）
            const pattern = /^[A-Za-z0-9_]+$/;
            if (value && !pattern.test(value)) {
                this.setCustomValidity('科室代码只能包含字母、数字和下划线');
            } else if (value.length > 20) {
                this.setCustomValidity('科室代码不能超过20个字符');
            } else {
                this.setCustomValidity('');
                
                // 检查代码是否已存在（仅在创建模式下）
                if (!$('#departmentForm').data('is-edit') && value.length >= 2) {
                    checkCodeAvailability(value);
                }
            }
        });
    }
    
    // 科室名称验证
    const nameInput = $('#name')[0];
    if (nameInput) {
        nameInput.addEventListener('input', function() {
            const value = this.value.trim();
            
            if (value.length > 100) {
                this.setCustomValidity('科室名称不能超过100个字符');
            } else {
                this.setCustomValidity('');
            }
        });
    }
}

/**
 * 验证单个字段
 */
function validateField(field) {
    const $field = $(field);
    const $feedback = $field.siblings('.invalid-feedback');
    
    if (field.checkValidity()) {
        $field.removeClass('is-invalid').addClass('is-valid');
    } else {
        $field.removeClass('is-valid').addClass('is-invalid');
        
        // 显示自定义错误消息
        if (field.validationMessage) {
            $feedback.text(field.validationMessage);
        }
    }
}

/**
 * 检查科室代码可用性
 */
function checkCodeAvailability(code) {
    // 防抖处理
    clearTimeout(window.codeCheckTimeout);
    window.codeCheckTimeout = setTimeout(() => {
        $.ajax({
            url: '/api/departments/check-code/',
            method: 'GET',
            data: { code: code },
            success: function(response) {
                const codeInput = $('#code')[0];
                if (response.exists) {
                    codeInput.setCustomValidity('该科室代码已存在');
                    validateField(codeInput);
                } else {
                    codeInput.setCustomValidity('');
                    validateField(codeInput);
                }
            },
            error: function() {
                // 忽略检查错误，不影响用户体验
                console.warn('科室代码可用性检查失败');
            }
        });
    }, 500);
}

/**
 * 初始化事件监听器
 */
function initializeEventListeners() {
    // 提交按钮加载状态
    $('#submitBtn').on('click', function() {
        const form = $('#departmentForm')[0];
        if (form.checkValidity()) {
            LoadingIndicator.show(this);
        }
    });
    
    // 取消按钮确认
    $('a[href*="department_list"]').on('click', function(event) {
        if (hasFormChanges()) {
            if (!confirm('您有未保存的更改，确定要离开吗？')) {
                event.preventDefault();
                return false;
            }
        }
    });
    
    // 监听表单变化
    $('#departmentForm input, #departmentForm select').on('change input', function() {
        markFormAsChanged();
    });
    
    // 页面离开前确认
    window.addEventListener('beforeunload', function(event) {
        if (hasFormChanges()) {
            event.preventDefault();
            event.returnValue = '您有未保存的更改，确定要离开吗？';
            return event.returnValue;
        }
    });
}

/**
 * 处理表单提交
 */
function handleFormSubmit() {
    const form = $('#departmentForm');
    const submitBtn = $('#submitBtn');
    
    // 显示加载状态
    LoadingIndicator.show(submitBtn[0]);
    
    // 收集表单数据
    const formData = {
        code: $('#code').val().trim(),
        name: $('#name').val().trim(),
        is_active: $('#is_active').is(':checked')
    };
    
    // 确定请求URL和方法
    const isEdit = form.data('is-edit');
    const deptId = form.data('dept-id');
    const url = isEdit ? `/api/departments/${deptId}/` : '/api/departments/';
    const method = isEdit ? 'PUT' : 'POST';
    
    // 发送AJAX请求
    $.ajax({
        url: url,
        method: method,
        headers: {
            'X-CSRFToken': getCsrfToken(),
            'Content-Type': 'application/json'
        },
        data: JSON.stringify(formData),
        success: function(response) {
            // 标记表单为已保存
            markFormAsSaved();
            
            // 显示成功消息
            const action = isEdit ? '更新' : '创建';
            showSuccessMessage(`科室${action}成功`);
            
            // 延迟跳转到列表页面
            setTimeout(() => {
                window.location.href = '/departments/';
            }, 1500);
        },
        error: function(xhr, status, error) {
            console.error('表单提交失败:', error);
            
            // 处理验证错误
            if (xhr.status === 400 && xhr.responseJSON) {
                handleValidationErrors(xhr.responseJSON);
            } else {
                const action = isEdit ? '更新' : '创建';
                showErrorMessage(`${action}科室失败，请检查输入信息后重试`);
            }
        },
        complete: function() {
            LoadingIndicator.hide(submitBtn[0]);
        }
    });
}

/**
 * 处理验证错误
 */
function handleValidationErrors(errors) {
    // 清除之前的错误状态
    $('.is-invalid').removeClass('is-invalid');
    
    // 显示字段错误
    Object.keys(errors).forEach(field => {
        const $field = $(`#${field}`);
        const $feedback = $field.siblings('.invalid-feedback');
        
        if ($field.length > 0) {
            $field.addClass('is-invalid');
            $feedback.text(errors[field][0] || errors[field]);
        }
    });
    
    // 滚动到第一个错误字段
    const firstErrorField = $('.is-invalid').first();
    if (firstErrorField.length > 0) {
        firstErrorField[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
        firstErrorField.focus();
    }
    
    // 显示通用错误消息
    showErrorMessage('请检查表单中的错误信息');
}

/**
 * 初始化删除功能
 */
function initializeDeleteFunction() {
    $('#deleteDeptBtn').on('click', function() {
        const deptId = $(this).data('dept-id');
        const deptName = $(this).data('dept-name');
        
        // 显示确认对话框
        $('#deleteDeptName').text(deptName);
        $('#confirmDeleteModal').modal('show');
        
        // 绑定确认删除事件
        $('#confirmDeleteBtn').off('click').on('click', function() {
            handleDeleteDepartment(deptId);
        });
    });
}

/**
 * 处理删除科室
 */
function handleDeleteDepartment(deptId) {
    const confirmBtn = $('#confirmDeleteBtn');
    LoadingIndicator.show(confirmBtn[0]);
    
    $.ajax({
        url: `/departments/${deptId}/delete/`,
        method: 'POST',
        headers: {
            'X-CSRFToken': getCsrfToken()
        },
        success: function(response) {
            $('#confirmDeleteModal').modal('hide');
            showSuccessMessage('科室删除成功');
            
            // 延迟跳转到列表页面
            setTimeout(() => {
                window.location.href = '/departments/';
            }, 1500);
        },
        error: function(xhr, status, error) {
            console.error('删除科室失败:', error);
            const errorMsg = xhr.responseJSON?.error || '删除科室失败，请重试';
            showErrorMessage(errorMsg);
        },
        complete: function() {
            LoadingIndicator.hide(confirmBtn[0]);
        }
    });
}

/**
 * 检查表单是否有变化
 */
function hasFormChanges() {
    return $('#departmentForm').data('has-changes') === true;
}

/**
 * 标记表单为已更改
 */
function markFormAsChanged() {
    $('#departmentForm').data('has-changes', true);
}

/**
 * 标记表单为已保存
 */
function markFormAsSaved() {
    $('#departmentForm').data('has-changes', false);
}
