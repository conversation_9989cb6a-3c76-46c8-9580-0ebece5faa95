# 测试文档

## 📋 目录
- [测试概述](#测试概述)
- [测试环境配置](#测试环境配置)
- [测试用例说明](#测试用例说明)
- [运行测试](#运行测试)
- [测试覆盖率](#测试覆盖率)
- [持续集成](#持续集成)

## 🎯 测试概述

### 测试策略
本项目采用多层次的测试策略，确保代码质量和功能可靠性：

1. **单元测试**：测试独立的函数和方法
2. **集成测试**：测试模块间的交互
3. **功能测试**：测试完整的业务流程
4. **界面测试**：测试模板渲染和前端交互
5. **权限测试**：测试访问控制和安全性

### 测试框架
- **Django TestCase**：基于Django的测试框架
- **Django Client**：模拟HTTP请求的测试客户端
- **Coverage.py**：代码覆盖率分析工具
- **Factory Boy**：测试数据生成工具（可选）

## ⚙️ 测试环境配置

### 数据库配置
测试使用SQLite内存数据库，确保测试速度和隔离性：

```python
# settings/test.py
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': ':memory:',
    }
}
```

### 测试设置
```python
# 禁用日志输出
LOGGING_CONFIG = None

# 禁用缓存
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.dummy.DummyCache',
    }
}

# 简化密码验证
AUTH_PASSWORD_VALIDATORS = []

# 禁用邮件发送
EMAIL_BACKEND = 'django.core.mail.backends.locmem.EmailBackend'
```

### 环境变量
```bash
# 设置测试环境
export DJANGO_SETTINGS_MODULE=config.settings.test
export TESTING=True
```

## 📝 测试用例说明

### 科室管理测试 (`test_department_views.py`)

#### DepartmentViewTest
测试科室管理的核心视图功能：

- **test_department_list_view_admin_access**：管理员访问科室列表
- **test_department_list_view_staff_denied**：科室人员访问被拒绝
- **test_department_create_view_post_success**：科室创建成功
- **test_department_create_view_post_duplicate_code**：重复代码处理
- **test_department_edit_view_post_success**：科室编辑成功
- **test_department_detail_view**：科室详情页面
- **test_department_delete_view**：科室删除功能
- **test_department_delete_view_with_users**：有用户的科室删除限制

#### DepartmentImportViewTest
测试科室Excel导入功能：

- **test_department_import_view_get**：导入页面访问
- **test_department_import_view_staff_denied**：权限控制
- **test_department_import_view_post_no_file**：无文件处理
- **test_department_import_view_post_invalid_file**：无效文件处理

#### DepartmentPermissionTest
测试科室管理权限控制：

- **test_unauthenticated_access_redirects**：未认证用户重定向
- **test_inactive_user_access_denied**：禁用用户访问拒绝
- **test_staff_access_restrictions**：科室人员访问限制
- **test_admin_full_access**：管理员完全访问权限

### 个人设置测试 (`test_profile_views.py`)

#### ProfileViewTest
测试个人信息管理功能：

- **test_profile_view_self_access**：用户查看自己信息
- **test_profile_view_admin_access_other_user**：管理员查看其他用户
- **test_profile_edit_view_post_success**：个人信息编辑成功
- **test_profile_edit_view_post_invalid_email**：无效邮箱处理
- **test_user_settings_view_get**：用户设置页面
- **test_user_settings_view_post_preferences**：偏好设置保存
- **test_user_settings_view_post_notifications**：通知设置保存

#### ChangePasswordViewTest
测试密码修改功能：

- **test_change_password_view_success**：密码修改成功
- **test_change_password_view_wrong_current_password**：当前密码错误
- **test_change_password_view_password_mismatch**：新密码不匹配
- **test_change_password_view_password_too_short**：密码长度不足

### 模板测试 (`test_department_templates.py`, `test_profile_templates.py`)

#### 模板渲染测试
- **模板使用验证**：确保使用正确的模板文件
- **内容渲染检查**：验证页面标题、表单字段、数据显示
- **上下文变量测试**：检查模板接收的数据
- **权限显示测试**：根据用户权限显示不同内容
- **响应式设计检查**：Bootstrap类和移动端元素
- **JavaScript/CSS集成**：静态文件引用验证

#### 面包屑导航测试
- **导航层次验证**：检查面包屑导航的正确性
- **链接有效性**：验证导航链接的正确性
- **权限相关导航**：不同用户角色的导航差异

### 集成测试 (`test_integration.py`)

#### DepartmentManagementIntegrationTest
测试科室管理完整工作流程：

- **test_complete_department_management_workflow**：
  1. 管理员登录
  2. 访问科室列表
  3. 创建新科室
  4. 查看科室详情
  5. 编辑科室信息
  6. 禁用科室
  7. 删除科室

#### ProfileManagementIntegrationTest
测试个人设置完整工作流程：

- **test_complete_profile_management_workflow**：
  1. 用户登录
  2. 查看个人信息
  3. 编辑个人信息
  4. 访问用户设置
  5. 保存偏好设置
  6. 修改密码

#### CrossModuleIntegrationTest
测试跨模块集成：

- **test_navigation_between_modules**：模块间导航测试
- **test_permission_consistency_across_modules**：跨模块权限一致性
- **test_data_consistency_across_operations**：跨操作数据一致性

## 🚀 运行测试

### 运行所有测试
```bash
# 运行所有测试
python manage.py test

# 运行特定应用的测试
python manage.py test apps.users

# 运行特定测试文件
python manage.py test apps.users.tests.test_department_views

# 运行特定测试类
python manage.py test apps.users.tests.test_department_views.DepartmentViewTest

# 运行特定测试方法
python manage.py test apps.users.tests.test_department_views.DepartmentViewTest.test_department_list_view_admin_access
```

### 并行测试
```bash
# 使用多进程运行测试（提高速度）
python manage.py test --parallel

# 指定进程数
python manage.py test --parallel 4
```

### 详细输出
```bash
# 显示详细输出
python manage.py test --verbosity=2

# 保留测试数据库（调试用）
python manage.py test --keepdb

# 显示警告
python manage.py test --debug-mode
```

### 测试特定标签
```bash
# 运行快速测试
python manage.py test --tag=fast

# 跳过慢速测试
python manage.py test --exclude-tag=slow
```

## 📊 测试覆盖率

### 安装Coverage
```bash
pip install coverage
```

### 运行覆盖率测试
```bash
# 运行测试并收集覆盖率数据
coverage run --source='.' manage.py test

# 生成覆盖率报告
coverage report

# 生成HTML覆盖率报告
coverage html

# 查看特定文件的覆盖率
coverage report apps/users/views.py
```

### 覆盖率配置
```ini
# .coveragerc
[run]
source = .
omit = 
    */venv/*
    */migrations/*
    */tests/*
    manage.py
    */settings/*
    */wsgi.py
    */asgi.py

[report]
exclude_lines =
    pragma: no cover
    def __repr__
    raise AssertionError
    raise NotImplementedError
```

### 目标覆盖率
- **整体覆盖率**：≥ 90%
- **视图层覆盖率**：≥ 95%
- **服务层覆盖率**：≥ 95%
- **模型层覆盖率**：≥ 85%

## 🔄 持续集成

### GitHub Actions配置
```yaml
# .github/workflows/test.yml
name: Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: test_db
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.11
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: Run tests
      run: |
        python manage.py test --parallel
      env:
        DATABASE_URL: mysql://root:password@127.0.0.1:3306/test_db
    
    - name: Generate coverage report
      run: |
        coverage run --source='.' manage.py test
        coverage xml
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v1
```

### 预提交钩子
```yaml
# .pre-commit-config.yaml
repos:
  - repo: local
    hooks:
      - id: tests
        name: tests
        entry: python manage.py test
        language: system
        pass_filenames: false
        always_run: true
```

## 🐛 测试调试

### 调试失败的测试
```bash
# 在第一个失败时停止
python manage.py test --failfast

# 显示详细的错误信息
python manage.py test --verbosity=2

# 使用pdb调试
import pdb; pdb.set_trace()
```

### 常见问题解决

#### 1. 数据库权限问题
```python
# 确保测试用户有创建数据库的权限
GRANT ALL PRIVILEGES ON test_*.* TO 'test_user'@'localhost';
```

#### 2. 静态文件问题
```python
# 测试设置中禁用静态文件收集
STATICFILES_STORAGE = 'django.contrib.staticfiles.storage.StaticFilesStorage'
```

#### 3. 时区问题
```python
# 使用UTC时间进行测试
USE_TZ = True
TIME_ZONE = 'UTC'
```

## 📈 测试最佳实践

### 1. 测试命名规范
- 测试方法名应该描述测试的内容
- 使用 `test_` 前缀
- 包含测试场景和期望结果

### 2. 测试数据管理
- 在 `setUp` 方法中创建测试数据
- 使用有意义的测试数据
- 避免硬编码的测试数据

### 3. 断言使用
- 使用具体的断言方法
- 提供有意义的错误消息
- 一个测试方法只测试一个功能点

### 4. 测试隔离
- 每个测试应该独立运行
- 不依赖其他测试的结果
- 清理测试产生的副作用

### 5. 性能考虑
- 使用内存数据库进行测试
- 避免不必要的数据库查询
- 使用并行测试提高速度

## 📋 测试检查清单

### 功能测试
- [ ] 所有CRUD操作都有测试覆盖
- [ ] 表单验证测试完整
- [ ] 错误处理测试充分
- [ ] 边界条件测试覆盖

### 权限测试
- [ ] 不同角色的访问权限测试
- [ ] 未认证用户的重定向测试
- [ ] 禁用用户的访问限制测试
- [ ] 对象级权限测试

### 集成测试
- [ ] 完整业务流程测试
- [ ] 跨模块交互测试
- [ ] 数据一致性测试
- [ ] 导航流程测试

### 性能测试
- [ ] 测试运行时间合理
- [ ] 数据库查询优化
- [ ] 内存使用合理
- [ ] 并发访问测试

通过完整的测试体系，确保系统的稳定性、安全性和可维护性。
