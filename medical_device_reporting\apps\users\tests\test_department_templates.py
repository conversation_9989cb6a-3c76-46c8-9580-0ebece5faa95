"""
科室管理模板测试
Department Templates Tests
"""

from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse

from apps.users.models import UserProfile, Department


class DepartmentTemplateTest(TestCase):
    """科室管理模板测试"""
    
    def setUp(self):
        """测试前准备"""
        self.client = Client()
        
        # 创建管理员用户
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>'
        )
        self.admin_profile = UserProfile.objects.create(
            user=self.admin_user,
            account_number='0001',
            role='admin',
            is_active=True
        )
        
        # 创建科室
        self.department = Department.objects.create(
            code='TEST',
            name='测试科室',
            is_active=True,
            created_by=self.admin_user
        )
        
        # 创建科室人员
        self.staff_user = User.objects.create_user(
            username='staff',
            email='<EMAIL>'
        )
        self.staff_profile = UserProfile.objects.create(
            user=self.staff_user,
            account_number='1001',
            department=self.department,
            role='staff',
            is_active=True
        )
    
    def test_department_list_template_rendering(self):
        """测试科室列表模板渲染"""
        self.client.force_login(self.admin_user)
        
        response = self.client.get(reverse('users:department_list'))
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'users/department_list.html')
        
        # 检查模板内容
        self.assertContains(response, '科室管理')
        self.assertContains(response, '新建科室')
        self.assertContains(response, '导入Excel')
        self.assertContains(response, '导出Excel')
        
        # 检查科室数据显示
        self.assertContains(response, self.department.code)
        self.assertContains(response, self.department.name)
        
        # 检查操作按钮
        self.assertContains(response, '编辑')
        self.assertContains(response, '查看详情')
        self.assertContains(response, '删除')
    
    def test_department_list_template_context_variables(self):
        """测试科室列表模板上下文变量"""
        self.client.force_login(self.admin_user)
        
        response = self.client.get(reverse('users:department_list'))
        
        # 检查上下文变量
        self.assertIn('departments', response.context)
        self.assertIn('total_departments', response.context)
        self.assertIn('active_departments', response.context)
        
        # 检查统计数据
        self.assertEqual(response.context['total_departments'], 1)
        self.assertEqual(response.context['active_departments'], 1)
    
    def test_department_form_template_rendering_create(self):
        """测试科室创建表单模板渲染"""
        self.client.force_login(self.admin_user)
        
        response = self.client.get(reverse('users:department_create'))
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'users/department_form.html')
        
        # 检查表单元素
        self.assertContains(response, 'name="code"')
        self.assertContains(response, 'name="name"')
        self.assertContains(response, 'name="is_active"')
        
        # 检查页面标题
        self.assertContains(response, '新建科室')
        self.assertContains(response, '创建科室')
        
        # 检查面包屑导航
        self.assertContains(response, '科室管理')
        self.assertContains(response, '新建科室')
    
    def test_department_form_template_rendering_edit(self):
        """测试科室编辑表单模板渲染"""
        self.client.force_login(self.admin_user)
        
        response = self.client.get(reverse('users:department_edit', args=[self.department.id]))
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'users/department_form.html')
        
        # 检查表单预填充数据
        self.assertContains(response, f'value="{self.department.code}"')
        self.assertContains(response, f'value="{self.department.name}"')
        
        # 检查页面标题
        self.assertContains(response, '编辑科室')
        self.assertContains(response, '更新科室')
        
        # 检查危险操作区域
        self.assertContains(response, '删除科室')
        self.assertContains(response, '危险操作')
        
        # 检查统计信息
        self.assertContains(response, '总用户数')
        self.assertContains(response, '活跃用户')
    
    def test_department_detail_template_rendering(self):
        """测试科室详情模板渲染"""
        self.client.force_login(self.admin_user)
        
        response = self.client.get(reverse('users:department_detail', args=[self.department.id]))
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'users/department_detail.html')
        
        # 检查科室信息显示
        self.assertContains(response, self.department.name)
        self.assertContains(response, self.department.code)
        
        # 检查用户列表
        self.assertContains(response, '科室用户列表')
        self.assertContains(response, self.staff_user.username)
        
        # 检查统计信息
        self.assertContains(response, '用户统计')
        self.assertContains(response, '总用户数')
        self.assertContains(response, '活跃用户')
        
        # 检查操作按钮
        self.assertContains(response, '编辑科室')
        self.assertContains(response, '添加用户')
    
    def test_department_import_template_rendering(self):
        """测试科室导入模板渲染"""
        self.client.force_login(self.admin_user)
        
        response = self.client.get(reverse('users:department_import'))
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'users/department_import.html')
        
        # 检查导入说明
        self.assertContains(response, '导入说明')
        self.assertContains(response, '文件格式要求')
        self.assertContains(response, '数据列要求')
        
        # 检查模板下载
        self.assertContains(response, '模板下载')
        self.assertContains(response, '下载模板')
        
        # 检查文件上传区域
        self.assertContains(response, '选择Excel文件')
        self.assertContains(response, 'name="excel_file"')
        
        # 检查导入选项
        self.assertContains(response, '导入选项')
        self.assertContains(response, 'name="skip_errors"')
        self.assertContains(response, 'name="update_existing"')
    
    def test_department_template_permission_display(self):
        """测试科室模板权限相关显示"""
        # 测试管理员视图
        self.client.force_login(self.admin_user)
        
        response = self.client.get(reverse('users:department_list'))
        
        # 管理员应该看到所有操作按钮
        self.assertContains(response, '新建科室')
        self.assertContains(response, '导入Excel')
        self.assertContains(response, '导出Excel')
        
        # 检查导航菜单
        self.assertContains(response, '用户管理')
        self.assertContains(response, '科室管理')
    
    def test_department_template_breadcrumb_navigation(self):
        """测试科室模板面包屑导航"""
        self.client.force_login(self.admin_user)
        
        # 测试科室列表面包屑
        response = self.client.get(reverse('users:department_list'))
        self.assertContains(response, '用户中心')
        self.assertContains(response, '科室管理')
        
        # 测试科室创建面包屑
        response = self.client.get(reverse('users:department_create'))
        self.assertContains(response, '用户中心')
        self.assertContains(response, '科室管理')
        self.assertContains(response, '新建科室')
        
        # 测试科室详情面包屑
        response = self.client.get(reverse('users:department_detail', args=[self.department.id]))
        self.assertContains(response, '用户中心')
        self.assertContains(response, '科室管理')
        self.assertContains(response, self.department.name)
    
    def test_department_template_responsive_design(self):
        """测试科室模板响应式设计元素"""
        self.client.force_login(self.admin_user)
        
        response = self.client.get(reverse('users:department_list'))
        
        # 检查Bootstrap响应式类
        self.assertContains(response, 'col-lg-')
        self.assertContains(response, 'col-md-')
        self.assertContains(response, 'col-sm-')
        
        # 检查移动端友好元素
        self.assertContains(response, 'btn-group-sm')
        self.assertContains(response, 'table-responsive')
    
    def test_department_template_javascript_integration(self):
        """测试科室模板JavaScript集成"""
        self.client.force_login(self.admin_user)
        
        # 测试科室列表页面JavaScript
        response = self.client.get(reverse('users:department_list'))
        self.assertContains(response, 'department_list.js')
        
        # 测试科室表单页面JavaScript
        response = self.client.get(reverse('users:department_create'))
        self.assertContains(response, 'department_form.js')
        
        # 测试科室详情页面JavaScript
        response = self.client.get(reverse('users:department_detail', args=[self.department.id]))
        self.assertContains(response, 'department_detail.js')
        
        # 测试科室导入页面JavaScript
        response = self.client.get(reverse('users:department_import'))
        self.assertContains(response, 'department_import.js')
    
    def test_department_template_css_integration(self):
        """测试科室模板CSS集成"""
        self.client.force_login(self.admin_user)
        
        response = self.client.get(reverse('users:department_list'))
        
        # 检查CSS文件引用
        self.assertContains(response, 'user_management.css')
        
        # 检查Bootstrap Icons
        self.assertContains(response, 'bi-building')
        self.assertContains(response, 'bi-plus-circle')
        self.assertContains(response, 'bi-upload')
    
    def test_department_template_error_handling(self):
        """测试科室模板错误处理"""
        self.client.force_login(self.admin_user)
        
        # 测试不存在的科室详情页面
        response = self.client.get(reverse('users:department_detail', args=[99999]))
        self.assertEqual(response.status_code, 404)
        
        # 测试不存在的科室编辑页面
        response = self.client.get(reverse('users:department_edit', args=[99999]))
        self.assertEqual(response.status_code, 404)
