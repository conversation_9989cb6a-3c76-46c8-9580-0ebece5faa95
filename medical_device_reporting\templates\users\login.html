{% extends 'base.html' %}
{% load static %}

{% block title %}用户登录 - 医疗器械不良事件上报平台{% endblock %}

{% block extra_css %}
<link href="{% static 'users/css/login.css' %}" rel="stylesheet">
{% endblock %}

{% block nav_items %}
<!-- 登录页面不显示导航菜单 -->
{% endblock %}

{% block content %}
<div class="login-container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="login-card card shadow-lg">
                <div class="card-header text-center bg-primary text-white">
                    <div class="login-header">
                        <i class="bi bi-shield-check login-icon"></i>
                        <h4 class="mb-0">用户登录</h4>
                        <p class="mb-0 mt-2">医疗器械不良事件上报平台</p>
                    </div>
                </div>
                
                <div class="card-body p-4">
                    <!-- 错误消息显示 -->
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                    
                    <!-- 登录表单 -->
                    <form method="post" class="needs-validation" novalidate id="loginForm">
                        {% csrf_token %}
                        
                        <div class="mb-4">
                            <label for="account_number" class="form-label">
                                <i class="bi bi-person-badge me-1"></i>
                                4位数账号
                            </label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="bi bi-hash"></i>
                                </span>
                                <input 
                                    type="text" 
                                    class="form-control form-control-lg" 
                                    id="account_number" 
                                    name="account_number" 
                                    placeholder="请输入4位数账号"
                                    maxlength="4" 
                                    pattern="[0-9]{4}" 
                                    required
                                    autocomplete="username"
                                    autofocus
                                >
                                <div class="invalid-feedback">
                                    请输入4位数字账号
                                </div>
                                <div class="valid-feedback">
                                    账号格式正确
                                </div>
                            </div>
                            <div class="form-text">
                                <i class="bi bi-info-circle me-1"></i>
                                请输入您的4位数字账号，无需密码
                            </div>
                        </div>
                        
                        <!-- 记住登录状态 -->
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="remember_me" name="remember_me">
                                <label class="form-check-label" for="remember_me">
                                    记住登录状态
                                </label>
                            </div>
                        </div>
                        
                        <!-- 登录按钮 -->
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg" id="loginBtn">
                                <span class="btn-text">
                                    <i class="bi bi-box-arrow-in-right me-2"></i>
                                    登录
                                </span>
                                <span class="btn-loading d-none">
                                    <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                    登录中...
                                </span>
                            </button>
                        </div>
                    </form>
                    
                    <!-- 帮助信息 -->
                    <div class="login-help mt-4">
                        <div class="text-center">
                            <small class="text-muted">
                                <i class="bi bi-question-circle me-1"></i>
                                如有问题，请联系系统管理员
                            </small>
                        </div>
                    </div>
                </div>
                
                <!-- 卡片底部 -->
                <div class="card-footer text-center bg-light">
                    <small class="text-muted">
                        <i class="bi bi-shield-check me-1"></i>
                        安全登录 · 数据保护
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 登录说明 -->
    <div class="row justify-content-center mt-4">
        <div class="col-md-8 col-lg-6">
            <div class="card border-0 bg-transparent">
                <div class="card-body text-center">
                    <h6 class="text-muted mb-3">
                        <i class="bi bi-info-circle me-2"></i>
                        登录说明
                    </h6>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="feature-item">
                                <i class="bi bi-person-check text-primary fs-4"></i>
                                <h6 class="mt-2">简单登录</h6>
                                <small class="text-muted">仅需4位数账号，无需密码</small>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="feature-item">
                                <i class="bi bi-shield-lock text-success fs-4"></i>
                                <h6 class="mt-2">安全可靠</h6>
                                <small class="text-muted">账号唯一性验证，确保安全</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'users/js/login.js' %}"></script>
{% endblock %}
