"""
用户管理API接口
User Management APIs for Medical Device Reporting Platform
"""

from rest_framework import status, serializers
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from .permissions import IsAdminUser, IsOwnerOrAdmin, IsDepartmentMemberOrAdmin
from rest_framework.pagination import PageNumberPagination
from django.http import Http404
import logging

from apps.common.exceptions import (
    BusinessLogicError,
    DataValidationError,
    ResourceNotFoundError
)
from .models import UserProfile, Department
from .services import (
    user_create, user_update, user_delete, user_activate, user_deactivate,
    user_change_role, user_change_department, users_bulk_activate, users_bulk_deactivate,
    department_create, department_update, department_delete
)
from .selectors import (
    user_list, user_list_paginated, user_get_by_id, user_get_by_account_number,
    department_list, department_list_with_user_counts, department_get_by_id,
    user_statistics
)
from .serializers import (
    UserSerializer, UserCreateSerializer, UserUpdateSerializer,
    UserRoleChangeSerializer, UserDepartmentChangeSerializer, UserBulkActionSerializer,
    DepartmentSerializer, DepartmentCreateSerializer
)

logger = logging.getLogger('apps.users')


class StandardResultsSetPagination(PageNumberPagination):
    """
    标准分页配置
    """
    page_size = 20
    page_size_query_param = 'page_size'
    max_page_size = 100


class UserListApi(APIView):
    """
    用户列表API
    """

    permission_classes = [IsAuthenticated]
    pagination_class = StandardResultsSetPagination

    def get(self, request):
        """
        获取用户列表
        """
        try:
            # 检查权限
            if not request.user.has_perm('users.view_userprofile'):
                return Response(
                    {'error': '没有查看用户的权限'},
                    status=status.HTTP_403_FORBIDDEN
                )

            # 获取查询参数
            department_id = request.query_params.get('department_id')
            role = request.query_params.get('role')
            is_active = request.query_params.get('is_active')
            search = request.query_params.get('search')
            ordering = request.query_params.get('ordering', 'account_number')
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('page_size', 20))

            # 参数转换
            if department_id:
                department_id = int(department_id)
            if is_active is not None:
                is_active = is_active.lower() == 'true'

            # 使用分页查询选择器
            result = user_list_paginated(
                department_id=department_id,
                role=role,
                is_active=is_active,
                search=search,
                ordering=ordering,
                page=page,
                page_size=page_size
            )

            # 序列化数据
            serializer = UserSerializer(result['users'], many=True)

            return Response({
                'results': serializer.data,
                'count': result['total_count'],
                'page': result['page'],
                'total_pages': result['total_pages'],
                'has_previous': result['has_previous'],
                'has_next': result['has_next'],
                'previous_page': result['previous_page'],
                'next_page': result['next_page'],
            })

        except ValueError as e:
            return Response(
                {'error': f'参数格式错误: {str(e)}'},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f'获取用户列表失败: {str(e)}')
            return Response(
                {'error': '获取用户列表失败'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class UserCreateApi(APIView):
    """
    用户创建API
    """

    permission_classes = [IsAdminUser]

    def post(self, request):
        """
        创建用户
        """
        try:
            # 检查权限
            if not request.user.has_perm('users.add_userprofile'):
                return Response(
                    {'error': '没有创建用户的权限'},
                    status=status.HTTP_403_FORBIDDEN
                )

            # 验证数据
            serializer = UserCreateSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            # 创建用户
            user_profile = user_create(
                **serializer.validated_data,
                created_by=request.user
            )

            # 返回创建的用户信息
            response_serializer = UserSerializer(user_profile)
            return Response(response_serializer.data, status=status.HTTP_201_CREATED)

        except serializers.ValidationError as e:
            return Response(
                {'error': e.detail},
                status=status.HTTP_400_BAD_REQUEST
            )
        except DataValidationError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except BusinessLogicError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f'创建用户失败: {str(e)}')
            return Response(
                {'error': '创建用户失败'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class UserDetailApi(APIView):
    """
    用户详情API
    """

    permission_classes = [IsAuthenticated]

    def get_object(self, pk):
        """
        获取用户对象
        """
        user_profile = user_get_by_id(pk)
        if not user_profile:
            raise Http404
        return user_profile

    def get(self, request, pk):
        """
        获取用户详情
        """
        try:
            # 检查权限
            if not request.user.has_perm('users.view_userprofile'):
                return Response(
                    {'error': '没有查看用户的权限'},
                    status=status.HTTP_403_FORBIDDEN
                )

            user_profile = self.get_object(pk)
            serializer = UserSerializer(user_profile)
            return Response(serializer.data)

        except Http404:
            return Response(
                {'error': '用户不存在'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f'获取用户详情失败: {str(e)}')
            return Response(
                {'error': '获取用户详情失败'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def put(self, request, pk):
        """
        更新用户信息
        """
        try:
            # 检查权限
            if not request.user.has_perm('users.change_userprofile'):
                return Response(
                    {'error': '没有修改用户的权限'},
                    status=status.HTTP_403_FORBIDDEN
                )

            user_profile = self.get_object(pk)

            # 验证数据
            serializer = UserUpdateSerializer(data=request.data, instance=user_profile)
            serializer.is_valid(raise_exception=True)

            # 更新用户
            updated_user = user_update(
                user_profile_id=user_profile.id,
                **serializer.validated_data,
                updated_by=request.user
            )

            # 返回更新后的用户信息
            response_serializer = UserSerializer(updated_user)
            return Response(response_serializer.data)

        except Http404:
            return Response(
                {'error': '用户不存在'},
                status=status.HTTP_404_NOT_FOUND
            )
        except serializers.ValidationError as e:
            return Response(
                {'error': e.detail},
                status=status.HTTP_400_BAD_REQUEST
            )
        except (DataValidationError, BusinessLogicError) as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f'更新用户失败: {str(e)}')
            return Response(
                {'error': '更新用户失败'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def delete(self, request, pk):
        """
        删除用户（软删除）
        """
        try:
            # 检查权限
            if not request.user.has_perm('users.delete_userprofile'):
                return Response(
                    {'error': '没有删除用户的权限'},
                    status=status.HTTP_403_FORBIDDEN
                )

            user_profile = self.get_object(pk)

            # 删除用户
            user_delete(
                user_profile_id=user_profile.id,
                deleted_by=request.user
            )

            return Response(status=status.HTTP_204_NO_CONTENT)

        except Http404:
            return Response(
                {'error': '用户不存在'},
                status=status.HTTP_404_NOT_FOUND
            )
        except BusinessLogicError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f'删除用户失败: {str(e)}')
            return Response(
                {'error': '删除用户失败'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class UserActivateApi(APIView):
    """
    用户激活API
    """

    permission_classes = [IsAdminUser]

    def post(self, request, pk):
        """
        激活用户
        """
        try:
            # 检查权限
            if not request.user.has_perm('users.change_userprofile'):
                return Response(
                    {'error': '没有修改用户的权限'},
                    status=status.HTTP_403_FORBIDDEN
                )

            user_profile = user_get_by_id(pk)
            if not user_profile:
                return Response(
                    {'error': '用户不存在'},
                    status=status.HTTP_404_NOT_FOUND
                )

            # 激活用户
            updated_user = user_activate(
                user_profile_id=user_profile.id,
                updated_by=request.user
            )

            serializer = UserSerializer(updated_user)
            return Response(serializer.data)

        except BusinessLogicError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f'激活用户失败: {str(e)}')
            return Response(
                {'error': '激活用户失败'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class UserDeactivateApi(APIView):
    """
    用户禁用API
    """

    permission_classes = [IsAdminUser]

    def post(self, request, pk):
        """
        禁用用户
        """
        try:
            # 检查权限
            if not request.user.has_perm('users.change_userprofile'):
                return Response(
                    {'error': '没有修改用户的权限'},
                    status=status.HTTP_403_FORBIDDEN
                )

            user_profile = user_get_by_id(pk)
            if not user_profile:
                return Response(
                    {'error': '用户不存在'},
                    status=status.HTTP_404_NOT_FOUND
                )

            # 禁用用户
            updated_user = user_deactivate(
                user_profile_id=user_profile.id,
                updated_by=request.user
            )

            serializer = UserSerializer(updated_user)
            return Response(serializer.data)

        except BusinessLogicError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f'禁用用户失败: {str(e)}')
            return Response(
                {'error': '禁用用户失败'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class UserRoleChangeApi(APIView):
    """
    用户角色变更API
    """

    permission_classes = [IsAuthenticated]

    def post(self, request, pk):
        """
        变更用户角色
        """
        try:
            # 检查权限
            if not request.user.has_perm('users.can_assign_roles'):
                return Response(
                    {'error': '没有分配角色的权限'},
                    status=status.HTTP_403_FORBIDDEN
                )

            user_profile = user_get_by_id(pk)
            if not user_profile:
                return Response(
                    {'error': '用户不存在'},
                    status=status.HTTP_404_NOT_FOUND
                )

            # 验证数据
            serializer = UserRoleChangeSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            # 变更角色
            updated_user = user_change_role(
                user_profile_id=user_profile.id,
                new_role=serializer.validated_data['role'],
                updated_by=request.user
            )

            response_serializer = UserSerializer(updated_user)
            return Response(response_serializer.data)

        except serializers.ValidationError as e:
            return Response(
                {'error': e.detail},
                status=status.HTTP_400_BAD_REQUEST
            )
        except (DataValidationError, BusinessLogicError) as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f'变更用户角色失败: {str(e)}')
            return Response(
                {'error': '变更用户角色失败'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class UserDepartmentChangeApi(APIView):
    """
    用户科室变更API
    """

    permission_classes = [IsAuthenticated]

    def post(self, request, pk):
        """
        变更用户科室
        """
        try:
            # 检查权限
            if not request.user.has_perm('users.change_userprofile'):
                return Response(
                    {'error': '没有修改用户的权限'},
                    status=status.HTTP_403_FORBIDDEN
                )

            user_profile = user_get_by_id(pk)
            if not user_profile:
                return Response(
                    {'error': '用户不存在'},
                    status=status.HTTP_404_NOT_FOUND
                )

            # 验证数据
            serializer = UserDepartmentChangeSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            # 变更科室
            updated_user = user_change_department(
                user_profile_id=user_profile.id,
                department_id=serializer.validated_data['department_id'],
                updated_by=request.user
            )

            response_serializer = UserSerializer(updated_user)
            return Response(response_serializer.data)

        except serializers.ValidationError as e:
            return Response(
                {'error': e.detail},
                status=status.HTTP_400_BAD_REQUEST
            )
        except (DataValidationError, BusinessLogicError) as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f'变更用户科室失败: {str(e)}')
            return Response(
                {'error': '变更用户科室失败'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class UserBulkActionApi(APIView):
    """
    用户批量操作API
    """

    permission_classes = [IsAdminUser]

    def post(self, request):
        """
        批量操作用户
        """
        try:
            # 检查权限
            if not request.user.has_perm('users.change_userprofile'):
                return Response(
                    {'error': '没有修改用户的权限'},
                    status=status.HTTP_403_FORBIDDEN
                )

            # 验证数据
            serializer = UserBulkActionSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            user_ids = serializer.validated_data['user_ids']
            action = serializer.validated_data['action']

            # 执行批量操作
            if action == 'activate':
                result = users_bulk_activate(
                    user_profile_ids=user_ids,
                    updated_by=request.user
                )
            elif action == 'deactivate':
                result = users_bulk_deactivate(
                    user_profile_ids=user_ids,
                    updated_by=request.user
                )
            else:
                return Response(
                    {'error': '无效的操作类型'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            return Response({
                'message': f'批量{action}操作完成',
                'success_count': result['success_count'],
                'failed_count': result['failed_count'],
                'errors': result['errors']
            })

        except serializers.ValidationError as e:
            return Response(
                {'error': e.detail},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f'批量操作用户失败: {str(e)}')
            return Response(
                {'error': '批量操作用户失败'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class DepartmentListApi(APIView):
    """
    科室列表API
    """

    permission_classes = [IsDepartmentMemberOrAdmin]

    def get(self, request):
        """
        获取科室列表
        """
        try:
            # 检查权限
            if not request.user.has_perm('users.view_department'):
                return Response(
                    {'error': '没有查看科室的权限'},
                    status=status.HTTP_403_FORBIDDEN
                )

            # 获取查询参数
            is_active = request.query_params.get('is_active')
            search = request.query_params.get('search')

            # 参数转换
            if is_active is not None:
                is_active = is_active.lower() == 'true'

            # 获取科室列表（带用户统计）
            departments = department_list_with_user_counts(
                is_active=is_active,
                search=search
            )

            # 序列化数据
            serializer = DepartmentSerializer(departments, many=True)
            return Response(serializer.data)

        except Exception as e:
            logger.error(f'获取科室列表失败: {str(e)}')
            return Response(
                {'error': '获取科室列表失败'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def post(self, request):
        """
        创建科室
        """
        try:
            # 检查权限
            if not request.user.has_perm('users.add_department'):
                return Response(
                    {'error': '没有创建科室的权限'},
                    status=status.HTTP_403_FORBIDDEN
                )

            # 验证数据
            serializer = DepartmentCreateSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            # 创建科室
            department = department_create(
                **serializer.validated_data,
                created_by=request.user
            )

            # 返回创建的科室信息
            response_serializer = DepartmentSerializer(department)
            return Response(response_serializer.data, status=status.HTTP_201_CREATED)

        except serializers.ValidationError as e:
            return Response(
                {'error': e.detail},
                status=status.HTTP_400_BAD_REQUEST
            )
        except (DataValidationError, BusinessLogicError) as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f'创建科室失败: {str(e)}')
            return Response(
                {'error': '创建科室失败'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class DepartmentDetailApi(APIView):
    """
    科室详情API
    """

    permission_classes = [IsAuthenticated]

    def get_object(self, pk):
        """
        获取科室对象
        """
        department = department_get_by_id(pk)
        if not department:
            raise Http404
        return department

    def get(self, request, pk):
        """
        获取科室详情
        """
        try:
            # 检查权限
            if not request.user.has_perm('users.view_department'):
                return Response(
                    {'error': '没有查看科室的权限'},
                    status=status.HTTP_403_FORBIDDEN
                )

            department = self.get_object(pk)
            serializer = DepartmentSerializer(department)
            return Response(serializer.data)

        except Http404:
            return Response(
                {'error': '科室不存在'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f'获取科室详情失败: {str(e)}')
            return Response(
                {'error': '获取科室详情失败'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def put(self, request, pk):
        """
        更新科室信息
        """
        try:
            # 检查权限
            if not request.user.has_perm('users.change_department'):
                return Response(
                    {'error': '没有修改科室的权限'},
                    status=status.HTTP_403_FORBIDDEN
                )

            department = self.get_object(pk)

            # 验证数据
            serializer = DepartmentSerializer(department, data=request.data, partial=True)
            serializer.is_valid(raise_exception=True)

            # 更新科室
            updated_department = department_update(
                department_id=department.id,
                **serializer.validated_data,
                updated_by=request.user
            )

            # 返回更新后的科室信息
            response_serializer = DepartmentSerializer(updated_department)
            return Response(response_serializer.data)

        except Http404:
            return Response(
                {'error': '科室不存在'},
                status=status.HTTP_404_NOT_FOUND
            )
        except serializers.ValidationError as e:
            return Response(
                {'error': e.detail},
                status=status.HTTP_400_BAD_REQUEST
            )
        except (DataValidationError, BusinessLogicError) as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f'更新科室失败: {str(e)}')
            return Response(
                {'error': '更新科室失败'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def patch(self, request, pk):
        """
        部分更新科室信息
        """
        return self.put(request, pk)

    def delete(self, request, pk):
        """
        删除科室（软删除）
        """
        try:
            # 检查权限
            if not request.user.has_perm('users.delete_department'):
                return Response(
                    {'error': '没有删除科室的权限'},
                    status=status.HTTP_403_FORBIDDEN
                )

            department = self.get_object(pk)

            # 删除科室
            department_delete(
                department_id=department.id,
                deleted_by=request.user
            )

            return Response(status=status.HTTP_204_NO_CONTENT)

        except Http404:
            return Response(
                {'error': '科室不存在'},
                status=status.HTTP_404_NOT_FOUND
            )
        except BusinessLogicError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f'删除科室失败: {str(e)}')
            return Response(
                {'error': '删除科室失败'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class UserStatisticsApi(APIView):
    """
    用户统计API
    """

    permission_classes = [IsDepartmentMemberOrAdmin]

    def get(self, request):
        """
        获取用户统计信息
        """
        try:
            # 检查权限
            if not request.user.has_perm('users.view_userprofile'):
                return Response(
                    {'error': '没有查看用户统计的权限'},
                    status=status.HTTP_403_FORBIDDEN
                )

            stats = user_statistics()

            return Response(stats)

        except Exception as e:
            logger.error(f'获取用户统计失败: {str(e)}')
            return Response(
                {'error': '获取用户统计失败'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class UserSearchApi(APIView):
    """
    用户搜索API
    """

    permission_classes = [IsDepartmentMemberOrAdmin]

    def get(self, request):
        """
        搜索用户
        """
        try:
            # 检查权限
            if not request.user.has_perm('users.view_userprofile'):
                return Response(
                    {'error': '没有查看用户的权限'},
                    status=status.HTTP_403_FORBIDDEN
                )

            # 获取搜索参数
            account_number = request.query_params.get('account_number')

            if account_number:
                # 按账号精确搜索
                user_profile = user_get_by_account_number(account_number)
                if user_profile:
                    serializer = UserSerializer(user_profile)
                    return Response(serializer.data)
                else:
                    return Response(
                        {'error': '用户不存在'},
                        status=status.HTTP_404_NOT_FOUND
                    )
            else:
                return Response(
                    {'error': '请提供搜索参数'},
                    status=status.HTTP_400_BAD_REQUEST
                )

        except Exception as e:
            logger.error(f'搜索用户失败: {str(e)}')
            return Response(
                {'error': '搜索用户失败'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class UserPermissionDebugApi(APIView):
    """
    用户权限调试API
    """

    permission_classes = [IsAuthenticated]

    def get(self, request):
        """
        获取当前用户权限信息
        """
        try:
            user = request.user
            debug_info = {
                'user_id': user.id,
                'username': user.username,
                'is_authenticated': user.is_authenticated,
                'has_profile': hasattr(user, 'profile'),
            }

            if hasattr(user, 'profile'):
                profile = user.profile
                debug_info.update({
                    'account_number': profile.account_number,
                    'role': profile.role,
                    'is_admin': profile.is_admin,
                    'is_active': profile.is_active,
                    'department': profile.department.name if profile.department else None,
                })

            # 检查权限
            debug_info.update({
                'has_change_userprofile_perm': user.has_perm('users.change_userprofile'),
                'has_view_userprofile_perm': user.has_perm('users.view_userprofile'),
                'has_add_userprofile_perm': user.has_perm('users.add_userprofile'),
                'has_delete_userprofile_perm': user.has_perm('users.delete_userprofile'),
            })

            # 检查权限类
            admin_permission = IsAdminUser()
            debug_info['passes_admin_permission'] = admin_permission.has_permission(request, None)

            return Response(debug_info)

        except Exception as e:
            logger.error(f'获取权限调试信息失败: {str(e)}')
            return Response(
                {'error': '获取权限调试信息失败'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
