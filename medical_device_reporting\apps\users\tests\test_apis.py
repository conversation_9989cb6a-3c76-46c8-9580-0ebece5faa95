"""
用户管理API测试
User Management APIs Tests
"""

import json
from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status

from apps.users.models import UserProfile, Department
from apps.users.services import initialize_user_groups


class UserAPITest(APITestCase):
    """用户API测试"""
    
    def setUp(self):
        """测试前准备"""
        # 初始化用户组
        initialize_user_groups()
        
        # 创建管理员用户
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>'
        )
        self.admin_profile = UserProfile.objects.create(
            user=self.admin_user,
            account_number='0001',
            role='admin',
            is_active=True
        )
        
        # 创建科室
        self.department = Department.objects.create(
            code='TEST',
            name='测试科室',
            created_by=self.admin_user
        )
        
        # 创建科室人员
        self.staff_user = User.objects.create_user(
            username='staff',
            email='<EMAIL>'
        )
        self.staff_profile = UserProfile.objects.create(
            user=self.staff_user,
            account_number='1001',
            department=self.department,
            role='staff',
            is_active=True
        )
        
        self.client = APIClient()
    
    def test_user_create_api_success(self):
        """测试用户创建API成功"""
        self.client.force_authenticate(user=self.admin_user)
        
        data = {
            'account_number': '1002',
            'username': '新用户',
            'first_name': '新',
            'last_name': '用户',
            'email': '<EMAIL>',
            'role': 'staff',
            'department_id': self.department.id,
            'phone': '***********'
        }
        
        response = self.client.post('/api/users/create/', data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('id', response.data)
        self.assertEqual(response.data['account_number'], '1002')
        
        # 验证用户确实被创建
        profile = UserProfile.objects.get(account_number='1002')
        self.assertEqual(profile.user.username, '新用户')
        self.assertEqual(profile.department, self.department)
    
    def test_user_create_api_permission_denied(self):
        """测试用户创建API权限拒绝"""
        self.client.force_authenticate(user=self.staff_user)
        
        data = {
            'account_number': '1003',
            'username': '新用户',
            'role': 'staff',
            'department_id': self.department.id
        }
        
        response = self.client.post('/api/users/create/', data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
    
    def test_user_create_api_validation_error(self):
        """测试用户创建API验证错误"""
        self.client.force_authenticate(user=self.admin_user)
        
        data = {
            'account_number': '12345',  # 无效账号
            'username': '新用户',
            'role': 'staff'
        }
        
        response = self.client.post('/api/users/create/', data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)
    
    def test_user_activate_api_success(self):
        """测试用户激活API成功"""
        self.client.force_authenticate(user=self.admin_user)
        
        # 先禁用用户
        self.staff_profile.is_active = False
        self.staff_profile.save()
        
        response = self.client.post(f'/api/users/{self.staff_profile.id}/activate/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('message', response.data)
        
        # 验证用户被激活
        self.staff_profile.refresh_from_db()
        self.assertTrue(self.staff_profile.is_active)
    
    def test_user_deactivate_api_success(self):
        """测试用户禁用API成功"""
        self.client.force_authenticate(user=self.admin_user)
        
        response = self.client.post(f'/api/users/{self.staff_profile.id}/deactivate/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('message', response.data)
        
        # 验证用户被禁用
        self.staff_profile.refresh_from_db()
        self.assertFalse(self.staff_profile.is_active)
    
    def test_user_bulk_action_api_success(self):
        """测试用户批量操作API成功"""
        self.client.force_authenticate(user=self.admin_user)
        
        # 创建另一个用户
        another_user = User.objects.create_user(username='another')
        another_profile = UserProfile.objects.create(
            user=another_user,
            account_number='1003',
            department=self.department,
            role='staff',
            is_active=True
        )
        
        data = {
            'user_ids': [self.staff_profile.id, another_profile.id],
            'action': 'deactivate'
        }
        
        response = self.client.post('/api/users/bulk-action/', data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('message', response.data)
        
        # 验证用户被批量禁用
        self.staff_profile.refresh_from_db()
        another_profile.refresh_from_db()
        self.assertFalse(self.staff_profile.is_active)
        self.assertFalse(another_profile.is_active)
    
    def test_user_statistics_api_success(self):
        """测试用户统计API成功"""
        self.client.force_authenticate(user=self.admin_user)
        
        response = self.client.get('/api/users/statistics/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('total_users', response.data)
        self.assertIn('active_users', response.data)
        self.assertIn('admin_users', response.data)
        self.assertIn('staff_users', response.data)
    
    def test_user_statistics_api_staff_access(self):
        """测试科室人员访问统计API"""
        self.client.force_authenticate(user=self.staff_user)
        
        response = self.client.get('/api/users/statistics/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # 科室人员应该能访问统计API
    
    def test_user_search_api_success(self):
        """测试用户搜索API成功"""
        self.client.force_authenticate(user=self.admin_user)
        
        response = self.client.get('/api/users/search/', {'q': 'staff'})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('results', response.data)
        self.assertIn('total', response.data)


class DepartmentAPITest(APITestCase):
    """科室API测试"""
    
    def setUp(self):
        """测试前准备"""
        # 创建管理员用户
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>'
        )
        self.admin_profile = UserProfile.objects.create(
            user=self.admin_user,
            account_number='0001',
            role='admin',
            is_active=True
        )
        
        # 创建科室
        self.department = Department.objects.create(
            code='TEST',
            name='测试科室',
            created_by=self.admin_user
        )
        
        # 创建科室人员
        self.staff_user = User.objects.create_user(
            username='staff',
            email='<EMAIL>'
        )
        self.staff_profile = UserProfile.objects.create(
            user=self.staff_user,
            account_number='1001',
            department=self.department,
            role='staff',
            is_active=True
        )
        
        self.client = APIClient()
    
    def test_department_list_api_admin_access(self):
        """测试管理员访问科室列表API"""
        self.client.force_authenticate(user=self.admin_user)
        
        response = self.client.get('/api/departments/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('results', response.data)
        self.assertTrue(len(response.data['results']) > 0)
    
    def test_department_list_api_staff_access(self):
        """测试科室人员访问科室列表API"""
        self.client.force_authenticate(user=self.staff_user)
        
        response = self.client.get('/api/departments/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # 科室人员应该能访问科室列表（只读）
    
    def test_department_create_api_admin_only(self):
        """测试只有管理员能创建科室"""
        # 管理员创建
        self.client.force_authenticate(user=self.admin_user)
        
        data = {
            'code': 'NEW',
            'name': '新科室',
            'description': '新创建的科室'
        }
        
        response = self.client.post('/api/departments/', data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # 科室人员尝试创建
        self.client.force_authenticate(user=self.staff_user)
        
        data = {
            'code': 'STAFF',
            'name': '科室人员创建',
        }
        
        response = self.client.post('/api/departments/', data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)


class AuthenticationAPITest(TestCase):
    """认证API测试"""
    
    def setUp(self):
        """测试前准备"""
        self.client = Client()
        
        # 创建测试用户
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>'
        )
        self.profile = UserProfile.objects.create(
            user=self.user,
            account_number='1001',
            role='staff',
            is_active=True
        )
    
    def test_login_with_account_number(self):
        """测试使用账号登录"""
        response = self.client.post('/login/', {
            'account_number': '1001'
        })
        
        # 应该重定向到用户中心
        self.assertEqual(response.status_code, 302)
        
        # 检查用户是否已登录
        user = response.wsgi_request.user
        self.assertTrue(user.is_authenticated)
        self.assertEqual(user, self.user)
    
    def test_login_with_invalid_account_number(self):
        """测试使用无效账号登录"""
        response = self.client.post('/login/', {
            'account_number': '9999'  # 不存在的账号
        })
        
        # 应该返回错误
        self.assertEqual(response.status_code, 200)  # 返回登录页面
        self.assertContains(response, '账号不存在或已被禁用')
    
    def test_login_with_inactive_user(self):
        """测试使用已禁用用户登录"""
        self.profile.is_active = False
        self.profile.save()
        
        response = self.client.post('/login/', {
            'account_number': '1001'
        })
        
        # 应该返回错误
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '账号不存在或已被禁用')
    
    def test_logout(self):
        """测试登出"""
        # 先登录
        self.client.force_login(self.user)
        
        # 然后登出
        response = self.client.post('/logout/')
        
        # 应该重定向到登录页面
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, '/login/')
    
    def test_dashboard_access_authenticated(self):
        """测试已认证用户访问用户中心"""
        self.client.force_login(self.user)
        
        response = self.client.get('/dashboard/')
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '用户中心')
    
    def test_dashboard_access_unauthenticated(self):
        """测试未认证用户访问用户中心"""
        response = self.client.get('/dashboard/')
        
        # 应该重定向到登录页面
        self.assertEqual(response.status_code, 302)
        self.assertTrue(response.url.startswith('/login/'))
