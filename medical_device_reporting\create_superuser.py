#!/usr/bin/env python
"""
创建Django超级用户和管理员用户的脚本
Create Django superuser and admin user script for Medical Device Reporting Platform
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from django.contrib.auth.models import User
from apps.users.models import UserProfile, Department
from apps.users.services import initialize_user_groups, user_create

def create_superuser():
    """创建Django超级用户（用于管理后台）"""
    try:
        # 检查是否已存在超级用户
        if User.objects.filter(is_superuser=True).exists():
            print("✅ Django超级用户已存在")
            superuser = User.objects.filter(is_superuser=True).first()
            print(f"   用户名: {superuser.username}")
            print(f"   邮箱: {superuser.email}")
            return superuser

        # 创建超级用户
        username = 'admin'
        email = '<EMAIL>'
        password = 'admin123456'

        user = User.objects.create_superuser(
            username=username,
            email=email,
            password=password
        )

        print("✅ Django超级用户创建成功！")
        print(f"   用户名: {username}")
        print(f"   邮箱: {email}")
        print(f"   密码: {password}")
        print(f"   管理后台: http://127.0.0.1:8000/admin/")

        return user

    except Exception as e:
        print(f"❌ 创建Django超级用户时发生错误: {e}")
        return None

def create_admin_user():
    """创建系统管理员用户（用于业务系统）"""
    try:
        # 检查是否已存在管理员用户
        if UserProfile.objects.filter(account_number='0001').exists():
            print("✅ 系统管理员用户已存在")
            admin_profile = UserProfile.objects.get(account_number='0001')
            print(f"   账号: {admin_profile.account_number}")
            print(f"   用户名: {admin_profile.user.username}")
            print(f"   角色: {admin_profile.get_role_display()}")
            return admin_profile

        # 初始化用户组和权限
        print("🔧 初始化用户组和权限...")
        initialize_user_groups()

        # 创建管理员用户
        print("🔧 创建系统管理员用户...")
        admin_profile = user_create(
            account_number='0001',
            username='系统管理员',
            first_name='系统',
            last_name='管理员',
            email='<EMAIL>',
            role='admin',
            is_active=True,
            created_by=None  # 系统创建
        )

        print("✅ 系统管理员用户创建成功！")
        print(f"   账号: {admin_profile.account_number}")
        print(f"   用户名: {admin_profile.user.username}")
        print(f"   角色: {admin_profile.get_role_display()}")
        print(f"   登录地址: http://127.0.0.1:8000/login/")

        return admin_profile

    except Exception as e:
        print(f"❌ 创建系统管理员用户时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return None

def create_test_department():
    """创建测试科室"""
    try:
        # 检查是否已存在测试科室
        if Department.objects.filter(code='TEST').exists():
            print("✅ 测试科室已存在")
            dept = Department.objects.get(code='TEST')
            print(f"   科室代码: {dept.code}")
            print(f"   科室名称: {dept.name}")
            return dept

        # 创建测试科室
        dept = Department.objects.create(
            code='TEST',
            name='测试科室',
            description='用于系统测试的科室',
            is_active=True
        )

        print("✅ 测试科室创建成功！")
        print(f"   科室代码: {dept.code}")
        print(f"   科室名称: {dept.name}")

        return dept

    except Exception as e:
        print(f"❌ 创建测试科室时发生错误: {e}")
        return None

def create_test_staff():
    """创建测试科室人员"""
    try:
        # 检查是否已存在测试人员
        if UserProfile.objects.filter(account_number='1001').exists():
            print("✅ 测试科室人员已存在")
            staff_profile = UserProfile.objects.get(account_number='1001')
            print(f"   账号: {staff_profile.account_number}")
            print(f"   用户名: {staff_profile.user.username}")
            print(f"   科室: {staff_profile.department.name if staff_profile.department else '无'}")
            return staff_profile

        # 获取测试科室
        test_dept = Department.objects.filter(code='TEST').first()
        if not test_dept:
            print("❌ 测试科室不存在，无法创建测试人员")
            return None

        # 创建测试人员
        staff_profile = user_create(
            account_number='1001',
            username='测试人员',
            first_name='测试',
            last_name='人员',
            email='<EMAIL>',
            role='staff',
            department_id=test_dept.id,
            is_active=True,
            created_by=None  # 系统创建
        )

        print("✅ 测试科室人员创建成功！")
        print(f"   账号: {staff_profile.account_number}")
        print(f"   用户名: {staff_profile.user.username}")
        print(f"   科室: {staff_profile.department.name}")
        print(f"   登录地址: http://127.0.0.1:8000/login/")

        return staff_profile

    except Exception as e:
        print(f"❌ 创建测试科室人员时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主函数：创建完整的用户体系"""
    print("=" * 60)
    print("医疗器械不良事件上报平台 - 用户初始化脚本")
    print("Medical Device Reporting Platform - User Initialization")
    print("=" * 60)

    success_count = 0
    total_count = 4

    # 1. 创建Django超级用户
    print("\n1. 创建Django超级用户...")
    superuser = create_superuser()
    if superuser:
        success_count += 1

    # 2. 创建测试科室
    print("\n2. 创建测试科室...")
    test_dept = create_test_department()
    if test_dept:
        success_count += 1

    # 3. 创建系统管理员用户
    print("\n3. 创建系统管理员用户...")
    admin_user = create_admin_user()
    if admin_user:
        success_count += 1

    # 4. 创建测试科室人员
    print("\n4. 创建测试科室人员...")
    staff_user = create_test_staff()
    if staff_user:
        success_count += 1

    # 5. 显示总结信息
    print("\n" + "=" * 60)
    print("初始化总结")
    print("=" * 60)
    print(f"初始化完成：{success_count}/{total_count} 项成功")

    if success_count == total_count:
        print("✅ 所有用户和数据初始化成功！")
        print("\n🚀 系统访问信息：")
        print("   - Django管理后台: http://127.0.0.1:8000/admin/")
        print("     用户名: admin")
        print("     密码: admin123456")
        print("\n   - 业务系统登录: http://127.0.0.1:8000/login/")
        print("     管理员账号: 0001")
        print("     测试人员账号: 1001")
        print("\n💡 提示：")
        print("   - 4位数账号登录无需密码")
        print("   - 管理员可以管理所有用户和科室")
        print("   - 科室人员只能查看自己的信息")
        print("   - 首次登录建议修改默认配置")

        return True
    else:
        print("❌ 部分初始化失败，请检查错误信息")
        print("\n🔧 故障排除建议：")
        print("   1. 确保数据库连接正常")
        print("   2. 确保已运行数据库迁移：python manage.py migrate")
        print("   3. 检查Django设置配置")
        print("   4. 查看详细错误信息进行调试")

        return False

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 发生未预期的错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
