/**
 * 科室导入页面JavaScript
 * Department Import Page JavaScript
 */

$(document).ready(function() {
    // 初始化文件上传功能
    initializeFileUpload();
    
    // 初始化事件监听器
    initializeEventListeners();
    
    // 初始化模板下载功能
    initializeTemplateDownload();
});

/**
 * 初始化文件上传功能
 */
function initializeFileUpload() {
    const uploadArea = $('#uploadArea');
    const fileInput = $('#excel_file');
    const fileInfo = $('#fileInfo');
    const submitBtn = $('#submitBtn');
    
    // 拖拽上传
    uploadArea.on('dragover', function(e) {
        e.preventDefault();
        e.stopPropagation();
        $(this).addClass('border-primary bg-light');
    });
    
    uploadArea.on('dragleave', function(e) {
        e.preventDefault();
        e.stopPropagation();
        $(this).removeClass('border-primary bg-light');
    });
    
    uploadArea.on('drop', function(e) {
        e.preventDefault();
        e.stopPropagation();
        $(this).removeClass('border-primary bg-light');
        
        const files = e.originalEvent.dataTransfer.files;
        if (files.length > 0) {
            handleFileSelect(files[0]);
        }
    });
    
    // 点击选择文件
    $('#selectFileBtn').on('click', function() {
        fileInput.click();
    });
    
    // 文件选择事件
    fileInput.on('change', function() {
        if (this.files.length > 0) {
            handleFileSelect(this.files[0]);
        }
    });
    
    // 移除文件按钮
    $('#removeFileBtn').on('click', function() {
        clearFileSelection();
    });
}

/**
 * 处理文件选择
 */
function handleFileSelect(file) {
    // 验证文件类型
    const allowedTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
        'application/vnd.ms-excel' // .xls
    ];
    
    if (!allowedTypes.includes(file.type)) {
        showErrorMessage('请选择Excel文件（.xlsx或.xls格式）');
        return;
    }
    
    // 验证文件大小（10MB）
    const maxSize = 10 * 1024 * 1024;
    if (file.size > maxSize) {
        showErrorMessage('文件大小不能超过10MB');
        return;
    }
    
    // 显示文件信息
    displayFileInfo(file);
    
    // 启用提交按钮
    $('#submitBtn').prop('disabled', false);
}

/**
 * 显示文件信息
 */
function displayFileInfo(file) {
    const fileSize = formatFileSize(file.size);
    
    $('#fileName').text(file.name);
    $('#fileSize').text(fileSize);
    
    // 隐藏上传区域，显示文件信息
    $('.upload-content').addClass('d-none');
    $('#fileInfo').removeClass('d-none');
}

/**
 * 清除文件选择
 */
function clearFileSelection() {
    // 清空文件输入
    $('#excel_file').val('');
    
    // 显示上传区域，隐藏文件信息
    $('.upload-content').removeClass('d-none');
    $('#fileInfo').addClass('d-none');
    
    // 禁用提交按钮
    $('#submitBtn').prop('disabled', true);
}

/**
 * 初始化事件监听器
 */
function initializeEventListeners() {
    // 表单提交
    $('#importForm').on('submit', function(e) {
        e.preventDefault();
        handleImportSubmit();
    });
    
    // 导入选项变化
    $('#skipErrors, #updateExisting').on('change', function() {
        updateImportOptions();
    });
}

/**
 * 处理导入提交
 */
function handleImportSubmit() {
    const fileInput = $('#excel_file')[0];
    
    if (!fileInput.files.length) {
        showErrorMessage('请先选择要导入的Excel文件');
        return;
    }
    
    // 显示进度模态框
    $('#importProgressModal').modal('show');
    
    // 创建FormData
    const formData = new FormData();
    formData.append('excel_file', fileInput.files[0]);
    formData.append('skip_errors', $('#skipErrors').is(':checked'));
    formData.append('update_existing', $('#updateExisting').is(':checked');
    formData.append('csrfmiddlewaretoken', getCsrfToken());
    
    // 重置进度
    updateProgress(0, '准备导入...');
    
    // 发送请求
    $.ajax({
        url: '/departments/import/',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        xhr: function() {
            const xhr = new window.XMLHttpRequest();
            
            // 上传进度
            xhr.upload.addEventListener('progress', function(e) {
                if (e.lengthComputable) {
                    const percentComplete = (e.loaded / e.total) * 50; // 上传占50%
                    updateProgress(percentComplete, '上传文件中...');
                }
            }, false);
            
            return xhr;
        },
        success: function(response) {
            updateProgress(100, '导入完成');
            
            setTimeout(() => {
                $('#importProgressModal').modal('hide');
                
                if (response.success) {
                    showImportResults(response);
                } else {
                    showErrorMessage(response.message || '导入失败');
                }
            }, 1000);
        },
        error: function(xhr, status, error) {
            console.error('导入失败:', error);
            $('#importProgressModal').modal('hide');
            
            let errorMessage = '导入失败，请重试';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            }
            
            showErrorMessage(errorMessage);
        }
    });
}

/**
 * 更新导入进度
 */
function updateProgress(percent, status) {
    $('#importProgress').css('width', percent + '%');
    $('#importStatus').text(status);
}

/**
 * 显示导入结果
 */
function showImportResults(response) {
    const { success_count, failed_count, errors } = response;
    
    let message = `导入完成！成功导入 ${success_count} 个科室`;
    if (failed_count > 0) {
        message += `，失败 ${failed_count} 个`;
    }
    
    if (failed_count > 0 && errors && errors.length > 0) {
        // 显示详细错误信息
        let errorDetails = '<div class="mt-3"><strong>错误详情：</strong><ul>';
        errors.slice(0, 5).forEach(error => {
            errorDetails += `<li>${error}</li>`;
        });
        if (errors.length > 5) {
            errorDetails += `<li>等${errors.length}个错误</li>`;
        }
        errorDetails += '</ul></div>';
        
        showWarningMessage(message + errorDetails);
    } else {
        showSuccessMessage(message);
    }
    
    // 清除文件选择
    setTimeout(() => {
        clearFileSelection();
    }, 2000);
}

/**
 * 初始化模板下载功能
 */
function initializeTemplateDownload() {
    $('#downloadTemplateBtn').on('click', function() {
        const btn = $(this);
        LoadingIndicator.show(btn[0]);
        
        // 创建下载链接
        const link = document.createElement('a');
        link.href = '/departments/template/';
        link.download = '科室导入模板.xlsx';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        setTimeout(() => {
            LoadingIndicator.hide(btn[0]);
            showSuccessMessage('模板下载完成');
        }, 1000);
    });
}

/**
 * 更新导入选项
 */
function updateImportOptions() {
    const skipErrors = $('#skipErrors').is(':checked');
    const updateExisting = $('#updateExisting').is(':checked');
    
    // 可以在这里添加选项变化的逻辑
    console.log('导入选项更新:', { skipErrors, updateExisting });
}

/**
 * 格式化文件大小
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 页面离开前确认
 */
window.addEventListener('beforeunload', function(event) {
    if ($('#excel_file')[0].files.length > 0) {
        event.preventDefault();
        event.returnValue = '您已选择了文件但未导入，确定要离开吗？';
        return event.returnValue;
    }
});
