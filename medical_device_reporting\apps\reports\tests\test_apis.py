"""
报告管理API测试
Reports Management APIs Tests
"""

import json
from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from datetime import date

from apps.users.models import UserProfile, Department
from apps.reports.models import AdverseEventReport


class ReportAPITest(TestCase):
    """报告API测试"""
    
    def setUp(self):
        """测试前准备"""
        self.client = Client()
        
        # 创建管理员用户
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # 创建普通用户
        self.staff_user = User.objects.create_user(
            username='staff',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # 创建科室
        self.department = Department.objects.create(
            code='TEST',
            name='测试科室',
            created_by=self.admin_user
        )
        
        # 创建用户配置文件
        self.admin_profile = UserProfile.objects.create(
            user=self.admin_user,
            account_number='0001',
            role='admin',
            created_by=self.admin_user
        )
        
        self.staff_profile = UserProfile.objects.create(
            user=self.staff_user,
            account_number='1001',
            department=self.department,
            role='staff',
            created_by=self.admin_user
        )
        
        # 创建测试报告
        self.test_report = AdverseEventReport.objects.create(
            reporter=self.staff_user,
            reporter_name='测试用户',
            patient_name='张三',
            event_date=date.today(),
            injury_level='moderate',
            event_description='测试事件描述',
            device_name='测试器械'
        )
    
    def test_report_list_api(self):
        """测试报告列表API"""
        self.client.login(username='staff', password='testpass123')
        url = reverse('reports:api_report_list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        
        self.assertIn('results', data)
        self.assertIn('count', data)
        self.assertTrue(len(data['results']) > 0)
        
        # 检查报告数据
        report_data = data['results'][0]
        self.assertEqual(report_data['id'], self.test_report.id)
        self.assertEqual(report_data['device_name'], self.test_report.device_name)
    
    def test_report_detail_api(self):
        """测试报告详情API"""
        self.client.login(username='staff', password='testpass123')
        url = reverse('reports:api_report_detail', kwargs={'report_id': self.test_report.id})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        
        self.assertEqual(data['id'], self.test_report.id)
        self.assertEqual(data['report_number'], self.test_report.report_number)
        self.assertEqual(data['device_name'], self.test_report.device_name)
        self.assertEqual(data['patient_name'], self.test_report.patient_name)
    
    def test_report_create_api(self):
        """测试报告创建API"""
        self.client.login(username='staff', password='testpass123')
        url = reverse('reports:api_report_create')
        
        data = {
            'reporter_name': '新测试用户',
            'patient_name': '李四',
            'patient_age': 30,
            'patient_gender': 'female',
            'event_date': date.today().strftime('%Y-%m-%d'),
            'injury_level': 'mild',
            'event_description': '新测试事件描述',
            'device_name': '新测试器械'
        }
        
        response = self.client.post(
            url,
            json.dumps(data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 201)
        response_data = json.loads(response.content)
        
        self.assertEqual(response_data['device_name'], '新测试器械')
        self.assertEqual(response_data['patient_name'], '李四')
        self.assertEqual(response_data['status'], 'draft')
        
        # 检查数据库中是否创建了报告
        new_report = AdverseEventReport.objects.filter(
            device_name='新测试器械'
        ).first()
        self.assertIsNotNone(new_report)
    
    def test_report_update_api(self):
        """测试报告更新API"""
        self.client.login(username='staff', password='testpass123')
        url = reverse('reports:api_report_update', kwargs={'report_id': self.test_report.id})
        
        data = {
            'patient_name': '更新患者',
            'device_name': '更新器械',
            'injury_level': 'severe'
        }
        
        response = self.client.put(
            url,
            json.dumps(data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        response_data = json.loads(response.content)
        
        self.assertEqual(response_data['patient_name'], '更新患者')
        self.assertEqual(response_data['device_name'], '更新器械')
        self.assertEqual(response_data['injury_level'], 'severe')
        
        # 检查数据库中的更新
        self.test_report.refresh_from_db()
        self.assertEqual(self.test_report.patient_name, '更新患者')
    
    def test_report_submit_api(self):
        """测试报告提交API"""
        self.client.login(username='staff', password='testpass123')
        url = reverse('reports:api_report_submit', kwargs={'report_id': self.test_report.id})
        
        response = self.client.post(url)
        
        self.assertEqual(response.status_code, 200)
        response_data = json.loads(response.content)
        
        self.assertEqual(response_data['status'], 'submitted')
        self.assertIsNotNone(response_data['submitted_at'])
        
        # 检查数据库中的状态更新
        self.test_report.refresh_from_db()
        self.assertEqual(self.test_report.status, 'submitted')
    
    def test_report_review_api_admin_only(self):
        """测试报告审核API仅管理员可用"""
        # 设置报告为已提交状态
        self.test_report.status = 'submitted'
        self.test_report.save()
        
        url = reverse('reports:api_report_review', kwargs={'report_id': self.test_report.id})
        
        # 普通用户访问应该被拒绝
        self.client.login(username='staff', password='testpass123')
        response = self.client.post(url, {
            'action': 'approve',
            'comments': '普通用户审核'
        })
        
        self.assertEqual(response.status_code, 403)
        
        # 管理员可以审核
        self.client.login(username='admin', password='testpass123')
        response = self.client.post(url, {
            'action': 'approve',
            'comments': '管理员审核通过'
        })
        
        self.assertEqual(response.status_code, 200)
        response_data = json.loads(response.content)
        
        self.assertEqual(response_data['status'], 'approved')
        self.assertEqual(response_data['review_comments'], '管理员审核通过')
    
    def test_report_delete_api(self):
        """测试报告删除API"""
        self.client.login(username='staff', password='testpass123')
        url = reverse('reports:api_report_delete', kwargs={'report_id': self.test_report.id})
        
        response = self.client.delete(url)
        
        self.assertEqual(response.status_code, 200)
        response_data = json.loads(response.content)
        
        self.assertTrue(response_data['is_deleted'])
        
        # 检查数据库中的软删除
        self.test_report.refresh_from_db()
        self.assertTrue(self.test_report.is_deleted)
    
    def test_dashboard_api(self):
        """测试仪表板API"""
        self.client.login(username='admin', password='testpass123')
        url = reverse('reports:api_dashboard')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        
        # 检查统计数据
        self.assertIn('stats', data)
        self.assertIn('recent_reports', data)
        
        stats = data['stats']
        self.assertIn('total_reports', stats)
        self.assertIn('pending_review', stats)
        self.assertIn('serious_events', stats)
    
    def test_statistics_api(self):
        """测试统计API"""
        self.client.login(username='admin', password='testpass123')
        url = reverse('reports:api_statistics')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        
        # 检查统计数据结构
        self.assertIn('total_reports', data)
        self.assertIn('status_distribution', data)
        self.assertIn('injury_level_distribution', data)
        self.assertIn('monthly_trend', data)
    
    def test_api_authentication_required(self):
        """测试API需要认证"""
        url = reverse('reports:api_report_list')
        response = self.client.get(url)
        
        # 未认证用户应该被重定向或返回401
        self.assertIn(response.status_code, [302, 401])
    
    def test_api_permission_control(self):
        """测试API权限控制"""
        # 创建其他用户的报告
        other_user = User.objects.create_user(username='other', password='testpass123')
        other_profile = UserProfile.objects.create(
            user=other_user,
            account_number='2001',
            department=self.department,
            role='staff',
            created_by=self.admin_user
        )
        
        other_report = AdverseEventReport.objects.create(
            reporter=other_user,
            reporter_name='其他用户',
            patient_name='王五',
            event_date=date.today(),
            injury_level='moderate',
            event_description='其他事件描述',
            device_name='其他器械'
        )
        
        # 普通用户不能访问其他人的报告详情
        self.client.login(username='staff', password='testpass123')
        url = reverse('reports:api_report_detail', kwargs={'report_id': other_report.id})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 403)
        
        # 管理员可以访问任何报告
        self.client.login(username='admin', password='testpass123')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
    
    def test_api_data_validation(self):
        """测试API数据验证"""
        self.client.login(username='staff', password='testpass123')
        url = reverse('reports:api_report_create')
        
        # 发送无效数据
        invalid_data = {
            'reporter_name': '',  # 空名称
            'patient_age': -1,    # 无效年龄
            'event_date': '2025-01-01',  # 未来日期
            'injury_level': 'invalid',   # 无效伤害程度
        }
        
        response = self.client.post(
            url,
            json.dumps(invalid_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 400)
        response_data = json.loads(response.content)
        
        self.assertIn('errors', response_data)
        self.assertTrue(len(response_data['errors']) > 0)
