/**
 * Report Detail JavaScript for Medical Device Reporting Platform
 * 医疗器械不良事件上报平台报告详情脚本
 */

document.addEventListener('DOMContentLoaded', function() {
    // 初始化报告详情页面
    initializeReportDetail();
    
    /**
     * 初始化报告详情页面
     */
    function initializeReportDetail() {
        // 绑定事件监听器
        bindEventListeners();
        
        // 初始化打印功能
        initializePrint();
        
        // 初始化时间线动画
        initializeTimelineAnimation();
        
        console.log('报告详情页面初始化完成');
    }
    
    /**
     * 绑定事件监听器
     */
    function bindEventListeners() {
        // 提交按钮
        const submitBtn = document.getElementById('submitBtn');
        if (submitBtn) {
            submitBtn.addEventListener('click', handleSubmitReport);
        }
        
        // 确认提交按钮
        const confirmSubmitBtn = document.getElementById('confirmSubmitBtn');
        if (confirmSubmitBtn) {
            confirmSubmitBtn.addEventListener('click', confirmSubmitReport);
        }
        
        // 打印按钮
        const printButtons = document.querySelectorAll('[onclick="window.print()"]');
        printButtons.forEach(button => {
            button.removeAttribute('onclick');
            button.addEventListener('click', handlePrint);
        });
        
        // 返回按钮
        const backButtons = document.querySelectorAll('.btn-back');
        backButtons.forEach(button => {
            button.addEventListener('click', handleGoBack);
        });
        
        // 键盘快捷键
        document.addEventListener('keydown', handleKeyboardShortcuts);
    }
    
    /**
     * 处理提交报告
     */
    function handleSubmitReport(event) {
        const button = event.target;
        const reportId = button.getAttribute('data-report-id');
        
        if (!reportId) {
            showErrorMessage('无法获取报告ID');
            return;
        }
        
        // 显示确认对话框
        const modal = document.getElementById('submitModal');
        if (modal) {
            const confirmBtn = document.getElementById('confirmSubmitBtn');
            if (confirmBtn) {
                confirmBtn.setAttribute('data-report-id', reportId);
            }
            
            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();
        }
    }
    
    /**
     * 确认提交报告
     */
    function confirmSubmitReport() {
        const confirmBtn = document.getElementById('confirmSubmitBtn');
        const reportId = confirmBtn ? confirmBtn.getAttribute('data-report-id') : null;
        
        if (!reportId) {
            showErrorMessage('无法获取报告ID');
            return;
        }
        
        // 显示加载状态
        showLoading(confirmBtn);
        
        const url = `/reports/api/reports/${reportId}/submit/`;
        
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken()
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            hideLoading(confirmBtn);
            
            // 关闭模态框
            const modal = document.getElementById('submitModal');
            if (modal) {
                const bootstrapModal = bootstrap.Modal.getInstance(modal);
                if (bootstrapModal) {
                    bootstrapModal.hide();
                }
            }
            
            showSuccessMessage('报告提交成功');
            
            // 延迟刷新页面以显示新状态
            setTimeout(() => {
                location.reload();
            }, 1500);
        })
        .catch(error => {
            hideLoading(confirmBtn);
            console.error('提交报告失败:', error);
            showErrorMessage('提交报告失败，请稍后重试');
        });
    }
    
    /**
     * 处理打印
     */
    function handlePrint(event) {
        event.preventDefault();
        
        // 添加打印样式类
        document.body.classList.add('printing');
        
        // 延迟执行打印，确保样式生效
        setTimeout(() => {
            window.print();
            
            // 打印完成后移除样式类
            setTimeout(() => {
                document.body.classList.remove('printing');
            }, 1000);
        }, 100);
    }
    
    /**
     * 处理返回
     */
    function handleGoBack(event) {
        event.preventDefault();
        
        // 检查是否有历史记录
        if (window.history.length > 1) {
            window.history.back();
        } else {
            // 如果没有历史记录，跳转到报告列表
            window.location.href = '/reports/list/';
        }
    }
    
    /**
     * 处理键盘快捷键
     */
    function handleKeyboardShortcuts(event) {
        // Ctrl + P: 打印
        if (event.ctrlKey && event.key === 'p') {
            event.preventDefault();
            handlePrint(event);
        }
        
        // Ctrl + S: 提交（如果可以提交）
        if (event.ctrlKey && event.key === 's') {
            event.preventDefault();
            const submitBtn = document.getElementById('submitBtn');
            if (submitBtn && !submitBtn.disabled) {
                handleSubmitReport({ target: submitBtn });
            }
        }
        
        // Escape: 返回
        if (event.key === 'Escape') {
            handleGoBack(event);
        }
    }
    
    /**
     * 初始化打印功能
     */
    function initializePrint() {
        // 添加打印前事件监听器
        window.addEventListener('beforeprint', function() {
            document.body.classList.add('printing');
        });
        
        // 添加打印后事件监听器
        window.addEventListener('afterprint', function() {
            document.body.classList.remove('printing');
        });
    }
    
    /**
     * 初始化时间线动画
     */
    function initializeTimelineAnimation() {
        const timelineItems = document.querySelectorAll('.timeline-item');
        
        // 为时间线项目添加动画效果
        timelineItems.forEach((item, index) => {
            item.style.opacity = '0';
            item.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                item.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                item.style.opacity = '1';
                item.style.transform = 'translateY(0)';
            }, index * 200);
        });
    }
    
    /**
     * 显示加载状态
     */
    function showLoading(button) {
        if (button) {
            button.disabled = true;
            const originalText = button.innerHTML;
            button.setAttribute('data-original-text', originalText);
            button.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status"></span>处理中...';
        }
    }
    
    /**
     * 隐藏加载状态
     */
    function hideLoading(button) {
        if (button) {
            button.disabled = false;
            const originalText = button.getAttribute('data-original-text');
            if (originalText) {
                button.innerHTML = originalText;
                button.removeAttribute('data-original-text');
            }
        }
    }
    
    /**
     * 获取CSRF Token
     */
    function getCsrfToken() {
        const token = document.querySelector('[name=csrfmiddlewaretoken]');
        return token ? token.value : '';
    }
    
    /**
     * 显示成功消息
     */
    function showSuccessMessage(message) {
        if (typeof showMessage === 'function') {
            showMessage(message, 'success', 3000);
        }
    }
    
    /**
     * 显示错误消息
     */
    function showErrorMessage(message) {
        if (typeof showMessage === 'function') {
            showMessage(message, 'danger', 5000);
        }
    }
    
    /**
     * 显示信息消息
     */
    function showInfoMessage(message) {
        if (typeof showMessage === 'function') {
            showMessage(message, 'info', 3000);
        }
    }
});
