{% extends 'base.html' %}
{% load static %}
{% load permission_tags %}

{% block title %}{% block page_title %}报告管理{% endblock %} - 医疗器械不良事件上报平台{% endblock %}

{% block extra_css %}
<link href="{% static 'reports/css/reports.css' %}" rel="stylesheet">
{% block reports_extra_css %}{% endblock %}
{% endblock %}

{% block nav_items %}
<li class="nav-item">
    <a class="nav-link" href="{% url 'users:dashboard' %}">
        <i class="bi bi-house"></i>
        用户中心
    </a>
</li>
<li class="nav-item">
    <a class="nav-link {% if request.resolver_match.namespace == 'reports' %}active{% endif %}" href="{% url 'reports:dashboard' %}">
        <i class="bi bi-file-medical"></i>
        报告管理
    </a>
</li>
<!-- 统计分析菜单 -->
<li class="nav-item dropdown">
    <a class="nav-link dropdown-toggle {% if request.resolver_match.url_name in 'statistics_dashboard,statistics_detail' %}active{% endif %}"
       href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
        <i class="bi bi-graph-up"></i>
        统计分析
    </a>
    <ul class="dropdown-menu">
        <li><a class="dropdown-item" href="{% url 'reports:statistics_dashboard' %}">
            <i class="bi bi-speedometer2 me-2"></i>统计仪表板
        </a></li>
        <li><a class="dropdown-item" href="{% url 'reports:statistics_detail' %}">
            <i class="bi bi-bar-chart me-2"></i>详细分析
        </a></li>
        {% if user.profile.is_admin %}
        <li><hr class="dropdown-divider"></li>
        <li><a class="dropdown-item" href="{% url 'reports:api_cache_stats' %}" target="_blank">
            <i class="bi bi-cpu me-2"></i>缓存监控
        </a></li>
        {% endif %}
    </ul>
</li>

{% if user.profile.is_admin %}
<li class="nav-item">
    <a class="nav-link" href="{% url 'users:user_list' %}">
        <i class="bi bi-people"></i>
        用户管理
    </a>
</li>
{% endif %}
<li class="nav-item dropdown">
    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
        <i class="bi bi-person-circle"></i>
        {{ user.get_full_name|default:user.username }}
    </a>
    <ul class="dropdown-menu">
        <li><a class="dropdown-item" href="{% url 'users:profile' %}"><i class="bi bi-gear me-2"></i>个人设置</a></li>
        <li><hr class="dropdown-divider"></li>
        <li><a class="dropdown-item" href="{% url 'users:logout' %}"><i class="bi bi-box-arrow-right me-2"></i>退出登录</a></li>
    </ul>
</li>
{% endblock %}

{% block content %}
<div class="reports-container">
    <!-- 面包屑导航 -->
    <nav aria-label="breadcrumb" class="mb-3">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'users:dashboard' %}">首页</a></li>
            <li class="breadcrumb-item"><a href="{% url 'reports:dashboard' %}">报告管理</a></li>
            {% block breadcrumb %}{% endblock %}
        </ol>
    </nav>

    <!-- 页面标题 -->
    {% block page_header %}
    <div class="page-header mb-4">
        <div class="row align-items-center">
            <div class="col">
                <h2 class="page-title">
                    <i class="bi bi-file-medical me-2"></i>
                    {% block page_heading %}报告管理{% endblock %}
                </h2>
                <p class="page-subtitle text-muted">{% block page_description %}医疗器械不良事件报告管理{% endblock %}</p>
            </div>
            <div class="col-auto">
                {% block page_actions %}{% endblock %}
            </div>
        </div>
    </div>
    {% endblock %}

    <!-- 消息提示 -->
    {% if messages %}
    <div class="messages-container mb-4">
        {% for message in messages %}
        <div class="alert alert-{{ message.tags|default:'info' }} alert-dismissible fade show" role="alert">
            {% if message.tags == 'error' %}
                <i class="bi bi-exclamation-triangle me-2"></i>
            {% elif message.tags == 'warning' %}
                <i class="bi bi-exclamation-circle me-2"></i>
            {% elif message.tags == 'success' %}
                <i class="bi bi-check-circle me-2"></i>
            {% else %}
                <i class="bi bi-info-circle me-2"></i>
            {% endif %}
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- 主要内容区域 -->
    {% block reports_content %}
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center py-5">
                    <i class="bi bi-file-medical display-1 text-muted mb-3"></i>
                    <h4 class="text-muted">报告管理模块</h4>
                    <p class="text-muted">请选择具体的功能页面</p>
                </div>
            </div>
        </div>
    </div>
    {% endblock %}
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'reports/js/reports.js' %}"></script>
{% block reports_extra_js %}{% endblock %}
{% endblock %}
