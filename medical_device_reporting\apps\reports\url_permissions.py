"""
不良事件上报管理URL权限配置
Adverse Event Reports Management URL Permissions Configuration
"""

from django.urls import reverse
from django.core.exceptions import PermissionDenied
from django.http import Http404

from .models import AdverseEventReport
from .selectors import report_detail


class URLPermissionConfig:
    """
    URL权限配置类
    
    定义各个URL的权限要求和检查逻辑
    """
    
    # URL权限映射
    URL_PERMISSIONS = {
        # 仪表板 - 科室成员或管理员
        'reports:dashboard': {
            'required_role': 'department_member_or_admin',
            'description': '报告管理仪表板'
        },
        
        # 报告列表 - 科室成员或管理员
        'reports:report_list': {
            'required_role': 'department_member_or_admin',
            'description': '报告列表页面'
        },
        
        # 报告创建 - 科室成员或管理员
        'reports:report_create': {
            'required_role': 'department_member_or_admin',
            'description': '创建报告页面'
        },
        
        # 分步创建 - 科室成员或管理员
        'reports:report_step_create': {
            'required_role': 'department_member_or_admin',
            'description': '分步创建报告页面'
        },
        
        # 报告详情 - 科室成员或管理员（对象级权限）
        'reports:report_detail': {
            'required_role': 'department_member_or_admin',
            'object_permission': True,
            'description': '报告详情页面'
        },
        
        # 报告编辑 - 报告所有者或管理员
        'reports:report_edit': {
            'required_role': 'report_owner_or_admin',
            'object_permission': True,
            'description': '编辑报告页面'
        },
        
        # 报告提交 - 报告所有者或管理员
        'reports:report_submit': {
            'required_role': 'report_owner_or_admin',
            'object_permission': True,
            'description': '提交报告'
        },
        
        # 报告审核 - 仅管理员
        'reports:report_review': {
            'required_role': 'admin_only',
            'object_permission': True,
            'description': '审核报告页面'
        },
        
        # 待审核报告 - 仅管理员
        'reports:pending_review': {
            'required_role': 'admin_only',
            'description': '待审核报告列表'
        },
        
        # 严重事件 - 仅管理员
        'reports:serious_events': {
            'required_role': 'admin_only',
            'description': '严重事件报告列表'
        },
    }
    
    @classmethod
    def check_url_permission(cls, user, url_name, **kwargs):
        """
        检查URL权限
        
        Args:
            user: 用户对象
            url_name: URL名称
            **kwargs: URL参数
            
        Returns:
            bool: 是否有权限
            
        Raises:
            PermissionDenied: 权限不足
            Http404: 对象不存在
        """
        
        # 检查用户是否已认证
        if not user.is_authenticated:
            raise PermissionDenied('用户未认证')
        
        # 检查用户是否有profile
        if not hasattr(user, 'profile') or not user.profile:
            raise PermissionDenied('用户配置文件不存在')
        
        # 获取URL权限配置
        permission_config = cls.URL_PERMISSIONS.get(url_name)
        if not permission_config:
            # 如果没有配置，默认允许已认证用户访问
            return True
        
        user_profile = user.profile
        required_role = permission_config['required_role']
        
        # 检查基础角色权限
        if not cls._check_role_permission(user_profile, required_role):
            raise PermissionDenied(f'访问 {permission_config["description"]} 需要相应权限')
        
        # 检查对象级权限
        if permission_config.get('object_permission'):
            report_id = kwargs.get('report_id') or kwargs.get('pk')
            if report_id:
                report = report_detail(report_id, user_profile=user_profile)
                if not report:
                    raise Http404('报告不存在或您没有权限查看')
                
                if not cls._check_object_permission(user_profile, report, required_role):
                    raise PermissionDenied(f'您没有权限对此报告执行 {permission_config["description"]} 操作')
        
        return True
    
    @classmethod
    def _check_role_permission(cls, user_profile, required_role):
        """
        检查角色权限
        
        Args:
            user_profile: 用户配置文件
            required_role: 所需角色
            
        Returns:
            bool: 是否有权限
        """
        
        if required_role == 'admin_only':
            return user_profile.is_admin
        
        elif required_role == 'department_member_or_admin':
            if user_profile.is_admin:
                return True
            return (user_profile.is_staff_member and 
                   user_profile.department and 
                   user_profile.department.is_active)
        
        elif required_role == 'report_owner_or_admin':
            # 基础权限检查，对象级权限在 _check_object_permission 中检查
            if user_profile.is_admin:
                return True
            return (user_profile.is_staff_member and 
                   user_profile.department and 
                   user_profile.department.is_active)
        
        return False
    
    @classmethod
    def _check_object_permission(cls, user_profile, report, required_role):
        """
        检查对象级权限
        
        Args:
            user_profile: 用户配置文件
            report: 报告对象
            required_role: 所需角色
            
        Returns:
            bool: 是否有权限
        """
        
        if required_role == 'admin_only':
            return user_profile.is_admin
        
        elif required_role == 'report_owner_or_admin':
            if user_profile.is_admin:
                return True
            # 检查是否为报告创建者
            return report.reporter == user_profile
        
        elif required_role == 'department_member_or_admin':
            if user_profile.is_admin:
                return True
            # 检查是否为同科室成员
            return (user_profile.department and 
                   report.department == user_profile.department)
        
        return False


def check_report_access_permission(user, report_id, action='view'):
    """
    检查报告访问权限的便捷函数
    
    Args:
        user: 用户对象
        report_id: 报告ID
        action: 操作类型 ('view', 'edit', 'submit', 'review')
        
    Returns:
        tuple: (report, has_permission)
        
    Raises:
        PermissionDenied: 权限不足
        Http404: 报告不存在
    """
    
    if not user.is_authenticated:
        raise PermissionDenied('用户未认证')
    
    if not hasattr(user, 'profile') or not user.profile:
        raise PermissionDenied('用户配置文件不存在')
    
    user_profile = user.profile
    
    # 获取报告
    report = report_detail(report_id, user_profile=user_profile)
    if not report:
        raise Http404('报告不存在或您没有权限查看')
    
    # 根据操作类型检查权限
    if action == 'view':
        # 查看权限：管理员或同科室成员
        has_permission = (user_profile.is_admin or 
                         (user_profile.department and 
                          report.department == user_profile.department))
    
    elif action == 'edit':
        # 编辑权限：管理员或报告创建者，且报告可编辑
        has_permission = (report.can_edit and 
                         (user_profile.is_admin or 
                          report.reporter == user_profile))
    
    elif action == 'submit':
        # 提交权限：管理员或报告创建者，且报告可提交
        has_permission = (report.can_submit and 
                         (user_profile.is_admin or 
                          report.reporter == user_profile))
    
    elif action == 'review':
        # 审核权限：仅管理员，且报告可审核
        has_permission = (user_profile.is_admin and report.can_review)
    
    else:
        has_permission = False
    
    if not has_permission:
        raise PermissionDenied(f'您没有权限对此报告执行 {action} 操作')
    
    return report, True


def get_user_accessible_reports(user):
    """
    获取用户可访问的报告查询集
    
    Args:
        user: 用户对象
        
    Returns:
        QuerySet: 用户可访问的报告查询集
    """
    
    if not user.is_authenticated:
        return AdverseEventReport.objects.none()
    
    if not hasattr(user, 'profile') or not user.profile:
        return AdverseEventReport.objects.none()
    
    user_profile = user.profile
    
    # 管理员可以访问所有报告
    if user_profile.is_admin:
        return AdverseEventReport.objects.filter(is_deleted=False)
    
    # 科室成员只能访问本科室的报告
    if (user_profile.is_staff_member and 
        user_profile.department and 
        user_profile.department.is_active):
        return AdverseEventReport.objects.filter(
            department=user_profile.department,
            is_deleted=False
        )
    
    # 其他用户无权访问
    return AdverseEventReport.objects.none()
