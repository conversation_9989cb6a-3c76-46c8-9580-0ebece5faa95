"""
不良事件上报管理应用配置
Adverse Event Reports Management App Configuration for Medical Device Reporting Platform
"""

from django.apps import AppConfig


class ReportsConfig(AppConfig):
    """
    不良事件上报管理应用配置类
    
    配置不良事件上报管理应用的基本信息和初始化逻辑
    """
    
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.reports'
    verbose_name = '不良事件上报管理'
    verbose_name_plural = '不良事件上报管理'
    
    def ready(self):
        """
        应用准备就绪时的初始化逻辑
        
        在Django启动时执行一次，用于注册信号处理器、
        初始化权限组、注册自定义检查等
        """
        
        # 导入信号处理器
        try:
            from . import signals
        except ImportError:
            pass
        
        # 初始化报告权限组
        self._setup_report_groups()
        
        # 初始化日志
        self._setup_logging()
    
    def _setup_report_groups(self):
        """
        初始化报告相关权限组
        """
        # 这里可以添加权限组初始化逻辑
        pass
    
    def _setup_logging(self):
        """
        设置应用日志配置
        """
        import logging
        logger = logging.getLogger(__name__)
        logger.info('不良事件上报管理应用已加载')
