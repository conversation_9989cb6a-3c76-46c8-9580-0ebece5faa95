#!/usr/bin/env python
"""
导航和权限控制集成测试
Navigation and Permission Control Integration Test

测试统计分析功能的导航集成和权限控制。
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from apps.users.models import UserProfile, Department

def create_test_users():
    """创建测试用户"""
    print("创建测试用户...")
    
    # 创建科室
    department = Department.objects.create(
        code='NAV_TEST',
        name='导航测试科室',
        is_active=True,
        created_by_id=1
    )
    
    # 创建管理员用户
    admin_user = User.objects.create_user(
        username='nav_test_admin',
        email='<EMAIL>'
    )
    admin_profile = UserProfile.objects.create(
        user=admin_user,
        account_number='9001',
        department=department,
        role='admin',
        created_by=admin_user
    )
    
    # 创建科室人员
    staff_user = User.objects.create_user(
        username='nav_test_staff',
        email='<EMAIL>'
    )
    staff_profile = UserProfile.objects.create(
        user=staff_user,
        account_number='9002',
        department=department,
        role='staff',
        created_by=admin_user
    )
    
    print(f"创建管理员用户: {admin_profile.account_number}")
    print(f"创建科室人员: {staff_profile.account_number}")
    
    return admin_user, staff_user, department

def cleanup_test_users(admin_user, staff_user, department):
    """清理测试用户"""
    print("清理测试用户...")
    try:
        UserProfile.objects.filter(user__in=[admin_user, staff_user]).delete()
        User.objects.filter(id__in=[admin_user.id, staff_user.id]).delete()
        Department.objects.filter(id=department.id).delete()
        print("测试用户清理完成")
    except Exception as e:
        print(f"清理测试用户失败: {e}")

def test_navigation_access():
    """测试导航访问权限"""
    print("\n=== 导航访问权限测试 ===")
    
    admin_user, staff_user, department = create_test_users()
    
    try:
        client = Client()
        
        # 测试页面列表
        test_pages = [
            ('用户中心', '/dashboard/'),
            ('报告管理', '/reports/'),
            ('统计仪表板', '/reports/statistics/'),
            ('详细分析', '/reports/statistics/detail/'),
        ]
        
        # 测试未登录用户访问
        print("\n--- 未登录用户访问测试 ---")
        for page_name, url in test_pages:
            response = client.get(url)
            if response.status_code == 302:  # 重定向到登录页面
                print(f"✅ {page_name}: 正确重定向到登录页面")
            else:
                print(f"❌ {page_name}: 状态码 {response.status_code}，应该重定向")
        
        # 测试管理员用户访问
        print("\n--- 管理员用户访问测试 ---")
        client.force_login(admin_user)
        for page_name, url in test_pages:
            response = client.get(url)
            if response.status_code == 200:
                print(f"✅ {page_name}: 管理员可以正常访问")
            else:
                print(f"❌ {page_name}: 状态码 {response.status_code}，管理员应该可以访问")
        
        # 测试科室人员访问
        print("\n--- 科室人员访问测试 ---")
        client.force_login(staff_user)
        for page_name, url in test_pages:
            response = client.get(url)
            if response.status_code == 200:
                print(f"✅ {page_name}: 科室人员可以正常访问")
            else:
                print(f"❌ {page_name}: 状态码 {response.status_code}，科室人员应该可以访问")
        
        return True
        
    except Exception as e:
        print(f"导航访问权限测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        cleanup_test_users(admin_user, staff_user, department)

def test_navigation_menu_content():
    """测试导航菜单内容"""
    print("\n=== 导航菜单内容测试 ===")
    
    admin_user, staff_user, department = create_test_users()
    
    try:
        client = Client()
        
        # 测试管理员导航菜单
        print("\n--- 管理员导航菜单测试 ---")
        client.force_login(admin_user)
        response = client.get('/dashboard/')
        
        if response.status_code == 200:
            content = response.content.decode('utf-8')
            
            # 检查统计分析菜单项
            if '统计分析' in content:
                print("✅ 统计分析菜单项存在")
            else:
                print("❌ 统计分析菜单项缺失")
            
            if '统计仪表板' in content:
                print("✅ 统计仪表板链接存在")
            else:
                print("❌ 统计仪表板链接缺失")
            
            if '详细分析' in content:
                print("✅ 详细分析链接存在")
            else:
                print("❌ 详细分析链接缺失")
            
            if '缓存监控' in content:
                print("✅ 管理员专用缓存监控链接存在")
            else:
                print("❌ 管理员专用缓存监控链接缺失")
        
        # 测试科室人员导航菜单
        print("\n--- 科室人员导航菜单测试 ---")
        client.force_login(staff_user)
        response = client.get('/dashboard/')
        
        if response.status_code == 200:
            content = response.content.decode('utf-8')
            
            # 检查统计分析菜单项
            if '统计分析' in content:
                print("✅ 科室人员可以看到统计分析菜单")
            else:
                print("❌ 科室人员看不到统计分析菜单")
            
            # 科室人员不应该看到缓存监控
            if '缓存监控' not in content:
                print("✅ 科室人员正确隐藏缓存监控链接")
            else:
                print("❌ 科室人员不应该看到缓存监控链接")
        
        return True
        
    except Exception as e:
        print(f"导航菜单内容测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        cleanup_test_users(admin_user, staff_user, department)

def test_breadcrumb_navigation():
    """测试面包屑导航"""
    print("\n=== 面包屑导航测试 ===")
    
    admin_user, staff_user, department = create_test_users()
    
    try:
        client = Client()
        client.force_login(admin_user)
        
        # 测试统计分析页面的面包屑
        test_pages = [
            ('统计仪表板', '/reports/statistics/', ['首页', '报告管理', '统计分析']),
            ('详细分析', '/reports/statistics/detail/', ['首页', '报告管理', '详细分析']),
        ]
        
        for page_name, url, expected_breadcrumbs in test_pages:
            response = client.get(url)
            if response.status_code == 200:
                content = response.content.decode('utf-8')
                
                # 检查面包屑项目
                all_found = True
                for breadcrumb in expected_breadcrumbs:
                    if breadcrumb not in content:
                        print(f"❌ {page_name}: 缺少面包屑项目 '{breadcrumb}'")
                        all_found = False
                
                if all_found:
                    print(f"✅ {page_name}: 面包屑导航正确")
            else:
                print(f"❌ {page_name}: 无法访问页面，状态码 {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"面包屑导航测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        cleanup_test_users(admin_user, staff_user, department)

def test_url_routing():
    """测试URL路由配置"""
    print("\n=== URL路由配置测试 ===")
    
    admin_user, staff_user, department = create_test_users()
    
    try:
        client = Client()
        client.force_login(admin_user)
        
        # 测试统计分析相关URL
        test_urls = [
            ('统计仪表板', '/reports/statistics/'),
            ('详细分析', '/reports/statistics/detail/'),
            ('统计API', '/reports/api/reports/statistics/'),
            ('缓存统计API', '/reports/api/cache/stats/'),
            ('缓存健康检查API', '/reports/api/cache/health/'),
        ]
        
        for url_name, url in test_urls:
            response = client.get(url)
            if response.status_code in [200, 302]:  # 200 OK 或 302 重定向都是正常的
                print(f"✅ {url_name}: URL路由正常 (状态码: {response.status_code})")
            else:
                print(f"❌ {url_name}: URL路由异常 (状态码: {response.status_code})")
        
        return True
        
    except Exception as e:
        print(f"URL路由配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        cleanup_test_users(admin_user, staff_user, department)

def test_permission_consistency():
    """测试权限一致性"""
    print("\n=== 权限一致性测试 ===")
    
    admin_user, staff_user, department = create_test_users()
    
    try:
        client = Client()
        
        # 测试页面权限和API权限的一致性
        test_cases = [
            ('统计仪表板页面', '/reports/statistics/', '统计API', '/reports/api/reports/statistics/'),
            ('详细分析页面', '/reports/statistics/detail/', '统计API', '/reports/api/reports/statistics/'),
        ]
        
        for user_type, user in [('管理员', admin_user), ('科室人员', staff_user)]:
            print(f"\n--- {user_type}权限一致性测试 ---")
            client.force_login(user)
            
            for page_name, page_url, api_name, api_url in test_cases:
                page_response = client.get(page_url)
                api_response = client.get(api_url)
                
                page_accessible = page_response.status_code == 200
                api_accessible = api_response.status_code == 200
                
                if page_accessible == api_accessible:
                    print(f"✅ {page_name}和{api_name}: 权限一致 (页面: {page_accessible}, API: {api_accessible})")
                else:
                    print(f"❌ {page_name}和{api_name}: 权限不一致 (页面: {page_accessible}, API: {api_accessible})")
        
        return True
        
    except Exception as e:
        print(f"权限一致性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        cleanup_test_users(admin_user, staff_user, department)

def main():
    """主测试函数"""
    print("🚀 开始导航和权限控制集成测试...")
    
    # 执行各项测试
    test_results = []
    
    test_results.append(test_navigation_access())
    test_results.append(test_navigation_menu_content())
    test_results.append(test_breadcrumb_navigation())
    test_results.append(test_url_routing())
    test_results.append(test_permission_consistency())
    
    # 总结测试结果
    print("\n=== 测试结果总结 ===")
    if all(test_results):
        print("🎉 导航和权限控制集成测试全部通过！")
        print("\n📋 功能验证总结:")
        print("✅ 导航菜单 - 统计分析菜单正确显示")
        print("✅ 权限控制 - 不同角色用户看到适当的功能")
        print("✅ 面包屑导航 - 导航路径正确显示")
        print("✅ URL路由 - 统计分析路由配置正确")
        print("✅ 权限一致性 - 页面和API权限保持一致")
        return True
    else:
        print("❌ 部分测试失败，请检查错误信息")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
