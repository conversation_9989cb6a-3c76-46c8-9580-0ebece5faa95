"""
用户认证中间件
User Authentication Middleware for Medical Device Reporting Platform
"""

import logging
from django.utils.deprecation import MiddlewareMixin
from django.contrib.auth import logout
from django.shortcuts import redirect
from django.urls import reverse
from django.utils import timezone

from .models import UserProfile

logger = logging.getLogger('apps.users')


class UserProfileMiddleware(MiddlewareMixin):
    """
    用户配置文件中间件
    
    检查用户配置文件状态，更新最后登录IP等信息
    """
    
    def process_request(self, request):
        """
        处理请求
        
        Args:
            request: HTTP请求对象
        """
        
        # 只处理已认证的用户
        if not request.user.is_authenticated:
            return None
        
        try:
            # 获取用户配置文件
            user_profile = getattr(request.user, 'profile', None)
            
            if not user_profile:
                logger.warning(f'用户缺少配置文件: {request.user.username}')
                # 如果用户没有配置文件，登出用户
                logout(request)
                return redirect('users:login')
            
            # 检查用户配置文件状态
            if not user_profile.is_active:
                logger.warning(f'用户配置文件已禁用: {user_profile.account_number}')
                logout(request)
                return redirect('users:login')
            
            # 检查科室状态（如果用户有科室）
            if user_profile.department and not user_profile.department.is_active:
                logger.warning(f'用户所属科室已禁用: {user_profile.account_number}')
                logout(request)
                return redirect('users:login')
            
            # 更新最后登录IP（仅在IP变化时更新）
            current_ip = self._get_client_ip(request)
            if current_ip and user_profile.last_login_ip != current_ip:
                user_profile.last_login_ip = current_ip
                user_profile.save(update_fields=['last_login_ip'])
                
                logger.debug(f'更新用户登录IP: {user_profile.account_number} - {current_ip}')
            
            # 将用户配置文件添加到请求对象中，方便视图使用
            request.user_profile = user_profile
            
        except Exception as e:
            logger.error(f'用户配置文件中间件处理错误: {str(e)}', exc_info=True)
            # 发生错误时登出用户，确保安全
            logout(request)
            return redirect('users:login')
        
        return None
    
    def _get_client_ip(self, request):
        """
        获取客户端IP地址
        
        Args:
            request: HTTP请求对象
            
        Returns:
            str: IP地址
        """
        
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR')
        
        return ip


class SessionSecurityMiddleware(MiddlewareMixin):
    """
    会话安全中间件
    
    增强会话安全性，防止会话劫持
    """
    
    def process_request(self, request):
        """
        处理请求
        
        Args:
            request: HTTP请求对象
        """
        
        # 只处理已认证的用户
        if not request.user.is_authenticated:
            return None
        
        try:
            # 检查会话中的用户信息
            session_user_id = request.session.get('_auth_user_id')
            if session_user_id and str(request.user.id) != str(session_user_id):
                logger.warning(f'会话用户ID不匹配: session={session_user_id}, user={request.user.id}')
                logout(request)
                return redirect('users:login')
            
            # 检查会话中的IP地址（可选的安全检查）
            session_ip = request.session.get('login_ip')
            current_ip = self._get_client_ip(request)
            
            if session_ip and current_ip and session_ip != current_ip:
                # IP地址变化，记录日志但不强制登出（考虑到移动网络等情况）
                logger.info(f'用户IP地址变化: {request.user.username} from {session_ip} to {current_ip}')
                # 更新会话中的IP地址
                request.session['login_ip'] = current_ip
            
            # 更新会话活动时间
            request.session['last_activity'] = timezone.now().isoformat()
            
        except Exception as e:
            logger.error(f'会话安全中间件处理错误: {str(e)}', exc_info=True)
        
        return None
    
    def _get_client_ip(self, request):
        """
        获取客户端IP地址
        
        Args:
            request: HTTP请求对象
            
        Returns:
            str: IP地址
        """
        
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR')
        
        return ip


class LoginRequiredMiddleware(MiddlewareMixin):
    """
    登录要求中间件

    确保某些URL需要登录才能访问
    """

    # 不需要登录的URL路径
    EXEMPT_URLS = [
        '/login/',
        '/admin/login/',
        '/static/',
        '/media/',
        '/__debug__/',
    ]

    def process_request(self, request):
        """
        处理请求

        Args:
            request: HTTP请求对象
        """

        # 检查是否为豁免URL
        path = request.path_info
        for exempt_url in self.EXEMPT_URLS:
            if path.startswith(exempt_url):
                return None

        # 如果用户未认证且不是豁免URL，重定向到登录页面
        if not request.user.is_authenticated:
            login_url = reverse('users:login')
            if path != login_url:
                return redirect(f'{login_url}?next={path}')

        return None


class PermissionControlMiddleware(MiddlewareMixin):
    """
    权限控制中间件

    基于角色的访问控制
    """

    # 需要管理员权限的URL模式
    ADMIN_REQUIRED_PATTERNS = [
        '/users/',  # 用户管理页面
        '/users/create/',  # 用户创建页面
        '/api/users/create/',  # 用户创建API
        '/api/users/bulk-action/',  # 批量操作API
        '/api/departments/',  # 科室管理API（POST/PUT/DELETE）
        '/reports/pending-review/',  # 待审核报告页面（仅管理员）
        '/reports/serious-events/',  # 严重事件报告页面（仅管理员）
    ]

    # 需要管理员权限的URL前缀（用于编辑页面等）
    ADMIN_REQUIRED_PREFIXES = [
        '/users/',  # 用户编辑页面 /users/{id}/edit/
        '/api/users/',  # 用户管理API（除了统计和搜索）
    ]

    # 需要科室成员或管理员权限的URL模式
    DEPARTMENT_MEMBER_OR_ADMIN_PATTERNS = [
        '/reports/',  # 报告管理相关页面
    ]

    # 管理员专用的API操作
    ADMIN_API_OPERATIONS = [
        'change-role',
        'change-department',
        'activate',
        'deactivate',
    ]

    def process_request(self, request):
        """
        处理请求

        Args:
            request: HTTP请求对象
        """

        # 只处理已认证的用户
        if not request.user.is_authenticated:
            return None

        # 检查用户是否有有效的profile
        if not hasattr(request.user, 'profile') or not request.user.profile:
            return None

        path = request.path_info
        method = request.method

        # 检查是否需要管理员权限
        if self._requires_admin_permission(path, method):
            if not request.user.profile.is_admin:
                logger.warning(f'非管理员用户尝试访问管理功能: {request.user.username} -> {path}')
                return self._handle_permission_denied(request, '需要管理员权限')

        # 检查是否需要科室成员或管理员权限
        if self._requires_department_member_or_admin_permission(path, method):
            if not self._is_department_member_or_admin(request.user.profile):
                logger.warning(f'无权限用户尝试访问功能: {request.user.username} -> {path}')
                return self._handle_permission_denied(request, '需要科室成员或管理员权限')

        return None

    def _requires_admin_permission(self, path, method):
        """
        检查是否需要管理员权限

        Args:
            path: URL路径
            method: HTTP方法

        Returns:
            bool: 是否需要管理员权限
        """

        # 检查精确匹配的管理员URL
        for pattern in self.ADMIN_REQUIRED_PATTERNS:
            if path == pattern:
                return True

        # 检查用户编辑页面
        if path.startswith('/users/') and path.endswith('/edit/'):
            return True

        # 检查报告审核页面
        if path.startswith('/reports/') and path.endswith('/review/'):
            return True

        # 检查用户状态切换 - 视图本身已有权限检查，不需要中间件拦截
        # if path.startswith('/users/') and path.endswith('/toggle-status/'):
        #     return True

        # API请求由DRF权限类处理，中间件不拦截
        if path.startswith('/api/'):
            return False

        return False

    def _requires_department_member_or_admin_permission(self, path, method):
        """
        检查是否需要科室成员或管理员权限

        Args:
            path: URL路径
            method: HTTP方法

        Returns:
            bool: 是否需要科室成员或管理员权限
        """

        # 检查报告管理相关页面
        for pattern in self.DEPARTMENT_MEMBER_OR_ADMIN_PATTERNS:
            if path.startswith(pattern):
                # 排除不需要权限的页面（如果有的话）
                return True

        return False

    def _is_department_member_or_admin(self, user_profile):
        """
        检查用户是否为科室成员或管理员

        Args:
            user_profile: 用户配置文件

        Returns:
            bool: 是否为科室成员或管理员
        """

        # 管理员有所有权限
        if user_profile.is_admin:
            return True

        # 科室成员需要有活跃的科室
        if user_profile.is_staff_member and user_profile.department and user_profile.department.is_active:
            return True

        return False

    def _handle_permission_denied(self, request, message):
        """
        处理权限拒绝

        Args:
            request: HTTP请求对象
            message: 错误消息

        Returns:
            HttpResponse: 响应对象
        """
        from django.http import JsonResponse

        # AJAX请求返回JSON
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({'error': message}, status=403)

        # API请求返回JSON
        if request.path.startswith('/api/'):
            return JsonResponse({'error': message}, status=403)

        # 普通请求重定向（不使用messages，避免中间件顺序问题）
        return redirect('users:dashboard')
