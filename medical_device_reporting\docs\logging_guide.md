# 日志系统使用指南

## 概述

医疗器械不良事件上报平台采用Django标准日志系统，支持多级别日志记录和不同环境的配置。

## 日志配置

### 开发环境 (development.py)

- **控制台日志**: DEBUG级别，简单格式
- **文件日志**: INFO级别，详细格式
- **文件大小**: 10MB，保留5个备份文件
- **日志文件**: `logs/django.log`

### 生产环境 (production.py)

- **文件日志**: INFO级别，详细格式
- **错误日志**: ERROR级别，单独文件
- **文件大小**: 50MB，保留10个备份文件
- **日志文件**: `logs/django.log`, `logs/django_error.log`
- **错误跟踪**: 集成Sentry（可选）

## 日志级别

1. **DEBUG**: 详细的调试信息
2. **INFO**: 一般信息
3. **WARNING**: 警告信息
4. **ERROR**: 错误信息
5. **CRITICAL**: 严重错误

## 使用示例

### 在视图中使用日志

```python
import logging

logger = logging.getLogger('apps')

def my_view(request):
    logger.info(f'用户 {request.user} 访问了页面')
    
    try:
        # 业务逻辑
        pass
    except Exception as e:
        logger.error(f'处理请求时发生错误: {e}', exc_info=True)
        return HttpResponse('服务器错误', status=500)
    
    logger.debug('请求处理完成')
    return HttpResponse('成功')
```

### 在模型中使用日志

```python
import logging

logger = logging.getLogger('apps')

class AdverseEvent(models.Model):
    # 模型字段...
    
    def save(self, *args, **kwargs):
        logger.info(f'保存不良事件记录: {self.id}')
        super().save(*args, **kwargs)
        logger.debug(f'不良事件记录 {self.id} 保存完成')
```

### 在服务层使用日志

```python
import logging

logger = logging.getLogger('apps')

class AdverseEventService:
    @staticmethod
    def create_event(data):
        logger.info('开始创建不良事件记录')
        
        try:
            # 验证数据
            logger.debug(f'验证数据: {data}')
            
            # 创建记录
            event = AdverseEvent.objects.create(**data)
            logger.info(f'成功创建不良事件记录: {event.id}')
            
            return event
            
        except ValidationError as e:
            logger.warning(f'数据验证失败: {e}')
            raise
        except Exception as e:
            logger.error(f'创建不良事件记录失败: {e}', exc_info=True)
            raise
```

## 日志格式

### 详细格式 (verbose)
```
INFO 2024-01-01 12:00:00,000 views 12345 67890 用户登录成功
```

### 简单格式 (simple)
```
INFO 用户登录成功
```

## 日志文件管理

- 日志文件位于 `logs/` 目录
- 自动轮转，避免文件过大
- 开发环境保留5个备份，生产环境保留10个备份
- 可通过环境变量调整日志级别

## 最佳实践

1. **合理使用日志级别**
   - DEBUG: 仅在开发环境使用
   - INFO: 记录重要的业务操作
   - WARNING: 记录可能的问题
   - ERROR: 记录错误和异常

2. **包含上下文信息**
   - 用户ID、请求ID等标识信息
   - 相关的业务数据

3. **避免敏感信息**
   - 不要记录密码、令牌等敏感数据
   - 对个人信息进行脱敏处理

4. **性能考虑**
   - 避免在循环中大量记录日志
   - 使用适当的日志级别

## 监控和告警

- 生产环境可集成Sentry进行错误跟踪
- 定期检查日志文件大小和磁盘空间
- 设置关键错误的告警机制
