#!/usr/bin/env python
"""
测试字段更新是否正确
Test field updates for patient info and device info
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from django.core.exceptions import ValidationError
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import date
from apps.users.models import UserProfile, Department
from apps.reports.models import AdverseEventReport
from apps.reports.forms import AdverseEventReportForm, PatientInfoForm, DeviceInfoForm

def test_model_changes():
    """测试模型字段更改"""
    print("=== 测试模型字段更改 ===")
    
    # 创建测试用户和科室
    import uuid
    unique_id = str(uuid.uuid4())[:8]
    admin_user = User.objects.create_user(username=f'testadmin_{unique_id}', email='<EMAIL>')
    test_user = User.objects.create_user(username=f'testuser_{unique_id}', email='<EMAIL>')
    
    department = Department.objects.create(
        code=f'TEST_{unique_id}',
        name=f'测试科室_{unique_id}',
        created_by=admin_user
    )
    
    user_profile = UserProfile.objects.create(
        user=test_user,
        account_number='9999',
        department=department,
        role='staff',
        created_by=admin_user
    )
    
    # 测试患者信息为非必填
    print("1. 测试患者信息为非必填...")
    try:
        report = AdverseEventReport(
            reporter=user_profile,
            reporter_phone='***********',
            department=department,
            # 患者信息留空
            patient_name='',
            patient_age=None,
            patient_gender='',
            event_date=date.today(),
            injury_level='mild',
            event_description='测试事件描述',
            device_name='测试器械',
            registration_number='TEST123',
            manufacturer='测试厂商',
            product_number='PROD001',  # 填写产品编号
            batch_number=''  # 批号留空
        )
        report.full_clean()
        print("✅ 患者信息可以为空")
    except ValidationError as e:
        print(f"❌ 患者信息验证失败: {e}")
    
    # 测试产品编号或批号必填其中一项
    print("2. 测试产品编号或批号必填其中一项...")
    try:
        report = AdverseEventReport(
            reporter=user_profile,
            reporter_phone='***********',
            department=department,
            patient_name='测试患者',
            event_date=date.today(),
            injury_level='mild',
            event_description='测试事件描述',
            device_name='测试器械',
            registration_number='TEST123',
            manufacturer='测试厂商',
            product_number='',  # 产品编号留空
            batch_number=''     # 批号也留空
        )
        report.full_clean()
        print("❌ 产品编号和批号都为空时应该验证失败")
    except ValidationError as e:
        if 'product_number' in str(e) or 'batch_number' in str(e):
            print("✅ 产品编号和批号都为空时正确验证失败")
        else:
            print(f"❌ 验证失败原因不正确: {e}")
    
    # 测试只填写产品编号
    print("3. 测试只填写产品编号...")
    try:
        report = AdverseEventReport(
            reporter=user_profile,
            reporter_phone='***********',
            department=department,
            patient_name='测试患者',
            event_date=date.today(),
            injury_level='mild',
            event_description='测试事件描述',
            device_name='测试器械',
            registration_number='TEST123',
            manufacturer='测试厂商',
            product_number='PROD001',  # 只填写产品编号
            batch_number=''
        )
        report.full_clean()
        print("✅ 只填写产品编号验证通过")
    except ValidationError as e:
        print(f"❌ 只填写产品编号验证失败: {e}")
    
    # 测试只填写批号
    print("4. 测试只填写批号...")
    try:
        report = AdverseEventReport(
            reporter=user_profile,
            reporter_phone='***********',
            department=department,
            patient_name='测试患者',
            event_date=date.today(),
            injury_level='mild',
            event_description='测试事件描述',
            device_name='测试器械',
            registration_number='TEST123',
            manufacturer='测试厂商',
            product_number='',
            batch_number='BATCH001'  # 只填写批号
        )
        report.full_clean()
        print("✅ 只填写批号验证通过")
    except ValidationError as e:
        print(f"❌ 只填写批号验证失败: {e}")
    
    # 清理测试数据
    AdverseEventReport.objects.filter(reporter=user_profile).delete()
    user_profile.delete()
    department.delete()
    test_user.delete()
    admin_user.delete()

def test_form_changes():
    """测试表单更改"""
    print("\n=== 测试表单更改 ===")
    
    # 测试患者信息表单
    print("1. 测试患者信息表单...")
    patient_form = PatientInfoForm({
        'patient_name': '',
        'patient_age': '',
        'patient_gender': '',
        'patient_contact': ''
    })
    
    if patient_form.is_valid():
        print("✅ 患者信息表单允许空值")
    else:
        print(f"❌ 患者信息表单验证失败: {patient_form.errors}")
    
    # 测试器械信息表单
    print("2. 测试器械信息表单...")
    device_form = DeviceInfoForm({
        'device_name': '测试器械',
        'registration_number': 'TEST123',
        'manufacturer': '测试厂商',
        'specification': '',
        'model': '',
        'product_number': '',  # 产品编号留空
        'batch_number': '',    # 批号留空
        'production_date': '',
        'expiry_date': ''
    })
    
    if not device_form.is_valid():
        if 'product_number' in str(device_form.errors) or 'batch_number' in str(device_form.errors):
            print("✅ 器械信息表单正确验证产品编号/批号")
        else:
            print(f"❌ 器械信息表单验证失败原因不正确: {device_form.errors}")
    else:
        print("❌ 器械信息表单应该验证失败")
    
    # 测试只填写产品编号
    device_form2 = DeviceInfoForm({
        'device_name': '测试器械',
        'registration_number': 'TEST123',
        'manufacturer': '测试厂商',
        'specification': '',
        'model': '',
        'product_number': 'PROD001',  # 填写产品编号
        'batch_number': '',           # 批号留空
        'production_date': '',
        'expiry_date': ''
    })
    
    if device_form2.is_valid():
        print("✅ 只填写产品编号的器械信息表单验证通过")
    else:
        print(f"❌ 只填写产品编号的器械信息表单验证失败: {device_form2.errors}")

def test_field_properties():
    """测试字段属性"""
    print("\n=== 测试字段属性 ===")
    
    # 检查模型字段属性
    model_fields = AdverseEventReport._meta.get_fields()
    field_dict = {field.name: field for field in model_fields}
    
    # 检查患者信息字段
    patient_name_field = field_dict.get('patient_name')
    if patient_name_field and patient_name_field.blank:
        print("✅ patient_name 字段允许空值")
    else:
        print("❌ patient_name 字段不允许空值")
    
    patient_age_field = field_dict.get('patient_age')
    if patient_age_field and patient_age_field.null and patient_age_field.blank:
        print("✅ patient_age 字段允许空值")
    else:
        print("❌ patient_age 字段不允许空值")
    
    patient_gender_field = field_dict.get('patient_gender')
    if patient_gender_field and patient_gender_field.blank:
        print("✅ patient_gender 字段允许空值")
    else:
        print("❌ patient_gender 字段不允许空值")
    
    # 检查器械故障表现字段
    device_malfunction_field = field_dict.get('device_malfunction')
    if device_malfunction_field and device_malfunction_field.blank:
        print("✅ device_malfunction 字段允许空值")
    else:
        print("❌ device_malfunction 字段不允许空值")

def main():
    """主测试函数"""
    print("🔧 开始测试字段更新...")
    
    try:
        # 测试模型更改
        test_model_changes()
        
        # 测试表单更改
        test_form_changes()
        
        # 测试字段属性
        test_field_properties()
        
        print("\n=== 测试结果总结 ===")
        print("🎉 所有字段更新测试完成！")
        print("\n📋 更新总结:")
        print("✅ 患者姓名、年龄、性别改为非必填")
        print("✅ 器械故障表现取消字符限制")
        print("✅ 事件描述取消字符限制")
        print("✅ 产品编号或产品批号至少填写其中一项")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
