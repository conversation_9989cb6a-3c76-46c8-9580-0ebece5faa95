"""
自定义系统检查
Custom system checks for Medical Device Reporting Platform
"""

from django.core.checks import Error, Warning, Info
from django.conf import settings
import os


def check_common_settings(app_configs, **kwargs):
    """
    检查通用设置配置
    
    Args:
        app_configs: 应用配置列表
        **kwargs: 其他参数
        
    Returns:
        list: 检查结果列表
    """
    errors = []
    
    # 检查必要的目录是否存在
    errors.extend(_check_required_directories())
    
    # 检查日志配置
    errors.extend(_check_logging_configuration())
    
    # 检查环境变量
    errors.extend(_check_environment_variables())
    
    # 检查文件上传配置
    errors.extend(_check_file_upload_settings())
    
    return errors


def _check_required_directories():
    """
    检查必要的目录是否存在
    
    Returns:
        list: 检查结果列表
    """
    errors = []
    
    # 检查日志目录
    if hasattr(settings, 'BASE_DIR'):
        logs_dir = os.path.join(settings.BASE_DIR, 'logs')
        if not os.path.exists(logs_dir):
            errors.append(
                Warning(
                    '日志目录不存在',
                    hint=f'请创建日志目录: {logs_dir}',
                    id='common.W001',
                )
            )
        elif not os.access(logs_dir, os.W_OK):
            errors.append(
                Error(
                    '日志目录不可写',
                    hint=f'请检查日志目录权限: {logs_dir}',
                    id='common.E001',
                )
            )
    
    # 检查媒体目录
    if hasattr(settings, 'MEDIA_ROOT') and settings.MEDIA_ROOT:
        if not os.path.exists(settings.MEDIA_ROOT):
            errors.append(
                Warning(
                    '媒体文件目录不存在',
                    hint=f'请创建媒体文件目录: {settings.MEDIA_ROOT}',
                    id='common.W002',
                )
            )
    
    # 检查静态文件目录
    if hasattr(settings, 'STATIC_ROOT') and settings.STATIC_ROOT:
        if not os.path.exists(settings.STATIC_ROOT):
            errors.append(
                Info(
                    '静态文件收集目录不存在',
                    hint=f'运行 collectstatic 命令创建: {settings.STATIC_ROOT}',
                    id='common.I001',
                )
            )
    
    return errors


def _check_logging_configuration():
    """
    检查日志配置
    
    Returns:
        list: 检查结果列表
    """
    errors = []
    
    if not hasattr(settings, 'LOGGING'):
        errors.append(
            Warning(
                '未配置日志系统',
                hint='请在settings中配置LOGGING',
                id='common.W003',
            )
        )
    else:
        logging_config = settings.LOGGING
        
        # 检查是否有文件处理器
        handlers = logging_config.get('handlers', {})
        has_file_handler = any(
            handler.get('class', '').endswith('FileHandler')
            for handler in handlers.values()
        )
        
        if not has_file_handler:
            errors.append(
                Info(
                    '未配置文件日志处理器',
                    hint='建议配置文件日志处理器以便问题排查',
                    id='common.I002',
                )
            )
    
    return errors


def _check_environment_variables():
    """
    检查环境变量配置
    
    Returns:
        list: 检查结果列表
    """
    errors = []
    
    # 检查SECRET_KEY
    if not getattr(settings, 'SECRET_KEY', None):
        errors.append(
            Error(
                'SECRET_KEY未设置',
                hint='请设置Django SECRET_KEY',
                id='common.E002',
            )
        )
    elif settings.SECRET_KEY == 'django-insecure-dev-key-change-in-production-2024-medical-device-reporting':
        if not settings.DEBUG:
            errors.append(
                Error(
                    '生产环境使用默认SECRET_KEY',
                    hint='请在生产环境设置安全的SECRET_KEY',
                    id='common.E003',
                )
            )
        else:
            errors.append(
                Warning(
                    '使用默认SECRET_KEY',
                    hint='建议设置自定义的SECRET_KEY',
                    id='common.W004',
                )
            )
    
    # 检查数据库配置
    databases = getattr(settings, 'DATABASES', {})
    default_db = databases.get('default', {})
    
    if default_db.get('PASSWORD') == 'temper0201..':
        if not settings.DEBUG:
            errors.append(
                Warning(
                    '生产环境使用默认数据库密码',
                    hint='建议在生产环境使用更安全的数据库密码',
                    id='common.W005',
                )
            )
    
    return errors


def _check_file_upload_settings():
    """
    检查文件上传配置
    
    Returns:
        list: 检查结果列表
    """
    errors = []
    
    # 检查文件上传大小限制
    max_upload_size = getattr(settings, 'FILE_UPLOAD_MAX_MEMORY_SIZE', None)
    if max_upload_size and max_upload_size > 10 * 1024 * 1024:  # 10MB
        errors.append(
            Warning(
                '文件上传内存限制过大',
                hint=f'当前设置: {max_upload_size} bytes，建议不超过10MB',
                id='common.W006',
            )
        )
    
    # 检查允许的文件类型（如果有配置）
    allowed_extensions = getattr(settings, 'ALLOWED_FILE_EXTENSIONS', None)
    if allowed_extensions is not None and not isinstance(allowed_extensions, (list, tuple)):
        errors.append(
            Error(
                'ALLOWED_FILE_EXTENSIONS配置格式错误',
                hint='应该是列表或元组格式',
                id='common.E004',
            )
        )
    
    return errors
