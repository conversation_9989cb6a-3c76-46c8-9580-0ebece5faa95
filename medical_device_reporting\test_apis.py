#!/usr/bin/env python
"""
REST API接口测试脚本
REST API Test Script for Medical Device Reporting Platform
"""

import os
import sys
import django
import json

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from django.test import Client
from django.contrib.auth.models import User, Group, Permission
from django.contrib.contenttypes.models import ContentType
from apps.users.models import UserProfile, Department
from apps.users.services import initialize_user_groups

def setup_test_data():
    """设置测试数据"""
    
    print("设置测试数据...")
    
    # 初始化用户组和权限
    initialize_user_groups()
    
    # 创建测试科室
    department, created = Department.objects.get_or_create(
        code='APITEST',
        defaults={
            'name': 'API测试科室',
            'is_active': True
        }
    )
    
    # 创建测试管理员用户
    admin_user, created = User.objects.get_or_create(
        username='api_admin',
        defaults={
            'first_name': 'API',
            'last_name': '管理员',
            'email': '<EMAIL>',
            'is_active': True
        }
    )
    
    admin_profile, created = UserProfile.objects.get_or_create(
        account_number='9999',
        defaults={
            'user': admin_user,
            'role': 'admin',
            'is_active': True
        }
    )
    
    # 分配管理员权限
    admin_group = Group.objects.get(name='管理员')
    admin_user.groups.add(admin_group)
    
    return admin_user, admin_profile, department

def test_apis():
    """测试API接口"""
    
    print("=== REST API接口测试 ===\n")
    
    # 设置测试数据
    admin_user, admin_profile, department = setup_test_data()
    
    # 创建测试客户端
    client = Client()
    
    # 登录管理员用户
    client.force_login(admin_user)
    
    # 1. 测试用户列表API
    print("1. 测试用户列表API...")
    try:
        response = client.get('/api/users/')
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 用户列表API成功: 共 {len(data.get('results', []))} 个用户")
        else:
            print(f"   ❌ 用户列表API失败: {response.status_code}")
            print(f"      错误: {response.content.decode()}")
    except Exception as e:
        print(f"   ❌ 用户列表API异常: {str(e)}")
    
    # 2. 测试用户创建API
    print("\n2. 测试用户创建API...")
    try:
        user_data = {
            'account_number': '8888',
            'username': 'api_test_user',
            'first_name': 'API测试',
            'last_name': '用户',
            'email': '<EMAIL>',
            'department_id': department.id,
            'role': 'staff',
            'is_active': True
        }
        
        response = client.post(
            '/api/users/create/',
            data=json.dumps(user_data),
            content_type='application/json'
        )
        
        if response.status_code == 201:
            data = response.json()
            print(f"   ✅ 用户创建API成功: {data.get('account_number')} - {data.get('username')}")
            created_user_id = data.get('id')
        else:
            print(f"   ❌ 用户创建API失败: {response.status_code}")
            print(f"      错误: {response.content.decode()}")
            created_user_id = None
    except Exception as e:
        print(f"   ❌ 用户创建API异常: {str(e)}")
        created_user_id = None
    
    # 3. 测试用户详情API
    if created_user_id:
        print("\n3. 测试用户详情API...")
        try:
            response = client.get(f'/api/users/{created_user_id}/')
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ 用户详情API成功: {data.get('account_number')} - {data.get('display_name')}")
            else:
                print(f"   ❌ 用户详情API失败: {response.status_code}")
        except Exception as e:
            print(f"   ❌ 用户详情API异常: {str(e)}")
    
    # 4. 测试用户更新API
    if created_user_id:
        print("\n4. 测试用户更新API...")
        try:
            update_data = {
                'first_name': 'API测试更新',
                'email': '<EMAIL>'
            }
            
            response = client.put(
                f'/api/users/{created_user_id}/',
                data=json.dumps(update_data),
                content_type='application/json'
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ 用户更新API成功: {data.get('first_name')}")
            else:
                print(f"   ❌ 用户更新API失败: {response.status_code}")
                print(f"      错误: {response.content.decode()}")
        except Exception as e:
            print(f"   ❌ 用户更新API异常: {str(e)}")
    
    # 5. 测试用户状态管理API
    if created_user_id:
        print("\n5. 测试用户状态管理API...")
        try:
            # 禁用用户
            response = client.post(f'/api/users/{created_user_id}/deactivate/')
            if response.status_code == 200:
                print("   ✅ 用户禁用API成功")
            else:
                print(f"   ❌ 用户禁用API失败: {response.status_code}")
            
            # 激活用户
            response = client.post(f'/api/users/{created_user_id}/activate/')
            if response.status_code == 200:
                print("   ✅ 用户激活API成功")
            else:
                print(f"   ❌ 用户激活API失败: {response.status_code}")
        except Exception as e:
            print(f"   ❌ 用户状态管理API异常: {str(e)}")
    
    # 6. 测试科室列表API
    print("\n6. 测试科室列表API...")
    try:
        response = client.get('/api/departments/')
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 科室列表API成功: 共 {len(data)} 个科室")
        else:
            print(f"   ❌ 科室列表API失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 科室列表API异常: {str(e)}")
    
    # 7. 测试科室创建API
    print("\n7. 测试科室创建API...")
    try:
        dept_data = {
            'name': 'API测试新科室',
            'code': 'APINEW',
            'is_active': True
        }
        
        response = client.post(
            '/api/departments/',
            data=json.dumps(dept_data),
            content_type='application/json'
        )
        
        if response.status_code == 201:
            data = response.json()
            print(f"   ✅ 科室创建API成功: {data.get('code')} - {data.get('name')}")
            created_dept_id = data.get('id')
        else:
            print(f"   ❌ 科室创建API失败: {response.status_code}")
            print(f"      错误: {response.content.decode()}")
            created_dept_id = None
    except Exception as e:
        print(f"   ❌ 科室创建API异常: {str(e)}")
        created_dept_id = None
    
    # 8. 测试统计API
    print("\n8. 测试统计API...")
    try:
        response = client.get('/api/users/statistics/')
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 统计API成功:")
            print(f"      用户统计: {data.get('user_statistics', {})}")
            print(f"      科室统计: {data.get('department_statistics', {})}")
        else:
            print(f"   ❌ 统计API失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 统计API异常: {str(e)}")
    
    # 9. 测试搜索API
    print("\n9. 测试搜索API...")
    try:
        response = client.get('/api/users/search/?account_number=9999')
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 搜索API成功: {data.get('account_number')} - {data.get('display_name')}")
        else:
            print(f"   ❌ 搜索API失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 搜索API异常: {str(e)}")
    
    # 10. 测试批量操作API
    if created_user_id:
        print("\n10. 测试批量操作API...")
        try:
            bulk_data = {
                'user_ids': [created_user_id],
                'action': 'deactivate'
            }
            
            response = client.post(
                '/api/users/bulk-action/',
                data=json.dumps(bulk_data),
                content_type='application/json'
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ 批量操作API成功: {data.get('message')}")
                print(f"      成功: {data.get('success_count')}, 失败: {data.get('failed_count')}")
            else:
                print(f"   ❌ 批量操作API失败: {response.status_code}")
                print(f"      错误: {response.content.decode()}")
        except Exception as e:
            print(f"   ❌ 批量操作API异常: {str(e)}")
    
    print("\n=== REST API接口测试完成 ===")
    
    return True

if __name__ == '__main__':
    try:
        test_apis()
        print("\n🎉 API测试完成！")
    except Exception as e:
        print(f"\n❌ API测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
