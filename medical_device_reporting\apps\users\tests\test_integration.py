"""
集成测试
Integration Tests
"""

from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from django.core.files.uploadedfile import SimpleUploadedFile
import json

from apps.users.models import UserProfile, Department


class DepartmentManagementIntegrationTest(TestCase):
    """科室管理集成测试"""
    
    def setUp(self):
        """测试前准备"""
        self.client = Client()
        
        # 创建管理员用户
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>'
        )
        self.admin_profile = UserProfile.objects.create(
            user=self.admin_user,
            account_number='0001',
            role='admin',
            is_active=True
        )
    
    def test_complete_department_management_workflow(self):
        """测试完整的科室管理工作流程"""
        # 1. 登录管理员
        self.client.force_login(self.admin_user)
        
        # 2. 访问科室列表页面
        response = self.client.get(reverse('users:department_list'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '科室管理')
        
        # 3. 创建新科室
        create_data = {
            'code': 'ICU',
            'name': '重症监护科',
            'is_active': True
        }
        response = self.client.post(reverse('users:department_create'), create_data)
        self.assertEqual(response.status_code, 302)
        
        # 验证科室被创建
        department = Department.objects.get(code='ICU')
        self.assertEqual(department.name, '重症监护科')
        self.assertEqual(department.created_by, self.admin_user)
        
        # 4. 查看科室详情
        response = self.client.get(reverse('users:department_detail', args=[department.id]))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '重症监护科')
        
        # 5. 编辑科室信息
        edit_data = {
            'code': 'ICU',  # 代码不能修改
            'name': '重症监护室',  # 修改名称
            'is_active': True
        }
        response = self.client.post(reverse('users:department_edit', args=[department.id]), edit_data)
        self.assertEqual(response.status_code, 302)
        
        # 验证科室被更新
        department.refresh_from_db()
        self.assertEqual(department.name, '重症监护室')
        
        # 6. 禁用科室
        edit_data['is_active'] = False
        response = self.client.post(reverse('users:department_edit', args=[department.id]), edit_data)
        self.assertEqual(response.status_code, 302)
        
        # 验证科室被禁用
        department.refresh_from_db()
        self.assertFalse(department.is_active)
        
        # 7. 删除科室
        response = self.client.post(reverse('users:department_delete', args=[department.id]))
        self.assertEqual(response.status_code, 302)
        
        # 验证科室被删除
        self.assertFalse(Department.objects.filter(id=department.id).exists())
    
    def test_department_user_relationship_workflow(self):
        """测试科室与用户关系的工作流程"""
        self.client.force_login(self.admin_user)
        
        # 1. 创建科室
        department = Department.objects.create(
            code='TEST',
            name='测试科室',
            created_by=self.admin_user
        )
        
        # 2. 创建用户并分配到科室
        user_data = {
            'account_number': '1001',
            'username': '测试用户',
            'first_name': '测试',
            'last_name': '用户',
            'email': '<EMAIL>',
            'role': 'staff',
            'department': department.id,
            'phone': '***********'
        }
        response = self.client.post(reverse('users:user_create'), user_data)
        self.assertEqual(response.status_code, 302)
        
        # 验证用户被创建并分配到科室
        user_profile = UserProfile.objects.get(account_number='1001')
        self.assertEqual(user_profile.department, department)
        
        # 3. 查看科室详情，应该显示用户
        response = self.client.get(reverse('users:department_detail', args=[department.id]))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '测试用户')
        
        # 4. 尝试删除有用户的科室，应该失败
        response = self.client.post(reverse('users:department_delete', args=[department.id]))
        self.assertEqual(response.status_code, 400)
        
        # 验证科室未被删除
        self.assertTrue(Department.objects.filter(id=department.id).exists())
        
        # 5. 移除用户后再删除科室
        user_profile.department = None
        user_profile.save()
        
        response = self.client.post(reverse('users:department_delete', args=[department.id]))
        self.assertEqual(response.status_code, 302)
        
        # 验证科室被删除
        self.assertFalse(Department.objects.filter(id=department.id).exists())


class ProfileManagementIntegrationTest(TestCase):
    """个人设置管理集成测试"""
    
    def setUp(self):
        """测试前准备"""
        self.client = Client()
        
        # 创建科室
        self.department = Department.objects.create(
            code='TEST',
            name='测试科室'
        )
        
        # 创建测试用户
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            first_name='测试',
            last_name='用户'
        )
        self.user_profile = UserProfile.objects.create(
            user=self.user,
            account_number='1001',
            department=self.department,
            role='staff',
            is_active=True
        )
    
    def test_complete_profile_management_workflow(self):
        """测试完整的个人设置管理工作流程"""
        # 1. 用户登录
        self.client.force_login(self.user)
        
        # 2. 查看个人信息
        response = self.client.get(reverse('users:profile'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '测试用户')
        self.assertContains(response, '1001')
        
        # 3. 编辑个人信息
        edit_data = {
            'first_name': '更新的测试',
            'last_name': '更新的用户',
            'email': '<EMAIL>'
        }
        response = self.client.post(reverse('users:profile_edit'), edit_data)
        self.assertEqual(response.status_code, 302)
        
        # 验证信息被更新
        self.user.refresh_from_db()
        self.assertEqual(self.user.first_name, '更新的测试')
        self.assertEqual(self.user.email, '<EMAIL>')
        
        # 4. 访问用户设置页面
        response = self.client.get(reverse('users:user_settings'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '个人设置')
        
        # 5. 保存偏好设置
        preferences_data = {
            'type': 'preferences',
            'value': {
                'theme': 'dark',
                'language': 'zh-cn',
                'timezone': 'Asia/Shanghai',
                'page_size': '20'
            }
        }
        response = self.client.post(
            reverse('users:user_settings'),
            data=json.dumps(preferences_data),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, 200)
        
        # 6. 修改密码
        password_data = {
            'current_password': 'testpass123',
            'new_password': 'newpass123',
            'confirm_password': 'newpass123'
        }
        response = self.client.post(reverse('users:change_password'), password_data)
        self.assertEqual(response.status_code, 200)
        
        # 验证密码被更改
        self.user.refresh_from_db()
        self.assertTrue(self.user.check_password('newpass123'))
    
    def test_admin_view_user_profile_workflow(self):
        """测试管理员查看用户信息的工作流程"""
        # 创建管理员
        admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>'
        )
        admin_profile = UserProfile.objects.create(
            user=admin_user,
            account_number='0001',
            role='admin',
            is_active=True
        )
        
        # 管理员登录
        self.client.force_login(admin_user)
        
        # 1. 从用户列表访问用户信息
        response = self.client.get(reverse('users:user_list'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.user.username)
        
        # 2. 查看用户详细信息
        response = self.client.get(reverse('users:user_profile', args=[self.user_profile.id]))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '用户信息')
        self.assertContains(response, self.user.username)
        
        # 3. 切换用户状态
        response = self.client.post(reverse('users:user_toggle_status', args=[self.user_profile.id]))
        self.assertEqual(response.status_code, 302)
        
        # 验证用户状态被切换
        self.user_profile.refresh_from_db()
        self.assertFalse(self.user_profile.is_active)


class CrossModuleIntegrationTest(TestCase):
    """跨模块集成测试"""
    
    def setUp(self):
        """测试前准备"""
        self.client = Client()
        
        # 创建管理员用户
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>'
        )
        self.admin_profile = UserProfile.objects.create(
            user=self.admin_user,
            account_number='0001',
            role='admin',
            is_active=True
        )
    
    def test_navigation_between_modules(self):
        """测试模块间导航"""
        self.client.force_login(self.admin_user)
        
        # 1. 从用户中心开始
        response = self.client.get(reverse('users:dashboard'))
        self.assertEqual(response.status_code, 200)
        
        # 2. 导航到用户管理
        response = self.client.get(reverse('users:user_list'))
        self.assertEqual(response.status_code, 200)
        
        # 3. 导航到科室管理
        response = self.client.get(reverse('users:department_list'))
        self.assertEqual(response.status_code, 200)
        
        # 4. 导航到个人设置
        response = self.client.get(reverse('users:profile'))
        self.assertEqual(response.status_code, 200)
        
        # 5. 返回用户中心
        response = self.client.get(reverse('users:dashboard'))
        self.assertEqual(response.status_code, 200)
    
    def test_permission_consistency_across_modules(self):
        """测试跨模块权限一致性"""
        # 创建科室人员
        staff_user = User.objects.create_user(
            username='staff',
            email='<EMAIL>'
        )
        staff_profile = UserProfile.objects.create(
            user=staff_user,
            account_number='1001',
            role='staff',
            is_active=True
        )
        
        self.client.force_login(staff_user)
        
        # 科室人员应该能访问个人设置
        accessible_urls = [
            reverse('users:dashboard'),
            reverse('users:profile'),
            reverse('users:profile_edit'),
            reverse('users:user_settings'),
        ]
        
        for url in accessible_urls:
            response = self.client.get(url)
            self.assertEqual(response.status_code, 200)
        
        # 科室人员不应该能访问管理功能
        restricted_urls = [
            reverse('users:user_list'),
            reverse('users:user_create'),
            reverse('users:department_list'),
            reverse('users:department_create'),
        ]
        
        for url in restricted_urls:
            response = self.client.get(url)
            self.assertIn(response.status_code, [302, 403])
    
    def test_data_consistency_across_operations(self):
        """测试跨操作数据一致性"""
        self.client.force_login(self.admin_user)
        
        # 1. 创建科室
        department = Department.objects.create(
            code='TEST',
            name='测试科室',
            created_by=self.admin_user
        )
        
        # 2. 创建用户并分配到科室
        user_data = {
            'account_number': '1001',
            'username': '测试用户',
            'role': 'staff',
            'department': department.id
        }
        response = self.client.post(reverse('users:user_create'), user_data)
        self.assertEqual(response.status_code, 302)
        
        user_profile = UserProfile.objects.get(account_number='1001')
        
        # 3. 在科室详情页面应该能看到用户
        response = self.client.get(reverse('users:department_detail', args=[department.id]))
        self.assertContains(response, '测试用户')
        
        # 4. 在用户列表页面应该能看到科室信息
        response = self.client.get(reverse('users:user_list'))
        self.assertContains(response, '测试科室')
        
        # 5. 修改科室名称
        edit_data = {
            'code': 'TEST',
            'name': '更新的测试科室',
            'is_active': True
        }
        response = self.client.post(reverse('users:department_edit', args=[department.id]), edit_data)
        self.assertEqual(response.status_code, 302)
        
        # 6. 在用户列表页面应该看到更新后的科室名称
        response = self.client.get(reverse('users:user_list'))
        self.assertContains(response, '更新的测试科室')
