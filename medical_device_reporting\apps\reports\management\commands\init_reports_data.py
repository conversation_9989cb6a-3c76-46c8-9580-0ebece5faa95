"""
初始化不良事件上报数据管理命令
Initialize Adverse Event Reports Data Management Command
"""

from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from django.db import transaction
from django.utils import timezone
from datetime import datetime, timedelta

from apps.reports.models import AdverseEventReport
from apps.users.models import UserProfile, Department


class Command(BaseCommand):
    """
    初始化不良事件上报相关数据
    
    包括：
    1. 创建权限组
    2. 设置权限
    3. 创建示例数据（可选）
    """
    
    help = '初始化不良事件上报管理模块的基础数据'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--create-sample-data',
            action='store_true',
            help='创建示例数据',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='强制重新创建（删除现有数据）',
        )
    
    def handle(self, *args, **options):
        """执行初始化"""
        self.stdout.write(
            self.style.SUCCESS('开始初始化不良事件上报管理数据...')
        )
        
        try:
            with transaction.atomic():
                # 1. 创建权限组
                self.create_permission_groups()
                
                # 2. 设置权限
                self.setup_permissions()
                
                # 3. 创建示例数据（如果需要）
                if options['create_sample_data']:
                    self.create_sample_data(force=options['force'])
                
                self.stdout.write(
                    self.style.SUCCESS('✅ 数据初始化完成！')
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ 初始化失败: {str(e)}')
            )
            raise CommandError(f'初始化失败: {str(e)}')
    
    def create_permission_groups(self):
        """创建权限组"""
        self.stdout.write('创建权限组...')
        
        # 报告管理员组
        report_admin_group, created = Group.objects.get_or_create(
            name='报告管理员'
        )
        if created:
            self.stdout.write(f'  ✅ 创建权限组: {report_admin_group.name}')
        else:
            self.stdout.write(f'  ℹ️  权限组已存在: {report_admin_group.name}')
        
        # 报告审核员组
        report_reviewer_group, created = Group.objects.get_or_create(
            name='报告审核员'
        )
        if created:
            self.stdout.write(f'  ✅ 创建权限组: {report_reviewer_group.name}')
        else:
            self.stdout.write(f'  ℹ️  权限组已存在: {report_reviewer_group.name}')
        
        # 报告提交员组
        report_submitter_group, created = Group.objects.get_or_create(
            name='报告提交员'
        )
        if created:
            self.stdout.write(f'  ✅ 创建权限组: {report_submitter_group.name}')
        else:
            self.stdout.write(f'  ℹ️  权限组已存在: {report_submitter_group.name}')
    
    def setup_permissions(self):
        """设置权限"""
        self.stdout.write('设置权限...')
        
        # 获取AdverseEventReport的ContentType
        content_type = ContentType.objects.get_for_model(AdverseEventReport)
        
        # 获取权限组
        report_admin_group = Group.objects.get(name='报告管理员')
        report_reviewer_group = Group.objects.get(name='报告审核员')
        report_submitter_group = Group.objects.get(name='报告提交员')
        
        # 报告管理员权限（所有权限）
        admin_permissions = Permission.objects.filter(
            content_type=content_type
        )
        report_admin_group.permissions.set(admin_permissions)
        self.stdout.write(f'  ✅ 设置报告管理员权限: {admin_permissions.count()}个')
        
        # 报告审核员权限
        reviewer_permission_codes = [
            'can_review_report',
            'can_view_all_reports',
            'view_adverseeventreport',
            'change_adverseeventreport',
        ]
        reviewer_permissions = Permission.objects.filter(
            content_type=content_type,
            codename__in=reviewer_permission_codes
        )
        report_reviewer_group.permissions.set(reviewer_permissions)
        self.stdout.write(f'  ✅ 设置报告审核员权限: {reviewer_permissions.count()}个')
        
        # 报告提交员权限
        submitter_permission_codes = [
            'can_submit_report',
            'view_adverseeventreport',
            'add_adverseeventreport',
            'change_adverseeventreport',
        ]
        submitter_permissions = Permission.objects.filter(
            content_type=content_type,
            codename__in=submitter_permission_codes
        )
        report_submitter_group.permissions.set(submitter_permissions)
        self.stdout.write(f'  ✅ 设置报告提交员权限: {submitter_permissions.count()}个')
    
    def create_sample_data(self, force=False):
        """创建示例数据"""
        self.stdout.write('创建示例数据...')
        
        # 如果强制重新创建，删除现有数据
        if force:
            AdverseEventReport.objects.all().delete()
            self.stdout.write('  🗑️  已删除现有报告数据')
        
        # 检查是否已有数据
        if AdverseEventReport.objects.exists() and not force:
            self.stdout.write('  ℹ️  示例数据已存在，跳过创建')
            return
        
        # 获取示例用户和科室
        try:
            # 获取第一个管理员用户
            admin_user = UserProfile.objects.filter(role='admin').first()
            if not admin_user:
                self.stdout.write('  ⚠️  未找到管理员用户，跳过示例数据创建')
                return
            
            # 使用管理员用户所属的科室
            department = admin_user.department
            if not department:
                self.stdout.write('  ⚠️  管理员用户未分配科室，跳过示例数据创建')
                return
            
            # 创建示例报告
            sample_reports = [
                {
                    'reporter': admin_user,
                    'department': department,
                    'reporter_phone': '13800138001',
                    'patient_name': '张三',
                    'patient_age': 45,
                    'patient_gender': 'male',
                    'patient_contact': '13900139001',
                    'device_malfunction': '设备在使用过程中突然停止工作，显示屏出现错误代码E001',
                    'event_date': timezone.now() - timedelta(days=1),
                    'injury_level': 'other',
                    'injury_description': '',
                    'event_description': '患者在进行常规检查时，医疗设备突然故障。设备使用时间约30分钟，使用目的为常规体检，按照标准操作程序使用。故障发生后立即停止使用，患者未受到伤害。已联系设备厂商进行维修。',
                    'initial_cause_analysis': '初步分析可能是设备内部电路故障导致',
                    'initial_treatment': '立即停止使用设备，患者检查改用备用设备完成',
                    'device_name': 'X光机',
                    'registration_number': 'NMPA20201234567',
                    'manufacturer': '某某医疗设备有限公司',
                    'specification': 'XR-100',
                    'model': 'Model-A',
                    'product_number': 'PN123456',
                    'batch_number': 'BN202401',
                    'production_date': datetime(2024, 1, 15).date(),
                    'expiry_date': datetime(2029, 1, 15).date(),
                    'status': 'draft',
                },
                {
                    'reporter': admin_user,
                    'department': department,
                    'reporter_phone': '13800138002',
                    'patient_name': '李四',
                    'patient_age': 32,
                    'patient_gender': 'female',
                    'patient_contact': '13900139002',
                    'device_malfunction': '输液泵流速控制异常，实际流速与设定值不符',
                    'event_date': timezone.now() - timedelta(days=3),
                    'injury_level': 'serious_injury',
                    'injury_description': '患者因输液速度过快出现不适症状',
                    'event_description': '患者在输液治疗过程中，输液泵出现流速控制异常。设备使用时间约2小时，用于静脉输液治疗，按照医嘱设定流速。发现异常后立即调整，患者出现轻微不适。已更换设备继续治疗。',
                    'initial_cause_analysis': '可能是流速传感器校准偏差导致',
                    'initial_treatment': '立即更换设备，密切观察患者状况，给予对症处理',
                    'device_name': '输液泵',
                    'registration_number': 'NMPA20201234568',
                    'manufacturer': '另一家医疗设备有限公司',
                    'specification': 'IP-200',
                    'model': 'Model-B',
                    'product_number': 'PN123457',
                    'batch_number': 'BN202402',
                    'production_date': datetime(2024, 2, 20).date(),
                    'expiry_date': datetime(2029, 2, 20).date(),
                    'status': 'submitted',
                    'submitted_at': timezone.now() - timedelta(days=2),
                }
            ]
            
            created_count = 0
            for report_data in sample_reports:
                report = AdverseEventReport(**report_data)
                report.save(user=admin_user.user)
                created_count += 1
                self.stdout.write(f'  ✅ 创建示例报告: {report.report_number}')
            
            self.stdout.write(f'  🎉 共创建 {created_count} 个示例报告')
            
        except Exception as e:
            self.stdout.write(f'  ❌ 创建示例数据失败: {str(e)}')
            raise
