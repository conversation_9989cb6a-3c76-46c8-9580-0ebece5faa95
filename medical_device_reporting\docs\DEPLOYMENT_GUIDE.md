# 部署指南
# Deployment Guide

## 概述

本文档提供医疗器械不良事件报告系统的完整部署指南，包括开发、测试和生产环境的配置。

## 系统要求

### 硬件要求

#### 最低配置
- CPU: 2核心
- 内存: 4GB RAM
- 存储: 20GB SSD
- 网络: 100Mbps

#### 推荐配置
- CPU: 4核心
- 内存: 8GB RAM
- 存储: 50GB SSD
- 网络: 1Gbps

#### 生产环境
- CPU: 8核心
- 内存: 16GB RAM
- 存储: 100GB SSD
- 网络: 1Gbps
- 负载均衡器
- 数据库集群

### 软件要求

- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / Windows Server 2019+
- **Python**: 3.8+
- **MySQL**: 8.0+
- **Redis**: 6.0+
- **Nginx**: 1.18+
- **Node.js**: 16+ (用于前端构建)

## 环境准备

### 1. 系统更新

```bash
# Ubuntu/Debian
sudo apt update && sudo apt upgrade -y

# CentOS/RHEL
sudo yum update -y

# 安装基础工具
sudo apt install -y curl wget git vim htop
```

### 2. Python环境

```bash
# 安装Python 3.8+
sudo apt install -y python3.8 python3.8-venv python3.8-dev

# 创建虚拟环境
python3.8 -m venv /opt/medical_device_reports/venv
source /opt/medical_device_reports/venv/bin/activate

# 升级pip
pip install --upgrade pip setuptools wheel
```

### 3. 数据库安装

```bash
# 安装MySQL 8.0
sudo apt install -y mysql-server mysql-client

# 安全配置
sudo mysql_secure_installation

# 创建数据库和用户
sudo mysql -u root -p
```

```sql
CREATE DATABASE medical_device_reports CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'medical_device_user'@'localhost' IDENTIFIED BY 'secure_password_here';
GRANT ALL PRIVILEGES ON medical_device_reports.* TO 'medical_device_user'@'localhost';
FLUSH PRIVILEGES;
```

### 4. Redis安装

```bash
# 安装Redis
sudo apt install -y redis-server

# 配置Redis
sudo nano /etc/redis/redis.conf

# 启动Redis
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

## 应用部署

### 1. 代码部署

```bash
# 创建应用目录
sudo mkdir -p /opt/medical_device_reports
sudo chown $USER:$USER /opt/medical_device_reports

# 克隆代码
cd /opt/medical_device_reports
git clone https://github.com/your-org/medical-device-reporting.git .

# 激活虚拟环境
source venv/bin/activate

# 安装依赖
pip install -r requirements/production.txt
```

### 2. 环境配置

```bash
# 创建环境变量文件
cp .env.example .env
nano .env
```

```bash
# .env 配置示例
DEBUG=False
SECRET_KEY=your_very_secure_secret_key_here_at_least_50_characters_long
ALLOWED_HOSTS=your-domain.com,www.your-domain.com

# 数据库配置
DATABASE_URL=mysql://medical_device_user:secure_password_here@localhost/medical_device_reports

# Redis配置
REDIS_URL=redis://:redis_password@localhost:6379/1

# 邮件配置
EMAIL_HOST=smtp.your-email-provider.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password
EMAIL_USE_TLS=True

# 文件存储
MEDIA_ROOT=/opt/medical_device_reports/media
STATIC_ROOT=/opt/medical_device_reports/staticfiles

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=/var/log/medical_device_reports/django.log
```

### 3. 数据库迁移

```bash
# 运行迁移
python manage.py migrate

# 创建超级用户
python manage.py createsuperuser

# 收集静态文件
python manage.py collectstatic --noinput

# 加载初始数据（如果有）
python manage.py loaddata fixtures/initial_data.json
```

### 4. 环境验证

```bash
# 运行环境验证脚本
python scripts/verify_environment.py

# 检查配置
python manage.py check --deploy
```

## Web服务器配置

### 1. Gunicorn配置

```bash
# 创建Gunicorn配置文件
nano /opt/medical_device_reports/gunicorn.conf.py
```

```python
# gunicorn.conf.py
import multiprocessing

bind = "127.0.0.1:8000"
workers = multiprocessing.cpu_count() * 2 + 1
worker_class = "sync"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 30
keepalive = 2
user = "www-data"
group = "www-data"
tmp_upload_dir = None
errorlog = "/var/log/medical_device_reports/gunicorn_error.log"
accesslog = "/var/log/medical_device_reports/gunicorn_access.log"
loglevel = "info"
```

### 2. Systemd服务

```bash
# 创建systemd服务文件
sudo nano /etc/systemd/system/medical-device-reports.service
```

```ini
[Unit]
Description=Medical Device Reports Django Application
After=network.target mysql.service redis.service

[Service]
Type=notify
User=www-data
Group=www-data
WorkingDirectory=/opt/medical_device_reports
Environment=PATH=/opt/medical_device_reports/venv/bin
ExecStart=/opt/medical_device_reports/venv/bin/gunicorn --config gunicorn.conf.py config.wsgi:application
ExecReload=/bin/kill -s HUP $MAINPID
Restart=on-failure
RestartSec=5

[Install]
WantedBy=multi-user.target
```

```bash
# 启动服务
sudo systemctl daemon-reload
sudo systemctl start medical-device-reports
sudo systemctl enable medical-device-reports
```

### 3. Nginx配置

```bash
# 创建Nginx配置
sudo nano /etc/nginx/sites-available/medical-device-reports
```

```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;

    # SSL配置
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # 文件上传大小限制
    client_max_body_size 50M;

    # 静态文件
    location /static/ {
        alias /opt/medical_device_reports/staticfiles/;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }

    # 媒体文件
    location /media/ {
        alias /opt/medical_device_reports/media/;
        expires 7d;
    }

    # 应用程序
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # 健康检查
    location /health/ {
        access_log off;
        proxy_pass http://127.0.0.1:8000;
    }
}
```

```bash
# 启用站点
sudo ln -s /etc/nginx/sites-available/medical-device-reports /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## SSL证书配置

### 使用Let's Encrypt

```bash
# 安装Certbot
sudo apt install -y certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# 设置自动续期
sudo crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

## 监控和日志

### 1. 日志配置

```bash
# 创建日志目录
sudo mkdir -p /var/log/medical_device_reports
sudo chown www-data:www-data /var/log/medical_device_reports

# 配置logrotate
sudo nano /etc/logrotate.d/medical-device-reports
```

```
/var/log/medical_device_reports/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload medical-device-reports
    endscript
}
```

### 2. 监控脚本

```bash
# 创建监控脚本
nano /opt/medical_device_reports/scripts/monitor.sh
```

```bash
#!/bin/bash
# 系统监控脚本

# 检查服务状态
systemctl is-active --quiet medical-device-reports || echo "Django服务异常"
systemctl is-active --quiet nginx || echo "Nginx服务异常"
systemctl is-active --quiet mysql || echo "MySQL服务异常"
systemctl is-active --quiet redis || echo "Redis服务异常"

# 检查磁盘空间
df -h | awk '$5 > 80 {print "磁盘空间不足: " $0}'

# 检查内存使用
free | awk 'NR==2{printf "内存使用率: %.2f%%\n", $3*100/$2}'

# 检查应用健康
curl -f http://localhost:8000/health/ > /dev/null || echo "应用健康检查失败"
```

## 备份策略

### 1. 数据库备份

```bash
# 创建备份脚本
nano /opt/medical_device_reports/scripts/backup_db.sh
```

```bash
#!/bin/bash
BACKUP_DIR="/opt/backups/mysql"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="medical_device_reports"

mkdir -p $BACKUP_DIR

mysqldump -u medical_device_user -p$DB_PASSWORD $DB_NAME | gzip > $BACKUP_DIR/backup_$DATE.sql.gz

# 保留最近30天的备份
find $BACKUP_DIR -name "backup_*.sql.gz" -mtime +30 -delete
```

### 2. 文件备份

```bash
# 媒体文件备份
rsync -av /opt/medical_device_reports/media/ /opt/backups/media/

# 配置文件备份
tar -czf /opt/backups/config_$(date +%Y%m%d).tar.gz /opt/medical_device_reports/.env /etc/nginx/sites-available/medical-device-reports
```

## 性能优化

### 1. 数据库优化

```sql
-- MySQL配置优化
SET GLOBAL innodb_buffer_pool_size = **********; -- 1GB
SET GLOBAL query_cache_size = 268435456; -- 256MB
SET GLOBAL max_connections = 200;
```

### 2. Redis优化

```conf
# redis.conf
maxmemory 512mb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

### 3. Django优化

```python
# settings/production.py
# 数据库连接池
DATABASES['default']['OPTIONS'] = {
    'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
    'charset': 'utf8mb4',
    'autocommit': True,
}

# 缓存配置
CACHES['default']['OPTIONS']['CONNECTION_POOL_KWARGS'] = {
    'max_connections': 50,
    'retry_on_timeout': True,
}

# 静态文件压缩
STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'
```

## 安全配置

### 1. 防火墙设置

```bash
# UFW防火墙配置
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw enable
```

### 2. 安全更新

```bash
# 自动安全更新
sudo apt install -y unattended-upgrades
sudo dpkg-reconfigure -plow unattended-upgrades
```

### 3. 应用安全

```python
# settings/production.py
SECURE_SSL_REDIRECT = True
SECURE_HSTS_SECONDS = 31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_BROWSER_XSS_FILTER = True
X_FRAME_OPTIONS = 'DENY'
```

## 故障排除

### 常见问题

1. **服务启动失败**: 检查日志 `journalctl -u medical-device-reports`
2. **数据库连接失败**: 验证数据库配置和权限
3. **静态文件404**: 运行 `python manage.py collectstatic`
4. **缓存问题**: 重启Redis服务
5. **SSL证书问题**: 检查证书有效期和配置

### 维护命令

```bash
# 重启所有服务
sudo systemctl restart medical-device-reports nginx mysql redis

# 查看日志
tail -f /var/log/medical_device_reports/django.log
tail -f /var/log/nginx/error.log

# 清理缓存
python manage.py shell -c "from django.core.cache import cache; cache.clear()"

# 数据库维护
python manage.py dbshell
```

## 相关文档

- [MySQL时区配置](DATABASE_TIMEZONE_SETUP.md)
- [Redis缓存配置](REDIS_CACHE_CONFIGURATION.md)
- [故障排除指南](TROUBLESHOOTING_GUIDE.md)
- [环境验证脚本](../scripts/verify_environment.py)
