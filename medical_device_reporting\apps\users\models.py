"""
用户管理数据模型
User Management Models for Medical Device Reporting Platform
"""

from django.db import models
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from django.utils import timezone

from apps.common.models import BaseModel, AuditableModel
from apps.common.utils import ValidationUtils


class Department(AuditableModel):
    """
    科室模型
    
    管理医院科室信息
    """
    
    name = models.CharField(
        max_length=100,
        unique=True,
        verbose_name='科室名称',
        help_text='科室的完整名称'
    )
    
    code = models.CharField(
        max_length=20,
        unique=True,
        verbose_name='科室代码',
        help_text='科室的唯一标识代码'
    )
    

    
    is_active = models.BooleanField(
        default=True,
        verbose_name='是否启用',
        help_text='科室是否处于启用状态'
    )
    
    class Meta:
        db_table = 'users_department'
        verbose_name = '科室'
        verbose_name_plural = '科室'
        ordering = ['code', 'name']
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['name']),
            models.Index(fields=['is_active']),
        ]
    
    def __str__(self):
        return f"{self.code} - {self.name}"
    
    def clean(self):
        """
        数据验证
        """
        super().clean()

        # 验证科室代码格式
        if self.code:
            if not self.code.replace('_', '').isalnum():
                raise ValidationError({
                    'code': '科室代码只能包含字母、数字和下划线'
                })

            # 验证代码长度
            if len(self.code) < 2 or len(self.code) > 20:
                raise ValidationError({
                    'code': '科室代码长度必须在2-20个字符之间'
                })

        # 验证科室名称
        if self.name:
            if len(self.name.strip()) < 2:
                raise ValidationError({
                    'name': '科室名称至少需要2个字符'
                })



    def save(self, *args, **kwargs):
        """
        保存时的额外处理
        """
        # 标准化数据
        if self.name:
            self.name = self.name.strip()
        if self.code:
            self.code = self.code.strip().upper()

        super().save(*args, **kwargs)




class UserRole(BaseModel):
    """
    用户角色模型

    定义系统中的用户角色和权限
    """

    name = models.CharField(
        max_length=50,
        unique=True,
        verbose_name='角色名称',
        help_text='角色的名称'
    )

    code = models.CharField(
        max_length=20,
        unique=True,
        verbose_name='角色代码',
        help_text='角色的唯一标识代码'
    )

    description = models.TextField(
        blank=True,
        verbose_name='角色描述',
        help_text='角色的详细描述'
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name='是否启用',
        help_text='角色是否处于启用状态'
    )

    is_system_role = models.BooleanField(
        default=False,
        verbose_name='是否系统角色',
        help_text='系统内置角色不可删除'
    )

    permissions = models.ManyToManyField(
        'auth.Permission',
        blank=True,
        verbose_name='权限',
        help_text='角色拥有的权限'
    )

    class Meta:
        db_table = 'users_userrole'
        verbose_name = '用户角色'
        verbose_name_plural = '用户角色'
        ordering = ['code', 'name']
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['name']),
            models.Index(fields=['is_active']),
        ]

    def __str__(self):
        return f"{self.code} - {self.name}"

    def clean(self):
        """
        数据验证
        """
        super().clean()

        # 验证角色代码格式
        if self.code:
            if not self.code.replace('_', '').isalnum():
                raise ValidationError({
                    'code': '角色代码只能包含字母、数字和下划线'
                })

            # 验证代码长度
            if len(self.code) < 2 or len(self.code) > 20:
                raise ValidationError({
                    'code': '角色代码长度必须在2-20个字符之间'
                })

        # 验证角色名称
        if self.name:
            if len(self.name.strip()) < 2:
                raise ValidationError({
                    'name': '角色名称至少需要2个字符'
                })

    def save(self, *args, **kwargs):
        """
        保存时的额外处理
        """
        # 标准化数据
        if self.name:
            self.name = self.name.strip()
        if self.code:
            self.code = self.code.strip().lower()

        super().save(*args, **kwargs)

    def add_permission(self, permission):
        """
        添加权限

        Args:
            permission: 权限对象
        """
        self.permissions.add(permission)

    def remove_permission(self, permission):
        """
        移除权限

        Args:
            permission: 权限对象
        """
        self.permissions.remove(permission)

    def has_permission(self, permission_codename):
        """
        检查角色是否有特定权限

        Args:
            permission_codename: 权限代码名

        Returns:
            bool: 是否有权限
        """
        return self.permissions.filter(codename=permission_codename).exists()

    @classmethod
    def get_system_roles(cls):
        """
        获取系统内置角色
        """
        return cls.objects.filter(is_system_role=True, is_active=True)

    @classmethod
    def get_custom_roles(cls):
        """
        获取自定义角色
        """
        return cls.objects.filter(is_system_role=False, is_active=True)


class UserProfile(AuditableModel):
    """
    用户配置文件模型
    
    扩展Django User模型，存储用户的额外信息
    """
    
    ROLE_CHOICES = [
        ('admin', '管理员'),
        ('staff', '科室人员'),
    ]
    
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='profile',
        verbose_name='用户',
        help_text='关联的Django用户对象'
    )
    
    account_number = models.CharField(
        max_length=4,
        unique=True,
        verbose_name='账号',
        help_text='4位数登录账号'
    )
    
    department = models.ForeignKey(
        Department,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='users',
        verbose_name='所属科室',
        help_text='用户所属的科室'
    )
    
    role = models.CharField(
        max_length=20,
        choices=ROLE_CHOICES,
        default='staff',
        verbose_name='用户角色',
        help_text='用户在系统中的角色'
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name='是否启用',
        help_text='用户账号是否处于启用状态'
    )
    

    
    last_login_ip = models.GenericIPAddressField(
        null=True,
        blank=True,
        verbose_name='最后登录IP',
        help_text='用户最后一次登录的IP地址'
    )
    
    class Meta:
        db_table = 'users_userprofile'
        verbose_name = '用户配置文件'
        verbose_name_plural = '用户配置文件'
        ordering = ['account_number']
        indexes = [
            models.Index(fields=['account_number']),
            models.Index(fields=['role']),
            models.Index(fields=['is_active']),
            models.Index(fields=['department']),
        ]
        permissions = [
            ('can_manage_users', '可以管理用户'),
            ('can_view_all_users', '可以查看所有用户'),
            ('can_assign_roles', '可以分配角色'),
        ]
    
    def __str__(self):
        return f"{self.account_number} - {self.user.get_full_name() or self.user.username}"
    
    def clean(self):
        """
        数据验证
        """
        super().clean()

        # 验证4位数账号格式
        if self.account_number:
            if not ValidationUtils.validate_account_number(self.account_number):
                raise ValidationError({
                    'account_number': '账号必须是4位数字'
                })

        # 验证科室人员必须有科室
        if self.role == 'staff' and not self.department:
            raise ValidationError({
                'department': '科室人员必须指定所属科室'
            })

        # 验证科室是否处于活跃状态
        if self.department and not self.department.is_active:
            raise ValidationError({
                'department': '不能分配到已禁用的科室'
            })



        # 验证用户邮箱格式
        if self.user and self.user.email:
            if not ValidationUtils.validate_email(self.user.email):
                raise ValidationError({
                    'user': {'email': '邮箱格式不正确'}
                })
    
    def save(self, *args, **kwargs):
        """
        保存时的额外处理
        """
        # 标准化数据
        if self.account_number:
            self.account_number = self.account_number.strip()

        # 调用clean方法进行验证
        self.clean()

        # 同步用户状态
        if self.user:
            self.user.is_active = self.is_active
            self.user.save(update_fields=['is_active'])

        super().save(*args, **kwargs)
    
    @property
    def display_name(self):
        """
        显示名称 - 中文格式：姓+名
        """
        if self.user.last_name and self.user.first_name:
            return f"{self.user.last_name}{self.user.first_name}"
        elif self.user.get_full_name():
            return self.user.get_full_name()
        else:
            return self.user.username

    @property
    def is_admin(self):
        """
        是否为管理员
        """
        return self.role == 'admin'

    @property
    def is_staff_member(self):
        """
        是否为科室人员
        """
        return self.role == 'staff'

    @property
    def full_info(self):
        """
        完整信息显示
        """
        info = f"{self.account_number} - {self.display_name}"
        if self.department:
            info += f" ({self.department.name})"
        return info



    def get_permissions(self):
        """
        获取用户权限列表
        """
        if not self.user:
            return []

        permissions = []
        for group in self.user.groups.all():
            permissions.extend(group.permissions.all())

        # 添加用户直接权限
        permissions.extend(self.user.user_permissions.all())

        return list(set(permissions))

    def has_permission(self, permission_codename):
        """
        检查用户是否有特定权限

        Args:
            permission_codename: 权限代码名

        Returns:
            bool: 是否有权限
        """
        if not self.user:
            return False

        return self.user.has_perm(f'users.{permission_codename}')

    def can_manage_users(self):
        """
        是否可以管理用户
        """
        return self.is_admin and self.has_permission('can_manage_users')

    def can_view_all_users(self):
        """
        是否可以查看所有用户
        """
        return self.is_admin and self.has_permission('can_view_all_users')

    def can_assign_roles(self):
        """
        是否可以分配角色
        """
        return self.is_admin and self.has_permission('can_assign_roles')

    def update_last_login_ip(self, ip_address):
        """
        更新最后登录IP

        Args:
            ip_address: IP地址
        """
        self.last_login_ip = ip_address
        self.save(update_fields=['last_login_ip'])

    def activate(self):
        """
        激活用户
        """
        self.is_active = True
        self.save(update_fields=['is_active'])

    def deactivate(self):
        """
        禁用用户
        """
        self.is_active = False
        self.save(update_fields=['is_active'])

    def change_department(self, new_department):
        """
        更换科室

        Args:
            new_department: 新科室对象
        """
        if new_department and not new_department.is_active:
            raise ValidationError('不能分配到已禁用的科室')

        self.department = new_department
        self.save(update_fields=['department'])

    def change_role(self, new_role):
        """
        更换角色

        Args:
            new_role: 新角色
        """
        if new_role not in dict(self.ROLE_CHOICES):
            raise ValidationError('无效的角色类型')

        old_role = self.role
        self.role = new_role

        # 验证角色和科室的关系
        if new_role == 'staff' and not self.department:
            raise ValidationError('科室人员必须指定所属科室')

        self.save(update_fields=['role'])

        # 记录角色变更日志
        import logging
        logger = logging.getLogger('apps.users')
        logger.info(
            f'用户角色变更: {self.account_number} 从 {old_role} 变更为 {new_role}',
            extra={
                'account_number': self.account_number,
                'old_role': old_role,
                'new_role': new_role,
            }
        )
