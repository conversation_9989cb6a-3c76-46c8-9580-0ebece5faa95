"""
用户管理服务层测试
User Management Services Tests
"""

from django.test import TestCase
from django.contrib.auth.models import User, Group
from django.core.exceptions import ValidationError

from apps.users.models import UserProfile, Department
from apps.users.services import (
    user_create, user_update, user_delete, user_activate, user_deactivate,
    department_create, department_update, initialize_user_groups
)
from apps.common.exceptions import DataValidationError, BusinessLogicError


class UserServiceTest(TestCase):
    """用户服务测试"""
    
    def setUp(self):
        """测试前准备"""
        # 初始化用户组
        initialize_user_groups()
        
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>'
        )
        
        self.department = Department.objects.create(
            code='TEST',
            name='测试科室',
            created_by=self.admin_user
        )
    
    def test_user_create_success(self):
        """测试用户创建成功"""
        profile = user_create(
            account_number='1001',
            username='测试用户',
            first_name='测试',
            last_name='用户',
            email='<EMAIL>',
            role='staff',
            department_id=self.department.id,
            phone='***********',
            created_by=self.admin_user
        )
        
        self.assertEqual(profile.account_number, '1001')
        self.assertEqual(profile.user.username, '测试用户')
        self.assertEqual(profile.role, 'staff')
        self.assertEqual(profile.department, self.department)
        self.assertTrue(profile.is_active)
        self.assertEqual(profile.created_by, self.admin_user)
    
    def test_user_create_admin_without_department(self):
        """测试创建无科室的管理员"""
        profile = user_create(
            account_number='0001',
            username='系统管理员',
            role='admin',
            created_by=self.admin_user
        )
        
        self.assertEqual(profile.role, 'admin')
        self.assertIsNone(profile.department)
        self.assertTrue(profile.is_active)
    
    def test_user_create_duplicate_account_number(self):
        """测试创建重复账号"""
        # 创建第一个用户
        user_create(
            account_number='1001',
            username='用户1',
            role='staff',
            department_id=self.department.id,
            created_by=self.admin_user
        )
        
        # 尝试创建重复账号
        with self.assertRaises(DataValidationError):
            user_create(
                account_number='1001',  # 重复账号
                username='用户2',
                role='staff',
                department_id=self.department.id,
                created_by=self.admin_user
            )
    
    def test_user_create_staff_without_department(self):
        """测试创建无科室的科室人员"""
        with self.assertRaises(BusinessLogicError):
            user_create(
                account_number='1001',
                username='科室人员',
                role='staff',
                department_id=None,  # 科室人员必须有科室
                created_by=self.admin_user
            )
    
    def test_user_create_invalid_account_number(self):
        """测试创建无效账号"""
        with self.assertRaises(DataValidationError):
            user_create(
                account_number='12345',  # 非4位数
                username='测试用户',
                role='staff',
                department_id=self.department.id,
                created_by=self.admin_user
            )
    
    def test_user_update_success(self):
        """测试用户更新成功"""
        profile = user_create(
            account_number='1001',
            username='原用户名',
            role='staff',
            department_id=self.department.id,
            created_by=self.admin_user
        )
        
        # 创建新科室
        new_department = Department.objects.create(
            code='NEW',
            name='新科室',
            created_by=self.admin_user
        )
        
        updated_profile = user_update(
            profile.id,
            username='新用户名',
            department_id=new_department.id,
            phone='***********',
            updated_by=self.admin_user
        )
        
        self.assertEqual(updated_profile.user.username, '新用户名')
        self.assertEqual(updated_profile.department, new_department)
        self.assertEqual(updated_profile.phone, '***********')
        self.assertEqual(updated_profile.updated_by, self.admin_user)
    
    def test_user_delete_success(self):
        """测试用户删除成功"""
        profile = user_create(
            account_number='1001',
            username='测试用户',
            role='staff',
            department_id=self.department.id,
            created_by=self.admin_user
        )
        
        result = user_delete(profile.id, deleted_by=self.admin_user)
        
        self.assertTrue(result)
        
        # 检查软删除
        profile.refresh_from_db()
        self.assertTrue(profile.is_deleted)
        self.assertFalse(profile.is_active)
        self.assertEqual(profile.updated_by, self.admin_user)
    
    def test_user_activate_success(self):
        """测试用户激活成功"""
        profile = user_create(
            account_number='1001',
            username='测试用户',
            role='staff',
            department_id=self.department.id,
            is_active=False,  # 创建时为非激活状态
            created_by=self.admin_user
        )
        
        activated_profile = user_activate(profile.id, activated_by=self.admin_user)
        
        self.assertTrue(activated_profile.is_active)
        self.assertTrue(activated_profile.user.is_active)
        self.assertEqual(activated_profile.updated_by, self.admin_user)
    
    def test_user_deactivate_success(self):
        """测试用户禁用成功"""
        profile = user_create(
            account_number='1001',
            username='测试用户',
            role='staff',
            department_id=self.department.id,
            created_by=self.admin_user
        )
        
        deactivated_profile = user_deactivate(profile.id, deactivated_by=self.admin_user)
        
        self.assertFalse(deactivated_profile.is_active)
        self.assertFalse(deactivated_profile.user.is_active)
        self.assertEqual(deactivated_profile.updated_by, self.admin_user)
    
    def test_user_operation_on_nonexistent_user(self):
        """测试对不存在用户的操作"""
        with self.assertRaises(DataValidationError):
            user_update(99999, username='不存在的用户')
        
        with self.assertRaises(DataValidationError):
            user_delete(99999)
        
        with self.assertRaises(DataValidationError):
            user_activate(99999)
        
        with self.assertRaises(DataValidationError):
            user_deactivate(99999)


class DepartmentServiceTest(TestCase):
    """科室服务测试"""
    
    def setUp(self):
        """测试前准备"""
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>'
        )
    
    def test_department_create_success(self):
        """测试科室创建成功"""
        department = department_create(
            code='TEST',
            name='测试科室',
            description='测试用科室',
            created_by=self.admin_user
        )
        
        self.assertEqual(department.code, 'TEST')
        self.assertEqual(department.name, '测试科室')
        self.assertEqual(department.description, '测试用科室')
        self.assertTrue(department.is_active)
        self.assertEqual(department.created_by, self.admin_user)
    
    def test_department_create_duplicate_code(self):
        """测试创建重复科室代码"""
        department_create(
            code='TEST',
            name='测试科室1',
            created_by=self.admin_user
        )
        
        with self.assertRaises(DataValidationError):
            department_create(
                code='TEST',  # 重复代码
                name='测试科室2',
                created_by=self.admin_user
            )
    
    def test_department_update_success(self):
        """测试科室更新成功"""
        department = department_create(
            code='TEST',
            name='原科室名',
            created_by=self.admin_user
        )
        
        updated_department = department_update(
            department.id,
            name='新科室名',
            description='更新后的描述',
            updated_by=self.admin_user
        )
        
        self.assertEqual(updated_department.name, '新科室名')
        self.assertEqual(updated_department.description, '更新后的描述')
        self.assertEqual(updated_department.updated_by, self.admin_user)


class UserGroupInitializationTest(TestCase):
    """用户组初始化测试"""
    
    def test_initialize_user_groups(self):
        """测试用户组初始化"""
        # 清除现有组
        Group.objects.all().delete()
        
        # 初始化用户组
        result = initialize_user_groups()
        
        self.assertTrue(result)
        
        # 检查组是否创建
        admin_group = Group.objects.filter(name='管理员').first()
        staff_group = Group.objects.filter(name='科室人员').first()
        
        self.assertIsNotNone(admin_group)
        self.assertIsNotNone(staff_group)
        
        # 检查权限分配
        self.assertTrue(admin_group.permissions.exists())
        self.assertTrue(staff_group.permissions.exists())
    
    def test_initialize_user_groups_idempotent(self):
        """测试用户组初始化的幂等性"""
        # 多次调用应该不会出错
        result1 = initialize_user_groups()
        result2 = initialize_user_groups()
        
        self.assertTrue(result1)
        self.assertTrue(result2)
        
        # 组数量应该保持不变
        admin_groups = Group.objects.filter(name='管理员')
        staff_groups = Group.objects.filter(name='科室人员')
        
        self.assertEqual(admin_groups.count(), 1)
        self.assertEqual(staff_groups.count(), 1)
