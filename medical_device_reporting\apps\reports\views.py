"""
不良事件上报管理视图
Adverse Event Reports Management Views for Medical Device Reporting Platform
"""

from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.views.generic import ListView, DetailView, CreateView, UpdateView
from django.urls import reverse_lazy, reverse
from django.utils.decorators import method_decorator
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from django.views.decorators.http import require_http_methods
from django.utils import timezone

from apps.users.permissions import (
    admin_required,
    department_member_or_admin_required,
    AdminRequiredMixin,
    DepartmentMemberOrAdminMixin
)
from apps.common.exceptions import (
    BusinessLogicError,
    DataValidationError,
    ResourceNotFoundError,
    ReportSubmissionError
)
from apps.users.models import UserProfile, Department

from .models import AdverseEventReport
from .forms import (
    AdverseEventReportForm,
    ReportSearchForm,
    ReportReviewForm,
    ReporterInfoForm,
    PatientInfoForm,
    EventInfoForm,
    DeviceInfoForm
)
from .services import (
    report_create,
    report_update,
    report_submit,
    report_review,
    get_report_by_id
)
from .selectors import (
    report_list,
    report_detail,
    user_reports,
    report_statistics,
    report_list_paginated,
    report_list_pending_review,
    report_list_serious_events
)

import logging

logger = logging.getLogger('apps.reports')


@department_member_or_admin_required
def report_list_view(request):
    """
    报告列表视图
    """
    # 获取搜索表单
    search_form = ReportSearchForm(request.GET or None)

    # 获取筛选参数
    search_params = {}
    if search_form.is_valid():
        search_params = {
            'search': search_form.cleaned_data.get('search'),
            'status': search_form.cleaned_data.get('status'),
            'department_id': search_form.cleaned_data.get('department').id if search_form.cleaned_data.get('department') else None,
            'injury_level': search_form.cleaned_data.get('injury_level'),
            'start_date': search_form.cleaned_data.get('start_date'),
            'end_date': search_form.cleaned_data.get('end_date'),
            'ordering': search_form.cleaned_data.get('ordering', '-created_at'),
        }

    # 获取分页参数
    page = request.GET.get('page', 1)
    page_size = int(request.GET.get('page_size', 20))

    try:
        # 获取用户配置文件
        try:
            user_profile = UserProfile.objects.get(user=request.user)
        except UserProfile.DoesNotExist:
            messages.error(request, '用户配置文件不存在，请联系管理员')
            return redirect('users:dashboard')

        # 获取分页报告列表
        paginated_data = report_list_paginated(
            user_profile=user_profile,
            page=page,
            page_size=page_size,
            **search_params
        )

        # 获取统计信息
        stats = report_statistics(user_profile=user_profile)

        context = {
            'reports': paginated_data['reports'],
            'page': paginated_data['page'],
            'total_pages': paginated_data['total_pages'],
            'total_count': paginated_data['total_count'],
            'has_previous': paginated_data['has_previous'],
            'has_next': paginated_data['has_next'],
            'previous_page': paginated_data['previous_page'],
            'next_page': paginated_data['next_page'],
            'start_index': paginated_data['start_index'],
            'end_index': paginated_data['end_index'],
            'search_form': search_form,
            'stats': stats,
            'page_size': page_size,
        }

        return render(request, 'reports/report_list.html', context)

    except Exception as e:
        logger.error(f'获取报告列表失败: {str(e)}')
        messages.error(request, '获取报告列表失败，请稍后重试')
        return render(request, 'reports/report_list.html', {
            'reports': [],
            'search_form': search_form,
            'stats': {},
        })


@department_member_or_admin_required
def report_detail_view(request, report_id):
    """
    报告详情视图
    """
    try:
        # 获取用户配置文件
        try:
            user_profile = UserProfile.objects.get(user=request.user)
        except UserProfile.DoesNotExist:
            messages.error(request, '用户配置文件不存在，请联系管理员')
            return redirect('users:dashboard')

        report = report_detail(report_id, user_profile=user_profile)
        if not report:
            messages.error(request, '报告不存在或您没有权限查看')
            return redirect('reports:report_list')

        context = {
            'report': report,
            'can_edit': report.can_edit and (
                user_profile.is_admin or
                report.reporter == user_profile
            ),
            'can_submit': report.can_submit and (
                user_profile.is_admin or
                report.reporter == user_profile
            ),
            'can_review': report.can_review and user_profile.is_admin,
        }

        return render(request, 'reports/report_detail.html', context)

    except Exception as e:
        logger.error(f'获取报告详情失败: {str(e)}')
        messages.error(request, '获取报告详情失败，请稍后重试')
        return redirect('reports:report_list')


@department_member_or_admin_required
def report_create_view(request):
    """
    报告创建视图
    """
    # 获取用户配置文件
    try:
        user_profile = UserProfile.objects.get(user=request.user)
    except UserProfile.DoesNotExist:
        messages.error(request, '用户配置文件不存在，请联系管理员')
        return redirect('users:dashboard')

    if request.method == 'POST':
        form = AdverseEventReportForm(request.POST, user_profile=user_profile)

        if form.is_valid():
            try:
                # 使用服务层创建报告
                report = report_create(
                    reporter_id=form.cleaned_data['reporter'].id,
                    department_id=form.cleaned_data['department'].id if form.cleaned_data['department'] else None,
                    reporter_phone=form.cleaned_data['reporter_phone'],
                    patient_name=form.cleaned_data['patient_name'],
                    patient_age=form.cleaned_data['patient_age'],
                    patient_gender=form.cleaned_data['patient_gender'],
                    patient_contact=form.cleaned_data['patient_contact'],
                    device_malfunction=form.cleaned_data['device_malfunction'],
                    event_date=form.cleaned_data['event_date'],
                    injury_level=form.cleaned_data['injury_level'],
                    injury_description=form.cleaned_data['injury_description'],
                    event_description=form.cleaned_data['event_description'],
                    initial_cause_analysis=form.cleaned_data['initial_cause_analysis'],
                    initial_treatment=form.cleaned_data['initial_treatment'],
                    device_name=form.cleaned_data['device_name'],
                    registration_number=form.cleaned_data['registration_number'],
                    manufacturer=form.cleaned_data['manufacturer'],
                    specification=form.cleaned_data['specification'],
                    model=form.cleaned_data['model'],
                    product_number=form.cleaned_data['product_number'],
                    batch_number=form.cleaned_data['batch_number'],
                    production_date=form.cleaned_data['production_date'],
                    expiry_date=form.cleaned_data['expiry_date'],
                    created_by=request.user
                )

                messages.success(request, f'报告 {report.report_number} 创建成功')
                return redirect('reports:report_detail', report_id=report.id)

            except (DataValidationError, BusinessLogicError) as e:
                messages.error(request, str(e))
            except Exception as e:
                logger.error(f'创建报告失败: {str(e)}')
                messages.error(request, '创建报告失败，请重试')
    else:
        form = AdverseEventReportForm(user_profile=user_profile)

    context = {
        'form': form,
        'is_edit': False,
        'title': '创建不良事件报告',
    }

    return render(request, 'reports/report_form.html', context)


@department_member_or_admin_required
def report_edit_view(request, report_id):
    """
    报告编辑视图
    """
    try:
        # 获取用户配置文件
        try:
            user_profile = UserProfile.objects.get(user=request.user)
        except UserProfile.DoesNotExist:
            messages.error(request, '用户配置文件不存在，请联系管理员')
            return redirect('users:dashboard')

        report = report_detail(report_id, user_profile=user_profile)
        if not report:
            messages.error(request, '报告不存在或您没有权限编辑')
            return redirect('reports:report_list')

        # 检查编辑权限
        if not report.can_edit:
            messages.error(request, '报告当前状态不允许编辑')
            return redirect('reports:report_detail', report_id=report_id)

        # 检查用户权限
        if not (user_profile.is_admin or report.reporter == user_profile):
            messages.error(request, '您没有权限编辑此报告')
            return redirect('reports:report_detail', report_id=report_id)

        if request.method == 'POST':
            form = AdverseEventReportForm(
                request.POST,
                instance=report,
                user_profile=user_profile
            )

            if form.is_valid():
                try:
                    # 使用服务层更新报告
                    updated_report = report_update(
                        report_id=report.id,
                        reporter_phone=form.cleaned_data['reporter_phone'],
                        patient_name=form.cleaned_data['patient_name'],
                        patient_age=form.cleaned_data['patient_age'],
                        patient_gender=form.cleaned_data['patient_gender'],
                        patient_contact=form.cleaned_data['patient_contact'],
                        device_malfunction=form.cleaned_data['device_malfunction'],
                        event_date=form.cleaned_data['event_date'],
                        injury_level=form.cleaned_data['injury_level'],
                        injury_description=form.cleaned_data['injury_description'],
                        event_description=form.cleaned_data['event_description'],
                        initial_cause_analysis=form.cleaned_data['initial_cause_analysis'],
                        initial_treatment=form.cleaned_data['initial_treatment'],
                        device_name=form.cleaned_data['device_name'],
                        registration_number=form.cleaned_data['registration_number'],
                        manufacturer=form.cleaned_data['manufacturer'],
                        specification=form.cleaned_data['specification'],
                        model=form.cleaned_data['model'],
                        product_number=form.cleaned_data['product_number'],
                        batch_number=form.cleaned_data['batch_number'],
                        production_date=form.cleaned_data['production_date'],
                        expiry_date=form.cleaned_data['expiry_date'],
                        updated_by=request.user
                    )

                    messages.success(request, f'报告 {updated_report.report_number} 更新成功')
                    return redirect('reports:report_detail', report_id=updated_report.id)

                except (DataValidationError, BusinessLogicError) as e:
                    messages.error(request, str(e))
                except Exception as e:
                    logger.error(f'更新报告失败: {str(e)}')
                    messages.error(request, '更新报告失败，请重试')
        else:
            form = AdverseEventReportForm(
                instance=report,
                user_profile=user_profile
            )

        context = {
            'form': form,
            'report': report,
            'is_edit': True,
            'title': f'编辑报告 {report.report_number}',
        }

        return render(request, 'reports/report_form.html', context)

    except Exception as e:
        logger.error(f'获取报告编辑页面失败: {str(e)}')
        messages.error(request, '获取报告信息失败，请稍后重试')
        return redirect('reports:report_list')


@department_member_or_admin_required
@require_http_methods(["POST"])
def report_submit_view(request, report_id):
    """
    报告提交视图
    """
    try:
        # 获取用户配置文件
        try:
            user_profile = UserProfile.objects.get(user=request.user)
        except UserProfile.DoesNotExist:
            return JsonResponse({'error': '用户配置文件不存在，请联系管理员'}, status=400)

        report = report_detail(report_id, user_profile=user_profile)
        if not report:
            return JsonResponse({'error': '报告不存在或您没有权限操作'}, status=404)

        # 检查提交权限
        if not report.can_submit:
            return JsonResponse({'error': '报告当前状态不允许提交'}, status=400)

        # 检查用户权限
        if not (user_profile.is_admin or report.reporter == user_profile):
            return JsonResponse({'error': '您没有权限提交此报告'}, status=403)

        # 提交报告
        submitted_report = report_submit(
            report_id=report.id,
            submitted_by=request.user
        )

        return JsonResponse({
            'success': True,
            'message': f'报告 {submitted_report.report_number} 提交成功',
            'status': submitted_report.status,
            'status_display': submitted_report.get_status_display()
        })

    except ReportSubmissionError as e:
        return JsonResponse({'error': str(e)}, status=400)
    except Exception as e:
        logger.error(f'提交报告失败: {str(e)}')
        return JsonResponse({'error': '提交报告失败，请重试'}, status=500)


@admin_required
def report_review_view(request, report_id):
    """
    报告审核视图
    """
    try:
        report = get_report_by_id(report_id)
        if not report:
            messages.error(request, '报告不存在')
            return redirect('reports:report_list')

        if request.method == 'POST':
            form = ReportReviewForm(request.POST)

            if form.is_valid():
                try:
                    # 获取用户配置文件
                    try:
                        user_profile = UserProfile.objects.get(user=request.user)
                    except UserProfile.DoesNotExist:
                        messages.error(request, '用户配置文件不存在，请联系管理员')
                        return redirect('users:dashboard')

                    # 使用服务层审核报告
                    reviewed_report = report_review(
                        report_id=report.id,
                        reviewer_id=user_profile.id,
                        action=form.cleaned_data['action'],
                        comments=form.cleaned_data['comments'],
                        reviewed_by=request.user
                    )

                    action_text = {
                        'start_review': '开始审核',
                        'approve': '批准',
                        'reject': '拒绝'
                    }.get(form.cleaned_data['action'], '审核')

                    messages.success(request, f'报告 {reviewed_report.report_number} {action_text}成功')
                    return redirect('reports:report_detail', report_id=reviewed_report.id)

                except (DataValidationError, BusinessLogicError) as e:
                    messages.error(request, str(e))
                except Exception as e:
                    logger.error(f'审核报告失败: {str(e)}')
                    messages.error(request, '审核报告失败，请重试')
        else:
            form = ReportReviewForm()

        context = {
            'report': report,
            'form': form,
            'title': f'审核报告 {report.report_number}',
        }

        return render(request, 'reports/report_review.html', context)

    except Exception as e:
        logger.error(f'获取报告审核页面失败: {str(e)}')
        messages.error(request, '获取报告信息失败，请稍后重试')
        return redirect('reports:report_list')


@admin_required
def report_pending_review_view(request):
    """
    待审核报告列表视图
    """
    try:
        # 获取分页参数
        page = request.GET.get('page', 1)
        page_size = int(request.GET.get('page_size', 20))

        # 获取用户配置文件
        try:
            user_profile = UserProfile.objects.get(user=request.user)
        except UserProfile.DoesNotExist:
            messages.error(request, '用户配置文件不存在，请联系管理员')
            return redirect('users:dashboard')

        # 获取待审核报告
        reports = report_list_pending_review(user_profile=user_profile)

        # 分页处理
        paginator = Paginator(reports, page_size)
        try:
            reports_page = paginator.page(page)
        except PageNotAnInteger:
            reports_page = paginator.page(1)
        except EmptyPage:
            reports_page = paginator.page(paginator.num_pages)

        context = {
            'reports': reports_page,
            'total_count': paginator.count,
            'title': '待审核报告',
        }

        return render(request, 'reports/report_pending_review.html', context)

    except Exception as e:
        logger.error(f'获取待审核报告列表失败: {str(e)}')
        messages.error(request, '获取待审核报告列表失败，请稍后重试')
        return render(request, 'reports/report_pending_review.html', {'reports': []})


@admin_required
def report_serious_events_view(request):
    """
    严重事件报告列表视图
    """
    try:
        # 获取分页参数
        page = request.GET.get('page', 1)
        page_size = int(request.GET.get('page_size', 20))
        days = request.GET.get('days')

        # 获取用户配置文件
        try:
            user_profile = UserProfile.objects.get(user=request.user)
        except UserProfile.DoesNotExist:
            messages.error(request, '用户配置文件不存在，请联系管理员')
            return redirect('users:dashboard')

        # 获取严重事件报告
        reports = report_list_serious_events(
            user_profile=user_profile,
            days=int(days) if days else None
        )

        # 分页处理
        paginator = Paginator(reports, page_size)
        try:
            reports_page = paginator.page(page)
        except PageNotAnInteger:
            reports_page = paginator.page(1)
        except EmptyPage:
            reports_page = paginator.page(paginator.num_pages)

        context = {
            'reports': reports_page,
            'total_count': paginator.count,
            'days': days,
            'title': '严重事件报告',
        }

        return render(request, 'reports/report_serious_events.html', context)

    except Exception as e:
        logger.error(f'获取严重事件报告列表失败: {str(e)}')
        messages.error(request, '获取严重事件报告列表失败，请稍后重试')
        return render(request, 'reports/report_serious_events.html', {'reports': []})


@department_member_or_admin_required
def report_dashboard_view(request):
    """
    报告管理仪表板视图
    """
    try:
        # 获取用户配置文件
        try:
            user_profile = UserProfile.objects.get(user=request.user)
        except UserProfile.DoesNotExist:
            messages.error(request, '用户配置文件不存在，请联系管理员')
            return redirect('users:dashboard')

        # 获取统计信息
        stats = report_statistics(user_profile=user_profile)

        # 获取最近的报告
        recent_reports = user_reports(
            user_profile=user_profile,
            ordering='-created_at'
        )[:10]

        # 获取待审核报告（仅管理员）
        pending_reports = []
        if user_profile.is_admin:
            pending_reports = report_list_pending_review(
                user_profile=user_profile
            )[:5]

        # 获取严重事件（仅管理员）
        serious_events = []
        if user_profile.is_admin:
            serious_events = report_list_serious_events(
                user_profile=user_profile,
                days=30
            )[:5]

        context = {
            'stats': stats,
            'recent_reports': recent_reports,
            'pending_reports': pending_reports,
            'serious_events': serious_events,
            'is_admin': request.user.profile.is_admin,
        }

        return render(request, 'reports/dashboard.html', context)

    except Exception as e:
        logger.error(f'获取报告仪表板数据失败: {str(e)}')
        messages.error(request, '获取仪表板数据失败，请稍后重试')
        return render(request, 'reports/dashboard.html', {
            'stats': {},
            'recent_reports': [],
            'pending_reports': [],
            'serious_events': [],
        })


@department_member_or_admin_required
def report_step_create_entry_view(request):
    """
    分步创建报告入口视图
    清除session数据并重定向到第一步
    """
    # 清除之前的表单数据
    if 'report_form_data' in request.session:
        del request.session['report_form_data']

    # 重定向到第一步
    return redirect('reports:report_step_create', step=1)


@department_member_or_admin_required
def report_step_create_view(request, step=1):
    """
    分步骤创建报告视图
    """
    step = int(step)
    if step < 1 or step > 4:
        return redirect('reports:report_step_create', step=1)

    # 从session获取数据
    form_data = request.session.get('report_form_data', {})

    if request.method == 'POST':
        if step == 1:
            # 上报人信息
            form = ReporterInfoForm(request.POST)
            # 限制上报人选择器只显示当前用户
            try:
                current_user_profile = UserProfile.objects.get(user=request.user)
            except UserProfile.DoesNotExist:
                messages.error(request, '用户配置文件不存在，请联系管理员')
                return redirect('users:dashboard')
            form.fields['reporter'].queryset = UserProfile.objects.filter(id=current_user_profile.id)

            if form.is_valid():
                # 确保上报人是当前登录用户
                if form.cleaned_data['reporter'] != current_user_profile:
                    messages.error(request, '只能为自己提交报告')
                    return redirect('reports:report_step_create', step=1)

                # 确保科室是当前用户的科室
                selected_department = form.cleaned_data['department']
                if selected_department and selected_department != current_user_profile.department:
                    messages.error(request, '只能选择自己所属的科室')
                    return redirect('reports:report_step_create', step=1)

                form_data.update({
                    'reporter': form.cleaned_data['reporter'].id,
                    'department': form.cleaned_data['department'].id if form.cleaned_data['department'] else None,
                    'reporter_phone': form.cleaned_data['reporter_phone'],
                })
                request.session['report_form_data'] = form_data
                return redirect('reports:report_step_create', step=2)

        elif step == 2:
            # 患者信息
            form = PatientInfoForm(request.POST)
            if form.is_valid():
                form_data.update({
                    'patient_name': form.cleaned_data['patient_name'],
                    'patient_age': form.cleaned_data['patient_age'],
                    'patient_gender': form.cleaned_data['patient_gender'],
                    'patient_contact': form.cleaned_data['patient_contact'],
                })
                request.session['report_form_data'] = form_data
                return redirect('reports:report_step_create', step=3)

        elif step == 3:
            # 事件信息
            form = EventInfoForm(request.POST)
            if form.is_valid():
                form_data.update({
                    'device_malfunction': form.cleaned_data['device_malfunction'],
                    'event_date': form.cleaned_data['event_date'].isoformat(),
                    'injury_level': form.cleaned_data['injury_level'],
                    'injury_description': form.cleaned_data['injury_description'],
                    'event_description': form.cleaned_data['event_description'],
                    'initial_cause_analysis': form.cleaned_data['initial_cause_analysis'],
                    'initial_treatment': form.cleaned_data['initial_treatment'],
                })
                request.session['report_form_data'] = form_data
                return redirect('reports:report_step_create', step=4)

        elif step == 4:
            # 器械信息
            form = DeviceInfoForm(request.POST)
            if form.is_valid():
                form_data.update({
                    'device_name': form.cleaned_data['device_name'],
                    'registration_number': form.cleaned_data['registration_number'],
                    'manufacturer': form.cleaned_data['manufacturer'],
                    'specification': form.cleaned_data['specification'],
                    'model': form.cleaned_data['model'],
                    'product_number': form.cleaned_data['product_number'],
                    'batch_number': form.cleaned_data['batch_number'],
                    'production_date': form.cleaned_data['production_date'].isoformat() if form.cleaned_data['production_date'] else None,
                    'expiry_date': form.cleaned_data['expiry_date'].isoformat() if form.cleaned_data['expiry_date'] else None,
                })

                # 创建报告
                try:
                    from datetime import datetime
                    report = report_create(
                        reporter_id=form_data['reporter'],
                        department_id=form_data.get('department'),
                        reporter_phone=form_data['reporter_phone'],
                        patient_name=form_data['patient_name'],
                        patient_age=form_data['patient_age'],
                        patient_gender=form_data['patient_gender'],
                        patient_contact=form_data.get('patient_contact', ''),
                        device_malfunction=form_data['device_malfunction'],
                        event_date=datetime.fromisoformat(form_data['event_date']),
                        injury_level=form_data['injury_level'],
                        injury_description=form_data.get('injury_description', ''),
                        event_description=form_data['event_description'],
                        initial_cause_analysis=form_data.get('initial_cause_analysis', ''),
                        initial_treatment=form_data.get('initial_treatment', ''),
                        device_name=form_data['device_name'],
                        registration_number=form_data['registration_number'],
                        manufacturer=form_data['manufacturer'],
                        specification=form_data.get('specification', ''),
                        model=form_data.get('model', ''),
                        product_number=form_data.get('product_number', ''),
                        batch_number=form_data.get('batch_number', ''),
                        production_date=datetime.fromisoformat(form_data['production_date']).date() if form_data.get('production_date') else None,
                        expiry_date=datetime.fromisoformat(form_data['expiry_date']).date() if form_data.get('expiry_date') else None,
                        created_by=request.user
                    )

                    # 清除session数据
                    if 'report_form_data' in request.session:
                        del request.session['report_form_data']

                    messages.success(request, f'报告 {report.report_number} 创建成功')
                    return redirect('reports:report_detail', report_id=report.id)

                except Exception as e:
                    logger.error(f'分步创建报告失败: {str(e)}')
                    messages.error(request, '创建报告失败，请重试')

    # 获取对应步骤的表单
    forms = {
        1: ReporterInfoForm,
        2: PatientInfoForm,
        3: EventInfoForm,
        4: DeviceInfoForm,
    }

    form_class = forms.get(step)
    if request.method == 'GET':
        # 预填充表单数据
        initial_data = {}
        if step == 1:
            # 第一步：上报人信息，默认设置为当前登录用户
            try:
                current_user_profile = UserProfile.objects.get(user=request.user)
            except UserProfile.DoesNotExist:
                messages.error(request, '用户配置文件不存在，请联系管理员')
                return redirect('users:dashboard')
            initial_data = {
                'reporter': current_user_profile,
                'department': current_user_profile.department,
                'reporter_phone': form_data.get('reporter_phone', ''),
            }
        elif step == 2:
            initial_data = {
                'patient_name': form_data.get('patient_name', ''),
                'patient_age': form_data.get('patient_age'),
                'patient_gender': form_data.get('patient_gender', ''),
                'patient_contact': form_data.get('patient_contact', ''),
            }
        elif step == 3:
            initial_data = {
                'device_malfunction': form_data.get('device_malfunction', ''),
                'event_date': datetime.fromisoformat(form_data['event_date']) if form_data.get('event_date') else None,
                'injury_level': form_data.get('injury_level', ''),
                'injury_description': form_data.get('injury_description', ''),
                'event_description': form_data.get('event_description', ''),
                'initial_cause_analysis': form_data.get('initial_cause_analysis', ''),
                'initial_treatment': form_data.get('initial_treatment', ''),
            }
        elif step == 4:
            initial_data = {
                'device_name': form_data.get('device_name', ''),
                'registration_number': form_data.get('registration_number', ''),
                'manufacturer': form_data.get('manufacturer', ''),
                'specification': form_data.get('specification', ''),
                'model': form_data.get('model', ''),
                'product_number': form_data.get('product_number', ''),
                'batch_number': form_data.get('batch_number', ''),
                'production_date': datetime.fromisoformat(form_data['production_date']).date() if form_data.get('production_date') else None,
                'expiry_date': datetime.fromisoformat(form_data['expiry_date']).date() if form_data.get('expiry_date') else None,
            }

        form = form_class(initial=initial_data)

        # 如果是第一步，限制上报人和科室选择器
        if step == 1:
            try:
                current_user_profile = UserProfile.objects.get(user=request.user)
            except UserProfile.DoesNotExist:
                messages.error(request, '用户配置文件不存在，请联系管理员')
                return redirect('users:dashboard')

            # 限制上报人选择器只显示当前用户
            form.fields['reporter'].queryset = UserProfile.objects.filter(id=current_user_profile.id)
            # 不使用disabled，而是通过CSS样式和JavaScript来防止修改
            form.fields['reporter'].widget.attrs['class'] += ' bg-light'
            form.fields['reporter'].widget.attrs['title'] = '当前登录用户，不可修改'

            # 限制科室选择器只显示当前用户所属科室
            if current_user_profile.department:
                form.fields['department'].queryset = Department.objects.filter(id=current_user_profile.department.id)
                form.fields['department'].widget.attrs['class'] += ' bg-light'
                form.fields['department'].widget.attrs['title'] = '当前用户所属科室，不可修改'
            else:
                # 如果用户没有科室，清空科室选择
                form.fields['department'].queryset = Department.objects.none()
    else:
        form = form_class(request.POST)

        # 如果是第一步，限制上报人和科室选择器
        if step == 1:
            try:
                current_user_profile = UserProfile.objects.get(user=request.user)
            except UserProfile.DoesNotExist:
                messages.error(request, '用户配置文件不存在，请联系管理员')
                return redirect('users:dashboard')

            # 限制上报人选择器只显示当前用户
            form.fields['reporter'].queryset = UserProfile.objects.filter(id=current_user_profile.id)

            # 限制科室选择器只显示当前用户所属科室
            if current_user_profile.department:
                form.fields['department'].queryset = Department.objects.filter(id=current_user_profile.department.id)
            else:
                form.fields['department'].queryset = Department.objects.none()

    step_titles = {
        1: '上报人信息',
        2: '患者信息',
        3: '事件信息',
        4: '器械信息',
    }

    context = {
        'form': form,
        'step': step,
        'total_steps': 4,
        'step_title': step_titles.get(step, ''),
        'progress_percent': (step / 4) * 100,
        'can_go_back': step > 1,
        'can_go_next': step < 4,
    }

    return render(request, 'reports/report_step_form.html', context)
