"""
用户管理视图
User Management Views for Medical Device Reporting Platform
"""

from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required, permission_required
from .permissions import admin_required, department_member_or_admin_required
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.utils import timezone
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
import json
import logging
import io
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill
from openpyxl.utils import get_column_letter

from apps.common.exceptions import (
    BusinessLogicError,
    DataValidationError,
    ResourceNotFoundError
)
from .models import UserProfile, Department
from .services import user_create, user_update, department_create, department_update, department_delete
from .selectors import (
    user_list,
    user_get_by_id,
    user_get_by_account_number,
    department_list,
    department_get_by_id,
    department_list_with_user_counts,
    user_statistics
)

logger = logging.getLogger('apps.users')


def login_view(request):
    """
    登录视图
    """
    if request.method == 'POST':
        account_number = request.POST.get('account_number', '').strip()

        if not account_number:
            messages.error(request, '请输入4位数账号')
            return render(request, 'users/login.html')

        # 使用自定义认证后端进行认证
        user = authenticate(request, account_number=account_number)

        if user is None:
            messages.error(request, '账号不存在或已被禁用')
            return render(request, 'users/login.html')

        # 登录用户
        login(request, user)

        # 在会话中记录登录信息
        request.session['login_ip'] = get_client_ip(request)
        request.session['login_time'] = timezone.now().isoformat()
        request.session['account_number'] = account_number

        logger.info(
            f'用户登录成功: {account_number} - {user.username}',
            extra={
                'account_number': account_number,
                'username': user.username,
                'ip_address': get_client_ip(request),
            }
        )

        # 重定向到首页或指定页面
        next_url = request.GET.get('next', '/')
        return redirect(next_url)

    return render(request, 'users/login.html')


@department_member_or_admin_required
def dashboard_view(request):
    """
    用户中心视图
    """
    try:
        # 获取用户统计数据
        stats = user_statistics()

        # 获取报告统计数据
        report_stats = {}
        try:
            # 导入报告选择器
            from apps.reports.selectors import (
                report_statistics,
                report_list
            )

            if request.user.profile.is_admin:
                # 管理员看到全局统计
                global_stats = report_statistics(user_profile=request.user.profile)
                report_stats = {
                    'my_reports': global_stats.get('total_count', 0),
                    'draft_reports': global_stats.get('draft_count', 0),
                    'submitted_reports': global_stats.get('submitted_count', 0),
                    'approved_reports': global_stats.get('approved_count', 0),
                    'total_reports': global_stats.get('total_count', 0),
                    'pending_review': global_stats.get('submitted_count', 0) + global_stats.get('under_review_count', 0),
                    'serious_events': global_stats.get('death_count', 0) + global_stats.get('serious_injury_count', 0),
                }
            else:
                # 普通用户看到个人统计
                user_reports = report_list(user_profile=request.user.profile)
                report_stats = {
                    'my_reports': user_reports.count(),
                    'draft_reports': user_reports.filter(status='draft').count(),
                    'submitted_reports': user_reports.filter(status='submitted').count(),
                    'approved_reports': user_reports.filter(status='approved').count(),
                }
        except ImportError:
            # 如果reports应用还没有完全配置，使用默认值
            logger.warning('Reports应用选择器未找到，使用默认统计数据')
            report_stats = {
                'total_reports': 0,
                'pending_review': 0,
                'serious_events': 0,
                'my_reports': 0,
                'draft_reports': 0,
                'submitted_reports': 0,
                'approved_reports': 0,
            }
        except Exception as e:
            # 处理其他可能的错误
            logger.error(f'获取报告统计数据失败: {str(e)}')
            report_stats = {
                'total_reports': 0,
                'pending_review': 0,
                'serious_events': 0,
                'my_reports': 0,
                'draft_reports': 0,
                'submitted_reports': 0,
                'approved_reports': 0,
            }

        context = {
            'stats': stats,
            'report_stats': report_stats,
        }

        return render(request, 'users/dashboard.html', context)

    except Exception as e:
        logger.error(f'获取用户中心数据失败: {str(e)}')
        messages.error(request, '获取数据失败，请稍后重试')
        return render(request, 'users/dashboard.html', {
            'stats': {},
            'report_stats': {}
        })


def logout_view(request):
    """
    登出视图
    """
    if request.user.is_authenticated:
        logger.info(f'用户登出: {request.user.username}')
    
    logout(request)
    messages.success(request, '已成功登出')
    return redirect('users:login')


@admin_required
def user_list_view(request):
    """
    用户列表视图
    """
    # 获取科室列表用于筛选
    departments = department_list(is_active=True)

    # 添加时间戳用于强制刷新缓存
    import time
    timestamp = str(int(time.time()))

    context = {
        'departments': departments,
        'role_choices': UserProfile.ROLE_CHOICES,
        'timestamp': timestamp,
    }

    return render(request, 'users/user_list.html', context)





@admin_required
def user_create_view(request):
    """
    用户创建视图
    """
    if request.method == 'POST':
        try:
            # 获取表单数据
            account_number = request.POST.get('account_number', '').strip()
            username = request.POST.get('username', '').strip()
            first_name = request.POST.get('first_name', '').strip()
            last_name = request.POST.get('last_name', '').strip()
            email = request.POST.get('email', '').strip()
            department_id = request.POST.get('department_id')
            role = request.POST.get('role', 'staff')

            is_active = request.POST.get('is_active') == 'on'
            
            # 转换department_id
            if department_id:
                department_id = int(department_id)
            else:
                department_id = None
            
            # 创建用户
            user_profile = user_create(
                account_number=account_number,
                username=username,
                first_name=first_name,
                last_name=last_name,
                email=email,
                department_id=department_id,
                role=role,

                is_active=is_active,
                created_by=request.user
            )
            
            messages.success(request, f'用户 {account_number} 创建成功')
            return redirect('users:user_list')
            
        except (DataValidationError, BusinessLogicError) as e:
            messages.error(request, str(e))
        except Exception as e:
            logger.error(f'创建用户失败: {str(e)}')
            messages.error(request, '创建用户失败，请重试')
    
    # 获取科室列表
    departments = department_list(is_active=True)
    
    context = {
        'departments': departments,
        'role_choices': UserProfile.ROLE_CHOICES,
    }
    
    return render(request, 'users/user_form.html', context)


@admin_required
def user_edit_view(request, user_id):
    """
    用户编辑视图
    """
    user_profile = user_get_by_id(user_id)
    if not user_profile:
        messages.error(request, '用户不存在')
        return redirect('users:user_list')
    
    if request.method == 'POST':
        try:
            # 获取表单数据
            username = request.POST.get('username', '').strip()
            first_name = request.POST.get('first_name', '').strip()
            last_name = request.POST.get('last_name', '').strip()
            email = request.POST.get('email', '').strip()
            department_id = request.POST.get('department_id')
            role = request.POST.get('role')

            is_active = request.POST.get('is_active') == 'on'
            
            # 转换department_id
            if department_id:
                department_id = int(department_id)
            else:
                department_id = None
            
            # 更新用户
            user_update(
                user_profile_id=user_profile.id,
                username=username,
                first_name=first_name,
                last_name=last_name,
                email=email,
                department_id=department_id,
                role=role,

                is_active=is_active,
                updated_by=request.user
            )
            
            messages.success(request, f'用户 {user_profile.account_number} 更新成功')
            return redirect('users:user_list')
            
        except (DataValidationError, BusinessLogicError, ResourceNotFoundError) as e:
            messages.error(request, str(e))
        except Exception as e:
            logger.error(f'更新用户失败: {str(e)}')
            messages.error(request, '更新用户失败，请重试')
    
    # 获取科室列表
    departments = department_list(is_active=True)
    
    context = {
        'user_profile': user_profile,
        'departments': departments,
        'role_choices': UserProfile.ROLE_CHOICES,
        'is_edit': True,
    }
    
    return render(request, 'users/user_form.html', context)


def get_client_ip(request):
    """
    获取客户端IP地址
    """
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


@login_required
@require_http_methods(["POST"])
def user_toggle_status(request, user_id):
    """
    切换用户状态（启用/禁用）
    """
    try:
        # 调试信息
        logger.info(f'用户状态切换请求: 用户={request.user.username}, 目标用户ID={user_id}')
        has_profile = hasattr(request.user, 'profile')
        is_admin = has_profile and request.user.profile.is_admin if has_profile else False
        logger.info(f'用户权限: has_profile={has_profile}, is_admin={is_admin}, has_perm={request.user.has_perm("users.change_userprofile")}')

        user_profile = user_get_by_id(user_id)
        if not user_profile:
            return JsonResponse({'error': '用户不存在'}, status=404)

        # 检查权限 - 使用管理员角色检查
        if not (hasattr(request.user, 'profile') and request.user.profile.is_admin):
            logger.warning(f'非管理员用户尝试切换用户状态: {request.user.username}')
            return JsonResponse({'error': '需要管理员权限'}, status=403)

        # 切换状态
        new_status = not user_profile.is_active
        user_update(
            user_profile_id=user_profile.id,
            is_active=new_status,
            updated_by=request.user
        )

        status_text = '启用' if new_status else '禁用'
        logger.info(f'用户状态切换成功: {user_profile.account_number} -> {status_text}')

        return JsonResponse({
            'success': True,
            'message': f'用户已{status_text}',
            'is_active': new_status
        })

    except Exception as e:
        logger.error(f'切换用户状态失败: {str(e)}')
        return JsonResponse({'error': '操作失败'}, status=500)


@login_required
def user_debug_permissions(request):
    """
    调试用户权限
    """
    debug_info = {
        'user_id': request.user.id,
        'username': request.user.username,
        'is_authenticated': request.user.is_authenticated,
        'has_profile': hasattr(request.user, 'profile'),
    }

    if hasattr(request.user, 'profile'):
        profile = request.user.profile
        debug_info.update({
            'account_number': profile.account_number,
            'role': profile.role,
            'is_admin': profile.is_admin,
            'is_active': profile.is_active,
            'department': profile.department.name if profile.department else None,
        })

    # 检查权限
    debug_info.update({
        'has_change_userprofile_perm': request.user.has_perm('users.change_userprofile'),
        'has_view_userprofile_perm': request.user.has_perm('users.view_userprofile'),
        'has_add_userprofile_perm': request.user.has_perm('users.add_userprofile'),
        'has_delete_userprofile_perm': request.user.has_perm('users.delete_userprofile'),
    })

    return JsonResponse(debug_info)


@login_required
@permission_required('users.view_userprofile', raise_exception=True)
def user_detail_view(request, pk):
    """
    用户详情视图
    """
    user_profile = user_get_by_id(pk)
    if not user_profile:
        messages.error(request, '用户不存在')
        return redirect('users:user_list')

    context = {
        'user_profile': user_profile,
    }

    return render(request, 'users/user_detail.html', context)


@admin_required
def department_list_view(request):
    """
    科室列表视图
    """
    # 获取查询参数
    search = request.GET.get('search', '').strip()
    is_active = request.GET.get('is_active')
    page = request.GET.get('page', 1)

    # 转换is_active参数
    if is_active == 'true':
        is_active = True
    elif is_active == 'false':
        is_active = False
    else:
        is_active = None

    try:
        # 获取科室列表（带用户统计）
        departments = department_list_with_user_counts(
            is_active=is_active,
            search=search,
            ordering='code'
        )

        # 计算总统计数据
        from django.db.models import Sum
        total_stats = departments.aggregate(
            total_users_sum=Sum('total_users'),
            active_users_sum=Sum('active_users'),
            admin_users_sum=Sum('admin_users'),
            staff_users_sum=Sum('staff_users')
        )

        # 分页处理
        paginator = Paginator(departments, 20)
        try:
            departments_page = paginator.page(page)
        except PageNotAnInteger:
            departments_page = paginator.page(1)
        except EmptyPage:
            departments_page = paginator.page(paginator.num_pages)

        context = {
            'departments': departments_page,
            'search': search,
            'is_active': is_active,
            'total_count': paginator.count,
            'total_users_sum': total_stats.get('total_users_sum', 0) or 0,
            'active_users_sum': total_stats.get('active_users_sum', 0) or 0,
            'admin_users_sum': total_stats.get('admin_users_sum', 0) or 0,
            'staff_users_sum': total_stats.get('staff_users_sum', 0) or 0,
        }

        return render(request, 'users/department_list.html', context)

    except Exception as e:
        logger.error(f'获取科室列表失败: {str(e)}')
        messages.error(request, '获取科室列表失败，请稍后重试')
        return render(request, 'users/department_list.html', {'departments': []})


@admin_required
def department_create_view(request):
    """
    科室创建视图
    """
    if request.method == 'POST':
        try:
            # 获取表单数据
            name = request.POST.get('name', '').strip()
            code = request.POST.get('code', '').strip()
            is_active = request.POST.get('is_active') == 'on'

            # 创建科室
            department = department_create(
                name=name,
                code=code,
                is_active=is_active,
                created_by=request.user
            )

            messages.success(request, f'科室 {department.code} - {department.name} 创建成功')
            return redirect('users:department_list')

        except (DataValidationError, BusinessLogicError) as e:
            messages.error(request, str(e))
        except Exception as e:
            logger.error(f'创建科室失败: {str(e)}')
            messages.error(request, '创建科室失败，请重试')

    return render(request, 'users/department_form.html', {'is_edit': False})


@admin_required
def department_edit_view(request, dept_id):
    """
    科室编辑视图
    """
    department = department_get_by_id(dept_id)
    if not department:
        messages.error(request, '科室不存在')
        return redirect('users:department_list')

    if request.method == 'POST':
        try:
            # 获取表单数据
            name = request.POST.get('name', '').strip()
            code = request.POST.get('code', '').strip()
            is_active = request.POST.get('is_active') == 'on'

            # 更新科室
            department_update(
                department_id=department.id,
                name=name,
                code=code,
                is_active=is_active,
                updated_by=request.user
            )

            messages.success(request, f'科室 {code} - {name} 更新成功')
            return redirect('users:department_list')

        except (DataValidationError, BusinessLogicError, ResourceNotFoundError) as e:
            messages.error(request, str(e))
        except Exception as e:
            logger.error(f'更新科室失败: {str(e)}')
            messages.error(request, '更新科室失败，请重试')

    context = {
        'department': department,
        'is_edit': True,
    }

    return render(request, 'users/department_form.html', context)


@admin_required
def department_detail_view(request, dept_id):
    """
    科室详情视图
    """
    department = department_get_by_id(dept_id)
    if not department:
        messages.error(request, '科室不存在')
        return redirect('users:department_list')

    # 获取科室下的用户列表
    users = user_list(department_id=dept_id, ordering='account_number')

    # 分页处理
    page = request.GET.get('page', 1)
    paginator = Paginator(users, 10)
    try:
        users_page = paginator.page(page)
    except PageNotAnInteger:
        users_page = paginator.page(1)
    except EmptyPage:
        users_page = paginator.page(paginator.num_pages)

    context = {
        'department': department,
        'users': users_page,
        'total_users': paginator.count,
    }

    return render(request, 'users/department_detail.html', context)


@admin_required
@require_http_methods(["POST"])
def department_delete_view(request, dept_id):
    """
    科室删除视图（AJAX）
    """
    try:
        department = department_get_by_id(dept_id)
        if not department:
            return JsonResponse({'error': '科室不存在'}, status=404)

        # 删除科室
        department_delete(
            department_id=department.id,
            deleted_by=request.user
        )

        return JsonResponse({
            'success': True,
            'message': f'科室 {department.code} - {department.name} 删除成功'
        })

    except BusinessLogicError as e:
        return JsonResponse({'error': str(e)}, status=400)
    except Exception as e:
        logger.error(f'删除科室失败: {str(e)}')
        return JsonResponse({'error': '删除科室失败'}, status=500)


@admin_required
@require_http_methods(["POST"])
def department_toggle_status(request, dept_id):
    """
    切换科室状态（启用/禁用）
    """
    try:
        department = department_get_by_id(dept_id)
        if not department:
            return JsonResponse({'error': '科室不存在'}, status=404)

        # 切换状态
        new_status = not department.is_active
        department_update(
            department_id=department.id,
            is_active=new_status,
            updated_by=request.user
        )

        status_text = '启用' if new_status else '禁用'
        return JsonResponse({
            'success': True,
            'message': f'科室已{status_text}',
            'is_active': new_status
        })

    except BusinessLogicError as e:
        return JsonResponse({'error': str(e)}, status=400)
    except Exception as e:
        logger.error(f'切换科室状态失败: {str(e)}')
        return JsonResponse({'error': '操作失败'}, status=500)


@admin_required
def department_export_excel(request):
    """
    导出科室Excel文件
    """
    try:
        # 获取查询参数
        search = request.GET.get('search', '').strip()
        is_active = request.GET.get('is_active')

        # 转换is_active参数
        if is_active == 'true':
            is_active = True
        elif is_active == 'false':
            is_active = False
        else:
            is_active = None

        # 获取科室数据
        departments = department_list_with_user_counts(
            is_active=is_active,
            search=search,
            ordering='code'
        )

        # 创建Excel工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "科室列表"

        # 设置标题样式
        title_font = Font(bold=True, size=12)
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        header_font = Font(bold=True, color="FFFFFF")
        center_alignment = Alignment(horizontal="center", vertical="center")

        # 设置表头
        headers = ['科室代码', '科室名称', '状态', '总用户数', '活跃用户数', '管理员数', '科室人员数', '创建时间', '更新时间']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = center_alignment

        # 填充数据
        for row, dept in enumerate(departments, 2):
            ws.cell(row=row, column=1, value=dept.code)
            ws.cell(row=row, column=2, value=dept.name)
            ws.cell(row=row, column=3, value='启用' if dept.is_active else '禁用')
            ws.cell(row=row, column=4, value=getattr(dept, 'total_users', 0))
            ws.cell(row=row, column=5, value=getattr(dept, 'active_users', 0))
            ws.cell(row=row, column=6, value=getattr(dept, 'admin_users', 0))
            ws.cell(row=row, column=7, value=getattr(dept, 'staff_users', 0))
            ws.cell(row=row, column=8, value=dept.created_at.strftime('%Y-%m-%d %H:%M:%S') if dept.created_at else '')
            ws.cell(row=row, column=9, value=dept.updated_at.strftime('%Y-%m-%d %H:%M:%S') if dept.updated_at else '')

        # 自动调整列宽
        for col in range(1, len(headers) + 1):
            column_letter = get_column_letter(col)
            ws.column_dimensions[column_letter].width = 15

        # 创建HTTP响应
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="科室列表_{timezone.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'

        # 保存到响应
        wb.save(response)
        return response

    except Exception as e:
        logger.error(f'导出科室Excel失败: {str(e)}')
        messages.error(request, '导出Excel失败，请重试')
        return redirect('users:department_list')


@admin_required
def department_toggle_status(request, dept_id):
    """
    切换科室状态
    """
    if request.method != 'POST':
        return JsonResponse({'error': '只支持POST请求'}, status=405)

    try:
        department = department_get_by_id(dept_id)
        if not department:
            return JsonResponse({'error': '科室不存在'}, status=404)

        # 切换状态
        new_status = not department.is_active

        # 使用服务层更新科室状态
        from .services import department_update
        department_update(
            department_id=dept_id,
            code=department.code,
            name=department.name,
            is_active=new_status,
            updated_by=request.user
        )

        # 记录日志
        action_text = '启用' if new_status else '禁用'
        logger.info(f'科室状态切换: {department.code} - {department.name} 已{action_text}')

        # 添加成功消息
        messages.success(request, f'科室 {department.code} - {department.name} 已{action_text}')

        # 重定向回科室列表页面
        return redirect('users:department_list')

    except Exception as e:
        logger.error(f'切换科室状态失败: {str(e)}')
        messages.error(request, f'操作失败：{str(e)}')
        return redirect('users:department_list')


@admin_required
def department_download_template(request):
    """
    下载科室导入模板
    """
    try:
        import openpyxl
        from django.http import HttpResponse

        # 创建工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "科室信息"

        # 设置表头
        headers = ['科室代码', '科室名称', '是否启用']
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)

        # 添加示例数据
        sample_data = [
            ['ICU', '重症监护科', '是'],
            ['ER', '急诊科', '是'],
            ['CARD', '心内科', '是'],
        ]

        for row, data in enumerate(sample_data, 2):
            for col, value in enumerate(data, 1):
                ws.cell(row=row, column=col, value=value)

        # 设置列宽
        ws.column_dimensions['A'].width = 15
        ws.column_dimensions['B'].width = 20
        ws.column_dimensions['C'].width = 10

        # 创建响应
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename="科室导入模板.xlsx"'

        # 保存到响应
        wb.save(response)
        return response

    except Exception as e:
        logger.error(f'下载模板失败: {str(e)}')
        messages.error(request, '下载模板失败，请重试')
        return redirect('users:department_import')


@admin_required
def department_import_excel(request):
    """
    导入科室Excel文件
    """
    if request.method == 'GET':
        # 显示导入页面
        return render(request, 'users/department_import.html')

    elif request.method == 'POST':
        try:
            excel_file = request.FILES.get('excel_file')
            if not excel_file:
                messages.error(request, '请选择要导入的Excel文件')
                return redirect('users:department_list')

            # 检查文件格式
            if not excel_file.name.endswith(('.xlsx', '.xls')):
                messages.error(request, '请上传Excel格式文件（.xlsx或.xls）')
                return redirect('users:department_list')

            # 读取Excel文件
            wb = openpyxl.load_workbook(excel_file)
            ws = wb.active

            success_count = 0
            error_count = 0
            errors = []

            # 跳过标题行，从第2行开始读取数据
            for row_num, row in enumerate(ws.iter_rows(min_row=2, values_only=True), 2):
                try:
                    # 检查行是否为空
                    if not any(row):
                        continue

                    code = str(row[0]).strip() if row[0] else ''
                    name = str(row[1]).strip() if row[1] else ''
                    is_active_str = str(row[2]).strip() if row[2] else '启用'

                    # 验证必填字段
                    if not code or not name:
                        errors.append(f'第{row_num}行：科室代码和名称不能为空')
                        error_count += 1
                        continue

                    # 转换状态
                    is_active = is_active_str in ['启用', '1', 'True', 'true', '是']

                    # 检查科室是否已存在
                    existing_dept = Department.objects.filter(code=code).first()
                    if existing_dept:
                        # 更新现有科室
                        department_update(
                            department_id=existing_dept.id,
                            name=name,
                            is_active=is_active,
                            updated_by=request.user
                        )
                    else:
                        # 创建新科室
                        department_create(
                            code=code,
                            name=name,
                            is_active=is_active,
                            created_by=request.user
                        )

                    success_count += 1

                except Exception as e:
                    errors.append(f'第{row_num}行：{str(e)}')
                    error_count += 1

            # 显示导入结果
            if success_count > 0:
                messages.success(request, f'成功导入 {success_count} 个科室')

            if error_count > 0:
                error_msg = f'导入失败 {error_count} 个科室'
                if errors:
                    error_msg += '：' + '；'.join(errors[:5])  # 只显示前5个错误
                    if len(errors) > 5:
                        error_msg += f'等{len(errors)}个错误'
                messages.error(request, error_msg)

            return redirect('users:department_list')

        except Exception as e:
            logger.error(f'导入科室Excel失败: {str(e)}')
            messages.error(request, f'导入失败：{str(e)}')
            return redirect('users:department_list')

    # GET请求显示导入页面
    return render(request, 'users/department_import.html')


# ==================== 个人设置相关视图 ====================

@department_member_or_admin_required
def profile_view(request):
    """
    个人信息查看页面
    """
    try:
        # 获取当前用户的配置文件
        user_profile = request.user.profile

        # 获取用户统计信息
        context = {
            'user_profile': user_profile,
            'user': request.user,
            'last_login': request.user.last_login,
            'date_joined': request.user.date_joined,
        }

        return render(request, 'users/profile.html', context)

    except Exception as e:
        logger.error(f'获取个人信息失败: {str(e)}')
        messages.error(request, '获取个人信息失败，请稍后重试')
        return redirect('users:dashboard')


@department_member_or_admin_required
def profile_edit_view(request):
    """
    个人信息编辑页面
    """
    user_profile = request.user.profile

    if request.method == 'POST':
        try:
            # 获取表单数据（只允许编辑特定字段）
            first_name = request.POST.get('first_name', '').strip()
            last_name = request.POST.get('last_name', '').strip()
            email = request.POST.get('email', '').strip()

            # 更新用户信息（只更新允许的字段）
            user_update(
                user_profile_id=user_profile.id,
                first_name=first_name,
                last_name=last_name,
                email=email,
                updated_by=request.user  # 用户更新自己的信息
            )

            messages.success(request, '个人信息更新成功')
            return redirect('users:profile')

        except (DataValidationError, BusinessLogicError) as e:
            messages.error(request, str(e))
        except Exception as e:
            logger.error(f'更新个人信息失败: {str(e)}')
            messages.error(request, '更新个人信息失败，请重试')

    context = {
        'user_profile': user_profile,
        'user': request.user,
        'is_edit': True,
    }

    return render(request, 'users/profile_edit.html', context)


@department_member_or_admin_required
def user_settings_view(request):
    """
    用户偏好设置页面
    """
    user_profile = request.user.profile

    if request.method == 'POST':
        try:
            # 处理AJAX设置保存请求
            if request.headers.get('Content-Type') == 'application/json':
                import json
                data = json.loads(request.body)

                # 这里可以扩展用户偏好设置
                # 目前预留接口，未来可以添加主题、语言等设置
                setting_type = data.get('type')
                setting_value = data.get('value')

                # 示例：保存用户偏好（可以扩展到UserProfile模型）
                if setting_type == 'theme':
                    # 未来可以添加主题设置
                    pass
                elif setting_type == 'language':
                    # 未来可以添加语言设置
                    pass

                return JsonResponse({
                    'success': True,
                    'message': '设置已保存'
                })

        except Exception as e:
            logger.error(f'保存用户设置失败: {str(e)}')
            if request.headers.get('Content-Type') == 'application/json':
                return JsonResponse({'error': '保存设置失败'}, status=500)
            else:
                messages.error(request, '保存设置失败，请重试')

    context = {
        'user_profile': user_profile,
        'user': request.user,
        # 预留设置选项
        'available_themes': ['light', 'dark', 'auto'],
        'available_languages': ['zh-cn', 'en'],
    }

    return render(request, 'users/user_settings.html', context)


@admin_required
def user_profile_view(request, user_id):
    """
    管理员查看用户信息页面
    """
    try:
        # 获取指定用户的配置文件
        target_user_profile = user_get_by_id(user_id)
        if not target_user_profile:
            messages.error(request, '用户不存在')
            return redirect('users:user_list')

        context = {
            'user_profile': target_user_profile,
            'target_user': target_user_profile.user,
            'is_admin_view': True,
            'last_login': target_user_profile.user.last_login,
            'date_joined': target_user_profile.user.date_joined,
        }

        return render(request, 'users/profile.html', context)

    except Exception as e:
        logger.error(f'获取用户信息失败: {str(e)}')
        messages.error(request, '获取用户信息失败，请稍后重试')
        return redirect('users:user_list')


@department_member_or_admin_required
@require_http_methods(["POST"])
def change_password_view(request):
    """
    修改密码（AJAX）
    """
    try:
        # 获取表单数据
        current_password = request.POST.get('current_password', '')
        new_password = request.POST.get('new_password', '')
        confirm_password = request.POST.get('confirm_password', '')

        # 验证当前密码
        if not request.user.check_password(current_password):
            return JsonResponse({'error': '当前密码不正确'}, status=400)

        # 验证新密码
        if new_password != confirm_password:
            return JsonResponse({'error': '两次输入的新密码不一致'}, status=400)

        if len(new_password) < 6:
            return JsonResponse({'error': '密码长度至少6位'}, status=400)

        # 更新密码
        request.user.set_password(new_password)
        request.user.save()

        # 记录日志
        logger.info(f'用户 {request.user.username} 修改了密码')

        return JsonResponse({
            'success': True,
            'message': '密码修改成功，请重新登录'
        })

    except Exception as e:
        logger.error(f'修改密码失败: {str(e)}')
        return JsonResponse({'error': '修改密码失败'}, status=500)
