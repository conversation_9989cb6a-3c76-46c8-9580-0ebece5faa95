#!/usr/bin/env python
"""
环境配置验证脚本
Environment Configuration Verification Script

验证医疗器械不良事件报告系统的环境配置是否正确。
"""

import os
import sys
import django
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from django.core.cache import cache
from django.db import connection
from django.conf import settings
from django.utils import timezone
from django.contrib.auth.models import User

def print_header(title):
    """打印标题"""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")

def print_check(name, status, message=""):
    """打印检查结果"""
    status_icon = "✅" if status else "❌"
    print(f"{status_icon} {name}")
    if message:
        print(f"   {message}")

def check_python_version():
    """检查Python版本"""
    print_header("Python环境检查")
    
    python_version = sys.version_info
    required_version = (3, 8)
    
    version_ok = python_version >= required_version
    print_check(
        f"Python版本 {python_version.major}.{python_version.minor}.{python_version.micro}",
        version_ok,
        f"要求: Python {required_version[0]}.{required_version[1]}+"
    )
    
    # 检查关键包
    packages = [
        ('django', 'Django'),
        ('mysql.connector', 'MySQL Connector'),
        ('redis', 'Redis'),
        ('django_redis', 'Django Redis'),
    ]
    
    for package, name in packages:
        try:
            __import__(package)
            print_check(f"{name} 包", True)
        except ImportError:
            print_check(f"{name} 包", False, f"请安装: pip install {package}")
    
    return version_ok

def check_django_settings():
    """检查Django设置"""
    print_header("Django配置检查")
    
    # 检查DEBUG设置
    debug_status = settings.DEBUG
    print_check(
        f"DEBUG模式: {debug_status}",
        True,
        "生产环境应设置为False" if debug_status else "生产环境配置正确"
    )
    
    # 检查SECRET_KEY
    secret_key_ok = len(settings.SECRET_KEY) >= 50
    print_check(
        "SECRET_KEY",
        secret_key_ok,
        "密钥长度应至少50个字符" if not secret_key_ok else "密钥配置正确"
    )
    
    # 检查ALLOWED_HOSTS
    allowed_hosts_ok = len(settings.ALLOWED_HOSTS) > 0 or settings.DEBUG
    print_check(
        f"ALLOWED_HOSTS: {settings.ALLOWED_HOSTS}",
        allowed_hosts_ok,
        "生产环境必须配置ALLOWED_HOSTS" if not allowed_hosts_ok else ""
    )
    
    # 检查时区设置
    timezone_ok = settings.USE_TZ
    print_check(
        f"时区支持: USE_TZ={settings.USE_TZ}",
        timezone_ok,
        f"当前时区: {settings.TIME_ZONE}"
    )
    
    return secret_key_ok and allowed_hosts_ok and timezone_ok

def check_database_connection():
    """检查数据库连接"""
    print_header("数据库连接检查")
    
    try:
        # 测试数据库连接
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
        
        print_check("数据库连接", True, f"连接成功: {result[0]}")
        
        # 检查数据库版本
        with connection.cursor() as cursor:
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()[0]
        
        print_check("数据库版本", True, f"MySQL版本: {version}")
        
        return True
        
    except Exception as e:
        print_check("数据库连接", False, f"连接失败: {e}")
        return False

def check_mysql_timezone():
    """检查MySQL时区配置"""
    print_header("MySQL时区配置检查")
    
    try:
        with connection.cursor() as cursor:
            # 检查时区表
            cursor.execute("SELECT COUNT(*) FROM mysql.time_zone")
            time_zone_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM mysql.time_zone_name")
            time_zone_name_count = cursor.fetchone()[0]
        
        timezone_ok = time_zone_count > 0 and time_zone_name_count > 0
        print_check(
            f"时区表数据",
            timezone_ok,
            f"time_zone: {time_zone_count}条, time_zone_name: {time_zone_name_count}条"
        )
        
        if not timezone_ok:
            print("   💡 解决方案:")
            print("   mysql_tzinfo_to_sql /usr/share/zoneinfo | mysql -u root -p mysql")
        
        # 测试Django时区函数
        try:
            from django.db.models.functions import TruncMonth
            from apps.reports.models import AdverseEventReport
            
            test_query = AdverseEventReport.objects.annotate(
                month=TruncMonth('created_at')
            )[:1]
            
            list(test_query)  # 执行查询
            print_check("Django时区函数", True, "TruncMonth函数正常工作")
            django_timezone_ok = True
            
        except Exception as e:
            print_check("Django时区函数", False, f"时区函数失败: {e}")
            django_timezone_ok = False
        
        return timezone_ok and django_timezone_ok
        
    except Exception as e:
        print_check("MySQL时区检查", False, f"检查失败: {e}")
        return False

def check_cache_connection():
    """检查缓存连接"""
    print_header("缓存连接检查")
    
    try:
        # 测试基本缓存操作
        test_key = 'env_verify_test'
        test_value = 'test_value_123'
        
        cache.set(test_key, test_value, 60)
        retrieved_value = cache.get(test_key)
        cache.delete(test_key)
        
        cache_ok = retrieved_value == test_value
        print_check("缓存基本操作", cache_ok)
        
        # 检查缓存后端
        cache_backend = str(cache.__class__)
        is_redis = 'redis' in cache_backend.lower()
        print_check(
            f"缓存后端",
            True,
            f"{cache_backend} ({'Redis' if is_redis else '其他'})"
        )
        
        # 测试缓存失效机制
        try:
            from apps.common.cache_utils import StatisticsCacheManager
            
            # 测试模式删除
            pattern = 'test_pattern:*'
            deleted_count = StatisticsCacheManager._delete_cache_pattern_with_count(pattern)
            print_check("缓存失效机制", True, f"模式删除功能正常")
            
        except Exception as e:
            print_check("缓存失效机制", False, f"失效机制错误: {e}")
        
        return cache_ok
        
    except Exception as e:
        print_check("缓存连接", False, f"连接失败: {e}")
        return False

def check_redis_specific():
    """检查Redis特定配置"""
    print_header("Redis配置检查")
    
    try:
        # 检查是否使用Redis
        cache_backend = str(cache.__class__)
        if 'redis' not in cache_backend.lower():
            print_check("Redis后端", False, "当前未使用Redis缓存")
            return False
        
        # 获取Redis客户端
        if hasattr(cache, '_cache') and hasattr(cache._cache, 'get_client'):
            client = cache._cache.get_client()
            
            # 检查Redis信息
            info = client.info()
            
            print_check("Redis连接", True, f"版本: {info.get('redis_version')}")
            print_check("Redis内存", True, f"使用: {info.get('used_memory_human')}")
            print_check("连接数", True, f"客户端: {info.get('connected_clients')}")
            
            # 检查命中率
            hits = info.get('keyspace_hits', 0)
            misses = info.get('keyspace_misses', 0)
            total = hits + misses
            
            if total > 0:
                hit_rate = (hits / total) * 100
                print_check("缓存命中率", True, f"{hit_rate:.1f}%")
            else:
                print_check("缓存命中率", True, "暂无统计数据")
            
            # 测试SCAN命令
            try:
                cursor, keys = client.scan(cursor=0, match='test:*', count=10)
                print_check("SCAN命令支持", True, "Redis SCAN命令可用")
            except Exception:
                print_check("SCAN命令支持", False, "Redis SCAN命令不可用")
            
            return True
        else:
            print_check("Redis客户端", False, "无法获取Redis客户端")
            return False
            
    except Exception as e:
        print_check("Redis配置检查", False, f"检查失败: {e}")
        return False

def check_file_permissions():
    """检查文件权限"""
    print_header("文件权限检查")
    
    # 检查项目目录权限
    project_dir = settings.BASE_DIR
    readable = os.access(project_dir, os.R_OK)
    writable = os.access(project_dir, os.W_OK)
    
    print_check("项目目录读权限", readable)
    print_check("项目目录写权限", writable)
    
    # 检查静态文件目录
    static_root = getattr(settings, 'STATIC_ROOT', None)
    if static_root:
        static_exists = os.path.exists(static_root)
        print_check(f"静态文件目录", static_exists, f"路径: {static_root}")
    
    # 检查媒体文件目录
    media_root = getattr(settings, 'MEDIA_ROOT', None)
    if media_root:
        media_exists = os.path.exists(media_root)
        media_writable = os.access(media_root, os.W_OK) if media_exists else False
        print_check(f"媒体文件目录", media_exists and media_writable, f"路径: {media_root}")
    
    # 检查日志目录
    log_dir = '/var/log/medical_device_reports'
    if os.path.exists(log_dir):
        log_writable = os.access(log_dir, os.W_OK)
        print_check("日志目录", log_writable, f"路径: {log_dir}")
    else:
        print_check("日志目录", False, f"目录不存在: {log_dir}")
    
    return readable and writable

def check_application_functionality():
    """检查应用程序功能"""
    print_header("应用程序功能检查")
    
    try:
        # 检查用户模型
        user_count = User.objects.count()
        print_check("用户模型", True, f"用户数量: {user_count}")
        
        # 检查报告模型
        from apps.reports.models import AdverseEventReport
        report_count = AdverseEventReport.objects.count()
        print_check("报告模型", True, f"报告数量: {report_count}")
        
        # 检查统计功能
        from apps.reports.selectors import report_statistics
        
        if user_count > 0:
            from apps.users.models import UserProfile
            user_profile = UserProfile.objects.first()
            
            if user_profile:
                stats = report_statistics(user_profile=user_profile)
                print_check("统计功能", True, f"统计数据获取成功")
            else:
                print_check("统计功能", False, "未找到用户配置文件")
        else:
            print_check("统计功能", False, "无用户数据，无法测试统计功能")
        
        return True
        
    except Exception as e:
        print_check("应用程序功能", False, f"功能检查失败: {e}")
        return False

def generate_report(results):
    """生成检查报告"""
    print_header("环境验证报告")
    
    total_checks = len(results)
    passed_checks = sum(results.values())
    
    print(f"总检查项: {total_checks}")
    print(f"通过检查: {passed_checks}")
    print(f"失败检查: {total_checks - passed_checks}")
    print(f"通过率: {(passed_checks / total_checks * 100):.1f}%")
    
    if passed_checks == total_checks:
        print("\n🎉 所有检查通过！环境配置正确。")
        return True
    elif passed_checks >= total_checks * 0.8:
        print("\n✅ 大部分检查通过，环境基本正常。")
        print("请检查失败的项目并进行修复。")
        return True
    else:
        print("\n⚠️ 多个检查失败，环境配置需要修复。")
        print("请参考故障排除指南进行修复。")
        return False

def main():
    """主验证函数"""
    print("🚀 开始环境配置验证...")
    print(f"验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行各项检查
    results = {
        'Python环境': check_python_version(),
        'Django配置': check_django_settings(),
        '数据库连接': check_database_connection(),
        'MySQL时区': check_mysql_timezone(),
        '缓存连接': check_cache_connection(),
        'Redis配置': check_redis_specific(),
        '文件权限': check_file_permissions(),
        '应用功能': check_application_functionality(),
    }
    
    # 生成报告
    success = generate_report(results)
    
    print("\n📋 相关文档:")
    print("- MySQL时区配置: docs/DATABASE_TIMEZONE_SETUP.md")
    print("- Redis缓存配置: docs/REDIS_CACHE_CONFIGURATION.md")
    print("- 故障排除指南: docs/TROUBLESHOOTING_GUIDE.md")
    
    print(f"\n🎯 环境验证完成！")
    
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())
