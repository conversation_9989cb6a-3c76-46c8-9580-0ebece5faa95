{% extends 'reports/base.html' %}
{% load static %}

{% block page_title %}报告列表{% endblock %}
{% block page_heading %}报告列表{% endblock %}
{% block page_description %}查看和管理不良事件报告{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item active">报告列表</li>
{% endblock %}

{% block page_actions %}
<div class="btn-group" role="group">
    <a href="{% url 'reports:step_create' %}" class="btn btn-primary">
        <i class="bi bi-plus-circle me-2"></i>
        新建报告
    </a>
    <a href="{% url 'reports:report_create' %}" class="btn btn-outline-primary">
        <i class="bi bi-file-text me-2"></i>
        完整表单
    </a>
</div>
{% endblock %}

{% block reports_extra_css %}
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap5.min.css">
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/select/1.7.0/css/select.bootstrap5.min.css">
{% endblock %}

{% block reports_content %}
<!-- 筛选面板 -->
<div class="filter-panel card mb-4">
    <div class="card-header">
        <h6 class="card-title mb-0">
            <i class="bi bi-funnel me-2"></i>
            筛选条件
        </h6>
    </div>
    <div class="card-body">
        <form id="filterForm" class="row g-3">
            <div class="col-md-3">
                <label for="statusFilter" class="form-label">状态</label>
                <select class="form-select" id="statusFilter" name="status">
                    <option value="">全部状态</option>
                    <option value="draft">草稿</option>
                    <option value="submitted">已提交</option>
                    <option value="under_review">审核中</option>
                    <option value="approved">已批准</option>
                    <option value="rejected">已拒绝</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="injuryLevelFilter" class="form-label">伤害程度</label>
                <select class="form-select" id="injuryLevelFilter" name="injury_level">
                    <option value="">全部程度</option>
                    <option value="death">死亡</option>
                    <option value="serious_injury">严重伤害</option>
                    <option value="other">其他</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="dateRangeFilter" class="form-label">日期范围</label>
                <select class="form-select" id="dateRangeFilter" name="date_range">
                    <option value="">全部时间</option>
                    <option value="today">今天</option>
                    <option value="week">本周</option>
                    <option value="month">本月</option>
                    <option value="quarter">本季度</option>
                    <option value="year">本年</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="searchInput" class="form-label">搜索</label>
                <div class="input-group">
                    <input type="text" class="form-control" id="searchInput" name="search" placeholder="报告编号、器械名称">
                    <button class="btn btn-outline-secondary" type="button" id="clearSearch">
                        <i class="bi bi-x"></i>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 报告列表 -->
<div class="report-list-panel card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h6 class="card-title mb-0">
                    <i class="bi bi-list me-2"></i>
                    报告列表
                    <span class="badge bg-secondary ms-2" id="totalCount">{{ total_count|default:0 }}</span>
                </h6>
            </div>
            <div class="col-auto">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-primary btn-sm" id="refreshBtn">
                        <i class="bi bi-arrow-clockwise me-1"></i>
                        刷新
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-sm" id="exportBtn">
                        <i class="bi bi-download me-1"></i>
                        导出
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        <!-- 数据表格 -->
        <div class="table-responsive">
            <table id="reportTable" class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th width="40">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="selectAll">
                            </div>
                        </th>
                        <th>报告编号</th>
                        <th>器械名称</th>
                        <th>上报人</th>
                        <th>事件日期</th>
                        <th>伤害程度</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th width="120">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for report in reports %}
                    <tr>
                        <td>
                            <div class="form-check">
                                <input class="form-check-input row-checkbox" type="checkbox" value="{{ report.id }}">
                            </div>
                        </td>
                        <td>
                            <a href="{% url 'reports:report_detail' report_id=report.id %}" class="text-decoration-none">
                                {{ report.report_number }}
                            </a>
                        </td>
                        <td>{{ report.device_name|truncatechars:30 }}</td>
                        <td>{{ report.reporter.display_name }}</td>
                        <td>{{ report.event_date|date:"Y-m-d" }}</td>
                        <td>
                            <span class="badge bg-{% if report.injury_level == 'death' %}danger{% elif report.injury_level == 'serious_injury' %}warning{% else %}secondary{% endif %}">
                                {{ report.get_injury_level_display }}
                            </span>
                        </td>
                        <td>
                            <span class="badge bg-{% if report.status == 'approved' %}success{% elif report.status == 'rejected' %}danger{% elif report.status == 'under_review' %}warning{% elif report.status == 'submitted' %}info{% else %}secondary{% endif %}">
                                {{ report.get_status_display }}
                            </span>
                        </td>
                        <td>{{ report.created_at|date:"Y-m-d H:i" }}</td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{% url 'reports:report_detail' report_id=report.id %}" class="btn btn-outline-primary btn-sm" title="查看详情">
                                    <i class="bi bi-eye"></i>
                                </a>
                                {% if report.can_edit and user.profile.is_admin or report.reporter == user.profile %}
                                <a href="{% url 'reports:report_edit' report_id=report.id %}" class="btn btn-outline-secondary btn-sm" title="编辑">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                {% endif %}
                                {% if report.can_submit and user.profile.is_admin or report.reporter == user.profile %}
                                <button type="button" class="btn btn-outline-success btn-sm submit-btn" data-report-id="{{ report.id }}" title="提交">
                                    <i class="bi bi-send"></i>
                                </button>
                                {% endif %}
                                {% if report.can_review and user.profile.is_admin %}
                                <a href="{% url 'reports:report_review' report_id=report.id %}" class="btn btn-outline-warning btn-sm" title="审核">
                                    <i class="bi bi-eye-fill"></i>
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="9" class="text-center py-4">
                            <i class="bi bi-inbox display-4 text-muted"></i>
                            <p class="text-muted mt-2">暂无报告数据</p>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- 分页 -->
        {% if reports.has_other_pages %}
        <nav aria-label="报告列表分页">
            <ul class="pagination justify-content-center">
                {% if reports.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">首页</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ reports.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">上一页</a>
                </li>
                {% endif %}

                {% for num in reports.paginator.page_range %}
                {% if reports.number == num %}
                <li class="page-item active">
                    <span class="page-link">{{ num }}</span>
                </li>
                {% elif num > reports.number|add:'-3' and num < reports.number|add:'3' %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ num }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">{{ num }}</a>
                </li>
                {% endif %}
                {% endfor %}

                {% if reports.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ reports.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">下一页</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ reports.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">末页</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    </div>
</div>

<!-- 提交确认模态框 -->
<div class="modal fade" id="submitModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-send me-2"></i>
                    确认提交
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要提交此报告吗？提交后将无法修改。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmSubmitBtn">确认提交</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

<!-- 隐藏的CSRF token表单 -->
<form style="display: none;">
    {% csrf_token %}
</form>

{% block reports_extra_js %}
<!-- DataTables JavaScript -->
<script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap5.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/select/1.7.0/js/dataTables.select.min.js"></script>

<script>
$(document).ready(function() {
    // 初始化DataTable
    var table = $('#reportTable').DataTable({
        "language": {
            "sProcessing": "处理中...",
            "sLengthMenu": "显示 _MENU_ 项结果",
            "sZeroRecords": "没有匹配结果",
            "sInfo": "显示第 _START_ 至 _END_ 项结果，共 _TOTAL_ 项",
            "sInfoEmpty": "显示第 0 至 0 项结果，共 0 项",
            "sInfoFiltered": "(由 _MAX_ 项结果过滤)",
            "sInfoPostFix": "",
            "sSearch": "搜索:",
            "sUrl": "",
            "sEmptyTable": "表中数据为空",
            "sLoadingRecords": "载入中...",
            "sInfoThousands": ",",
            "oPaginate": {
                "sFirst": "首页",
                "sPrevious": "上页",
                "sNext": "下页",
                "sLast": "末页"
            },
            "oAria": {
                "sSortAscending": ": 以升序排列此列",
                "sSortDescending": ": 以降序排列此列"
            }
        },
        "pageLength": 20,
        "order": [[7, "desc"]], // 按创建时间降序
        "columnDefs": [
            { "orderable": false, "targets": [0, 8] } // 复选框和操作列不可排序
        ]
    });

    // 全选/取消全选
    $('#selectAll').on('change', function() {
        $('.row-checkbox').prop('checked', this.checked);
    });

    // 提交报告
    let currentReportId = null;
    $('.submit-btn').on('click', function() {
        currentReportId = $(this).data('report-id');
        $('#submitModal').modal('show');
    });

    // 获取CSRF token的函数
    function getCSRFToken() {
        // 方法1: 从隐藏表单获取
        let token = $('[name=csrfmiddlewaretoken]').val();
        if (token) return token;

        // 方法2: 从cookie获取
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, 10) === 'csrftoken=') {
                    cookieValue = decodeURIComponent(cookie.substring(10));
                    break;
                }
            }
        }
        return cookieValue;
    }

    $('#confirmSubmitBtn').on('click', function() {
        if (currentReportId) {
            const csrfToken = getCSRFToken();
            if (!csrfToken) {
                alert('安全验证失败，请刷新页面重试');
                return;
            }

            $.ajax({
                url: `/reports/${currentReportId}/submit/`,
                type: 'POST',
                headers: {
                    'X-CSRFToken': csrfToken
                },
                success: function(response) {
                    $('#submitModal').modal('hide');
                    location.reload();
                },
                error: function(xhr) {
                    let errorMsg = '未知错误';
                    if (xhr.responseJSON && xhr.responseJSON.error) {
                        errorMsg = xhr.responseJSON.error;
                    } else if (xhr.status === 403) {
                        errorMsg = '权限不足或安全验证失败';
                    } else if (xhr.status === 404) {
                        errorMsg = '报告不存在';
                    } else if (xhr.status === 500) {
                        errorMsg = '服务器内部错误';
                    }
                    alert('提交失败：' + errorMsg);
                }
            });
        }
    });

    // 刷新按钮
    $('#refreshBtn').on('click', function() {
        location.reload();
    });

    // 导出按钮
    $('#exportBtn').on('click', function() {
        // TODO: 实现导出功能
        alert('导出功能开发中...');
    });
});
</script>
{% endblock %}
