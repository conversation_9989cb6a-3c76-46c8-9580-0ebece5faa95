# 贡献指南

感谢您对医疗器械不良事件上报平台的关注！我们欢迎所有形式的贡献，包括但不限于：

- 🐛 报告Bug
- 💡 提出新功能建议
- 📝 改进文档
- 🔧 提交代码修复
- ✨ 开发新功能
- 🧪 编写测试用例

## 🚀 快速开始

### 1. 环境准备

确保您的开发环境满足以下要求：
- Python 3.11+
- MySQL 8.0+
- Git

### 2. Fork 和克隆项目

```bash
# Fork 项目到您的GitHub账户
# 然后克隆您的Fork
git clone https://github.com/YOUR_USERNAME/medical_device_reporting.git
cd medical_device_reporting

# 添加上游仓库
git remote add upstream https://github.com/ORIGINAL_OWNER/medical_device_reporting.git
```

### 3. 设置开发环境

```bash
# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements/development.txt

# 配置数据库
python create_database.py
python manage.py migrate --settings=config.settings.development

# 创建测试用户
python create_superuser.py
```

### 4. 运行测试

```bash
# 运行所有测试
python manage.py test --settings=config.settings.development

# 运行特定测试
python test_user_management.py
```

## 📋 贡献流程

### 1. 创建Issue

在开始编码之前，请先创建一个Issue来描述：
- 🐛 **Bug报告**: 详细描述问题、重现步骤、期望行为
- 💡 **功能请求**: 说明功能需求、使用场景、实现建议
- 📝 **文档改进**: 指出文档中的问题或需要补充的内容

### 2. 创建分支

```bash
# 同步最新代码
git checkout main
git pull upstream main

# 创建功能分支
git checkout -b feature/your-feature-name
# 或
git checkout -b bugfix/issue-number
```

### 3. 开发和测试

- 遵循项目的编码规范
- 编写必要的测试用例
- 确保所有测试通过
- 更新相关文档

### 4. 提交代码

```bash
# 添加文件
git add .

# 提交（使用有意义的提交信息）
git commit -m "feat: 添加用户批量导入功能"

# 推送到您的Fork
git push origin feature/your-feature-name
```

### 5. 创建Pull Request

- 在GitHub上创建Pull Request
- 填写详细的PR描述
- 关联相关的Issue
- 等待代码审查

## 📝 编码规范

### Python代码规范

遵循PEP 8标准，并注意以下要点：

```python
# 1. 导入顺序
import os
import sys

from django.db import models
from django.contrib.auth.models import User

from apps.common.utils import some_function

# 2. 类和函数命名
class UserProfile(models.Model):
    """用户配置文件模型"""
    pass

def create_user_profile(user_data):
    """创建用户配置文件"""
    pass

# 3. 常量定义
ROLE_CHOICES = [
    ('admin', '管理员'),
    ('staff', '科室人员'),
]

# 4. 文档字符串
def user_create(account_number, username, role, **kwargs):
    """
    创建新用户
    
    Args:
        account_number (str): 4位数账号
        username (str): 用户名
        role (str): 用户角色
        **kwargs: 其他用户信息
        
    Returns:
        UserProfile: 创建的用户配置文件
        
    Raises:
        DataValidationError: 数据验证失败
        BusinessLogicError: 业务逻辑错误
    """
    pass
```

### Django最佳实践

```python
# 1. 模型设计
class UserProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    account_number = models.CharField(max_length=4, unique=True)
    
    class Meta:
        db_table = 'user_profiles'
        verbose_name = '用户配置文件'
        verbose_name_plural = '用户配置文件'

# 2. 视图函数
@admin_required
def user_create_view(request):
    """用户创建视图"""
    if request.method == 'POST':
        # 处理POST请求
        pass
    return render(request, 'users/create.html')

# 3. API视图
class UserCreateApi(APIView):
    permission_classes = [IsAdminUser]
    
    def post(self, request):
        serializer = UserCreateSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        # 处理逻辑
        return Response(data, status=status.HTTP_201_CREATED)
```

### 前端代码规范

```javascript
// 1. 变量命名使用驼峰式
const userName = 'admin';
const userProfile = {};

// 2. 函数命名清晰明确
function createUser(userData) {
    // 实现逻辑
}

// 3. AJAX请求统一处理
function apiRequest(url, method, data) {
    return $.ajax({
        url: url,
        method: method,
        data: JSON.stringify(data),
        contentType: 'application/json',
        headers: {
            'X-CSRFToken': getCsrfToken()
        }
    });
}
```

## 🧪 测试规范

### 1. 测试文件命名

```
test_user_management.py      # 用户管理功能测试
test_permissions.py          # 权限控制测试
test_apis.py                # API接口测试
```

### 2. 测试用例编写

```python
import unittest
from django.test import TestCase, Client
from django.contrib.auth.models import User

class UserManagementTest(TestCase):
    def setUp(self):
        """测试前准备"""
        self.client = Client()
        self.admin_user = User.objects.create_user(
            username='admin',
            password='password'
        )
    
    def test_user_creation(self):
        """测试用户创建功能"""
        # 准备测试数据
        user_data = {
            'account_number': '1001',
            'username': 'test_user',
            'role': 'staff'
        }
        
        # 执行测试
        response = self.client.post('/api/users/create/', user_data)
        
        # 验证结果
        self.assertEqual(response.status_code, 201)
        self.assertIn('id', response.json())
    
    def tearDown(self):
        """测试后清理"""
        User.objects.all().delete()
```

### 3. 测试覆盖要求

- 新功能必须包含测试用例
- 测试覆盖率应达到80%以上
- 包含正常流程和异常流程测试
- API接口需要测试各种HTTP状态码

## 📚 文档规范

### 1. 代码注释

```python
# 单行注释：解释复杂逻辑
user_count = User.objects.filter(is_active=True).count()  # 获取活跃用户数量

"""
多行注释：
用于解释复杂的业务逻辑或算法
"""

def complex_business_logic():
    """
    复杂业务逻辑函数
    
    这里详细说明函数的作用、参数、返回值等
    """
    pass
```

### 2. API文档

```python
class UserCreateApi(APIView):
    """
    用户创建API
    
    POST /api/users/create/
    
    请求参数:
    - account_number (str): 4位数账号
    - username (str): 用户名
    - role (str): 用户角色 (admin/staff)
    - department_id (int, optional): 科室ID
    
    响应:
    - 201: 创建成功，返回用户信息
    - 400: 参数错误
    - 403: 权限不足
    """
```

### 3. README更新

当添加新功能时，请更新README.md中的相关部分：
- 功能列表
- 安装说明
- 使用示例
- API文档链接

## 🔍 代码审查

### 审查清单

- [ ] 代码符合项目编码规范
- [ ] 包含必要的测试用例
- [ ] 测试全部通过
- [ ] 文档已更新
- [ ] 没有引入安全漏洞
- [ ] 性能影响可接受
- [ ] 向后兼容性良好

### 审查流程

1. 自动化检查（CI/CD）
2. 代码审查员人工审查
3. 讨论和修改
4. 最终批准和合并

## 🐛 Bug报告模板

```markdown
## Bug描述
简要描述遇到的问题

## 重现步骤
1. 进入...
2. 点击...
3. 看到错误...

## 期望行为
描述您期望发生的情况

## 实际行为
描述实际发生的情况

## 环境信息
- 操作系统: [例如 Windows 10]
- 浏览器: [例如 Chrome 91]
- Python版本: [例如 3.11.0]
- Django版本: [例如 5.1.0]

## 附加信息
添加任何其他有助于解决问题的信息
```

## 💡 功能请求模板

```markdown
## 功能描述
清楚简洁地描述您想要的功能

## 问题背景
描述这个功能要解决的问题

## 解决方案
描述您希望的解决方案

## 替代方案
描述您考虑过的其他解决方案

## 附加信息
添加任何其他相关信息或截图
```

## 📞 联系方式

如果您有任何问题或需要帮助，请通过以下方式联系我们：

- 📧 邮箱: [项目邮箱]
- 💬 讨论区: [GitHub Discussions链接]
- 🐛 问题跟踪: [GitHub Issues链接]

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者！您的贡献让这个项目变得更好。

---

再次感谢您的贡献！🎉
