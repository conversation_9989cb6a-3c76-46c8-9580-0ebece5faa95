"""
不良事件上报管理服务层
Adverse Event Reports Management Services for Medical Device Reporting Platform
"""

import logging
from datetime import datetime, date
from typing import Optional, Dict, Any

from django.db import transaction
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.utils import timezone

from apps.common.exceptions import (
    BusinessLogicError,
    DataValidationError,
    ResourceNotFoundError,
    AdverseEventError,
    DeviceValidationError,
    ReportSubmissionError,
    PatientDataError,
    ReporterAuthorizationError
)
from apps.common.utils import ValidationUtils
from apps.users.models import UserProfile, Department
from .models import AdverseEventReport

logger = logging.getLogger('apps.reports')


@transaction.atomic
def report_create(
    *,
    reporter_id: int,
    department_id: Optional[int] = None,
    reporter_phone: str,
    patient_name: str,
    patient_age: int,
    patient_gender: str,
    patient_contact: str = '',
    device_malfunction: str,
    event_date: datetime,
    injury_level: str,
    injury_description: str = '',
    event_description: str,
    initial_cause_analysis: str = '',
    initial_treatment: str = '',
    device_name: str,
    registration_number: str,
    manufacturer: str,
    specification: str = '',
    model: str = '',
    product_number: str = '',
    batch_number: str = '',
    production_date: Optional[date] = None,
    expiry_date: Optional[date] = None,
    created_by: Optional[User] = None
) -> AdverseEventReport:
    """
    创建不良事件报告

    Args:
        reporter_id: 上报人ID
        department_id: 科室ID（可选，默认使用上报人所属科室）
        reporter_phone: 上报人联系电话
        patient_name: 患者姓名
        patient_age: 患者年龄
        patient_gender: 患者性别
        patient_contact: 患者联系方式
        device_malfunction: 器械故障表现
        event_date: 事件发生日期
        injury_level: 伤害程度
        injury_description: 伤害表现
        event_description: 事件陈述
        initial_cause_analysis: 初步原因分析
        initial_treatment: 初步处理情况
        device_name: 医疗器械名称
        registration_number: 注册证号
        manufacturer: 生产企业名称
        specification: 规格
        model: 型号
        product_number: 产品编号
        batch_number: 产品批号
        production_date: 生产日期
        expiry_date: 有效期至
        created_by: 创建者

    Returns:
        AdverseEventReport: 创建的报告

    Raises:
        ResourceNotFoundError: 上报人或科室不存在
        DataValidationError: 数据验证失败
        ReporterAuthorizationError: 上报人权限不足
        PatientDataError: 患者数据错误
        DeviceValidationError: 器械信息验证失败
        AdverseEventError: 业务逻辑错误
    """

    # 验证上报人
    try:
        reporter = UserProfile.objects.select_related('user', 'department').get(
            id=reporter_id,
            is_active=True,
            is_deleted=False
        )
    except UserProfile.DoesNotExist:
        raise ResourceNotFoundError(f'上报人 ID {reporter_id} 不存在或已禁用')

    # 验证上报人权限
    if not _can_submit_report(reporter):
        raise ReporterAuthorizationError(f'用户 {reporter.account_number} 没有提交报告的权限')

    # 确定科室
    if department_id:
        try:
            department = Department.objects.get(id=department_id, is_active=True)
        except Department.DoesNotExist:
            raise ResourceNotFoundError(f'科室 ID {department_id} 不存在或已禁用')

        # 验证上报人和科室的关系
        if reporter.role == 'staff' and reporter.department != department:
            raise ReporterAuthorizationError('科室人员只能为本科室提交报告')
    else:
        department = reporter.department
        if not department:
            raise DataValidationError('上报人未分配科室，请指定科室')

    # 验证数据
    _validate_report_data(
        reporter_phone=reporter_phone,
        patient_name=patient_name,
        patient_age=patient_age,
        patient_gender=patient_gender,
        device_malfunction=device_malfunction,
        event_date=event_date,
        injury_level=injury_level,
        injury_description=injury_description,
        event_description=event_description,
        device_name=device_name,
        registration_number=registration_number,
        manufacturer=manufacturer,
        production_date=production_date,
        expiry_date=expiry_date
    )

    try:
        # 创建报告
        report = AdverseEventReport(
            reporter=reporter,
            department=department,
            reporter_phone=reporter_phone,
            patient_name=patient_name,
            patient_age=patient_age,
            patient_gender=patient_gender,
            patient_contact=patient_contact,
            device_malfunction=device_malfunction,
            event_date=event_date,
            injury_level=injury_level,
            injury_description=injury_description,
            event_description=event_description,
            initial_cause_analysis=initial_cause_analysis,
            initial_treatment=initial_treatment,
            device_name=device_name,
            registration_number=registration_number,
            manufacturer=manufacturer,
            specification=specification,
            model=model,
            product_number=product_number,
            batch_number=batch_number,
            production_date=production_date,
            expiry_date=expiry_date,
            status='draft'
        )

        # 保存报告（会自动生成报告编号）
        report.save(user=created_by)

        logger.info(
            f'不良事件报告创建成功: {report.report_number}',
            extra={
                'report_id': report.id,
                'report_number': report.report_number,
                'reporter_id': reporter_id,
                'department_id': department.id,
                'device_name': device_name,
                'injury_level': injury_level,
                'created_by': created_by.username if created_by else None
            }
        )

        return report

    except ValidationError as e:
        logger.error(f'报告创建验证失败: {str(e)}')
        raise DataValidationError(f'报告数据验证失败: {str(e)}')
    except Exception as e:
        logger.error(f'报告创建失败: {str(e)}')
        raise AdverseEventError(f'报告创建失败: {str(e)}')


@transaction.atomic
def report_update(
    *,
    report_id: int,
    reporter_phone: Optional[str] = None,
    patient_name: Optional[str] = None,
    patient_age: Optional[int] = None,
    patient_gender: Optional[str] = None,
    patient_contact: Optional[str] = None,
    device_malfunction: Optional[str] = None,
    event_date: Optional[datetime] = None,
    injury_level: Optional[str] = None,
    injury_description: Optional[str] = None,
    event_description: Optional[str] = None,
    initial_cause_analysis: Optional[str] = None,
    initial_treatment: Optional[str] = None,
    device_name: Optional[str] = None,
    registration_number: Optional[str] = None,
    manufacturer: Optional[str] = None,
    specification: Optional[str] = None,
    model: Optional[str] = None,
    product_number: Optional[str] = None,
    batch_number: Optional[str] = None,
    production_date: Optional[date] = None,
    expiry_date: Optional[date] = None,
    updated_by: Optional[User] = None
) -> AdverseEventReport:
    """
    更新不良事件报告

    Args:
        report_id: 报告ID
        其他参数: 可选的更新字段
        updated_by: 更新者

    Returns:
        AdverseEventReport: 更新后的报告

    Raises:
        ResourceNotFoundError: 报告不存在
        DataValidationError: 数据验证失败
        AdverseEventError: 业务逻辑错误
    """

    try:
        report = AdverseEventReport.objects.select_related(
            'reporter', 'department'
        ).get(id=report_id)
    except AdverseEventReport.DoesNotExist:
        raise ResourceNotFoundError(f'报告 ID {report_id} 不存在')

    # 检查报告是否可以编辑
    if not report.can_edit:
        raise AdverseEventError(f'报告 {report.report_number} 当前状态不允许编辑')

    # 更新字段
    update_fields = {}

    if reporter_phone is not None:
        update_fields['reporter_phone'] = reporter_phone
    if patient_name is not None:
        update_fields['patient_name'] = patient_name
    if patient_age is not None:
        update_fields['patient_age'] = patient_age
    if patient_gender is not None:
        update_fields['patient_gender'] = patient_gender
    if patient_contact is not None:
        update_fields['patient_contact'] = patient_contact
    if device_malfunction is not None:
        update_fields['device_malfunction'] = device_malfunction
    if event_date is not None:
        update_fields['event_date'] = event_date
    if injury_level is not None:
        update_fields['injury_level'] = injury_level
    if injury_description is not None:
        update_fields['injury_description'] = injury_description
    if event_description is not None:
        update_fields['event_description'] = event_description
    if initial_cause_analysis is not None:
        update_fields['initial_cause_analysis'] = initial_cause_analysis
    if initial_treatment is not None:
        update_fields['initial_treatment'] = initial_treatment
    if device_name is not None:
        update_fields['device_name'] = device_name
    if registration_number is not None:
        update_fields['registration_number'] = registration_number
    if manufacturer is not None:
        update_fields['manufacturer'] = manufacturer
    if specification is not None:
        update_fields['specification'] = specification
    if model is not None:
        update_fields['model'] = model
    if product_number is not None:
        update_fields['product_number'] = product_number
    if batch_number is not None:
        update_fields['batch_number'] = batch_number
    if production_date is not None:
        update_fields['production_date'] = production_date
    if expiry_date is not None:
        update_fields['expiry_date'] = expiry_date

    # 验证更新的数据
    if update_fields:
        _validate_report_data(**update_fields)

        # 应用更新
        for field, value in update_fields.items():
            setattr(report, field, value)

    try:
        # 保存更新
        report.save(user=updated_by)

        logger.info(
            f'不良事件报告更新成功: {report.report_number}',
            extra={
                'report_id': report_id,
                'report_number': report.report_number,
                'updated_fields': list(update_fields.keys()),
                'updated_by': updated_by.username if updated_by else None
            }
        )

        return report

    except ValidationError as e:
        logger.error(f'报告更新验证失败: {str(e)}')
        raise DataValidationError(f'报告数据验证失败: {str(e)}')
    except Exception as e:
        logger.error(f'报告更新失败: {str(e)}')
        raise AdverseEventError(f'报告更新失败: {str(e)}')


@transaction.atomic
def report_submit(
    *,
    report_id: int,
    submitted_by: Optional[User] = None
) -> AdverseEventReport:
    """
    提交不良事件报告

    Args:
        report_id: 报告ID
        submitted_by: 提交者

    Returns:
        AdverseEventReport: 提交后的报告

    Raises:
        ResourceNotFoundError: 报告不存在
        ReportSubmissionError: 提交失败
        AdverseEventError: 业务逻辑错误
    """

    try:
        report = AdverseEventReport.objects.select_related(
            'reporter', 'department'
        ).get(id=report_id)
    except AdverseEventReport.DoesNotExist:
        raise ResourceNotFoundError(f'报告 ID {report_id} 不存在')

    # 检查报告是否可以提交
    if not report.can_submit:
        raise ReportSubmissionError(f'报告 {report.report_number} 当前状态不允许提交')

    # 验证报告完整性
    _validate_report_completeness(report)

    try:
        # 使用模型的submit方法
        report.submit(user=submitted_by)

        logger.info(
            f'不良事件报告提交成功: {report.report_number}',
            extra={
                'report_id': report_id,
                'report_number': report.report_number,
                'reporter_id': report.reporter.id,
                'department_id': report.department.id,
                'submitted_by': submitted_by.username if submitted_by else None
            }
        )

        return report

    except ValidationError as e:
        logger.error(f'报告提交验证失败: {str(e)}')
        raise ReportSubmissionError(f'报告提交验证失败: {str(e)}')
    except Exception as e:
        logger.error(f'报告提交失败: {str(e)}')
        raise ReportSubmissionError(f'报告提交失败: {str(e)}')


@transaction.atomic
def report_review(
    *,
    report_id: int,
    reviewer_id: int,
    action: str,
    comments: str = '',
    reviewed_by: Optional[User] = None
) -> AdverseEventReport:
    """
    审核不良事件报告

    Args:
        report_id: 报告ID
        reviewer_id: 审核人ID
        action: 审核动作 ('start_review', 'approve', 'reject')
        comments: 审核意见
        reviewed_by: 审核操作者

    Returns:
        AdverseEventReport: 审核后的报告

    Raises:
        ResourceNotFoundError: 报告或审核人不存在
        ReporterAuthorizationError: 审核人权限不足
        AdverseEventError: 业务逻辑错误
    """

    try:
        report = AdverseEventReport.objects.select_related(
            'reporter', 'department'
        ).get(id=report_id)
    except AdverseEventReport.DoesNotExist:
        raise ResourceNotFoundError(f'报告 ID {report_id} 不存在')

    try:
        reviewer = UserProfile.objects.select_related('user').get(
            id=reviewer_id,
            is_active=True,
            is_deleted=False
        )
    except UserProfile.DoesNotExist:
        raise ResourceNotFoundError(f'审核人 ID {reviewer_id} 不存在或已禁用')

    # 验证审核人权限
    if not _can_review_report(reviewer):
        raise ReporterAuthorizationError(f'用户 {reviewer.account_number} 没有审核报告的权限')

    # 检查报告是否可以审核
    if not report.can_review:
        raise AdverseEventError(f'报告 {report.report_number} 当前状态不允许审核')

    try:
        if action == 'start_review':
            # 开始审核
            report.start_review(reviewer, user=reviewed_by)
            action_text = '开始审核'

        elif action == 'approve':
            # 批准报告
            report.approve(reviewer, comments, user=reviewed_by)
            action_text = '批准'

        elif action == 'reject':
            # 拒绝报告
            if not comments:
                raise AdverseEventError('拒绝报告时必须填写审核意见')
            report.reject(reviewer, comments, user=reviewed_by)
            action_text = '拒绝'

        else:
            raise AdverseEventError(f'无效的审核动作: {action}')

        logger.info(
            f'不良事件报告审核成功: {report.report_number} - {action_text}',
            extra={
                'report_id': report_id,
                'report_number': report.report_number,
                'reviewer_id': reviewer_id,
                'action': action,
                'comments': comments,
                'reviewed_by': reviewed_by.username if reviewed_by else None
            }
        )

        return report

    except ValidationError as e:
        logger.error(f'报告审核验证失败: {str(e)}')
        raise AdverseEventError(f'报告审核验证失败: {str(e)}')
    except Exception as e:
        logger.error(f'报告审核失败: {str(e)}')
        raise AdverseEventError(f'报告审核失败: {str(e)}')


def generate_report_number() -> str:
    """
    生成唯一的报告编号

    Returns:
        str: 报告编号
    """
    today = timezone.now().date()
    date_str = today.strftime('%Y%m%d')
    prefix = f'AER{date_str}'

    # 查找当天最大的序号
    latest_report = AdverseEventReport.objects.filter(
        report_number__startswith=prefix
    ).order_by('-report_number').first()

    if latest_report:
        # 提取序号并加1
        last_number = int(latest_report.report_number[-3:])
        next_number = last_number + 1
    else:
        next_number = 1

    return f'{prefix}{next_number:03d}'


def _can_submit_report(user_profile: UserProfile) -> bool:
    """
    检查用户是否可以提交报告

    Args:
        user_profile: 用户配置文件

    Returns:
        bool: 是否可以提交
    """
    # 管理员可以提交报告
    if user_profile.is_admin:
        return True

    # 科室人员可以提交报告（需要有活跃的科室）
    if user_profile.is_staff_member and user_profile.department and user_profile.department.is_active:
        return True

    return False


def _can_review_report(user_profile: UserProfile) -> bool:
    """
    检查用户是否可以审核报告

    Args:
        user_profile: 用户配置文件

    Returns:
        bool: 是否可以审核
    """
    # 只有管理员可以审核报告
    return user_profile.is_admin


def _validate_report_data(**kwargs) -> None:
    """
    验证报告数据

    Args:
        **kwargs: 报告数据字段

    Raises:
        DataValidationError: 数据验证失败
        PatientDataError: 患者数据错误
        DeviceValidationError: 器械信息验证失败
    """

    # 验证上报人联系电话
    if 'reporter_phone' in kwargs:
        phone = kwargs['reporter_phone']
        if phone and not ValidationUtils.validate_phone(phone):
            raise DataValidationError('上报人联系电话格式不正确')

    # 验证患者信息
    if 'patient_name' in kwargs:
        name = kwargs['patient_name']
        if not name or not name.strip():
            raise PatientDataError('患者姓名不能为空')
        if len(name.strip()) > 100:
            raise PatientDataError('患者姓名长度不能超过100个字符')

    if 'patient_age' in kwargs:
        age = kwargs['patient_age']
        if age is not None and (age < 0 or age > 150):
            raise PatientDataError('患者年龄必须在0-150岁之间')

    if 'patient_gender' in kwargs:
        gender = kwargs['patient_gender']
        valid_genders = dict(AdverseEventReport.GENDER_CHOICES).keys()
        if gender and gender not in valid_genders:
            raise PatientDataError(f'无效的患者性别: {gender}')

    # 验证事件信息
    if 'device_malfunction' in kwargs:
        malfunction = kwargs['device_malfunction']
        if not malfunction or not malfunction.strip():
            raise DeviceValidationError('器械故障表现不能为空')

    if 'event_date' in kwargs:
        event_date = kwargs['event_date']
        if event_date and event_date > timezone.now():
            raise DataValidationError('事件发生日期不能是未来时间')

    if 'injury_level' in kwargs:
        injury_level = kwargs['injury_level']
        valid_levels = dict(AdverseEventReport.INJURY_LEVEL_CHOICES).keys()
        if injury_level and injury_level not in valid_levels:
            raise DataValidationError(f'无效的伤害程度: {injury_level}')

    if 'event_description' in kwargs:
        description = kwargs['event_description']
        if not description or not description.strip():
            raise DataValidationError('事件陈述不能为空')

    # 验证器械信息
    if 'device_name' in kwargs:
        device_name = kwargs['device_name']
        if not device_name or not device_name.strip():
            raise DeviceValidationError('医疗器械名称不能为空')

    if 'registration_number' in kwargs:
        reg_number = kwargs['registration_number']
        if not reg_number or not reg_number.strip():
            raise DeviceValidationError('注册证号不能为空')

    if 'manufacturer' in kwargs:
        manufacturer = kwargs['manufacturer']
        if not manufacturer or not manufacturer.strip():
            raise DeviceValidationError('生产企业名称不能为空')

    # 验证日期逻辑
    production_date = kwargs.get('production_date')
    expiry_date = kwargs.get('expiry_date')
    if production_date and expiry_date and production_date >= expiry_date:
        raise DeviceValidationError('有效期必须晚于生产日期')


def _validate_report_completeness(report: AdverseEventReport) -> None:
    """
    验证报告完整性（提交前检查）

    Args:
        report: 报告实例

    Raises:
        ReportSubmissionError: 报告不完整
    """

    missing_fields = []

    # 检查必填字段
    required_fields = {
        'reporter_phone': '上报人联系电话',
        'patient_name': '患者姓名',
        'patient_age': '患者年龄',
        'patient_gender': '患者性别',
        'device_malfunction': '器械故障表现',
        'event_date': '事件发生日期',
        'injury_level': '伤害程度',
        'event_description': '事件陈述',
        'device_name': '医疗器械名称',
        'registration_number': '注册证号',
        'manufacturer': '生产企业名称'
    }

    for field, field_name in required_fields.items():
        value = getattr(report, field)
        if not value or (isinstance(value, str) and not value.strip()):
            missing_fields.append(field_name)

    # 检查伤害描述（如果有伤害）
    if report.injury_level in ['death', 'serious_injury'] and not report.injury_description:
        missing_fields.append('伤害表现（伤害程度为死亡或严重伤害时必填）')

    if missing_fields:
        raise ReportSubmissionError(f'报告信息不完整，缺少以下必填字段: {", ".join(missing_fields)}')


# 查询相关的服务函数

def get_report_by_id(report_id: int) -> AdverseEventReport:
    """
    根据ID获取报告

    Args:
        report_id: 报告ID

    Returns:
        AdverseEventReport: 报告实例

    Raises:
        ResourceNotFoundError: 报告不存在
    """
    try:
        return AdverseEventReport.objects.select_related(
            'reporter', 'reporter__user', 'department',
            'reviewed_by', 'reviewed_by__user'
        ).get(id=report_id)
    except AdverseEventReport.DoesNotExist:
        raise ResourceNotFoundError(f'报告 ID {report_id} 不存在')


def get_report_by_number(report_number: str) -> AdverseEventReport:
    """
    根据报告编号获取报告

    Args:
        report_number: 报告编号

    Returns:
        AdverseEventReport: 报告实例

    Raises:
        ResourceNotFoundError: 报告不存在
    """
    try:
        return AdverseEventReport.objects.select_related(
            'reporter', 'reporter__user', 'department',
            'reviewed_by', 'reviewed_by__user'
        ).get(report_number=report_number)
    except AdverseEventReport.DoesNotExist:
        raise ResourceNotFoundError(f'报告编号 {report_number} 不存在')


def get_user_reports(user_profile: UserProfile, status: Optional[str] = None) -> 'QuerySet[AdverseEventReport]':
    """
    获取用户相关的报告

    Args:
        user_profile: 用户配置文件
        status: 报告状态过滤

    Returns:
        QuerySet: 报告查询集
    """
    queryset = AdverseEventReport.objects.select_related(
        'reporter', 'department', 'reviewed_by'
    )

    if user_profile.is_admin:
        # 管理员可以查看所有报告
        pass
    else:
        # 科室人员只能查看本科室的报告
        if user_profile.department:
            queryset = queryset.filter(department=user_profile.department)
        else:
            # 没有科室的用户只能查看自己的报告
            queryset = queryset.filter(reporter=user_profile)

    if status:
        queryset = queryset.filter(status=status)

    return queryset.order_by('-created_at')
