"""
不良事件上报管理服务层
Adverse Event Reports Management Services for Medical Device Reporting Platform
"""

import logging
from datetime import datetime, date
from typing import Optional, Dict, Any, List
import io
import base64
from pathlib import Path

from django.db import transaction
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.http import HttpResponse
from django.conf import settings

from apps.common.exceptions import (
    BusinessLogicError,
    DataValidationError,
    ResourceNotFoundError,
    AdverseEventError,
    DeviceValidationError,
    ReportSubmissionError,
    PatientDataError,
    ReporterAuthorizationError
)
from apps.common.utils import ValidationUtils
from apps.users.models import UserProfile, Department
from .models import AdverseEventReport

logger = logging.getLogger('apps.reports')


@transaction.atomic
def report_create(
    *,
    reporter_id: int,
    department_id: Optional[int] = None,
    reporter_phone: str,
    patient_name: str = '',
    patient_age: Optional[int] = None,
    patient_gender: str = '',
    patient_contact: str = '',
    device_malfunction: str = '',
    event_date: datetime,
    injury_level: str,
    injury_description: str = '',
    event_description: str,
    initial_cause_analysis: str = '',
    initial_treatment: str = '',
    device_name: str,
    registration_number: str,
    manufacturer: str,
    specification: str = '',
    model: str = '',
    product_number: str = '',
    batch_number: str = '',
    production_date: Optional[date] = None,
    expiry_date: Optional[date] = None,
    created_by: Optional[User] = None
) -> AdverseEventReport:
    """
    创建不良事件报告

    Args:
        reporter_id: 上报人ID
        department_id: 科室ID（可选，默认使用上报人所属科室）
        reporter_phone: 上报人联系电话
        patient_name: 患者姓名（可选）
        patient_age: 患者年龄（可选）
        patient_gender: 患者性别（可选）
        patient_contact: 患者联系方式（可选）
        device_malfunction: 器械故障表现（可选）
        event_date: 事件发生日期
        injury_level: 伤害程度
        injury_description: 伤害表现
        event_description: 事件陈述
        initial_cause_analysis: 初步原因分析
        initial_treatment: 初步处理情况
        device_name: 医疗器械名称
        registration_number: 注册证号
        manufacturer: 生产企业名称
        specification: 规格
        model: 型号
        product_number: 产品编号
        batch_number: 产品批号
        production_date: 生产日期
        expiry_date: 有效期至
        created_by: 创建者

    Returns:
        AdverseEventReport: 创建的报告

    Raises:
        ResourceNotFoundError: 上报人或科室不存在
        DataValidationError: 数据验证失败
        ReporterAuthorizationError: 上报人权限不足
        PatientDataError: 患者数据错误
        DeviceValidationError: 器械信息验证失败
        AdverseEventError: 业务逻辑错误
    """

    # 验证上报人
    try:
        reporter = UserProfile.objects.select_related('user', 'department').get(
            id=reporter_id,
            is_active=True,
            is_deleted=False
        )
    except UserProfile.DoesNotExist:
        raise ResourceNotFoundError(f'上报人 ID {reporter_id} 不存在或已禁用')

    # 验证上报人权限
    if not _can_submit_report(reporter):
        raise ReporterAuthorizationError(f'用户 {reporter.account_number} 没有提交报告的权限')

    # 确定科室
    if department_id:
        try:
            department = Department.objects.get(id=department_id, is_active=True)
        except Department.DoesNotExist:
            raise ResourceNotFoundError(f'科室 ID {department_id} 不存在或已禁用')

        # 验证上报人和科室的关系
        if reporter.role == 'staff' and reporter.department != department:
            raise ReporterAuthorizationError('科室人员只能为本科室提交报告')
    else:
        department = reporter.department
        if not department:
            raise DataValidationError('上报人未分配科室，请指定科室')

    # 验证数据
    _validate_report_data(
        reporter_phone=reporter_phone,
        patient_name=patient_name,
        patient_age=patient_age,
        patient_gender=patient_gender,
        device_malfunction=device_malfunction,
        event_date=event_date,
        injury_level=injury_level,
        injury_description=injury_description,
        event_description=event_description,
        device_name=device_name,
        registration_number=registration_number,
        manufacturer=manufacturer,
        production_date=production_date,
        expiry_date=expiry_date
    )

    try:
        # 创建报告
        report = AdverseEventReport(
            reporter=reporter,
            department=department,
            reporter_phone=reporter_phone,
            patient_name=patient_name,
            patient_age=patient_age,
            patient_gender=patient_gender,
            patient_contact=patient_contact,
            device_malfunction=device_malfunction,
            event_date=event_date,
            injury_level=injury_level,
            injury_description=injury_description,
            event_description=event_description,
            initial_cause_analysis=initial_cause_analysis,
            initial_treatment=initial_treatment,
            device_name=device_name,
            registration_number=registration_number,
            manufacturer=manufacturer,
            specification=specification,
            model=model,
            product_number=product_number,
            batch_number=batch_number,
            production_date=production_date,
            expiry_date=expiry_date,
            status='draft'
        )

        # 保存报告（会自动生成报告编号）
        report.save(user=created_by)

        logger.info(
            f'不良事件报告创建成功: {report.report_number}',
            extra={
                'report_id': report.id,
                'report_number': report.report_number,
                'reporter_id': reporter_id,
                'department_id': department.id,
                'device_name': device_name,
                'injury_level': injury_level,
                'created_by': created_by.username if created_by else None
            }
        )

        return report

    except ValidationError as e:
        logger.error(f'报告创建验证失败: {str(e)}')
        raise DataValidationError(f'报告数据验证失败: {str(e)}')
    except Exception as e:
        logger.error(f'报告创建失败: {str(e)}')
        raise AdverseEventError(f'报告创建失败: {str(e)}')


@transaction.atomic
def report_update(
    *,
    report_id: int,
    reporter_phone: Optional[str] = None,
    patient_name: Optional[str] = None,
    patient_age: Optional[int] = None,
    patient_gender: Optional[str] = None,
    patient_contact: Optional[str] = None,
    device_malfunction: Optional[str] = None,
    event_date: Optional[datetime] = None,
    injury_level: Optional[str] = None,
    injury_description: Optional[str] = None,
    event_description: Optional[str] = None,
    initial_cause_analysis: Optional[str] = None,
    initial_treatment: Optional[str] = None,
    device_name: Optional[str] = None,
    registration_number: Optional[str] = None,
    manufacturer: Optional[str] = None,
    specification: Optional[str] = None,
    model: Optional[str] = None,
    product_number: Optional[str] = None,
    batch_number: Optional[str] = None,
    production_date: Optional[date] = None,
    expiry_date: Optional[date] = None,
    updated_by: Optional[User] = None
) -> AdverseEventReport:
    """
    更新不良事件报告

    Args:
        report_id: 报告ID
        其他参数: 可选的更新字段
        updated_by: 更新者

    Returns:
        AdverseEventReport: 更新后的报告

    Raises:
        ResourceNotFoundError: 报告不存在
        DataValidationError: 数据验证失败
        AdverseEventError: 业务逻辑错误
    """

    try:
        report = AdverseEventReport.objects.select_related(
            'reporter', 'department'
        ).get(id=report_id)
    except AdverseEventReport.DoesNotExist:
        raise ResourceNotFoundError(f'报告 ID {report_id} 不存在')

    # 检查报告是否可以编辑
    if not report.can_edit:
        raise AdverseEventError(f'报告 {report.report_number} 当前状态不允许编辑')

    # 更新字段
    update_fields = {}

    if reporter_phone is not None:
        update_fields['reporter_phone'] = reporter_phone
    if patient_name is not None:
        update_fields['patient_name'] = patient_name
    if patient_age is not None:
        update_fields['patient_age'] = patient_age
    if patient_gender is not None:
        update_fields['patient_gender'] = patient_gender
    if patient_contact is not None:
        update_fields['patient_contact'] = patient_contact
    if device_malfunction is not None:
        update_fields['device_malfunction'] = device_malfunction
    if event_date is not None:
        update_fields['event_date'] = event_date
    if injury_level is not None:
        update_fields['injury_level'] = injury_level
    if injury_description is not None:
        update_fields['injury_description'] = injury_description
    if event_description is not None:
        update_fields['event_description'] = event_description
    if initial_cause_analysis is not None:
        update_fields['initial_cause_analysis'] = initial_cause_analysis
    if initial_treatment is not None:
        update_fields['initial_treatment'] = initial_treatment
    if device_name is not None:
        update_fields['device_name'] = device_name
    if registration_number is not None:
        update_fields['registration_number'] = registration_number
    if manufacturer is not None:
        update_fields['manufacturer'] = manufacturer
    if specification is not None:
        update_fields['specification'] = specification
    if model is not None:
        update_fields['model'] = model
    if product_number is not None:
        update_fields['product_number'] = product_number
    if batch_number is not None:
        update_fields['batch_number'] = batch_number
    if production_date is not None:
        update_fields['production_date'] = production_date
    if expiry_date is not None:
        update_fields['expiry_date'] = expiry_date

    # 验证更新的数据
    if update_fields:
        _validate_report_data(**update_fields)

        # 应用更新
        for field, value in update_fields.items():
            setattr(report, field, value)

    try:
        # 保存更新
        report.save(user=updated_by)

        logger.info(
            f'不良事件报告更新成功: {report.report_number}',
            extra={
                'report_id': report_id,
                'report_number': report.report_number,
                'updated_fields': list(update_fields.keys()),
                'updated_by': updated_by.username if updated_by else None
            }
        )

        return report

    except ValidationError as e:
        logger.error(f'报告更新验证失败: {str(e)}')
        raise DataValidationError(f'报告数据验证失败: {str(e)}')
    except Exception as e:
        logger.error(f'报告更新失败: {str(e)}')
        raise AdverseEventError(f'报告更新失败: {str(e)}')


@transaction.atomic
def report_submit(
    *,
    report_id: int,
    submitted_by: Optional[User] = None
) -> AdverseEventReport:
    """
    提交不良事件报告

    Args:
        report_id: 报告ID
        submitted_by: 提交者

    Returns:
        AdverseEventReport: 提交后的报告

    Raises:
        ResourceNotFoundError: 报告不存在
        ReportSubmissionError: 提交失败
        AdverseEventError: 业务逻辑错误
    """

    try:
        report = AdverseEventReport.objects.select_related(
            'reporter', 'department'
        ).get(id=report_id)
    except AdverseEventReport.DoesNotExist:
        raise ResourceNotFoundError(f'报告 ID {report_id} 不存在')

    # 检查报告是否可以提交
    if not report.can_submit:
        raise ReportSubmissionError(f'报告 {report.report_number} 当前状态不允许提交')

    # 验证报告完整性
    _validate_report_completeness(report)

    try:
        # 使用模型的submit方法
        report.submit(user=submitted_by)

        logger.info(
            f'不良事件报告提交成功: {report.report_number}',
            extra={
                'report_id': report_id,
                'report_number': report.report_number,
                'reporter_id': report.reporter.id,
                'department_id': report.department.id,
                'submitted_by': submitted_by.username if submitted_by else None
            }
        )

        return report

    except ValidationError as e:
        logger.error(f'报告提交验证失败: {str(e)}')
        raise ReportSubmissionError(f'报告提交验证失败: {str(e)}')
    except Exception as e:
        logger.error(f'报告提交失败: {str(e)}')
        raise ReportSubmissionError(f'报告提交失败: {str(e)}')


@transaction.atomic
def report_review(
    *,
    report_id: int,
    reviewer_id: int,
    action: str,
    comments: str = '',
    reviewed_by: Optional[User] = None
) -> AdverseEventReport:
    """
    审核不良事件报告

    Args:
        report_id: 报告ID
        reviewer_id: 审核人ID
        action: 审核动作 ('approve', 'reject')
        comments: 审核意见
        reviewed_by: 审核操作者

    Returns:
        AdverseEventReport: 审核后的报告

    Raises:
        ResourceNotFoundError: 报告或审核人不存在
        ReporterAuthorizationError: 审核人权限不足
        AdverseEventError: 业务逻辑错误
    """

    try:
        report = AdverseEventReport.objects.select_related(
            'reporter', 'department'
        ).get(id=report_id)
    except AdverseEventReport.DoesNotExist:
        raise ResourceNotFoundError(f'报告 ID {report_id} 不存在')

    try:
        reviewer = UserProfile.objects.select_related('user').get(
            id=reviewer_id,
            is_active=True,
            is_deleted=False
        )
    except UserProfile.DoesNotExist:
        raise ResourceNotFoundError(f'审核人 ID {reviewer_id} 不存在或已禁用')

    # 验证审核人权限
    if not _can_review_report(reviewer):
        raise ReporterAuthorizationError(f'用户 {reviewer.account_number} 没有审核报告的权限')

    # 检查报告是否可以审核
    if not report.can_review:
        raise AdverseEventError(f'报告 {report.report_number} 当前状态不允许审核')

    try:
        if action == 'approve':
            # 批准报告
            report.approve(reviewer, comments, user=reviewed_by)
            action_text = '批准'

        elif action == 'reject':
            # 拒绝报告
            if not comments:
                raise AdverseEventError('拒绝报告时必须填写审核意见')
            report.reject(reviewer, comments, user=reviewed_by)
            action_text = '拒绝'

        else:
            raise AdverseEventError(f'无效的审核动作: {action}')

        logger.info(
            f'不良事件报告审核成功: {report.report_number} - {action_text}',
            extra={
                'report_id': report_id,
                'report_number': report.report_number,
                'reviewer_id': reviewer_id,
                'action': action,
                'comments': comments,
                'reviewed_by': reviewed_by.username if reviewed_by else None
            }
        )

        return report

    except ValidationError as e:
        logger.error(f'报告审核验证失败: {str(e)}')
        raise AdverseEventError(f'报告审核验证失败: {str(e)}')
    except Exception as e:
        logger.error(f'报告审核失败: {str(e)}')
        raise AdverseEventError(f'报告审核失败: {str(e)}')


def generate_report_number() -> str:
    """
    生成唯一的报告编号

    Returns:
        str: 报告编号
    """
    today = timezone.now().date()
    date_str = today.strftime('%Y%m%d')
    prefix = f'AER{date_str}'

    # 查找当天最大的序号
    latest_report = AdverseEventReport.objects.filter(
        report_number__startswith=prefix
    ).order_by('-report_number').first()

    if latest_report:
        # 提取序号并加1
        last_number = int(latest_report.report_number[-3:])
        next_number = last_number + 1
    else:
        next_number = 1

    return f'{prefix}{next_number:03d}'


def _can_submit_report(user_profile: UserProfile) -> bool:
    """
    检查用户是否可以提交报告

    Args:
        user_profile: 用户配置文件

    Returns:
        bool: 是否可以提交
    """
    # 管理员可以提交报告
    if user_profile.is_admin:
        return True

    # 科室人员可以提交报告（需要有活跃的科室）
    if user_profile.is_staff_member and user_profile.department and user_profile.department.is_active:
        return True

    return False


def _can_review_report(user_profile: UserProfile) -> bool:
    """
    检查用户是否可以审核报告

    Args:
        user_profile: 用户配置文件

    Returns:
        bool: 是否可以审核
    """
    # 只有管理员可以审核报告
    return user_profile.is_admin


def _validate_report_data(**kwargs) -> None:
    """
    验证报告数据

    Args:
        **kwargs: 报告数据字段

    Raises:
        DataValidationError: 数据验证失败
        PatientDataError: 患者数据错误
        DeviceValidationError: 器械信息验证失败
    """

    # 验证上报人联系电话
    if 'reporter_phone' in kwargs:
        phone = kwargs['reporter_phone']
        if phone and not ValidationUtils.validate_phone(phone):
            raise DataValidationError('上报人联系电话格式不正确')

    # 验证患者信息（现在为可选）
    if 'patient_name' in kwargs:
        name = kwargs['patient_name']
        if name and len(name.strip()) > 100:
            raise PatientDataError('患者姓名长度不能超过100个字符')

    if 'patient_age' in kwargs:
        age = kwargs['patient_age']
        if age is not None and (age < 0 or age > 150):
            raise PatientDataError('患者年龄必须在0-150岁之间')

    if 'patient_gender' in kwargs:
        gender = kwargs['patient_gender']
        valid_genders = dict(AdverseEventReport.GENDER_CHOICES).keys()
        if gender and gender not in valid_genders:
            raise PatientDataError(f'无效的患者性别: {gender}')

    # 验证事件信息
    if 'device_malfunction' in kwargs:
        malfunction = kwargs['device_malfunction']
        # 器械故障表现现在是可选的，不需要验证为空

    if 'event_date' in kwargs:
        event_date = kwargs['event_date']
        if event_date and event_date > timezone.now():
            raise DataValidationError('事件发生日期不能是未来时间')

    if 'injury_level' in kwargs:
        injury_level = kwargs['injury_level']
        valid_levels = dict(AdverseEventReport.INJURY_LEVEL_CHOICES).keys()
        if injury_level and injury_level not in valid_levels:
            raise DataValidationError(f'无效的伤害程度: {injury_level}')

    if 'event_description' in kwargs:
        description = kwargs['event_description']
        if not description or not description.strip():
            raise DataValidationError('事件陈述不能为空')

    # 验证器械信息
    if 'device_name' in kwargs:
        device_name = kwargs['device_name']
        if not device_name or not device_name.strip():
            raise DeviceValidationError('医疗器械名称不能为空')

    if 'registration_number' in kwargs:
        reg_number = kwargs['registration_number']
        if not reg_number or not reg_number.strip():
            raise DeviceValidationError('注册证号不能为空')

    if 'manufacturer' in kwargs:
        manufacturer = kwargs['manufacturer']
        if not manufacturer or not manufacturer.strip():
            raise DeviceValidationError('生产企业名称不能为空')

    # 验证日期逻辑
    production_date = kwargs.get('production_date')
    expiry_date = kwargs.get('expiry_date')
    if production_date and expiry_date and production_date >= expiry_date:
        raise DeviceValidationError('有效期必须晚于生产日期')


def _validate_report_completeness(report: AdverseEventReport) -> None:
    """
    验证报告完整性（提交前检查）

    Args:
        report: 报告实例

    Raises:
        ReportSubmissionError: 报告不完整
    """

    missing_fields = []

    # 检查必填字段（患者信息和器械故障表现现在是可选的）
    required_fields = {
        'reporter_phone': '上报人联系电话',
        'event_date': '事件发生日期',
        'injury_level': '伤害程度',
        'event_description': '事件陈述',
        'device_name': '医疗器械名称',
        'registration_number': '注册证号',
        'manufacturer': '生产企业名称'
    }

    for field, field_name in required_fields.items():
        value = getattr(report, field)
        if not value or (isinstance(value, str) and not value.strip()):
            missing_fields.append(field_name)

    # 检查产品编号或批号至少填写其中一项
    if not report.product_number and not report.batch_number:
        missing_fields.append('产品编号或产品批号（至少填写其中一项）')

    # 检查伤害描述（如果有伤害）
    if report.injury_level in ['death', 'serious_injury'] and not report.injury_description:
        missing_fields.append('伤害表现（伤害程度为死亡或严重伤害时必填）')

    if missing_fields:
        raise ReportSubmissionError(f'报告信息不完整，缺少以下必填字段: {", ".join(missing_fields)}')


# 查询相关的服务函数

def get_report_by_id(report_id: int) -> AdverseEventReport:
    """
    根据ID获取报告

    Args:
        report_id: 报告ID

    Returns:
        AdverseEventReport: 报告实例

    Raises:
        ResourceNotFoundError: 报告不存在
    """
    try:
        return AdverseEventReport.objects.select_related(
            'reporter', 'reporter__user', 'department',
            'reviewed_by', 'reviewed_by__user'
        ).get(id=report_id)
    except AdverseEventReport.DoesNotExist:
        raise ResourceNotFoundError(f'报告 ID {report_id} 不存在')


def get_report_by_number(report_number: str) -> AdverseEventReport:
    """
    根据报告编号获取报告

    Args:
        report_number: 报告编号

    Returns:
        AdverseEventReport: 报告实例

    Raises:
        ResourceNotFoundError: 报告不存在
    """
    try:
        return AdverseEventReport.objects.select_related(
            'reporter', 'reporter__user', 'department',
            'reviewed_by', 'reviewed_by__user'
        ).get(report_number=report_number)
    except AdverseEventReport.DoesNotExist:
        raise ResourceNotFoundError(f'报告编号 {report_number} 不存在')


def get_user_reports(user_profile: UserProfile, status: Optional[str] = None) -> 'QuerySet[AdverseEventReport]':
    """
    获取用户相关的报告

    Args:
        user_profile: 用户配置文件
        status: 报告状态过滤

    Returns:
        QuerySet: 报告查询集
    """
    queryset = AdverseEventReport.objects.select_related(
        'reporter', 'department', 'reviewed_by'
    )

    if user_profile.is_admin:
        # 管理员可以查看所有报告
        pass
    else:
        # 科室人员只能查看本科室的报告
        if user_profile.department:
            queryset = queryset.filter(department=user_profile.department)
        else:
            # 没有科室的用户只能查看自己的报告
            queryset = queryset.filter(reporter=user_profile)

    if status:
        queryset = queryset.filter(status=status)

    return queryset.order_by('-created_at')


# 数据导出相关服务函数

def export_statistics_report(
    user_profile: 'UserProfile',
    format: str = 'excel',
    chart_data: Optional[Dict] = None,
    filters: Optional[Dict] = None,
    analysis_type: str = 'summary'
) -> HttpResponse:
    """
    导出统计分析报告

    Args:
        user_profile: 用户配置文件
        format: 导出格式 ('excel', 'pdf')
        chart_data: 图表数据
        filters: 筛选条件
        analysis_type: 分析类型

    Returns:
        HttpResponse: 导出文件响应

    Raises:
        BusinessLogicError: 业务逻辑错误
        DataValidationError: 数据验证失败
    """
    try:
        if format.lower() == 'excel':
            return _export_statistics_excel(user_profile, chart_data, filters, analysis_type)
        elif format.lower() == 'pdf':
            return _export_statistics_pdf(user_profile, chart_data, filters, analysis_type)
        else:
            raise DataValidationError(f'不支持的导出格式: {format}')

    except Exception as e:
        logger.error(f'导出统计报告失败: {str(e)}')
        raise BusinessLogicError(f'导出统计报告失败: {str(e)}')


def _export_statistics_excel(
    user_profile: 'UserProfile',
    chart_data: Optional[Dict] = None,
    filters: Optional[Dict] = None,
    analysis_type: str = 'summary'
) -> HttpResponse:
    """
    导出Excel格式的统计报告
    """
    try:
        from openpyxl import Workbook
        from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
        from openpyxl.chart import BarChart, LineChart, PieChart, Reference
        from openpyxl.drawing.image import Image
        import pandas as pd

        # 创建工作簿
        wb = Workbook()

        # 删除默认工作表
        wb.remove(wb.active)

        # 创建概览工作表
        ws_summary = wb.create_sheet('统计概览')
        _create_summary_sheet(ws_summary, user_profile, filters)

        # 根据分析类型创建相应的工作表
        if analysis_type == 'time_series' and chart_data:
            ws_time = wb.create_sheet('时间序列分析')
            _create_time_series_sheet(ws_time, chart_data, user_profile)

        elif analysis_type == 'cross_dimension' and chart_data:
            ws_cross = wb.create_sheet('交叉维度分析')
            _create_cross_dimension_sheet(ws_cross, chart_data, user_profile)

        elif analysis_type == 'device_stats' and chart_data:
            ws_device = wb.create_sheet('器械统计分析')
            _create_device_stats_sheet(ws_device, chart_data, user_profile)

        elif analysis_type == 'department_stats' and chart_data and user_profile.is_admin:
            ws_dept = wb.create_sheet('科室统计分析')
            _create_department_stats_sheet(ws_dept, chart_data, user_profile)

        elif analysis_type == 'trend_analysis' and chart_data:
            ws_trend = wb.create_sheet('趋势分析')
            _create_trend_analysis_sheet(ws_trend, chart_data, user_profile)

        # 如果有多种图表数据，创建综合分析工作表
        if chart_data and isinstance(chart_data, dict) and len(chart_data) > 1:
            ws_comprehensive = wb.create_sheet('综合分析')
            _create_comprehensive_sheet(ws_comprehensive, chart_data, user_profile)

        # 保存到内存
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)

        # 生成文件名
        timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
        filename = f'统计分析报告_{analysis_type}_{timestamp}.xlsx'

        # 创建响应
        response = HttpResponse(
            output.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="{filename}"'

        logger.info(
            f'Excel统计报告导出成功: {filename}',
            extra={
                'user_id': user_profile.user.id,
                'analysis_type': analysis_type,
                'filters': filters
            }
        )

        return response

    except Exception as e:
        logger.error(f'Excel导出失败: {str(e)}')
        raise BusinessLogicError(f'Excel导出失败: {str(e)}')


def _export_statistics_pdf(
    user_profile: 'UserProfile',
    chart_data: Optional[Dict] = None,
    filters: Optional[Dict] = None,
    analysis_type: str = 'summary'
) -> HttpResponse:
    """
    导出PDF格式的统计报告
    """
    try:
        from reportlab.lib.pagesizes import A4, letter
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import inch
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
        from reportlab.lib import colors
        from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
        from reportlab.pdfbase import pdfmetrics
        from reportlab.pdfbase.ttfonts import TTFont
        import matplotlib.pyplot as plt
        import matplotlib
        matplotlib.use('Agg')  # 使用非交互式后端

        # 注册中文字体（如果可用）
        try:
            # 尝试使用系统中文字体
            font_path = _get_chinese_font_path()
            if font_path:
                pdfmetrics.registerFont(TTFont('SimHei', font_path))
                chinese_font = 'SimHei'
            else:
                chinese_font = 'Helvetica'  # 回退到默认字体
        except:
            chinese_font = 'Helvetica'

        # 创建PDF文档
        output = io.BytesIO()
        doc = SimpleDocTemplate(output, pagesize=A4)

        # 获取样式
        styles = getSampleStyleSheet()

        # 创建自定义样式
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontName=chinese_font,
            fontSize=18,
            alignment=TA_CENTER,
            spaceAfter=30
        )

        heading_style = ParagraphStyle(
            'CustomHeading',
            parent=styles['Heading2'],
            fontName=chinese_font,
            fontSize=14,
            spaceAfter=12
        )

        normal_style = ParagraphStyle(
            'CustomNormal',
            parent=styles['Normal'],
            fontName=chinese_font,
            fontSize=10,
            spaceAfter=6
        )

        # 构建PDF内容
        story = []

        # 标题
        title = f'医疗器械不良事件统计分析报告'
        story.append(Paragraph(title, title_style))
        story.append(Spacer(1, 12))

        # 报告信息
        report_info = [
            ['报告生成时间', timezone.now().strftime('%Y年%m月%d日 %H:%M:%S')],
            ['分析类型', _get_analysis_type_name(analysis_type)],
            ['生成用户', user_profile.user.get_full_name() or user_profile.user.username],
            ['用户角色', '管理员' if user_profile.is_admin else '科室人员']
        ]

        if filters:
            if filters.get('start_date'):
                report_info.append(['开始日期', filters['start_date']])
            if filters.get('end_date'):
                report_info.append(['结束日期', filters['end_date']])

        info_table = Table(report_info, colWidths=[2*inch, 3*inch])
        info_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), chinese_font),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('BACKGROUND', (1, 0), (1, -1), colors.white),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))

        story.append(info_table)
        story.append(Spacer(1, 20))

        # 添加统计概览
        story.append(Paragraph('统计概览', heading_style))
        _add_summary_to_pdf(story, user_profile, normal_style, chinese_font)
        story.append(Spacer(1, 20))

        # 根据分析类型添加相应的图表和数据
        if chart_data:
            if analysis_type == 'time_series':
                _add_time_series_to_pdf(story, chart_data, heading_style, normal_style, chinese_font)
            elif analysis_type == 'cross_dimension':
                _add_cross_dimension_to_pdf(story, chart_data, heading_style, normal_style, chinese_font)
            elif analysis_type == 'device_stats':
                _add_device_stats_to_pdf(story, chart_data, heading_style, normal_style, chinese_font)
            elif analysis_type == 'department_stats' and user_profile.is_admin:
                _add_department_stats_to_pdf(story, chart_data, heading_style, normal_style, chinese_font)
            elif analysis_type == 'trend_analysis':
                _add_trend_analysis_to_pdf(story, chart_data, heading_style, normal_style, chinese_font)

        # 生成PDF
        doc.build(story)
        output.seek(0)

        # 生成文件名
        timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
        filename = f'统计分析报告_{analysis_type}_{timestamp}.pdf'

        # 创建响应
        response = HttpResponse(output.getvalue(), content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="{filename}"'

        logger.info(
            f'PDF统计报告导出成功: {filename}',
            extra={
                'user_id': user_profile.user.id,
                'analysis_type': analysis_type,
                'filters': filters
            }
        )

        return response

    except Exception as e:
        logger.error(f'PDF导出失败: {str(e)}')
        raise BusinessLogicError(f'PDF导出失败: {str(e)}')


# Excel工作表创建辅助函数

def _create_summary_sheet(worksheet, user_profile, filters=None):
    """创建统计概览工作表"""
    try:
        from openpyxl.styles import Font, Alignment, PatternFill
        from .selectors import report_statistics

        # 设置标题
        worksheet['A1'] = '医疗器械不良事件统计概览'
        worksheet['A1'].font = Font(size=16, bold=True)
        worksheet['A1'].alignment = Alignment(horizontal='center')
        worksheet.merge_cells('A1:D1')

        # 获取统计数据
        stats = report_statistics(user_profile=user_profile)

        # 基础统计信息
        row = 3
        headers = ['统计项目', '数值', '说明', '占比']
        for col, header in enumerate(headers, 1):
            cell = worksheet.cell(row=row, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

        # 统计数据
        stats_data = [
            ('总报告数', stats.get('total_count', 0), '所有状态的报告总数', '100%'),
            ('已批准', stats.get('approved_count', 0), '已通过审核的报告', f"{(stats.get('approved_count', 0) / max(stats.get('total_count', 1), 1) * 100):.1f}%"),
            ('待审核', stats.get('submitted_count', 0), '已提交待审核的报告', f"{(stats.get('submitted_count', 0) / max(stats.get('total_count', 1), 1) * 100):.1f}%"),
            ('草稿', stats.get('draft_count', 0), '尚未提交的草稿', f"{(stats.get('draft_count', 0) / max(stats.get('total_count', 1), 1) * 100):.1f}%"),
            ('严重事件', stats.get('serious_injury_count', 0), '严重伤害事件数量', f"{(stats.get('serious_injury_count', 0) / max(stats.get('total_count', 1), 1) * 100):.1f}%"),
            ('死亡事件', stats.get('death_count', 0), '死亡事件数量', f"{(stats.get('death_count', 0) / max(stats.get('total_count', 1), 1) * 100):.1f}%"),
        ]

        for i, (item, value, desc, ratio) in enumerate(stats_data, 4):
            worksheet.cell(row=i, column=1, value=item)
            worksheet.cell(row=i, column=2, value=value)
            worksheet.cell(row=i, column=3, value=desc)
            worksheet.cell(row=i, column=4, value=ratio)

        # 调整列宽
        worksheet.column_dimensions['A'].width = 15
        worksheet.column_dimensions['B'].width = 10
        worksheet.column_dimensions['C'].width = 25
        worksheet.column_dimensions['D'].width = 10

        # 添加生成信息
        info_row = len(stats_data) + 6
        worksheet.cell(row=info_row, column=1, value='报告生成时间:')
        worksheet.cell(row=info_row, column=2, value=timezone.now().strftime('%Y-%m-%d %H:%M:%S'))
        worksheet.cell(row=info_row + 1, column=1, value='生成用户:')
        worksheet.cell(row=info_row + 1, column=2, value=user_profile.user.get_full_name() or user_profile.user.username)

        if filters:
            if filters.get('start_date'):
                worksheet.cell(row=info_row + 2, column=1, value='开始日期:')
                worksheet.cell(row=info_row + 2, column=2, value=filters['start_date'])
            if filters.get('end_date'):
                worksheet.cell(row=info_row + 3, column=1, value='结束日期:')
                worksheet.cell(row=info_row + 3, column=2, value=filters['end_date'])

    except Exception as e:
        logger.error(f'创建概览工作表失败: {str(e)}')


def _create_time_series_sheet(worksheet, chart_data, user_profile):
    """创建时间序列分析工作表"""
    try:
        from openpyxl.styles import Font, Alignment, PatternFill
        from openpyxl.chart import LineChart, Reference

        # 设置标题
        worksheet['A1'] = '时间序列分析'
        worksheet['A1'].font = Font(size=14, bold=True)
        worksheet['A1'].alignment = Alignment(horizontal='center')
        worksheet.merge_cells('A1:D1')

        # 表头
        headers = ['时间', '总报告数', '严重事件数', '死亡事件数']
        for col, header in enumerate(headers, 1):
            cell = worksheet.cell(row=3, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

        # 数据
        time_series_data = chart_data if isinstance(chart_data, list) else chart_data.get('data', [])
        for i, item in enumerate(time_series_data, 4):
            worksheet.cell(row=i, column=1, value=item.get('period', ''))
            worksheet.cell(row=i, column=2, value=item.get('count', 0))
            worksheet.cell(row=i, column=3, value=item.get('serious_count', 0))
            worksheet.cell(row=i, column=4, value=item.get('death_count', 0))

        # 创建图表
        if len(time_series_data) > 1:
            chart = LineChart()
            chart.title = "时间序列趋势图"
            chart.style = 13
            chart.x_axis.title = '时间'
            chart.y_axis.title = '数量'

            data = Reference(worksheet, min_col=2, min_row=3, max_col=4, max_row=len(time_series_data) + 3)
            cats = Reference(worksheet, min_col=1, min_row=4, max_row=len(time_series_data) + 3)
            chart.add_data(data, titles_from_data=True)
            chart.set_categories(cats)

            worksheet.add_chart(chart, "F3")

        # 调整列宽
        for col in ['A', 'B', 'C', 'D']:
            worksheet.column_dimensions[col].width = 15

    except Exception as e:
        logger.error(f'创建时间序列工作表失败: {str(e)}')


def _create_cross_dimension_sheet(worksheet, chart_data, user_profile):
    """创建交叉维度分析工作表"""
    try:
        from openpyxl.styles import Font, Alignment, PatternFill
        from openpyxl.chart import BarChart, Reference

        # 设置标题
        worksheet['A1'] = '交叉维度分析'
        worksheet['A1'].font = Font(size=14, bold=True)
        worksheet['A1'].alignment = Alignment(horizontal='center')
        worksheet.merge_cells('A1:D1')

        # 获取交叉数据
        cross_data = chart_data.get('cross_data', []) if isinstance(chart_data, dict) else []

        if cross_data:
            # 表头
            headers = ['维度1值', '维度2值', '报告数量', '占比']
            for col, header in enumerate(headers, 1):
                cell = worksheet.cell(row=3, column=col, value=header)
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

            # 数据
            total_records = chart_data.get('total_records', sum(item.get('count', 0) for item in cross_data))
            for i, item in enumerate(cross_data, 4):
                count = item.get('count', 0)
                percentage = (count / max(total_records, 1)) * 100

                worksheet.cell(row=i, column=1, value=item.get('dimension1_value', ''))
                worksheet.cell(row=i, column=2, value=item.get('dimension2_value', ''))
                worksheet.cell(row=i, column=3, value=count)
                worksheet.cell(row=i, column=4, value=f'{percentage:.1f}%')

            # 创建图表
            if len(cross_data) > 1:
                chart = BarChart()
                chart.title = "交叉维度分布图"
                chart.style = 10
                chart.x_axis.title = '维度组合'
                chart.y_axis.title = '报告数量'

                data = Reference(worksheet, min_col=3, min_row=3, max_col=3, max_row=len(cross_data) + 3)
                cats = Reference(worksheet, min_col=1, min_row=4, max_row=len(cross_data) + 3)
                chart.add_data(data, titles_from_data=True)
                chart.set_categories(cats)

                worksheet.add_chart(chart, "F3")

        # 调整列宽
        for col in ['A', 'B', 'C', 'D']:
            worksheet.column_dimensions[col].width = 15

    except Exception as e:
        logger.error(f'创建交叉维度工作表失败: {str(e)}')


def _create_device_stats_sheet(worksheet, chart_data, user_profile):
    """创建器械统计工作表"""
    try:
        from openpyxl.styles import Font, Alignment, PatternFill
        from openpyxl.chart import PieChart, Reference

        # 设置标题
        worksheet['A1'] = '器械统计分析'
        worksheet['A1'].font = Font(size=14, bold=True)
        worksheet['A1'].alignment = Alignment(horizontal='center')
        worksheet.merge_cells('A1:E1')

        # 获取器械数据
        device_data = chart_data if isinstance(chart_data, list) else chart_data.get('data', [])

        if device_data:
            # 表头
            headers = ['器械名称', '制造商', '报告数量', '风险比例', '最近报告时间']
            for col, header in enumerate(headers, 1):
                cell = worksheet.cell(row=3, column=col, value=header)
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

            # 数据
            for i, item in enumerate(device_data, 4):
                worksheet.cell(row=i, column=1, value=item.get('device_name', ''))
                worksheet.cell(row=i, column=2, value=item.get('manufacturer', ''))
                worksheet.cell(row=i, column=3, value=item.get('count', 0))
                worksheet.cell(row=i, column=4, value=f"{item.get('risk_ratio', 0):.1f}%")
                worksheet.cell(row=i, column=5, value=item.get('last_report_date', ''))

            # 创建图表
            if len(device_data) > 1:
                chart = PieChart()
                chart.title = "器械报告分布图"

                data = Reference(worksheet, min_col=3, min_row=4, max_row=min(len(device_data) + 3, 13))  # 最多显示10个
                labels = Reference(worksheet, min_col=1, min_row=4, max_row=min(len(device_data) + 3, 13))
                chart.add_data(data)
                chart.set_categories(labels)

                worksheet.add_chart(chart, "G3")

        # 调整列宽
        for col in ['A', 'B', 'C', 'D', 'E']:
            worksheet.column_dimensions[col].width = 15

    except Exception as e:
        logger.error(f'创建器械统计工作表失败: {str(e)}')


def _create_department_stats_sheet(worksheet, chart_data, user_profile):
    """创建科室统计工作表"""
    try:
        from openpyxl.styles import Font, Alignment, PatternFill
        from openpyxl.chart import BarChart, Reference

        # 设置标题
        worksheet['A1'] = '科室统计分析'
        worksheet['A1'].font = Font(size=14, bold=True)
        worksheet['A1'].alignment = Alignment(horizontal='center')
        worksheet.merge_cells('A1:F1')

        # 获取科室数据
        dept_data = chart_data if isinstance(chart_data, list) else chart_data.get('data', [])

        if dept_data:
            # 表头
            headers = ['科室名称', '报告数量', '严重事件数', '平均处理时间(天)', '批准率(%)', '效率评级']
            for col, header in enumerate(headers, 1):
                cell = worksheet.cell(row=3, column=col, value=header)
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

            # 数据
            for i, item in enumerate(dept_data, 4):
                avg_time = item.get('avg_process_time', 0)
                approval_rate = item.get('approval_rate', 0)

                # 简单的效率评级
                if approval_rate >= 90 and avg_time <= 3:
                    efficiency = '优秀'
                elif approval_rate >= 80 and avg_time <= 5:
                    efficiency = '良好'
                elif approval_rate >= 70 and avg_time <= 7:
                    efficiency = '一般'
                else:
                    efficiency = '待改进'

                worksheet.cell(row=i, column=1, value=item.get('department_name', ''))
                worksheet.cell(row=i, column=2, value=item.get('count', 0))
                worksheet.cell(row=i, column=3, value=item.get('serious_count', 0))
                worksheet.cell(row=i, column=4, value=f'{avg_time:.1f}')
                worksheet.cell(row=i, column=5, value=f'{approval_rate:.1f}')
                worksheet.cell(row=i, column=6, value=efficiency)

            # 创建图表
            if len(dept_data) > 1:
                chart = BarChart()
                chart.title = "科室报告数量对比"
                chart.style = 10
                chart.x_axis.title = '科室'
                chart.y_axis.title = '报告数量'

                data = Reference(worksheet, min_col=2, min_row=3, max_col=3, max_row=len(dept_data) + 3)
                cats = Reference(worksheet, min_col=1, min_row=4, max_row=len(dept_data) + 3)
                chart.add_data(data, titles_from_data=True)
                chart.set_categories(cats)

                worksheet.add_chart(chart, "H3")

        # 调整列宽
        for col in ['A', 'B', 'C', 'D', 'E', 'F']:
            worksheet.column_dimensions[col].width = 15

    except Exception as e:
        logger.error(f'创建科室统计工作表失败: {str(e)}')


def _create_trend_analysis_sheet(worksheet, chart_data, user_profile):
    """创建趋势分析工作表"""
    try:
        from openpyxl.styles import Font, Alignment, PatternFill
        from openpyxl.chart import LineChart, Reference

        # 设置标题
        worksheet['A1'] = '趋势分析'
        worksheet['A1'].font = Font(size=14, bold=True)
        worksheet['A1'].alignment = Alignment(horizontal='center')
        worksheet.merge_cells('A1:D1')

        # 获取趋势数据
        trend_data = chart_data.get('trend_data', []) if isinstance(chart_data, dict) else []

        if trend_data:
            # 表头
            headers = ['时间', '数值', '变化量', '变化率(%)']
            for col, header in enumerate(headers, 1):
                cell = worksheet.cell(row=3, column=col, value=header)
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

            # 数据
            for i, item in enumerate(trend_data, 4):
                prev_value = trend_data[i-1].get('value', 0) if i > 0 else 0
                current_value = item.get('value', 0)
                change = current_value - prev_value if i > 0 else 0
                change_rate = (change / max(prev_value, 1)) * 100 if i > 0 and prev_value > 0 else 0

                worksheet.cell(row=i, column=1, value=item.get('period', ''))
                worksheet.cell(row=i, column=2, value=current_value)
                worksheet.cell(row=i, column=3, value=change if i > 0 else 0)
                worksheet.cell(row=i, column=4, value=f'{change_rate:.1f}' if i > 0 else '0.0')

            # 创建图表
            if len(trend_data) > 1:
                chart = LineChart()
                chart.title = "趋势变化图"
                chart.style = 13
                chart.x_axis.title = '时间'
                chart.y_axis.title = '数值'

                data = Reference(worksheet, min_col=2, min_row=3, max_col=2, max_row=len(trend_data) + 3)
                cats = Reference(worksheet, min_col=1, min_row=4, max_row=len(trend_data) + 3)
                chart.add_data(data, titles_from_data=True)
                chart.set_categories(cats)

                worksheet.add_chart(chart, "F3")

        # 添加趋势分析结论
        if isinstance(chart_data, dict):
            conclusion_row = len(trend_data) + 6
            worksheet.cell(row=conclusion_row, column=1, value='趋势分析结论:')
            worksheet.cell(row=conclusion_row, column=1).font = Font(bold=True)

            growth_rate = chart_data.get('growth_rate', 0)
            trend_direction = chart_data.get('trend_direction', 'stable')
            analysis = chart_data.get('analysis', '暂无分析')

            worksheet.cell(row=conclusion_row + 1, column=1, value=f'增长率: {growth_rate:.1f}%')
            worksheet.cell(row=conclusion_row + 2, column=1, value=f'趋势方向: {trend_direction}')
            worksheet.cell(row=conclusion_row + 3, column=1, value=f'分析结论: {analysis}')
            worksheet.merge_cells(f'A{conclusion_row + 3}:D{conclusion_row + 3}')

        # 调整列宽
        for col in ['A', 'B', 'C', 'D']:
            worksheet.column_dimensions[col].width = 15

    except Exception as e:
        logger.error(f'创建趋势分析工作表失败: {str(e)}')


def _create_comprehensive_sheet(worksheet, chart_data, user_profile):
    """创建综合分析工作表"""
    try:
        from openpyxl.styles import Font, Alignment, PatternFill

        # 设置标题
        worksheet['A1'] = '综合分析报告'
        worksheet['A1'].font = Font(size=14, bold=True)
        worksheet['A1'].alignment = Alignment(horizontal='center')
        worksheet.merge_cells('A1:D1')

        row = 3

        # 遍历所有图表数据
        for chart_type, data in chart_data.items():
            if not data:
                continue

            # 添加分析类型标题
            worksheet.cell(row=row, column=1, value=_get_analysis_type_name(chart_type))
            worksheet.cell(row=row, column=1).font = Font(size=12, bold=True)
            row += 1

            # 根据数据类型添加摘要信息
            if chart_type == 'time_series' and isinstance(data, list):
                total_reports = sum(item.get('count', 0) for item in data)
                worksheet.cell(row=row, column=1, value=f'时间段内总报告数: {total_reports}')
                row += 1

            elif chart_type == 'device_stats' and isinstance(data, list):
                top_device = data[0] if data else {}
                worksheet.cell(row=row, column=1, value=f'报告最多的器械: {top_device.get("device_name", "无")} ({top_device.get("count", 0)}次)')
                row += 1

            elif chart_type == 'department_stats' and isinstance(data, list):
                top_dept = data[0] if data else {}
                worksheet.cell(row=row, column=1, value=f'报告最多的科室: {top_dept.get("department_name", "无")} ({top_dept.get("count", 0)}次)')
                row += 1

            row += 2  # 添加间距

        # 调整列宽
        worksheet.column_dimensions['A'].width = 30

    except Exception as e:
        logger.error(f'创建综合分析工作表失败: {str(e)}')


# PDF辅助函数

def _get_chinese_font_path():
    """获取中文字体路径"""
    import platform
    import os

    system = platform.system()

    if system == "Windows":
        # Windows系统字体路径
        font_paths = [
            "C:/Windows/Fonts/simhei.ttf",
            "C:/Windows/Fonts/simsun.ttc",
            "C:/Windows/Fonts/msyh.ttc"
        ]
    elif system == "Darwin":  # macOS
        font_paths = [
            "/System/Library/Fonts/PingFang.ttc",
            "/System/Library/Fonts/STHeiti Light.ttc"
        ]
    else:  # Linux
        font_paths = [
            "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc",
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"
        ]

    for path in font_paths:
        if os.path.exists(path):
            return path

    return None


def _get_analysis_type_name(analysis_type):
    """获取分析类型的中文名称"""
    type_names = {
        'summary': '统计概览',
        'time_series': '时间序列分析',
        'cross_dimension': '交叉维度分析',
        'device_stats': '器械统计分析',
        'department_stats': '科室统计分析',
        'trend_analysis': '趋势分析'
    }
    return type_names.get(analysis_type, analysis_type)


def _add_summary_to_pdf(story, user_profile, normal_style, chinese_font):
    """添加统计概览到PDF"""
    try:
        from reportlab.platypus import Table, TableStyle
        from reportlab.lib import colors
        from .selectors import report_statistics

        # 获取统计数据
        stats = report_statistics(user_profile=user_profile)

        # 创建统计表格
        stats_data = [
            ['统计项目', '数值', '占比'],
            ['总报告数', str(stats.get('total_count', 0)), '100%'],
            ['已批准', str(stats.get('approved_count', 0)), f"{(stats.get('approved_count', 0) / max(stats.get('total_count', 1), 1) * 100):.1f}%"],
            ['待审核', str(stats.get('submitted_count', 0)), f"{(stats.get('submitted_count', 0) / max(stats.get('total_count', 1), 1) * 100):.1f}%"],
            ['草稿', str(stats.get('draft_count', 0)), f"{(stats.get('draft_count', 0) / max(stats.get('total_count', 1), 1) * 100):.1f}%"],
            ['严重事件', str(stats.get('serious_injury_count', 0)), f"{(stats.get('serious_injury_count', 0) / max(stats.get('total_count', 1), 1) * 100):.1f}%"],
            ['死亡事件', str(stats.get('death_count', 0)), f"{(stats.get('death_count', 0) / max(stats.get('total_count', 1), 1) * 100):.1f}%"],
        ]

        stats_table = Table(stats_data, colWidths=[2*inch, 1*inch, 1*inch])
        stats_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, -1), chinese_font),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))

        story.append(stats_table)

    except Exception as e:
        logger.error(f'添加PDF统计概览失败: {str(e)}')


def _add_time_series_to_pdf(story, chart_data, heading_style, normal_style, chinese_font):
    """添加时间序列分析到PDF"""
    try:
        from reportlab.platypus import Paragraph, Table, TableStyle, Spacer
        from reportlab.lib import colors

        story.append(Paragraph('时间序列分析', heading_style))

        time_series_data = chart_data if isinstance(chart_data, list) else chart_data.get('data', [])

        if time_series_data:
            # 创建数据表格
            table_data = [['时间', '总报告数', '严重事件数', '死亡事件数']]
            for item in time_series_data:
                table_data.append([
                    item.get('period', ''),
                    str(item.get('count', 0)),
                    str(item.get('serious_count', 0)),
                    str(item.get('death_count', 0))
                ])

            data_table = Table(table_data, colWidths=[1.5*inch, 1*inch, 1*inch, 1*inch])
            data_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
                ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, -1), chinese_font),
                ('FONTSIZE', (0, 0), (-1, -1), 9),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(data_table)
            story.append(Spacer(1, 12))

            # 添加趋势分析
            total_reports = sum(item.get('count', 0) for item in time_series_data)
            avg_reports = total_reports / len(time_series_data) if time_series_data else 0

            analysis_text = f"时间段内总报告数: {total_reports}，平均每期报告数: {avg_reports:.1f}"
            story.append(Paragraph(analysis_text, normal_style))
        else:
            story.append(Paragraph('暂无时间序列数据', normal_style))

        story.append(Spacer(1, 20))

    except Exception as e:
        logger.error(f'添加PDF时间序列分析失败: {str(e)}')


def _add_cross_dimension_to_pdf(story, chart_data, heading_style, normal_style, chinese_font):
    """添加交叉维度分析到PDF"""
    try:
        from reportlab.platypus import Paragraph, Table, TableStyle, Spacer
        from reportlab.lib import colors

        story.append(Paragraph('交叉维度分析', heading_style))

        cross_data = chart_data.get('cross_data', []) if isinstance(chart_data, dict) else []

        if cross_data:
            # 创建数据表格
            table_data = [['维度1值', '维度2值', '报告数量', '占比']]
            total_records = chart_data.get('total_records', sum(item.get('count', 0) for item in cross_data))

            for item in cross_data[:10]:  # 只显示前10个
                count = item.get('count', 0)
                percentage = (count / max(total_records, 1)) * 100
                table_data.append([
                    item.get('dimension1_value', ''),
                    item.get('dimension2_value', ''),
                    str(count),
                    f'{percentage:.1f}%'
                ])

            data_table = Table(table_data, colWidths=[1.5*inch, 1.5*inch, 1*inch, 1*inch])
            data_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
                ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, -1), chinese_font),
                ('FONTSIZE', (0, 0), (-1, -1), 9),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(data_table)
            story.append(Spacer(1, 12))

            # 添加分析结论
            top_combination = cross_data[0] if cross_data else {}
            analysis_text = f"最常见的组合: {top_combination.get('dimension1_value', '')} - {top_combination.get('dimension2_value', '')} ({top_combination.get('count', 0)}次)"
            story.append(Paragraph(analysis_text, normal_style))
        else:
            story.append(Paragraph('暂无交叉维度数据', normal_style))

        story.append(Spacer(1, 20))

    except Exception as e:
        logger.error(f'添加PDF交叉维度分析失败: {str(e)}')


def _add_device_stats_to_pdf(story, chart_data, heading_style, normal_style, chinese_font):
    """添加器械统计分析到PDF"""
    try:
        from reportlab.platypus import Paragraph, Table, TableStyle, Spacer
        from reportlab.lib import colors

        story.append(Paragraph('器械统计分析', heading_style))

        device_data = chart_data if isinstance(chart_data, list) else chart_data.get('data', [])

        if device_data:
            # 创建数据表格
            table_data = [['器械名称', '制造商', '报告数量', '风险比例']]
            for item in device_data[:10]:  # 只显示前10个
                table_data.append([
                    item.get('device_name', ''),
                    item.get('manufacturer', ''),
                    str(item.get('count', 0)),
                    f"{item.get('risk_ratio', 0):.1f}%"
                ])

            data_table = Table(table_data, colWidths=[2*inch, 1.5*inch, 1*inch, 1*inch])
            data_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
                ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, -1), chinese_font),
                ('FONTSIZE', (0, 0), (-1, -1), 9),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(data_table)
            story.append(Spacer(1, 12))

            # 添加分析结论
            top_device = device_data[0] if device_data else {}
            total_reports = sum(item.get('count', 0) for item in device_data)
            analysis_text = f"报告最多的器械: {top_device.get('device_name', '')} ({top_device.get('count', 0)}次，占{(top_device.get('count', 0) / max(total_reports, 1) * 100):.1f}%)"
            story.append(Paragraph(analysis_text, normal_style))
        else:
            story.append(Paragraph('暂无器械统计数据', normal_style))

        story.append(Spacer(1, 20))

    except Exception as e:
        logger.error(f'添加PDF器械统计分析失败: {str(e)}')


def _add_department_stats_to_pdf(story, chart_data, heading_style, normal_style, chinese_font):
    """添加科室统计分析到PDF"""
    try:
        from reportlab.platypus import Paragraph, Table, TableStyle, Spacer
        from reportlab.lib import colors

        story.append(Paragraph('科室统计分析', heading_style))

        dept_data = chart_data if isinstance(chart_data, list) else chart_data.get('data', [])

        if dept_data:
            # 创建数据表格
            table_data = [['科室名称', '报告数量', '严重事件数', '平均处理时间', '批准率']]
            for item in dept_data:
                table_data.append([
                    item.get('department_name', ''),
                    str(item.get('count', 0)),
                    str(item.get('serious_count', 0)),
                    f"{item.get('avg_process_time', 0):.1f}天",
                    f"{item.get('approval_rate', 0):.1f}%"
                ])

            data_table = Table(table_data, colWidths=[2*inch, 1*inch, 1*inch, 1.2*inch, 1*inch])
            data_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
                ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, -1), chinese_font),
                ('FONTSIZE', (0, 0), (-1, -1), 9),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(data_table)
            story.append(Spacer(1, 12))

            # 添加分析结论
            top_dept = dept_data[0] if dept_data else {}
            total_reports = sum(item.get('count', 0) for item in dept_data)
            analysis_text = f"报告最多的科室: {top_dept.get('department_name', '')} ({top_dept.get('count', 0)}次，占{(top_dept.get('count', 0) / max(total_reports, 1) * 100):.1f}%)"
            story.append(Paragraph(analysis_text, normal_style))
        else:
            story.append(Paragraph('暂无科室统计数据', normal_style))

        story.append(Spacer(1, 20))

    except Exception as e:
        logger.error(f'添加PDF科室统计分析失败: {str(e)}')


def _add_trend_analysis_to_pdf(story, chart_data, heading_style, normal_style, chinese_font):
    """添加趋势分析到PDF"""
    try:
        from reportlab.platypus import Paragraph, Table, TableStyle, Spacer
        from reportlab.lib import colors

        story.append(Paragraph('趋势分析', heading_style))

        trend_data = chart_data.get('trend_data', []) if isinstance(chart_data, dict) else []

        if trend_data:
            # 创建数据表格
            table_data = [['时间', '数值', '变化量', '变化率']]
            for i, item in enumerate(trend_data):
                prev_value = trend_data[i-1].get('value', 0) if i > 0 else 0
                current_value = item.get('value', 0)
                change = current_value - prev_value if i > 0 else 0
                change_rate = (change / max(prev_value, 1)) * 100 if i > 0 and prev_value > 0 else 0

                table_data.append([
                    item.get('period', ''),
                    str(current_value),
                    str(change) if i > 0 else '0',
                    f'{change_rate:.1f}%' if i > 0 else '0.0%'
                ])

            data_table = Table(table_data, colWidths=[1.5*inch, 1*inch, 1*inch, 1*inch])
            data_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
                ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, -1), chinese_font),
                ('FONTSIZE', (0, 0), (-1, -1), 9),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(data_table)
            story.append(Spacer(1, 12))

            # 添加趋势分析结论
            if isinstance(chart_data, dict):
                growth_rate = chart_data.get('growth_rate', 0)
                trend_direction = chart_data.get('trend_direction', 'stable')
                analysis = chart_data.get('analysis', '暂无分析')

                conclusion_text = f"增长率: {growth_rate:.1f}%，趋势方向: {trend_direction}。{analysis}"
                story.append(Paragraph(conclusion_text, normal_style))
        else:
            story.append(Paragraph('暂无趋势分析数据', normal_style))

        story.append(Spacer(1, 20))

    except Exception as e:
        logger.error(f'添加PDF趋势分析失败: {str(e)}')
