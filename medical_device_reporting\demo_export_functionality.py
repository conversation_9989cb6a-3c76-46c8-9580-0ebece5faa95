#!/usr/bin/env python
"""
数据导出功能演示
Export Functionality Demo
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from django.contrib.auth.models import User
from django.utils import timezone
from datetime import datetime, timedelta
from apps.users.models import UserProfile, Department
from apps.reports.models import AdverseEventReport
from apps.reports.services import export_statistics_report

def demo_export_functionality():
    """演示数据导出功能"""
    print("=== 🚀 数据导出功能演示 ===")
    
    try:
        # 创建演示用户
        import uuid
        unique_id = str(uuid.uuid4())[:8]
        
        # 创建科室
        department = Department.objects.create(
            code=f'DEMO_{unique_id[:4]}',
            name=f'演示科室_{unique_id[:4]}',
            is_active=True,
            created_by_id=1
        )
        
        # 创建管理员用户
        admin_user = User.objects.create_user(
            username=f'demo_admin_{unique_id}', 
            email='<EMAIL>',
            first_name='演示',
            last_name='管理员'
        )
        admin_profile = UserProfile.objects.create(
            user=admin_user,
            account_number=f'{hash(unique_id) % 9000 + 1000:04d}',
            department=department,
            role='admin',
            created_by=admin_user
        )
        
        print(f"✅ 创建演示用户: {admin_profile.user.get_full_name()} (账号: {admin_profile.account_number})")
        
        # 创建演示数据
        print("\n📊 创建演示数据...")
        base_date = timezone.now() - timedelta(days=180)
        demo_reports = []
        
        # 创建多样化的测试数据
        devices = [
            ('心脏起搏器', 'REG001', '美敦力公司'),
            ('人工关节', 'REG002', '强生公司'),
            ('血管支架', 'REG003', '波士顿科学'),
            ('胰岛素泵', 'REG004', '美敦力公司'),
            ('人工晶体', 'REG005', '爱尔康公司'),
        ]
        
        injury_levels = ['other', 'other', 'other', 'serious_injury', 'death']
        statuses = ['approved', 'approved', 'submitted', 'approved', 'draft']
        
        for i in range(25):
            device_info = devices[i % len(devices)]
            report_date = base_date + timedelta(days=i * 7)
            
            report = AdverseEventReport.objects.create(
                reporter=admin_profile,
                department=department,
                reporter_phone='13800138000',
                device_name=device_info[0],
                registration_number=device_info[1],
                manufacturer=device_info[2],
                product_number=f'PROD{i:03d}',
                batch_number=f'BATCH{i:03d}',
                event_date=report_date,
                injury_level=injury_levels[i % len(injury_levels)],
                injury_description=f'演示伤害表现{i}' if injury_levels[i % len(injury_levels)] != 'other' else '',
                event_description=f'演示事件描述{i}：设备在使用过程中出现异常',
                status=statuses[i % len(statuses)],
                created_at=report_date,
                created_by=admin_user
            )
            demo_reports.append(report)
        
        print(f"   ✅ 创建了{len(demo_reports)}个演示报告")
        print(f"   📈 包含{len(set(r.device_name for r in demo_reports))}种不同器械")
        print(f"   ⚠️ 包含{len([r for r in demo_reports if r.injury_level == 'serious_injury'])}个严重事件")
        print(f"   💀 包含{len([r for r in demo_reports if r.injury_level == 'death'])}个死亡事件")
        
        # 演示1: Excel导出
        print("\n📋 演示1: Excel格式导出")
        try:
            excel_response = export_statistics_report(
                user_profile=admin_profile,
                format='excel',
                analysis_type='summary'
            )
            
            print(f"   ✅ Excel导出成功")
            print(f"   📄 文件大小: {len(excel_response.content)} bytes")
            print(f"   🗂️ Content-Type: {excel_response.get('Content-Type')}")
            
            # 保存文件到本地（可选）
            filename = f'demo_export_summary_{unique_id}.xlsx'
            with open(filename, 'wb') as f:
                f.write(excel_response.content)
            print(f"   💾 文件已保存: {filename}")
            
        except Exception as e:
            print(f"   ❌ Excel导出失败: {e}")
        
        # 演示2: PDF导出
        print("\n📑 演示2: PDF格式导出")
        try:
            pdf_response = export_statistics_report(
                user_profile=admin_profile,
                format='pdf',
                analysis_type='summary'
            )
            
            print(f"   ✅ PDF导出成功")
            print(f"   📄 文件大小: {len(pdf_response.content)} bytes")
            print(f"   🗂️ Content-Type: {pdf_response.get('Content-Type')}")
            
            # 保存文件到本地（可选）
            filename = f'demo_export_summary_{unique_id}.pdf'
            with open(filename, 'wb') as f:
                f.write(pdf_response.content)
            print(f"   💾 文件已保存: {filename}")
            
        except Exception as e:
            print(f"   ❌ PDF导出失败: {e}")
        
        # 演示3: 时间序列分析导出
        print("\n📈 演示3: 时间序列分析导出")
        try:
            # 获取时间序列数据
            from apps.reports.selectors import get_time_series_statistics
            
            time_series_data = get_time_series_statistics(
                user_profile=admin_profile,
                start_date=base_date,
                end_date=timezone.now(),
                granularity='month'
            )
            
            excel_response = export_statistics_report(
                user_profile=admin_profile,
                format='excel',
                chart_data=time_series_data,
                analysis_type='time_series'
            )
            
            print(f"   ✅ 时间序列Excel导出成功")
            print(f"   📄 文件大小: {len(excel_response.content)} bytes")
            
            filename = f'demo_export_timeseries_{unique_id}.xlsx'
            with open(filename, 'wb') as f:
                f.write(excel_response.content)
            print(f"   💾 文件已保存: {filename}")
            
        except Exception as e:
            print(f"   ❌ 时间序列导出失败: {e}")
        
        # 演示4: 器械统计分析导出
        print("\n🔧 演示4: 器械统计分析导出")
        try:
            from apps.reports.selectors import get_device_statistics
            
            device_data = get_device_statistics(
                user_profile=admin_profile,
                limit=10,
                include_manufacturer=True
            )
            
            excel_response = export_statistics_report(
                user_profile=admin_profile,
                format='excel',
                chart_data=device_data,
                analysis_type='device_stats'
            )
            
            print(f"   ✅ 器械统计Excel导出成功")
            print(f"   📄 文件大小: {len(excel_response.content)} bytes")
            
            filename = f'demo_export_devices_{unique_id}.xlsx'
            with open(filename, 'wb') as f:
                f.write(excel_response.content)
            print(f"   💾 文件已保存: {filename}")
            
        except Exception as e:
            print(f"   ❌ 器械统计导出失败: {e}")
        
        # 演示5: 综合分析导出
        print("\n🎯 演示5: 综合分析导出")
        try:
            # 获取多种数据
            comprehensive_data = {}
            
            try:
                comprehensive_data['time_series'] = get_time_series_statistics(
                    user_profile=admin_profile,
                    granularity='month'
                )
            except:
                pass
            
            try:
                comprehensive_data['device_stats'] = get_device_statistics(
                    user_profile=admin_profile,
                    limit=10
                )
            except:
                pass
            
            excel_response = export_statistics_report(
                user_profile=admin_profile,
                format='excel',
                chart_data=comprehensive_data,
                analysis_type='comprehensive'
            )
            
            print(f"   ✅ 综合分析Excel导出成功")
            print(f"   📄 文件大小: {len(excel_response.content)} bytes")
            print(f"   📊 包含{len(comprehensive_data)}种分析类型")
            
            filename = f'demo_export_comprehensive_{unique_id}.xlsx'
            with open(filename, 'wb') as f:
                f.write(excel_response.content)
            print(f"   💾 文件已保存: {filename}")
            
        except Exception as e:
            print(f"   ❌ 综合分析导出失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理演示数据
        try:
            AdverseEventReport.objects.filter(created_by=admin_user).delete()
            UserProfile.objects.filter(created_by=admin_user).delete()
            Department.objects.filter(code__contains=unique_id[:4]).delete()
            User.objects.filter(username__contains=unique_id).delete()
            print(f"\n🧹 演示数据已清理")
        except:
            pass

def main():
    """主演示函数"""
    print("🎬 开始数据导出功能演示...")
    
    success = demo_export_functionality()
    
    print("\n=== 🎉 演示总结 ===")
    if success:
        print("✅ 数据导出功能演示成功！")
        print("\n📋 功能特性总结:")
        print("🔹 支持Excel和PDF两种格式导出")
        print("🔹 支持多种分析类型：统计概览、时间序列、器械统计等")
        print("🔹 支持灵活的参数配置和筛选条件")
        print("🔹 生成的文件包含完整的数据表格和图表")
        print("🔹 支持中文字体和本地化内容")
        print("🔹 完善的错误处理和日志记录")
        print("\n🚀 导出功能已完全实现并可投入使用！")
        return True
    else:
        print("❌ 演示失败，请检查错误信息")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
