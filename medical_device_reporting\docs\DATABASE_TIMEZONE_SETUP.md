# MySQL时区配置指导
# MySQL Timezone Configuration Guide

## 问题描述

当使用Django的时间序列统计功能时，可能会遇到以下错误：

```
Database returned an invalid datetime value. Are time zone definitions for your database installed?
```

这个错误通常发生在使用Django的时间截断函数（如TruncYear、TruncMonth等）时，MySQL数据库缺少时区表数据。

## 解决方案

### 方案一：安装MySQL时区表（推荐）

#### 1. Linux/macOS系统

在命令行中执行以下命令：

```bash
# 方法1：使用系统时区数据
mysql_tzinfo_to_sql /usr/share/zoneinfo | mysql -u root -p mysql

# 方法2：如果上述路径不存在，尝试其他常见路径
mysql_tzinfo_to_sql /usr/share/lib/zoneinfo | mysql -u root -p mysql
```

#### 2. Windows系统

Windows系统通常没有内置的时区数据文件，可以：

**选项A：下载时区数据**
```bash
# 下载MySQL官方时区数据
# 访问：https://dev.mysql.com/downloads/timezones.html
# 下载对应版本的时区数据SQL文件并导入
mysql -u root -p mysql < timezone_data.sql
```

**选项B：使用在线时区数据**
```sql
-- 连接到MySQL并执行
mysql -u root -p

-- 在MySQL命令行中执行
SOURCE timezone_data.sql;
```

#### 3. Docker环境

如果使用Docker运行MySQL：

```bash
# 进入MySQL容器
docker exec -it mysql_container_name bash

# 在容器内执行
mysql_tzinfo_to_sql /usr/share/zoneinfo | mysql -u root -p mysql
```

#### 4. 验证安装

安装完成后，验证时区表是否正确加载：

```sql
-- 连接到MySQL
mysql -u root -p

-- 检查时区表
USE mysql;
SELECT COUNT(*) FROM time_zone;
SELECT COUNT(*) FROM time_zone_name;

-- 应该返回非零值
-- time_zone表通常有几百行
-- time_zone_name表通常有几千行
```

### 方案二：代码层面兼容性处理

如果无法安装时区表，可以修改代码以避免使用时区相关的函数。

#### 修改Django设置

在`settings.py`中：

```python
# 禁用时区支持（不推荐，会影响其他功能）
USE_TZ = False

# 或者设置数据库时区
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        # ... 其他配置
        'OPTIONS': {
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
            # 不设置时区相关选项
        },
    }
}
```

## 常见问题排除

### 1. 权限问题

如果遇到权限错误：

```bash
# 确保MySQL用户有足够权限
mysql -u root -p
GRANT ALL PRIVILEGES ON mysql.* TO 'your_user'@'localhost';
FLUSH PRIVILEGES;
```

### 2. 时区数据路径问题

不同系统的时区数据路径：

- **Ubuntu/Debian**: `/usr/share/zoneinfo`
- **CentOS/RHEL**: `/usr/share/zoneinfo`
- **macOS**: `/usr/share/zoneinfo`
- **FreeBSD**: `/usr/share/zoneinfo`

### 3. MySQL版本兼容性

- **MySQL 5.7+**: 支持所有时区功能
- **MySQL 8.0+**: 推荐版本，时区支持更完善
- **MariaDB**: 使用相同的时区表结构

### 4. 云服务提供商

#### AWS RDS
```sql
-- AWS RDS通常已预装时区数据
-- 如果没有，联系AWS支持
CALL mysql.rds_load_timezone();
```

#### Google Cloud SQL
```sql
-- Google Cloud SQL通常已包含时区数据
-- 检查是否可用
SELECT COUNT(*) FROM mysql.time_zone;
```

#### Azure Database for MySQL
```sql
-- Azure通常已包含时区数据
-- 如果遇到问题，检查服务器参数
```

## 验证修复效果

安装时区表后，测试Django应用：

```python
# 在Django shell中测试
python manage.py shell

# 测试时区功能
from django.utils import timezone
from django.db.models.functions import TruncMonth
from apps.reports.models import AdverseEventReport

# 这应该不再报错
reports = AdverseEventReport.objects.annotate(
    month=TruncMonth('created_at')
).values('month').annotate(count=Count('id'))

print(list(reports))
```

## 预防措施

1. **部署检查清单**：
   - [ ] 确认MySQL时区表已安装
   - [ ] 验证Django时区设置正确
   - [ ] 测试时间相关查询功能

2. **监控建议**：
   - 监控时区相关错误日志
   - 定期检查时区表数据完整性

3. **文档记录**：
   - 记录生产环境的时区配置
   - 维护部署步骤文档

## 自动化验证

使用环境验证脚本检查时区配置：

```bash
# 运行环境验证脚本
python scripts/verify_environment.py

# 检查特定的时区配置
python manage.py shell
>>> from scripts.verify_environment import check_mysql_timezone
>>> check_mysql_timezone()
```

## 相关链接

- [MySQL时区支持官方文档](https://dev.mysql.com/doc/refman/8.0/en/time-zone-support.html)
- [Django时区支持文档](https://docs.djangoproject.com/en/stable/topics/i18n/timezones/)
- [MySQL时区数据下载](https://dev.mysql.com/downloads/timezones.html)
- [故障排除指南](TROUBLESHOOTING_GUIDE.md)

## 技术支持

如果遇到问题，请提供以下信息：

1. MySQL版本：`SELECT VERSION();`
2. 操作系统版本
3. Django版本
4. 具体错误信息
5. 时区表状态：`SELECT COUNT(*) FROM mysql.time_zone;`
6. 环境验证脚本输出结果
