"""
不良事件上报管理权限控制
Adverse Event Reports Management Permissions for Medical Device Reporting Platform
"""

from rest_framework import permissions
from django.contrib.auth.models import User

from apps.users.permissions import (
    AdminRequiredMixin,
    DepartmentMemberOrAdminMixin,
    OwnerOrAdminMixin
)


class IsReportOwnerOrAdmin(permissions.BasePermission):
    """
    报告所有者或管理员权限
    
    允许报告创建者和管理员访问报告
    """
    
    def has_object_permission(self, request, view, obj):
        # 管理员可以访问所有报告
        if hasattr(request.user, 'profile') and request.user.profile.is_admin:
            return True
        
        # 报告创建者可以访问自己的报告
        if hasattr(obj, 'created_by') and obj.created_by == request.user:
            return True
        
        # 同科室成员可以查看（只读权限）
        if (request.method in permissions.SAFE_METHODS and 
            hasattr(request.user, 'profile') and 
            hasattr(obj, 'department') and
            request.user.profile.department == obj.department):
            return True
        
        return False


class CanSubmitReport(permissions.BasePermission):
    """
    可以提交报告权限
    
    只有科室成员或管理员可以提交报告
    """
    
    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False
        
        # 检查用户是否有有效的profile
        if not hasattr(request.user, 'profile'):
            return False
        
        profile = request.user.profile
        
        # 管理员可以提交报告
        if profile.is_admin:
            return True
        
        # 科室成员可以提交报告
        if profile.is_staff_member and profile.department and profile.department.is_active:
            return True
        
        return False


class CanReviewReport(permissions.BasePermission):
    """
    可以审核报告权限
    
    只有管理员可以审核报告
    """
    
    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False
        
        # 只有管理员可以审核报告
        return (hasattr(request.user, 'profile') and
                request.user.profile.is_admin)


class CanViewAllReports(permissions.BasePermission):
    """
    可以查看所有报告权限

    管理员可以查看所有报告，科室成员只能查看本科室报告
    """

    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False

        # 检查用户是否有profile
        if not hasattr(request.user, 'profile') or not request.user.profile:
            return False

        user_profile = request.user.profile

        # 管理员可以查看所有报告
        if user_profile.is_admin:
            return True

        # 科室成员可以查看报告（具体范围在视图中控制）
        if user_profile.is_staff_member and user_profile.department and user_profile.department.is_active:
            return True

        return False


class CanExportReports(permissions.BasePermission):
    """
    可以导出报告权限

    管理员和有权限的科室成员可以导出报告
    """

    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False

        # 检查用户是否有profile
        if not hasattr(request.user, 'profile') or not request.user.profile:
            return False

        user_profile = request.user.profile

        # 管理员可以导出报告
        if user_profile.is_admin:
            return True

        # 科室成员可以导出本科室报告
        if user_profile.is_staff_member and user_profile.department and user_profile.department.is_active:
            return True

        return False


# 权限检查函数

def check_report_edit_permission(user, report):
    """
    检查报告编辑权限

    Args:
        user: 用户对象
        report: 报告对象

    Returns:
        bool: 是否有编辑权限

    Raises:
        PermissionDenied: 权限不足
    """
    from django.core.exceptions import PermissionDenied

    if not user.is_authenticated:
        raise PermissionDenied('用户未认证')

    if not hasattr(user, 'profile') or not user.profile:
        raise PermissionDenied('用户配置文件不存在')

    user_profile = user.profile

    # 检查报告是否可以编辑
    if not report.can_edit:
        raise PermissionDenied('报告当前状态不允许编辑')

    # 管理员可以编辑任何报告
    if user_profile.is_admin:
        return True

    # 报告创建者可以编辑自己的报告
    if report.reporter == user_profile:
        return True

    raise PermissionDenied('您没有权限编辑此报告')


def check_report_submit_permission(user, report):
    """
    检查报告提交权限

    Args:
        user: 用户对象
        report: 报告对象

    Returns:
        bool: 是否有提交权限

    Raises:
        PermissionDenied: 权限不足
    """
    from django.core.exceptions import PermissionDenied

    if not user.is_authenticated:
        raise PermissionDenied('用户未认证')

    if not hasattr(user, 'profile') or not user.profile:
        raise PermissionDenied('用户配置文件不存在')

    user_profile = user.profile

    # 检查报告是否可以提交
    if not report.can_submit:
        raise PermissionDenied('报告当前状态不允许提交')

    # 管理员可以提交任何报告
    if user_profile.is_admin:
        return True

    # 报告创建者可以提交自己的报告
    if report.reporter == user_profile:
        return True

    raise PermissionDenied('您没有权限提交此报告')


def check_report_review_permission(user, report):
    """
    检查报告审核权限

    Args:
        user: 用户对象
        report: 报告对象

    Returns:
        bool: 是否有审核权限

    Raises:
        PermissionDenied: 权限不足
    """
    from django.core.exceptions import PermissionDenied

    if not user.is_authenticated:
        raise PermissionDenied('用户未认证')

    if not hasattr(user, 'profile') or not user.profile:
        raise PermissionDenied('用户配置文件不存在')

    user_profile = user.profile

    # 只有管理员可以审核报告
    if not user_profile.is_admin:
        raise PermissionDenied('只有管理员可以审核报告')

    # 检查报告是否可以审核
    if not report.can_review:
        raise PermissionDenied('报告当前状态不允许审核')

    return True
