#!/usr/bin/env python
"""
创建MySQL数据库的脚本
Create MySQL database script for Medical Device Reporting Platform
"""

import MySQLdb
import sys

def create_database():
    """创建数据库"""
    try:
        # 连接到MySQL服务器（不指定数据库）
        connection = MySQLdb.connect(
            host='localhost',
            user='root',
            password='temper0201..',
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # 创建数据库
        cursor.execute("""
            CREATE DATABASE IF NOT EXISTS medical_device_reporting_dev 
            CHARACTER SET utf8mb4 
            COLLATE utf8mb4_unicode_ci
        """)
        
        print("✅ 数据库 'medical_device_reporting_dev' 创建成功！")
        
        # 显示数据库信息
        cursor.execute("SHOW DATABASES LIKE 'medical_device_reporting_dev'")
        result = cursor.fetchone()
        if result:
            print(f"✅ 数据库已存在: {result[0]}")
        
        cursor.close()
        connection.close()
        
        return True
        
    except MySQLdb.Error as e:
        print(f"❌ 创建数据库时发生错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 发生未知错误: {e}")
        return False

if __name__ == '__main__':
    print("正在创建MySQL数据库...")
    success = create_database()
    
    if success:
        print("数据库创建完成，可以继续执行Django迁移。")
        sys.exit(0)
    else:
        print("数据库创建失败，请检查MySQL服务和配置。")
        sys.exit(1)
