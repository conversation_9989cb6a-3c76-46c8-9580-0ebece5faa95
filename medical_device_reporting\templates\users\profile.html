{% extends 'base.html' %}
{% load static %}

{% block title %}
{% if is_admin_view %}{{ target_user.get_full_name|default:target_user.username }} - 用户信息{% else %}个人信息{% endif %} - 医疗器械不良事件上报平台
{% endblock %}

{% block extra_css %}
<link href="{% static 'users/css/user_management.css' %}" rel="stylesheet">
{% endblock %}

{% block nav_items %}
<li class="nav-item">
    <a class="nav-link" href="{% url 'users:dashboard' %}">
        <i class="bi bi-house"></i>
        用户中心
    </a>
</li>
{% if user.profile.is_admin %}
<li class="nav-item">
    <a class="nav-link" href="{% url 'users:user_list' %}">
        <i class="bi bi-people"></i>
        用户管理
    </a>
</li>
<li class="nav-item">
    <a class="nav-link" href="{% url 'users:department_list' %}">
        <i class="bi bi-building"></i>
        科室管理
    </a>
</li>
{% endif %}
<li class="nav-item dropdown">
    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
        <i class="bi bi-person-circle"></i>
        {{ user.get_full_name|default:user.username }}
    </a>
    <ul class="dropdown-menu">
        <li><a class="dropdown-item" href="{% url 'users:profile' %}"><i class="bi bi-person me-2"></i>个人信息</a></li>
        <li><a class="dropdown-item" href="{% url 'users:user_settings' %}"><i class="bi bi-gear me-2"></i>个人设置</a></li>
        <li><hr class="dropdown-divider"></li>
        <li><a class="dropdown-item" href="{% url 'users:logout' %}"><i class="bi bi-box-arrow-right me-2"></i>退出登录</a></li>
    </ul>
</li>
{% endblock %}

{% block content %}
<div class="profile-container">
    <!-- 页面标题 -->
    <div class="page-header mb-4">
        <div class="row align-items-center">
            <div class="col">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'users:dashboard' %}">用户中心</a></li>
                        {% if is_admin_view %}
                        <li class="breadcrumb-item"><a href="{% url 'users:user_list' %}">用户管理</a></li>
                        <li class="breadcrumb-item active">{{ target_user.get_full_name|default:target_user.username }}</li>
                        {% else %}
                        <li class="breadcrumb-item active">个人信息</li>
                        {% endif %}
                    </ol>
                </nav>
                <h2 class="page-title">
                    <i class="bi bi-person-badge me-2"></i>
                    {% if is_admin_view %}
                        用户信息 - {{ target_user.get_full_name|default:target_user.username }}
                    {% else %}
                        个人信息
                    {% endif %}
                </h2>
                <p class="page-subtitle text-muted">
                    {% if is_admin_view %}
                        查看用户 {{ user_profile.account_number }} 的详细信息
                    {% else %}
                        查看和管理您的个人账户信息
                    {% endif %}
                </p>
            </div>
            <div class="col-auto">
                <div class="btn-group" role="group">
                    {% if is_admin_view %}
                    <a href="{% url 'users:user_list' %}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left me-2"></i>
                        返回列表
                    </a>
                    <a href="{% url 'users:user_edit' user_profile.id %}" class="btn btn-primary">
                        <i class="bi bi-pencil me-2"></i>
                        编辑用户
                    </a>
                    {% else %}
                    <a href="{% url 'users:profile_edit' %}" class="btn btn-primary">
                        <i class="bi bi-pencil me-2"></i>
                        编辑信息
                    </a>
                    <a href="{% url 'users:user_settings' %}" class="btn btn-outline-secondary">
                        <i class="bi bi-gear me-2"></i>
                        个人设置
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 基本信息卡片 -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-bottom">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-person-lines-fill me-2"></i>
                        基本信息
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">账号：</dt>
                                <dd class="col-sm-8">
                                    <span class="badge bg-primary fs-6">{{ user_profile.account_number }}</span>
                                </dd>
                                
                                <dt class="col-sm-4">用户名：</dt>
                                <dd class="col-sm-8">
                                    {% if is_admin_view %}
                                        {{ target_user.username }}
                                    {% else %}
                                        {{ user.username }}
                                    {% endif %}
                                </dd>
                                
                                <dt class="col-sm-4">姓名：</dt>
                                <dd class="col-sm-8">
                                    {% if is_admin_view %}
                                        {{ target_user.get_full_name|default:"未设置" }}
                                    {% else %}
                                        {{ user.get_full_name|default:"未设置" }}
                                    {% endif %}
                                </dd>
                                
                                <dt class="col-sm-4">邮箱：</dt>
                                <dd class="col-sm-8">
                                    {% if is_admin_view %}
                                        {% if target_user.email %}
                                            <a href="mailto:{{ target_user.email }}" class="text-decoration-none">
                                                {{ target_user.email }}
                                            </a>
                                        {% else %}
                                            <span class="text-muted">未设置</span>
                                        {% endif %}
                                    {% else %}
                                        {% if user.email %}
                                            <a href="mailto:{{ user.email }}" class="text-decoration-none">
                                                {{ user.email }}
                                            </a>
                                        {% else %}
                                            <span class="text-muted">未设置</span>
                                        {% endif %}
                                    {% endif %}
                                </dd>
                            </dl>
                        </div>
                        
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">角色：</dt>
                                <dd class="col-sm-8">
                                    {% if user_profile.role == 'admin' %}
                                        <span class="badge bg-warning">
                                            <i class="bi bi-shield-check me-1"></i>管理员
                                        </span>
                                    {% elif user_profile.role == 'staff' %}
                                        <span class="badge bg-info">
                                            <i class="bi bi-person-badge me-1"></i>科室人员
                                        </span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ user_profile.get_role_display }}</span>
                                    {% endif %}
                                </dd>
                                
                                <dt class="col-sm-4">科室：</dt>
                                <dd class="col-sm-8">
                                    {% if user_profile.department %}
                                        <span class="badge bg-success">
                                            {{ user_profile.department.code }} - {{ user_profile.department.name }}
                                        </span>
                                    {% else %}
                                        <span class="text-muted">未分配</span>
                                    {% endif %}
                                </dd>
                                
                                <dt class="col-sm-4">状态：</dt>
                                <dd class="col-sm-8">
                                    {% if user_profile.is_active %}
                                        <span class="badge bg-success">
                                            <i class="bi bi-check-circle me-1"></i>正常
                                        </span>
                                    {% else %}
                                        <span class="badge bg-danger">
                                            <i class="bi bi-x-circle me-1"></i>禁用
                                        </span>
                                    {% endif %}
                                </dd>
                                
                                <dt class="col-sm-4">注册时间：</dt>
                                <dd class="col-sm-8">
                                    <small class="text-muted">
                                        {% if is_admin_view %}
                                            {{ date_joined|date:"Y-m-d H:i:s" }}
                                        {% else %}
                                            {{ user.date_joined|date:"Y-m-d H:i:s" }}
                                        {% endif %}
                                    </small>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 登录信息卡片 -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-clock-history me-2"></i>
                        登录信息
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-item mb-3">
                                <label class="form-label">最后登录时间：</label>
                                <div>
                                    {% if last_login %}
                                        <span class="text-success">
                                            <i class="bi bi-check-circle me-1"></i>
                                            {{ last_login|date:"Y-m-d H:i:s" }}
                                        </span>
                                    {% else %}
                                        <span class="text-muted">从未登录</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item mb-3">
                                <label class="form-label">登录IP：</label>
                                <div>
                                    {% if user_profile.last_login_ip %}
                                        <code>{{ user_profile.last_login_ip }}</code>
                                    {% else %}
                                        <span class="text-muted">无记录</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧信息面板 -->
        <div class="col-lg-4">
            <!-- 快速操作卡片 -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-bottom">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-lightning me-2"></i>
                        快速操作
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        {% if not is_admin_view %}
                        <a href="{% url 'users:profile_edit' %}" class="btn btn-primary">
                            <i class="bi bi-pencil me-2"></i>
                            编辑个人信息
                        </a>
                        <a href="{% url 'users:user_settings' %}" class="btn btn-outline-secondary">
                            <i class="bi bi-gear me-2"></i>
                            个人设置
                        </a>
                        <button type="button" class="btn btn-outline-warning" data-bs-toggle="modal" data-bs-target="#changePasswordModal">
                            <i class="bi bi-key me-2"></i>
                            修改密码
                        </button>
                        {% else %}
                        <a href="{% url 'users:user_edit' user_profile.id %}" class="btn btn-primary">
                            <i class="bi bi-pencil me-2"></i>
                            编辑用户信息
                        </a>
                        <button type="button" class="btn btn-outline-warning toggle-status-btn" 
                                data-user-id="{{ user_profile.id }}"
                                data-current-status="{{ user_profile.is_active|yesno:'true,false' }}">
                            {% if user_profile.is_active %}
                                <i class="bi bi-pause-circle me-2"></i>
                                禁用用户
                            {% else %}
                                <i class="bi bi-play-circle me-2"></i>
                                启用用户
                            {% endif %}
                        </button>
                        {% endif %}
                        <a href="{% url 'users:dashboard' %}" class="btn btn-outline-secondary">
                            <i class="bi bi-house me-2"></i>
                            返回首页
                        </a>
                    </div>
                </div>
            </div>

            <!-- 创建信息卡片 -->
            {% if user_profile.created_by or user_profile.updated_by %}
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-info-circle me-2"></i>
                        创建信息
                    </h6>
                </div>
                <div class="card-body">
                    {% if user_profile.created_by %}
                    <div class="info-item mb-2">
                        <label class="form-label">创建人：</label>
                        <div>{{ user_profile.created_by.get_full_name|default:user_profile.created_by.username }}</div>
                    </div>
                    {% endif %}
                    
                    <div class="info-item mb-2">
                        <label class="form-label">创建时间：</label>
                        <div>
                            <small class="text-muted">{{ user_profile.created_at|date:"Y-m-d H:i:s" }}</small>
                        </div>
                    </div>
                    
                    {% if user_profile.updated_by %}
                    <div class="info-item mb-2">
                        <label class="form-label">最后更新人：</label>
                        <div>{{ user_profile.updated_by.get_full_name|default:user_profile.updated_by.username }}</div>
                    </div>
                    {% endif %}
                    
                    {% if user_profile.updated_at %}
                    <div class="info-item">
                        <label class="form-label">更新时间：</label>
                        <div>
                            <small class="text-muted">{{ user_profile.updated_at|date:"Y-m-d H:i:s" }}</small>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- 修改密码模态框 -->
{% if not is_admin_view %}
<div class="modal fade" id="changePasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-key me-2"></i>
                    修改密码
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="changePasswordForm">
                <div class="modal-body">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="current_password" class="form-label">当前密码</label>
                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                    </div>
                    <div class="mb-3">
                        <label for="new_password" class="form-label">新密码</label>
                        <input type="password" class="form-control" id="new_password" name="new_password" 
                               minlength="6" required>
                        <div class="form-text">密码长度至少6位</div>
                    </div>
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">确认新密码</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                               minlength="6" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-circle me-2"></i>
                        确认修改
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script src="{% static 'users/js/profile.js' %}"></script>
{% endblock %}
