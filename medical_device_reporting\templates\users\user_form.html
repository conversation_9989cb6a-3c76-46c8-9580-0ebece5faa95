{% extends 'base.html' %}
{% load static %}

{% block title %}
{% if is_edit %}编辑用户{% else %}新建用户{% endif %} - 医疗器械不良事件上报平台
{% endblock %}

{% block extra_css %}
<link href="{% static 'users/css/user_management.css' %}" rel="stylesheet">
{% endblock %}

{% block nav_items %}
<li class="nav-item">
    <a class="nav-link" href="{% url 'users:dashboard' %}">
        <i class="bi bi-house"></i>
        用户中心
    </a>
</li>
<li class="nav-item">
    <a class="nav-link" href="{% url 'users:user_list' %}">
        <i class="bi bi-people"></i>
        用户管理
    </a>
</li>
<li class="nav-item dropdown">
    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
        <i class="bi bi-person-circle"></i>
        {{ user.get_full_name|default:user.username }}
    </a>
    <ul class="dropdown-menu">
        <li><a class="dropdown-item" href="#"><i class="bi bi-gear me-2"></i>个人设置</a></li>
        <li><hr class="dropdown-divider"></li>
        <li><a class="dropdown-item" href="{% url 'users:logout' %}"><i class="bi bi-box-arrow-right me-2"></i>退出登录</a></li>
    </ul>
</li>
{% endblock %}

{% block content %}
<div class="user-form-container">
    <!-- 页面标题 -->
    <div class="page-header mb-4">
        <div class="row align-items-center">
            <div class="col">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'users:dashboard' %}">用户中心</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'users:user_list' %}">用户管理</a></li>
                        <li class="breadcrumb-item active">
                            {% if is_edit %}编辑用户{% else %}新建用户{% endif %}
                        </li>
                    </ol>
                </nav>
                <h2 class="page-title">
                    {% if is_edit %}
                        <i class="bi bi-person-gear me-2"></i>
                        编辑用户
                    {% else %}
                        <i class="bi bi-person-plus me-2"></i>
                        新建用户
                    {% endif %}
                </h2>
                {% if is_edit %}
                <p class="page-subtitle text-muted">编辑用户 {{ user_profile.account_number }} 的信息</p>
                {% else %}
                <p class="page-subtitle text-muted">创建新的系统用户账号</p>
                {% endif %}
            </div>
            <div class="col-auto">
                <a href="{% url 'users:user_list' %}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left me-2"></i>
                    返回列表
                </a>
            </div>
        </div>
    </div>

    <!-- 用户表单 -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-person-badge me-2"></i>
                        用户信息
                    </h6>
                </div>
                <div class="card-body">
                    <!-- 错误消息显示 -->
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <form method="post" class="needs-validation" novalidate id="userForm">
                        {% csrf_token %}
                        
                        <div class="row">
                            <!-- 基本信息 -->
                            <div class="col-md-6">
                                <h6 class="form-section-title">基本信息</h6>
                                
                                <!-- 账号 -->
                                <div class="mb-3">
                                    <label for="account_number" class="form-label required">
                                        <i class="bi bi-hash me-1"></i>
                                        4位数账号
                                    </label>
                                    <input 
                                        type="text" 
                                        class="form-control" 
                                        id="account_number" 
                                        name="account_number" 
                                        value="{% if is_edit %}{{ user_profile.account_number }}{% endif %}"
                                        maxlength="4" 
                                        pattern="[0-9]{4}" 
                                        required
                                        {% if is_edit %}readonly{% endif %}
                                        placeholder="请输入4位数字账号"
                                    >
                                    <div class="invalid-feedback">
                                        请输入4位数字账号
                                    </div>
                                    {% if is_edit %}
                                    <div class="form-text">
                                        <i class="bi bi-info-circle me-1"></i>
                                        账号创建后不可修改
                                    </div>
                                    {% endif %}
                                </div>

                                <!-- 用户名 -->
                                <div class="mb-3">
                                    <label for="username" class="form-label required">
                                        <i class="bi bi-person me-1"></i>
                                        用户名
                                    </label>
                                    <input 
                                        type="text" 
                                        class="form-control" 
                                        id="username" 
                                        name="username" 
                                        value="{% if is_edit %}{{ user_profile.user.username }}{% endif %}"
                                        required
                                        placeholder="请输入用户名"
                                    >
                                    <div class="invalid-feedback">
                                        请输入用户名
                                    </div>
                                </div>

                                <!-- 姓名 -->
                                <div class="row">
                                    <div class="col-6">
                                        <div class="mb-3">
                                            <label for="first_name" class="form-label">
                                                <i class="bi bi-person-badge me-1"></i>
                                                姓
                                            </label>
                                            <input 
                                                type="text" 
                                                class="form-control" 
                                                id="first_name" 
                                                name="first_name" 
                                                value="{% if is_edit %}{{ user_profile.user.first_name }}{% endif %}"
                                                placeholder="姓"
                                            >
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="mb-3">
                                            <label for="last_name" class="form-label">
                                                <i class="bi bi-person-badge me-1"></i>
                                                名
                                            </label>
                                            <input 
                                                type="text" 
                                                class="form-control" 
                                                id="last_name" 
                                                name="last_name" 
                                                value="{% if is_edit %}{{ user_profile.user.last_name }}{% endif %}"
                                                placeholder="名"
                                            >
                                        </div>
                                    </div>
                                </div>

                                <!-- 邮箱 -->
                                <div class="mb-3">
                                    <label for="email" class="form-label">
                                        <i class="bi bi-envelope me-1"></i>
                                        邮箱地址
                                    </label>
                                    <input 
                                        type="email" 
                                        class="form-control" 
                                        id="email" 
                                        name="email" 
                                        value="{% if is_edit %}{{ user_profile.user.email }}{% endif %}"
                                        placeholder="请输入邮箱地址"
                                    >
                                    <div class="invalid-feedback">
                                        请输入有效的邮箱地址
                                    </div>
                                </div>
                            </div>

                            <!-- 职务信息 -->
                            <div class="col-md-6">
                                <h6 class="form-section-title">职务信息</h6>
                                
                                <!-- 科室 -->
                                <div class="mb-3">
                                    <label for="department_id" class="form-label">
                                        <i class="bi bi-building me-1"></i>
                                        所属科室
                                    </label>
                                    <select class="form-select" id="department_id" name="department_id">
                                        <option value="">请选择科室</option>
                                        {% for department in departments %}
                                        <option value="{{ department.id }}" 
                                            {% if is_edit and user_profile.department_id == department.id %}selected{% endif %}>
                                            {{ department.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                    <div class="form-text">
                                        <i class="bi bi-info-circle me-1"></i>
                                        科室人员必须选择所属科室
                                    </div>
                                </div>

                                <!-- 角色 -->
                                <div class="mb-3">
                                    <label for="role" class="form-label required">
                                        <i class="bi bi-shield-check me-1"></i>
                                        用户角色
                                    </label>
                                    <select class="form-select" id="role" name="role" required>
                                        {% for role_value, role_label in role_choices %}
                                        <option value="{{ role_value }}" 
                                            {% if is_edit and user_profile.role == role_value %}selected{% endif %}>
                                            {{ role_label }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                    <div class="invalid-feedback">
                                        请选择用户角色
                                    </div>
                                </div>

                                <!-- 状态 -->
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-toggle-on me-1"></i>
                                        账号状态
                                    </label>
                                    <div class="form-check form-switch">
                                        <input 
                                            class="form-check-input" 
                                            type="checkbox" 
                                            id="is_active" 
                                            name="is_active"
                                            {% if not is_edit or user_profile.is_active %}checked{% endif %}
                                        >
                                        <label class="form-check-label" for="is_active">
                                            启用账号
                                        </label>
                                    </div>
                                    <div class="form-text">
                                        <i class="bi bi-info-circle me-1"></i>
                                        禁用的账号无法登录系统
                                    </div>
                                </div>

                                {% if is_edit %}
                                <!-- 创建信息 -->
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-clock-history me-1"></i>
                                        创建信息
                                    </label>
                                    <div class="form-control-plaintext">
                                        <small class="text-muted">
                                            创建时间：{{ user_profile.created_at|date:"Y-m-d H:i" }}<br>
                                            {% if user_profile.created_by %}
                                            创建人：{{ user_profile.created_by.get_full_name|default:user_profile.created_by.username }}
                                            {% endif %}
                                        </small>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- 表单按钮 -->
                        <div class="form-actions mt-4 pt-3 border-top">
                            <div class="row">
                                <div class="col">
                                    <a href="{% url 'users:user_list' %}" class="btn btn-outline-secondary">
                                        <i class="bi bi-x-circle me-2"></i>
                                        取消
                                    </a>
                                </div>
                                <div class="col-auto">
                                    <button type="submit" class="btn btn-primary" id="submitBtn">
                                        <span class="btn-text">
                                            <i class="bi bi-check-circle me-2"></i>
                                            {% if is_edit %}更新用户{% else %}创建用户{% endif %}
                                        </span>
                                        <span class="btn-loading d-none">
                                            <span class="spinner-border spinner-border-sm me-2"></span>
                                            {% if is_edit %}更新中...{% else %}创建中...{% endif %}
                                        </span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'users/js/user_form.js' %}"></script>
{% endblock %}
