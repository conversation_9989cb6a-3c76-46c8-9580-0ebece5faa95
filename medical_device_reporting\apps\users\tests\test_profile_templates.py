"""
个人设置模板测试
Profile Templates Tests
"""

from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse

from apps.users.models import UserProfile, Department


class ProfileTemplateTest(TestCase):
    """个人设置模板测试"""
    
    def setUp(self):
        """测试前准备"""
        self.client = Client()
        
        # 创建管理员用户
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            first_name='管理',
            last_name='员'
        )
        self.admin_profile = UserProfile.objects.create(
            user=self.admin_user,
            account_number='0001',
            role='admin',
            is_active=True
        )
        
        # 创建科室
        self.department = Department.objects.create(
            code='TEST',
            name='测试科室',
            created_by=self.admin_user
        )
        
        # 创建科室人员
        self.staff_user = User.objects.create_user(
            username='staff',
            email='<EMAIL>',
            first_name='科室',
            last_name='人员'
        )
        self.staff_profile = UserProfile.objects.create(
            user=self.staff_user,
            account_number='1001',
            department=self.department,
            role='staff',
            is_active=True
        )
    
    def test_profile_template_rendering_self_view(self):
        """测试个人信息模板渲染（用户查看自己）"""
        self.client.force_login(self.staff_user)
        
        response = self.client.get(reverse('users:profile'))
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'users/profile.html')
        
        # 检查页面标题
        self.assertContains(response, '个人信息')
        
        # 检查用户信息显示
        self.assertContains(response, self.staff_profile.account_number)
        self.assertContains(response, self.staff_user.first_name)
        self.assertContains(response, self.staff_user.last_name)
        self.assertContains(response, self.department.name)
        
        # 检查快速操作按钮
        self.assertContains(response, '编辑信息')
        self.assertContains(response, '个人设置')
        self.assertContains(response, '修改密码')
        
        # 检查面包屑导航
        self.assertContains(response, '用户中心')
        self.assertContains(response, '个人信息')
    
    def test_profile_template_rendering_admin_view(self):
        """测试个人信息模板渲染（管理员查看其他用户）"""
        self.client.force_login(self.admin_user)
        
        response = self.client.get(reverse('users:user_profile', args=[self.staff_profile.id]))
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'users/profile.html')
        
        # 检查页面标题（管理员视图）
        self.assertContains(response, '用户信息')
        self.assertContains(response, self.staff_user.first_name)
        
        # 检查管理员操作按钮
        self.assertContains(response, '编辑用户')
        self.assertContains(response, '启用用户')  # 或禁用用户
        
        # 检查面包屑导航（管理员视图）
        self.assertContains(response, '用户中心')
        self.assertContains(response, '用户管理')
    
    def test_profile_edit_template_rendering(self):
        """测试个人信息编辑模板渲染"""
        self.client.force_login(self.staff_user)
        
        response = self.client.get(reverse('users:profile_edit'))
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'users/profile_edit.html')
        
        # 检查页面标题
        self.assertContains(response, '编辑个人信息')
        
        # 检查表单字段
        self.assertContains(response, 'name="first_name"')
        self.assertContains(response, 'name="last_name"')
        self.assertContains(response, 'name="email"')
        
        # 检查不可编辑字段
        self.assertContains(response, self.staff_profile.account_number)
        self.assertContains(response, 'readonly')
        
        # 检查编辑说明
        self.assertContains(response, '编辑说明')
        self.assertContains(response, '您只能修改姓名和邮箱信息')
        
        # 检查其他操作
        self.assertContains(response, '修改密码')
        self.assertContains(response, '个人设置')
    
    def test_user_settings_template_rendering(self):
        """测试用户设置模板渲染"""
        self.client.force_login(self.staff_user)
        
        response = self.client.get(reverse('users:user_settings'))
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'users/user_settings.html')
        
        # 检查页面标题
        self.assertContains(response, '个人设置')
        
        # 检查标签页
        self.assertContains(response, '基本设置')
        self.assertContains(response, '偏好设置')
        self.assertContains(response, '安全设置')
        self.assertContains(response, '通知设置')
        
        # 检查基本设置内容
        self.assertContains(response, '当前信息')
        self.assertContains(response, '快速操作')
        
        # 检查偏好设置内容
        self.assertContains(response, '界面主题')
        self.assertContains(response, '界面语言')
        self.assertContains(response, '时区设置')
        
        # 检查安全设置内容
        self.assertContains(response, '密码安全')
        self.assertContains(response, '登录记录')
        self.assertContains(response, '账户状态')
        
        # 检查通知设置内容
        self.assertContains(response, '邮件通知')
        self.assertContains(response, '系统通知')
    
    def test_profile_template_context_variables(self):
        """测试个人信息模板上下文变量"""
        self.client.force_login(self.staff_user)
        
        response = self.client.get(reverse('users:profile'))
        
        # 检查上下文变量
        self.assertIn('user_profile', response.context)
        self.assertIn('user', response.context)
        self.assertIn('last_login', response.context)
        self.assertIn('date_joined', response.context)
        
        # 检查变量值
        self.assertEqual(response.context['user_profile'], self.staff_profile)
        self.assertEqual(response.context['user'], self.staff_user)
    
    def test_profile_template_permission_display(self):
        """测试个人信息模板权限相关显示"""
        # 测试普通用户视图
        self.client.force_login(self.staff_user)
        
        response = self.client.get(reverse('users:profile'))
        
        # 普通用户应该看到个人操作
        self.assertContains(response, '编辑信息')
        self.assertContains(response, '个人设置')
        
        # 普通用户不应该看到管理员操作
        self.assertNotContains(response, '编辑用户')
        self.assertNotContains(response, '禁用用户')
        
        # 测试管理员视图
        self.client.force_login(self.admin_user)
        
        response = self.client.get(reverse('users:user_profile', args=[self.staff_profile.id]))
        
        # 管理员应该看到管理操作
        self.assertContains(response, '编辑用户')
    
    def test_profile_template_breadcrumb_navigation(self):
        """测试个人信息模板面包屑导航"""
        # 测试用户查看自己的面包屑
        self.client.force_login(self.staff_user)
        
        response = self.client.get(reverse('users:profile'))
        self.assertContains(response, '用户中心')
        self.assertContains(response, '个人信息')
        
        # 测试个人信息编辑面包屑
        response = self.client.get(reverse('users:profile_edit'))
        self.assertContains(response, '用户中心')
        self.assertContains(response, '个人信息')
        self.assertContains(response, '编辑信息')
        
        # 测试管理员查看用户的面包屑
        self.client.force_login(self.admin_user)
        
        response = self.client.get(reverse('users:user_profile', args=[self.staff_profile.id]))
        self.assertContains(response, '用户中心')
        self.assertContains(response, '用户管理')
    
    def test_profile_template_form_validation_display(self):
        """测试个人信息模板表单验证显示"""
        self.client.force_login(self.staff_user)
        
        response = self.client.get(reverse('users:profile_edit'))
        
        # 检查表单验证类
        self.assertContains(response, 'needs-validation')
        self.assertContains(response, 'invalid-feedback')
        
        # 检查必填字段标识
        self.assertContains(response, 'required')
    
    def test_profile_template_javascript_integration(self):
        """测试个人信息模板JavaScript集成"""
        self.client.force_login(self.staff_user)
        
        # 测试个人信息页面JavaScript
        response = self.client.get(reverse('users:profile'))
        self.assertContains(response, 'profile.js')
        
        # 测试个人信息编辑页面JavaScript
        response = self.client.get(reverse('users:profile_edit'))
        self.assertContains(response, 'profile_edit.js')
        
        # 测试用户设置页面JavaScript
        response = self.client.get(reverse('users:user_settings'))
        self.assertContains(response, 'user_settings.js')
    
    def test_profile_template_css_integration(self):
        """测试个人信息模板CSS集成"""
        self.client.force_login(self.staff_user)
        
        response = self.client.get(reverse('users:profile'))
        
        # 检查CSS文件引用
        self.assertContains(response, 'user_management.css')
        
        # 检查Bootstrap Icons
        self.assertContains(response, 'bi-person-badge')
        self.assertContains(response, 'bi-pencil')
        self.assertContains(response, 'bi-gear')
    
    def test_profile_template_modal_integration(self):
        """测试个人信息模板模态框集成"""
        self.client.force_login(self.staff_user)
        
        response = self.client.get(reverse('users:profile'))
        
        # 检查修改密码模态框
        self.assertContains(response, 'changePasswordModal')
        self.assertContains(response, 'current_password')
        self.assertContains(response, 'new_password')
        self.assertContains(response, 'confirm_password')
    
    def test_profile_template_responsive_design(self):
        """测试个人信息模板响应式设计元素"""
        self.client.force_login(self.staff_user)
        
        response = self.client.get(reverse('users:profile'))
        
        # 检查Bootstrap响应式类
        self.assertContains(response, 'col-lg-')
        self.assertContains(response, 'col-md-')
        
        # 检查移动端友好元素
        self.assertContains(response, 'btn-group')
        self.assertContains(response, 'card')
    
    def test_profile_template_accessibility(self):
        """测试个人信息模板可访问性元素"""
        self.client.force_login(self.staff_user)
        
        response = self.client.get(reverse('users:profile'))
        
        # 检查ARIA标签
        self.assertContains(response, 'aria-label')
        self.assertContains(response, 'role=')
        
        # 检查表单标签关联
        self.assertContains(response, 'for=')
        
        # 检查语义化HTML
        self.assertContains(response, '<nav')
        self.assertContains(response, '<main')
    
    def test_user_settings_template_tabs_functionality(self):
        """测试用户设置模板标签页功能"""
        self.client.force_login(self.staff_user)
        
        response = self.client.get(reverse('users:user_settings'))
        
        # 检查标签页导航
        self.assertContains(response, 'nav-tabs')
        self.assertContains(response, 'data-bs-toggle="tab"')
        
        # 检查标签页内容
        self.assertContains(response, 'tab-content')
        self.assertContains(response, 'tab-pane')
        
        # 检查各个标签页ID
        self.assertContains(response, 'basic-pane')
        self.assertContains(response, 'preferences-pane')
        self.assertContains(response, 'security-pane')
        self.assertContains(response, 'notifications-pane')
