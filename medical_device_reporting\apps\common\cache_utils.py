"""
统计数据缓存工具
Statistics Data Cache Utilities

提供统计分析功能的智能缓存机制，支持分层缓存、自动失效和性能监控。
"""

import hashlib
import json
import logging
from datetime import datetime, timedelta
from functools import wraps
from typing import Any, Dict, List, Optional, Union, Callable

from django.core.cache import cache
from django.conf import settings
from django.utils import timezone

logger = logging.getLogger(__name__)


class StatisticsCacheManager:
    """统计数据缓存管理器"""
    
    # 缓存键前缀
    CACHE_PREFIX = 'stats'
    
    # 默认缓存时间（秒）
    DEFAULT_TIMEOUT = 3600  # 1小时
    
    # 缓存层级
    CACHE_LEVELS = {
        'global': 'global',      # 全局级缓存
        'department': 'dept',    # 科室级缓存
        'user': 'user',          # 用户级缓存
    }
    
    # 缓存超时配置
    CACHE_TIMEOUTS = {
        'report_statistics': 1800,           # 30分钟
        'time_series_statistics': 3600,      # 1小时
        'cross_dimension_statistics': 2700,  # 45分钟
        'device_statistics': 3600,           # 1小时
        'department_statistics': 1800,       # 30分钟
        'trend_analysis': 7200,              # 2小时
    }
    
    @classmethod
    def generate_cache_key(
        cls,
        function_name: str,
        user_profile: Optional['UserProfile'] = None,
        **kwargs
    ) -> str:
        """
        生成缓存键
        
        Args:
            function_name: 函数名称
            user_profile: 用户配置文件
            **kwargs: 其他参数
            
        Returns:
            str: 缓存键
        """
        # 基础键组件
        key_parts = [cls.CACHE_PREFIX, function_name]
        
        # 添加用户权限层级
        if user_profile:
            if user_profile.is_admin:
                key_parts.append(cls.CACHE_LEVELS['global'])
            else:
                key_parts.extend([
                    cls.CACHE_LEVELS['department'],
                    str(user_profile.department_id)
                ])
        else:
            key_parts.append(cls.CACHE_LEVELS['global'])
        
        # 处理参数
        param_dict = {}
        for key, value in kwargs.items():
            if value is not None:
                if isinstance(value, datetime):
                    param_dict[key] = value.isoformat()
                elif isinstance(value, (list, dict)):
                    param_dict[key] = json.dumps(value, sort_keys=True)
                else:
                    param_dict[key] = str(value)
        
        # 生成参数哈希
        if param_dict:
            param_str = json.dumps(param_dict, sort_keys=True)
            param_hash = hashlib.md5(param_str.encode()).hexdigest()[:8]
            key_parts.append(param_hash)
        
        cache_key = ':'.join(key_parts)
        
        # 确保键长度不超过限制
        if len(cache_key) > 250:
            cache_key = cache_key[:200] + hashlib.md5(cache_key.encode()).hexdigest()[:8]
        
        return cache_key
    
    @classmethod
    def get_timeout(cls, function_name: str) -> int:
        """获取函数的缓存超时时间"""
        return cls.CACHE_TIMEOUTS.get(function_name, cls.DEFAULT_TIMEOUT)
    
    @classmethod
    def get_cache_pattern(cls, function_name: str, level: str = None) -> str:
        """获取缓存键模式，用于批量删除"""
        pattern_parts = [cls.CACHE_PREFIX, function_name]
        if level:
            pattern_parts.append(level)
        return ':'.join(pattern_parts) + '*'
    
    @classmethod
    def invalidate_cache(
        cls,
        function_name: str = None,
        user_profile: Optional['UserProfile'] = None,
        level: str = None
    ):
        """
        失效缓存
        
        Args:
            function_name: 函数名称，None表示所有统计函数
            user_profile: 用户配置文件
            level: 缓存层级
        """
        try:
            if function_name:
                # 失效特定函数的缓存
                pattern = cls.get_cache_pattern(function_name, level)
                cls._delete_cache_pattern(pattern)
                logger.info(f'缓存失效: {pattern}')
            else:
                # 失效所有统计缓存
                for func_name in cls.CACHE_TIMEOUTS.keys():
                    pattern = cls.get_cache_pattern(func_name, level)
                    cls._delete_cache_pattern(pattern)
                logger.info(f'批量缓存失效: 所有统计函数')
                
        except Exception as e:
            logger.error(f'缓存失效失败: {e}')
    
    @classmethod
    def _delete_cache_pattern(cls, pattern: str):
        """删除匹配模式的缓存键"""
        try:
            # 尝试使用Redis的keys命令（仅在开发环境）
            if hasattr(cache, '_cache') and hasattr(cache._cache, 'get_client'):
                client = cache._cache.get_client()
                keys = client.keys(pattern)
                if keys:
                    client.delete(*keys)
            else:
                # 回退到Django缓存API（性能较低）
                logger.warning(f'无法使用模式删除，跳过: {pattern}')
        except Exception as e:
            logger.error(f'模式删除失败: {pattern}, 错误: {e}')
    
    @classmethod
    def get_cache_stats(cls) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            stats = {
                'cache_backend': str(cache.__class__),
                'cache_location': getattr(settings, 'CACHES', {}).get('default', {}).get('LOCATION', 'Unknown'),
                'timestamp': timezone.now().isoformat(),
            }
            
            # 尝试获取Redis统计信息
            if hasattr(cache, '_cache') and hasattr(cache._cache, 'get_client'):
                client = cache._cache.get_client()
                info = client.info()
                stats.update({
                    'redis_version': info.get('redis_version'),
                    'used_memory': info.get('used_memory_human'),
                    'connected_clients': info.get('connected_clients'),
                    'keyspace_hits': info.get('keyspace_hits'),
                    'keyspace_misses': info.get('keyspace_misses'),
                })
                
                # 计算命中率
                hits = info.get('keyspace_hits', 0)
                misses = info.get('keyspace_misses', 0)
                total = hits + misses
                if total > 0:
                    stats['hit_rate'] = round((hits / total) * 100, 2)
            
            return stats
            
        except Exception as e:
            logger.error(f'获取缓存统计失败: {e}')
            return {'error': str(e)}


def cache_statistics_result(
    timeout: Optional[int] = None,
    key_prefix: str = None,
    invalidate_on_error: bool = True
):
    """
    统计结果缓存装饰器
    
    Args:
        timeout: 缓存超时时间（秒）
        key_prefix: 缓存键前缀
        invalidate_on_error: 出错时是否失效缓存
        
    Usage:
        @cache_statistics_result(timeout=3600)
        def get_time_series_statistics(...):
            return data
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 提取用户配置文件
            user_profile = kwargs.get('user_profile') or (args[0] if args else None)

            # 生成缓存键（排除user_profile避免重复传递）
            function_name = key_prefix or func.__name__
            cache_kwargs = {k: v for k, v in kwargs.items() if k != 'user_profile'}
            cache_key = StatisticsCacheManager.generate_cache_key(
                function_name=function_name,
                user_profile=user_profile,
                **cache_kwargs
            )
            
            # 尝试从缓存获取
            try:
                cached_result = cache.get(cache_key)
                if cached_result is not None:
                    logger.debug(f'缓存命中: {cache_key}')
                    return cached_result
            except Exception as e:
                logger.warning(f'缓存读取失败: {cache_key}, 错误: {e}')
            
            # 执行原函数
            try:
                result = func(*args, **kwargs)
                
                # 缓存结果
                cache_timeout = timeout or StatisticsCacheManager.get_timeout(function_name)
                try:
                    cache.set(cache_key, result, cache_timeout)
                    logger.debug(f'缓存设置: {cache_key}, 超时: {cache_timeout}秒')
                except Exception as e:
                    logger.warning(f'缓存设置失败: {cache_key}, 错误: {e}')
                
                return result
                
            except Exception as e:
                # 出错时可选择失效相关缓存
                if invalidate_on_error:
                    try:
                        StatisticsCacheManager.invalidate_cache(function_name, user_profile)
                    except:
                        pass
                raise
        
        return wrapper
    return decorator


def invalidate_statistics_cache(
    function_names: Union[str, List[str]] = None,
    user_profile: Optional['UserProfile'] = None,
    level: str = None
):
    """
    失效统计缓存的便捷函数
    
    Args:
        function_names: 函数名称或函数名称列表
        user_profile: 用户配置文件
        level: 缓存层级
    """
    if isinstance(function_names, str):
        function_names = [function_names]
    
    if function_names:
        for func_name in function_names:
            StatisticsCacheManager.invalidate_cache(func_name, user_profile, level)
    else:
        StatisticsCacheManager.invalidate_cache(None, user_profile, level)


def warm_up_cache(user_profile: Optional['UserProfile'] = None):
    """
    预热缓存 - 预先加载常用统计数据
    
    Args:
        user_profile: 用户配置文件
    """
    try:
        from apps.reports.selectors import (
            report_statistics,
            get_time_series_statistics,
            get_device_statistics
        )
        
        logger.info('开始预热统计缓存...')
        
        # 预热基础统计
        report_statistics(user_profile=user_profile)
        
        # 预热时间序列（最近3个月）
        end_date = timezone.now()
        start_date = end_date - timedelta(days=90)
        get_time_series_statistics(
            user_profile=user_profile,
            start_date=start_date,
            end_date=end_date
        )
        
        # 预热器械统计
        get_device_statistics(user_profile=user_profile, limit=10)
        
        logger.info('统计缓存预热完成')
        
    except Exception as e:
        logger.error(f'缓存预热失败: {e}')
