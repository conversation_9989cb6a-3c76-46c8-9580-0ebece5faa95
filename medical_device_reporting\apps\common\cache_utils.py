"""
统计数据缓存工具
Statistics Data Cache Utilities

提供统计分析功能的智能缓存机制，支持分层缓存、自动失效和性能监控。
"""

import hashlib
import json
import logging
from datetime import datetime, timedelta
from functools import wraps
from typing import Any, Dict, List, Optional, Union, Callable

from django.core.cache import cache
from django.conf import settings
from django.utils import timezone

logger = logging.getLogger(__name__)


class StatisticsCacheManager:
    """统计数据缓存管理器"""
    
    # 缓存键前缀
    CACHE_PREFIX = 'stats'
    
    # 默认缓存时间（秒）
    DEFAULT_TIMEOUT = 3600  # 1小时
    
    # 缓存层级
    CACHE_LEVELS = {
        'global': 'global',      # 全局级缓存
        'department': 'dept',    # 科室级缓存
        'user': 'user',          # 用户级缓存
    }
    
    # 缓存超时配置
    CACHE_TIMEOUTS = {
        'report_statistics': 1800,           # 30分钟
        'time_series_statistics': 3600,      # 1小时
        'cross_dimension_statistics': 2700,  # 45分钟
        'device_statistics': 3600,           # 1小时
        'department_statistics': 1800,       # 30分钟
        'trend_analysis': 7200,              # 2小时
    }
    
    @classmethod
    def generate_cache_key(
        cls,
        function_name: str,
        user_profile: Optional['UserProfile'] = None,
        **kwargs
    ) -> str:
        """
        生成缓存键
        
        Args:
            function_name: 函数名称
            user_profile: 用户配置文件
            **kwargs: 其他参数
            
        Returns:
            str: 缓存键
        """
        # 基础键组件
        key_parts = [cls.CACHE_PREFIX, function_name]
        
        # 添加用户权限层级
        if user_profile:
            if user_profile.is_admin:
                key_parts.append(cls.CACHE_LEVELS['global'])
            else:
                key_parts.extend([
                    cls.CACHE_LEVELS['department'],
                    str(user_profile.department_id)
                ])
        else:
            key_parts.append(cls.CACHE_LEVELS['global'])
        
        # 处理参数
        param_dict = {}
        for key, value in kwargs.items():
            if value is not None:
                if isinstance(value, datetime):
                    param_dict[key] = value.isoformat()
                elif isinstance(value, (list, dict)):
                    param_dict[key] = json.dumps(value, sort_keys=True)
                else:
                    param_dict[key] = str(value)
        
        # 生成参数哈希
        if param_dict:
            param_str = json.dumps(param_dict, sort_keys=True)
            param_hash = hashlib.md5(param_str.encode()).hexdigest()[:8]
            key_parts.append(param_hash)
        
        cache_key = ':'.join(key_parts)
        
        # 确保键长度不超过限制
        if len(cache_key) > 250:
            cache_key = cache_key[:200] + hashlib.md5(cache_key.encode()).hexdigest()[:8]
        
        return cache_key
    
    @classmethod
    def get_timeout(cls, function_name: str) -> int:
        """获取函数的缓存超时时间"""
        return cls.CACHE_TIMEOUTS.get(function_name, cls.DEFAULT_TIMEOUT)
    
    @classmethod
    def get_cache_pattern(cls, function_name: str, level: str = None) -> str:
        """获取缓存键模式，用于批量删除"""
        pattern_parts = [cls.CACHE_PREFIX, function_name]
        if level:
            pattern_parts.append(level)
        return ':'.join(pattern_parts) + '*'
    
    @classmethod
    def invalidate_cache(
        cls,
        function_name: str = None,
        user_profile: Optional['UserProfile'] = None,
        level: str = None
    ):
        """
        失效缓存
        
        Args:
            function_name: 函数名称，None表示所有统计函数
            user_profile: 用户配置文件
            level: 缓存层级
        """
        try:
            total_deleted = 0
            if function_name:
                # 失效特定函数的缓存
                pattern = cls.get_cache_pattern(function_name, level)
                deleted = cls._delete_cache_pattern_with_count(pattern)
                total_deleted += deleted

                if deleted > 0:
                    logger.info(f'缓存失效: {function_name}, 删除了{deleted}个键')
                else:
                    logger.debug(f'缓存失效: {function_name}, 未找到匹配的键')
            else:
                # 失效所有统计缓存
                for func_name in cls.CACHE_TIMEOUTS.keys():
                    pattern = cls.get_cache_pattern(func_name, level)
                    deleted = cls._delete_cache_pattern_with_count(pattern)
                    total_deleted += deleted

                if total_deleted > 0:
                    logger.info(f'批量缓存失效: 所有统计函数, 删除了{total_deleted}个键')
                else:
                    logger.debug(f'批量缓存失效: 所有统计函数, 未找到匹配的键')

        except Exception as e:
            logger.error(f'缓存失效失败: {e}')
    
    @classmethod
    def _delete_cache_pattern_with_count(cls, pattern: str) -> int:
        """删除匹配模式的缓存键并返回删除数量"""
        try:
            # 尝试使用Redis客户端
            if hasattr(cache, '_cache') and hasattr(cache._cache, 'get_client'):
                client = cache._cache.get_client()
                return cls._delete_redis_pattern(client, pattern)
            else:
                # 回退到Django缓存API
                cls._delete_django_cache_pattern(pattern)
                return 0  # Django API无法准确计数
        except Exception as e:
            logger.warning(f'缓存失效失败: {pattern}, 错误: {e}')
            return 0

    @classmethod
    def _delete_cache_pattern(cls, pattern: str):
        """删除匹配模式的缓存键（兼容性方法）"""
        cls._delete_cache_pattern_with_count(pattern)

    @classmethod
    def _delete_redis_pattern(cls, client, pattern: str) -> int:
        """使用Redis SCAN命令安全删除匹配模式的键"""
        deleted_count = 0

        # 方法1: 尝试使用SCAN命令（推荐）
        try:
            cursor = 0
            scan_count = 0
            max_scans = 1000  # 防止无限循环

            while scan_count < max_scans:
                cursor, keys = client.scan(cursor=cursor, match=pattern, count=100)
                if keys:
                    # 批量删除找到的键
                    deleted_count += client.delete(*keys)

                scan_count += 1
                # 如果cursor为0，表示扫描完成
                if cursor == 0:
                    break

            if scan_count >= max_scans:
                logger.warning(f'SCAN命令达到最大扫描次数限制: {pattern}')

            return deleted_count

        except AttributeError:
            # 方法2: Redis客户端不支持scan命令，尝试使用keys命令
            logger.debug(f'Redis客户端不支持SCAN命令，尝试KEYS命令: {pattern}')

        except Exception as e:
            logger.debug(f'Redis SCAN命令失败，尝试KEYS命令: {e}')

        # 方法2: 降级到KEYS命令
        try:
            keys = client.keys(pattern)
            if keys:
                # 分批删除，避免一次删除太多键
                batch_size = 100
                for i in range(0, len(keys), batch_size):
                    batch_keys = keys[i:i + batch_size]
                    deleted_count += client.delete(*batch_keys)
            return deleted_count

        except Exception as e:
            logger.debug(f'Redis KEYS命令也失败: {e}')

        # 方法3: 最后的降级策略 - 尝试删除常见的键模式
        try:
            common_patterns = [
                pattern.replace('*', 'global'),
                pattern.replace('*', 'dept'),
                pattern.replace('*', 'user'),
            ]

            for key_pattern in common_patterns:
                try:
                    if client.exists(key_pattern):
                        client.delete(key_pattern)
                        deleted_count += 1
                except:
                    pass

        except Exception as e:
            logger.debug(f'Redis降级删除也失败: {e}')

        return deleted_count

    @classmethod
    def _delete_django_cache_pattern(cls, pattern: str):
        """使用Django缓存API删除匹配模式的键（降级处理）"""
        try:
            # Django缓存API不直接支持模式删除
            logger.debug(f'使用Django缓存API处理模式: {pattern}')

            # 生成可能的缓存键变体
            possible_keys = cls._generate_possible_cache_keys(pattern)
            deleted_count = 0

            for key in possible_keys:
                try:
                    # 检查键是否存在（某些缓存后端支持）
                    if hasattr(cache, 'has_key') and cache.has_key(key):
                        cache.delete(key)
                        deleted_count += 1
                    else:
                        # 直接尝试删除（即使键不存在也不会报错）
                        cache.delete(key)
                except Exception:
                    pass

            logger.debug(f'Django缓存API处理完成: {pattern}, 尝试删除{len(possible_keys)}个键')

        except Exception as e:
            logger.debug(f'Django缓存删除失败: {e}')

    @classmethod
    def _generate_possible_cache_keys(cls, pattern: str) -> List[str]:
        """根据模式生成可能的缓存键"""
        keys = []

        # 基本替换
        base_replacements = ['global', 'dept', 'user', 'admin']
        for replacement in base_replacements:
            keys.append(pattern.replace('*', replacement))

        # 如果是统计函数，添加更多可能的键
        if 'stats:' in pattern:
            # 提取函数名
            parts = pattern.split(':')
            if len(parts) >= 2:
                func_name = parts[1]

                # 添加不同用户级别的键
                for level in ['global', 'dept', 'user']:
                    keys.append(f'stats:{func_name}:{level}')

                # 添加带哈希的键（常见模式）
                import hashlib
                for suffix in ['', 'default', 'cached']:
                    hash_suffix = hashlib.md5(suffix.encode()).hexdigest()[:8]
                    keys.append(f'stats:{func_name}:{level}:{hash_suffix}')

        # 去重并返回
        return list(set(keys))
    
    @classmethod
    def get_cache_stats(cls) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            stats = {
                'cache_backend': str(cache.__class__),
                'cache_location': getattr(settings, 'CACHES', {}).get('default', {}).get('LOCATION', 'Unknown'),
                'timestamp': timezone.now().isoformat(),
            }
            
            # 尝试获取Redis统计信息
            if hasattr(cache, '_cache') and hasattr(cache._cache, 'get_client'):
                client = cache._cache.get_client()
                info = client.info()
                stats.update({
                    'redis_version': info.get('redis_version'),
                    'used_memory': info.get('used_memory_human'),
                    'connected_clients': info.get('connected_clients'),
                    'keyspace_hits': info.get('keyspace_hits'),
                    'keyspace_misses': info.get('keyspace_misses'),
                })
                
                # 计算命中率
                hits = info.get('keyspace_hits', 0)
                misses = info.get('keyspace_misses', 0)
                total = hits + misses
                if total > 0:
                    stats['hit_rate'] = round((hits / total) * 100, 2)
            
            return stats
            
        except Exception as e:
            logger.error(f'获取缓存统计失败: {e}')
            return {'error': str(e)}


def cache_statistics_result(
    timeout: Optional[int] = None,
    key_prefix: str = None,
    invalidate_on_error: bool = True
):
    """
    统计结果缓存装饰器
    
    Args:
        timeout: 缓存超时时间（秒）
        key_prefix: 缓存键前缀
        invalidate_on_error: 出错时是否失效缓存
        
    Usage:
        @cache_statistics_result(timeout=3600)
        def get_time_series_statistics(...):
            return data
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 提取用户配置文件
            user_profile = kwargs.get('user_profile') or (args[0] if args else None)

            # 生成缓存键（排除user_profile避免重复传递）
            function_name = key_prefix or func.__name__
            cache_kwargs = {k: v for k, v in kwargs.items() if k != 'user_profile'}
            cache_key = StatisticsCacheManager.generate_cache_key(
                function_name=function_name,
                user_profile=user_profile,
                **cache_kwargs
            )
            
            # 尝试从缓存获取
            try:
                cached_result = cache.get(cache_key)
                if cached_result is not None:
                    logger.debug(f'缓存命中: {cache_key}')
                    return cached_result
            except Exception as e:
                logger.warning(f'缓存读取失败: {cache_key}, 错误: {e}')
            
            # 执行原函数
            try:
                result = func(*args, **kwargs)
                
                # 缓存结果
                cache_timeout = timeout or StatisticsCacheManager.get_timeout(function_name)
                try:
                    cache.set(cache_key, result, cache_timeout)
                    logger.debug(f'缓存设置: {cache_key}, 超时: {cache_timeout}秒')
                except Exception as e:
                    logger.warning(f'缓存设置失败: {cache_key}, 错误: {e}')
                
                return result
                
            except Exception as e:
                # 出错时可选择失效相关缓存
                if invalidate_on_error:
                    try:
                        StatisticsCacheManager.invalidate_cache(function_name, user_profile)
                    except:
                        pass
                raise
        
        return wrapper
    return decorator


def invalidate_statistics_cache(
    function_names: Union[str, List[str]] = None,
    user_profile: Optional['UserProfile'] = None,
    level: str = None
):
    """
    失效统计缓存的便捷函数
    
    Args:
        function_names: 函数名称或函数名称列表
        user_profile: 用户配置文件
        level: 缓存层级
    """
    if isinstance(function_names, str):
        function_names = [function_names]
    
    if function_names:
        for func_name in function_names:
            StatisticsCacheManager.invalidate_cache(func_name, user_profile, level)
    else:
        StatisticsCacheManager.invalidate_cache(None, user_profile, level)


def warm_up_cache(user_profile: Optional['UserProfile'] = None):
    """
    预热缓存 - 预先加载常用统计数据
    
    Args:
        user_profile: 用户配置文件
    """
    try:
        from apps.reports.selectors import (
            report_statistics,
            get_time_series_statistics,
            get_device_statistics
        )
        
        logger.info('开始预热统计缓存...')
        
        # 预热基础统计
        report_statistics(user_profile=user_profile)
        
        # 预热时间序列（最近3个月）
        end_date = timezone.now()
        start_date = end_date - timedelta(days=90)
        get_time_series_statistics(
            user_profile=user_profile,
            start_date=start_date,
            end_date=end_date
        )
        
        # 预热器械统计
        get_device_statistics(user_profile=user_profile, limit=10)
        
        logger.info('统计缓存预热完成')
        
    except Exception as e:
        logger.error(f'缓存预热失败: {e}')
