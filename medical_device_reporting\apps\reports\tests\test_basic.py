"""
不良事件上报管理基础测试
Basic Tests for Adverse Event Reports Management
"""

from django.test import TestCase
from django.apps import apps
from django.conf import settings


class ReportsAppBasicTest(TestCase):
    """
    Reports应用基础功能测试
    """
    
    def test_app_config(self):
        """测试应用配置"""
        app_config = apps.get_app_config('reports')
        self.assertEqual(app_config.name, 'apps.reports')
        self.assertEqual(app_config.verbose_name, '不良事件上报管理')
    
    def test_app_in_installed_apps(self):
        """测试应用是否在INSTALLED_APPS中"""
        self.assertIn('apps.reports', settings.INSTALLED_APPS)
    
    def test_models_import(self):
        """测试模型导入"""
        try:
            from apps.reports import models
            self.assertTrue(True)
        except ImportError:
            self.fail("无法导入reports.models")
    
    def test_views_import(self):
        """测试视图导入"""
        try:
            from apps.reports import views
            self.assertTrue(True)
        except ImportError:
            self.fail("无法导入reports.views")
    
    def test_services_import(self):
        """测试服务层导入"""
        try:
            from apps.reports import services
            self.assertTrue(True)
        except ImportError:
            self.fail("无法导入reports.services")
    
    def test_selectors_import(self):
        """测试选择器导入"""
        try:
            from apps.reports import selectors
            self.assertTrue(True)
        except ImportError:
            self.fail("无法导入reports.selectors")
    
    def test_permissions_import(self):
        """测试权限导入"""
        try:
            from apps.reports import permissions
            self.assertTrue(True)
        except ImportError:
            self.fail("无法导入reports.permissions")
    
    def test_serializers_import(self):
        """测试序列化器导入"""
        try:
            from apps.reports import serializers
            self.assertTrue(True)
        except ImportError:
            self.fail("无法导入reports.serializers")
    
    def test_forms_import(self):
        """测试表单导入"""
        try:
            from apps.reports import forms
            self.assertTrue(True)
        except ImportError:
            self.fail("无法导入reports.forms")

    def test_model_import(self):
        """测试模型导入"""
        try:
            from apps.reports.models import AdverseEventReport
            self.assertTrue(True)
        except ImportError:
            self.fail("无法导入AdverseEventReport模型")

    def test_model_fields(self):
        """测试模型字段"""
        from apps.reports.models import AdverseEventReport

        # 检查关键字段是否存在
        field_names = [field.name for field in AdverseEventReport._meta.fields]

        required_fields = [
            'report_number', 'status', 'reporter', 'department',
            'patient_name', 'patient_age', 'patient_gender',
            'device_malfunction', 'event_date', 'injury_level',
            'device_name', 'registration_number', 'manufacturer'
        ]

        for field_name in required_fields:
            self.assertIn(field_name, field_names, f"缺少字段: {field_name}")

    def test_model_choices(self):
        """测试模型选择项"""
        from apps.reports.models import AdverseEventReport

        # 检查状态选择
        status_choices = [choice[0] for choice in AdverseEventReport.STATUS_CHOICES]
        expected_statuses = ['draft', 'submitted', 'under_review', 'approved', 'rejected']

        for status in expected_statuses:
            self.assertIn(status, status_choices, f"缺少状态选择: {status}")

        # 检查伤害程度选择
        injury_choices = [choice[0] for choice in AdverseEventReport.INJURY_LEVEL_CHOICES]
        expected_injuries = ['death', 'serious_injury', 'other']

        for injury in expected_injuries:
            self.assertIn(injury, injury_choices, f"缺少伤害程度选择: {injury}")

    def test_model_permissions(self):
        """测试模型权限"""
        from apps.reports.models import AdverseEventReport

        permission_codes = [perm[0] for perm in AdverseEventReport._meta.permissions]
        expected_permissions = [
            'can_submit_report', 'can_review_report',
            'can_view_all_reports', 'can_export_reports'
        ]

        for perm in expected_permissions:
            self.assertIn(perm, permission_codes, f"缺少权限: {perm}")

    def test_admin_import(self):
        """测试管理后台导入"""
        try:
            from apps.reports.admin import AdverseEventReportAdmin
            self.assertTrue(True)
        except ImportError:
            self.fail("无法导入AdverseEventReportAdmin")

    def test_services_import(self):
        """测试服务层导入"""
        try:
            from apps.reports import services
            self.assertTrue(True)
        except ImportError:
            self.fail("无法导入reports.services")

    def test_service_functions(self):
        """测试服务层函数"""
        from apps.reports import services

        # 检查关键服务函数是否存在
        required_functions = [
            'report_create', 'report_update', 'report_submit',
            'report_review', 'generate_report_number',
            'get_report_by_id', 'get_report_by_number', 'get_user_reports'
        ]

        for func_name in required_functions:
            self.assertTrue(hasattr(services, func_name), f"缺少服务函数: {func_name}")

    def test_generate_report_number(self):
        """测试报告编号生成"""
        from apps.reports.services import generate_report_number

        # 生成报告编号
        report_number = generate_report_number()

        # 验证格式
        self.assertIsInstance(report_number, str)
        self.assertTrue(report_number.startswith('AER'))
        self.assertEqual(len(report_number), 14)  # AER + 8位日期 + 3位序号

    def test_selectors_import(self):
        """测试选择器导入"""
        try:
            from apps.reports import selectors
            self.assertTrue(True)
        except ImportError:
            self.fail("无法导入reports.selectors")

    def test_selector_functions(self):
        """测试选择器函数"""
        from apps.reports import selectors

        # 检查关键选择器函数是否存在
        required_functions = [
            'report_list', 'report_detail', 'report_get_by_number',
            'user_reports', 'department_reports', 'report_statistics',
            'report_list_paginated', 'report_search_suggestions',
            'report_list_pending_review', 'report_list_serious_events'
        ]

        for func_name in required_functions:
            self.assertTrue(hasattr(selectors, func_name), f"缺少选择器函数: {func_name}")

    def test_permission_filter_function(self):
        """测试权限过滤函数"""
        from apps.reports.selectors import _apply_permission_filter
        from apps.reports.models import AdverseEventReport
        from apps.users.models import UserProfile

        # 检查函数是否存在
        self.assertTrue(callable(_apply_permission_filter))

        # 创建基础查询集
        queryset = AdverseEventReport.objects.all()

        # 测试函数调用（不会实际执行查询）
        try:
            # 这里只是测试函数调用，不会实际查询数据库
            self.assertTrue(True)
        except Exception as e:
            self.fail(f"权限过滤函数调用失败: {str(e)}")

    def test_forms_classes(self):
        """测试表单类"""
        from apps.reports import forms

        # 检查关键表单类是否存在
        required_forms = [
            'ReporterInfoForm', 'PatientInfoForm', 'EventInfoForm',
            'DeviceInfoForm', 'AdverseEventReportForm', 'ReportSearchForm',
            'ReportReviewForm', 'ReportBulkActionForm'
        ]

        for form_name in required_forms:
            self.assertTrue(hasattr(forms, form_name), f"缺少表单类: {form_name}")

    def test_serializers_classes(self):
        """测试序列化器类"""
        from apps.reports import serializers

        # 检查关键序列化器类是否存在
        required_serializers = [
            'AdverseEventReportListSerializer', 'AdverseEventReportDetailSerializer',
            'AdverseEventReportCreateSerializer', 'AdverseEventReportUpdateSerializer',
            'AdverseEventReportReviewSerializer', 'ReportStatisticsSerializer',
            'ReportSearchSerializer', 'ReportBulkActionSerializer'
        ]

        for serializer_name in required_serializers:
            self.assertTrue(hasattr(serializers, serializer_name), f"缺少序列化器类: {serializer_name}")

    def test_form_instantiation(self):
        """测试表单实例化"""
        from apps.reports.forms import ReportSearchForm, ReportReviewForm

        # 测试搜索表单
        search_form = ReportSearchForm()
        self.assertIn('search', search_form.fields)
        self.assertIn('status', search_form.fields)

        # 测试审核表单
        review_form = ReportReviewForm()
        self.assertIn('action', review_form.fields)
        self.assertIn('comments', review_form.fields)

    def test_serializer_instantiation(self):
        """测试序列化器实例化"""
        from apps.reports.serializers import ReportStatisticsSerializer, ReportSearchSerializer

        # 测试统计序列化器
        stats_serializer = ReportStatisticsSerializer()
        self.assertIn('total_count', stats_serializer.fields)
        self.assertIn('draft_count', stats_serializer.fields)

        # 测试搜索序列化器
        search_serializer = ReportSearchSerializer()
        self.assertIn('search', search_serializer.fields)
        self.assertIn('status', search_serializer.fields)

    def test_views_import(self):
        """测试视图导入"""
        try:
            from apps.reports import views
            self.assertTrue(True)
        except ImportError:
            self.fail("无法导入reports.views")

    def test_apis_import(self):
        """测试API导入"""
        try:
            from apps.reports import apis
            self.assertTrue(True)
        except ImportError:
            self.fail("无法导入reports.apis")

    def test_urls_import(self):
        """测试URL配置导入"""
        try:
            from apps.reports.urls import urlpatterns
            self.assertTrue(len(urlpatterns) > 0)
        except ImportError:
            self.fail("无法导入reports.urls")

    def test_view_functions(self):
        """测试视图函数"""
        from apps.reports import views

        # 检查关键视图函数是否存在
        required_views = [
            'report_list_view', 'report_detail_view', 'report_create_view',
            'report_edit_view', 'report_submit_view', 'report_review_view',
            'report_dashboard_view', 'report_step_create_view'
        ]

        for view_name in required_views:
            self.assertTrue(hasattr(views, view_name), f"缺少视图函数: {view_name}")

    def test_api_classes(self):
        """测试API类"""
        from apps.reports import apis

        # 检查关键API类是否存在
        required_apis = [
            'ReportListAPIView', 'ReportDetailAPIView', 'ReportCreateAPIView',
            'ReportUpdateAPIView', 'ReportSubmitAPIView', 'ReportReviewAPIView',
            'ReportStatisticsAPIView'
        ]

        for api_name in required_apis:
            self.assertTrue(hasattr(apis, api_name), f"缺少API类: {api_name}")

    def test_url_patterns(self):
        """测试URL模式"""
        from apps.reports.urls import urlpatterns

        # 检查URL数量
        self.assertGreater(len(urlpatterns), 15, "URL模式数量不足")

        # 检查是否包含关键URL
        url_patterns = [str(pattern.pattern) for pattern in urlpatterns]

        required_patterns = ['', 'list/', 'create/', 'api/reports/']
        for pattern in required_patterns:
            self.assertIn(pattern, url_patterns, f"缺少URL模式: {pattern}")

    def test_url_reverse(self):
        """测试URL反向解析"""
        from django.urls import reverse

        # 测试关键URL的反向解析
        try:
            dashboard_url = reverse('reports:dashboard')
            self.assertEqual(dashboard_url, '/reports/')

            list_url = reverse('reports:report_list')
            self.assertEqual(list_url, '/reports/list/')

            create_url = reverse('reports:report_create')
            self.assertEqual(create_url, '/reports/create/')

        except Exception as e:
            self.fail(f"URL反向解析失败: {str(e)}")

    def test_permission_classes(self):
        """测试权限类"""
        from apps.reports.permissions import (
            IsReportOwnerOrAdmin, CanSubmitReport, CanReviewReport,
            CanViewAllReports, CanExportReports
        )

        # 检查权限类是否可以实例化
        permission_classes = [
            IsReportOwnerOrAdmin, CanSubmitReport, CanReviewReport,
            CanViewAllReports, CanExportReports
        ]

        for permission_class in permission_classes:
            try:
                instance = permission_class()
                self.assertTrue(hasattr(instance, 'has_permission'))
            except Exception as e:
                self.fail(f"权限类 {permission_class.__name__} 实例化失败: {str(e)}")

    def test_url_permission_config(self):
        """测试URL权限配置"""
        from apps.reports.url_permissions import URLPermissionConfig

        # 检查权限配置
        self.assertGreater(len(URLPermissionConfig.URL_PERMISSIONS), 5)

        # 检查关键URL的权限配置
        required_urls = [
            'reports:dashboard', 'reports:report_list', 'reports:report_create',
            'reports:report_detail', 'reports:report_edit'
        ]

        for url_name in required_urls:
            self.assertIn(url_name, URLPermissionConfig.URL_PERMISSIONS,
                         f"缺少URL权限配置: {url_name}")

    def test_middleware_integration(self):
        """测试中间件集成"""
        from apps.users.middleware import PermissionControlMiddleware

        middleware = PermissionControlMiddleware(lambda x: x)

        # 检查是否添加了reports相关的权限模式
        self.assertIn('/reports/pending-review/', middleware.ADMIN_REQUIRED_PATTERNS)
        self.assertIn('/reports/serious-events/', middleware.ADMIN_REQUIRED_PATTERNS)
        self.assertIn('/reports/', middleware.DEPARTMENT_MEMBER_OR_ADMIN_PATTERNS)
