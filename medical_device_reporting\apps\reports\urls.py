"""
不良事件上报管理URL配置
Adverse Event Reports Management URLs for Medical Device Reporting Platform
"""

from django.urls import path
from . import views, apis

app_name = 'reports'

# 页面视图URL
urlpatterns = [
    # 仪表板
    path('', views.report_dashboard_view, name='dashboard'),

    # 报告管理
    path('list/', views.report_list_view, name='report_list'),
    path('create/', views.report_create_view, name='report_create'),
    path('create/step/', views.report_step_create_entry_view, name='step_create'),
    path('create/step/<int:step>/', views.report_step_create_view, name='report_step_create'),
    path('<int:report_id>/', views.report_detail_view, name='report_detail'),
    path('<int:report_id>/edit/', views.report_edit_view, name='report_edit'),

    # 报告操作
    path('<int:report_id>/submit/', views.report_submit_view, name='report_submit'),
    path('<int:report_id>/review/', views.report_review_view, name='report_review'),

    # 专项列表
    path('pending-review/', views.report_pending_review_view, name='pending_review'),
    path('serious-events/', views.report_serious_events_view, name='serious_events'),
]

# API URL模式
api_urlpatterns = [
    # 报告CRUD API
    path('api/reports/', apis.ReportListAPIView.as_view(), name='api_report_list'),
    path('api/reports/create/', apis.ReportCreateAPIView.as_view(), name='api_report_create'),
    path('api/reports/<int:pk>/', apis.ReportDetailAPIView.as_view(), name='api_report_detail'),
    path('api/reports/<int:pk>/update/', apis.ReportUpdateAPIView.as_view(), name='api_report_update'),

    # 报告操作API
    path('api/reports/<int:pk>/submit/', apis.ReportSubmitAPIView.as_view(), name='api_report_submit'),
    path('api/reports/<int:pk>/review/', apis.ReportReviewAPIView.as_view(), name='api_report_review'),

    # 统计和搜索API
    path('api/reports/statistics/', apis.ReportStatisticsAPIView.as_view(), name='api_report_statistics'),
    path('api/reports/search-suggestions/', apis.report_search_suggestions, name='api_search_suggestions'),
    path('api/reports/dashboard-data/', apis.report_dashboard_data, name='api_dashboard_data'),
]

# 合并URL模式
urlpatterns += api_urlpatterns
